package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.config.MineRuProperties;
import com.edu.maizi_edu_sys.service.impl.MineRuTokenManagerImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * MineRU Token Manager 测试
 */
@ExtendWith(MockitoExtension.class)
public class MineRuTokenManagerTest {

    @Mock
    private MineRuProperties mineRuProperties;

    @Mock
    private MineRuProperties.Api apiConfig;

    @Mock
    private MineRuProperties.Api.LoadBalance loadBalance;

    private MineRuTokenManagerImpl tokenManager;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(tokenManager, "mineRuProperties", mineRuProperties);
        ReflectionTestUtils.setField(tokenManager, "apiConfig", apiConfig);
        when(mineRuProperties.getApi()).thenReturn(apiConfig);
        when(apiConfig.getLoadBalance()).thenReturn(loadBalance);
        when(loadBalance.getStrategy()).thenReturn("priority");
        when(loadBalance.isFailoverEnabled()).thenReturn(true);
    }

    @Test
    void testGetNextAvailableToken_PriorityStrategy() {
        // 准备测试数据
        List<MineRuProperties.Api.TokenConfig> tokens = Arrays.asList(
                createTokenConfig("token1", "Token 1", 1, 2000, true),
                createTokenConfig("token2", "Token 2", 2, 2000, true),
                createTokenConfig("token3", "Token 3", 1, 2000, true)
        );
        
        when(apiConfig.getTokens()).thenReturn(tokens);
        
        // 测试获取下一个可用token
        MineRuProperties.Api.TokenConfig token = tokenManager.getNextAvailableToken(100);
        
        assertNotNull(token);
        assertEquals(1, token.getPriority()); // 应该返回优先级最高的token
        assertTrue(Arrays.asList("token1", "token3").contains(token.getKey()));
    }

    @Test
    void testGetNextAvailableToken_NoAvailableTokens() {
        // 准备测试数据 - 所有token都被禁用
        List<MineRuProperties.Api.TokenConfig> tokens = Arrays.asList(
                createTokenConfig("token1", "Token 1", 1, 2000, false),
                createTokenConfig("token2", "Token 2", 2, 2000, false)
        );
        
        when(apiConfig.getTokens()).thenReturn(tokens);
        
        // 测试获取下一个可用token
        MineRuProperties.Api.TokenConfig token = tokenManager.getNextAvailableToken(100);
        
        assertNull(token); // 应该返回null，因为没有可用的token
    }

    @Test
    void testIsTokenAvailable() {
        // 准备测试数据
        List<MineRuProperties.Api.TokenConfig> tokens = Arrays.asList(
                createTokenConfig("token1", "Token 1", 1, 2000, true),
                createTokenConfig("token2", "Token 2", 2, 2000, false)
        );
        
        when(apiConfig.getTokens()).thenReturn(tokens);
        
        // 测试token可用性
        assertTrue(tokenManager.isTokenAvailable("token1"));
        assertFalse(tokenManager.isTokenAvailable("token2"));
        assertFalse(tokenManager.isTokenAvailable("nonexistent"));
    }

    @Test
    void testMarkTokenUnavailable() {
        // 准备测试数据
        List<MineRuProperties.Api.TokenConfig> tokens = Arrays.asList(
                createTokenConfig("token1", "Token 1", 1, 2000, true)
        );
        
        when(apiConfig.getTokens()).thenReturn(tokens);
        
        // 标记token不可用
        tokenManager.markTokenUnavailable("token1", "Test failure");
        
        // 验证token不可用
        assertFalse(tokenManager.isTokenAvailable("token1"));
    }

    @Test
    void testMarkTokenAvailable() {
        // 准备测试数据
        List<MineRuProperties.Api.TokenConfig> tokens = Arrays.asList(
                createTokenConfig("token1", "Token 1", 1, 2000, true)
        );
        
        when(apiConfig.getTokens()).thenReturn(tokens);
        
        // 先标记token不可用
        tokenManager.markTokenUnavailable("token1", "Test failure");
        assertFalse(tokenManager.isTokenAvailable("token1"));
        
        // 再标记token可用
        tokenManager.markTokenAvailable("token1");
        assertTrue(tokenManager.isTokenAvailable("token1"));
    }

    private MineRuProperties.Api.TokenConfig createTokenConfig(String key, String name, int priority, int dailyLimit, boolean enabled) {
        MineRuProperties.Api.TokenConfig config = new MineRuProperties.Api.TokenConfig();
        config.setKey(key);
        config.setName(name);
        config.setPriority(priority);
        config.setDailyLimit(dailyLimit);
        config.setEnabled(enabled);
        return config;
    }
}
