# Test environment configuration
spring.datasource.url=***************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=Hilury157195!
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA configuration for test
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect

# Disable caching for tests
app.features.topic-cache-enabled=false

# MyBatis-Plus configuration
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis-plus.type-aliases-package=com.edu.maizi_edu_sys.entity
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.global-config.db-config.logic-delete-field=deleted
mybatis-plus.global-config.db-config.logic-delete-value=1
mybatis-plus.global-config.db-config.logic-not-delete-value=0

# Redis configuration
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.database=0

# JWT configuration
jwt.secret=F9A8C28B7E5D3F1A6E5D4C3B2A1F0E9D8C7B6A5F4E3D2C1B0A9G8H7I6J5K4L3M2N1
jwt.expiration=86400000

# Logging configuration
logging.level.com.edu.maizi_edu_sys=DEBUG
logging.level.org.springframework=INFO
logging.level.org.hibernate=INFO