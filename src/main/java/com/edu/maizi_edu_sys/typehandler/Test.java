package com.edu.maizi_edu_sys.typehandler;

public class Test {

    public static void main(String[] args) {

        StringBuffer sb = new StringBuffer();
        for (int i = 4; i <= 24; i++) {
            if (i != 24)
                sb.append(i).append("-").append(i).append(",");
            else
                sb.append(i).append("-").append(i);

        }


        System.out.println(sb.toString());
    }
}
