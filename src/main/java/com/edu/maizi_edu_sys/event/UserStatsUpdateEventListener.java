package com.edu.maizi_edu_sys.event;

import com.edu.maizi_edu_sys.service.UserStatsCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 用户统计更新事件监听器
 * 监听用户统计相关事件，自动刷新缓存
 * 
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserStatsUpdateEventListener {
    
    private final UserStatsCacheService userStatsCacheService;
    
    /**
     * 处理用户统计更新事件
     */
    @EventListener
    @Async
    public void handleUserStatsUpdateEvent(UserStatsUpdateEvent event) {
        try {
            Long userId = event.getUserId();
            UserStatsUpdateEvent.UpdateType updateType = event.getUpdateType();
            
            log.debug("收到用户统计更新事件: userId={}, type={}, description={}", 
                userId, updateType, event.getDescription());
            
            // 根据更新类型决定是否需要刷新缓存
            switch (updateType) {
                case TOPIC_UPLOADED:
                case TOPIC_APPROVED:
                case TOPIC_REJECTED:
                case TOPIC_DELETED:
                case BATCH_UPLOAD:
                    // 这些操作都会影响用户统计，需要刷新缓存
                    userStatsCacheService.refreshUserStatsCache(userId);
                    log.debug("已刷新用户{}的统计缓存，原因: {}", userId, updateType.getDescription());
                    break;
                default:
                    log.debug("未知的更新类型: {}, 跳过缓存刷新", updateType);
                    break;
            }
            
        } catch (Exception e) {
            log.error("处理用户统计更新事件失败", e);
        }
    }
}
