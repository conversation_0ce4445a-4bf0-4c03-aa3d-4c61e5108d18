package com.edu.maizi_edu_sys.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 用户统计更新事件
 * 当用户上传题目或审核状态变化时触发
 * 
 * <AUTHOR>
 * @since 2025-07-27
 */
@Getter
public class UserStatsUpdateEvent extends ApplicationEvent {
    
    private final Long userId;
    private final UpdateType updateType;
    private final String description;
    
    public UserStatsUpdateEvent(Object source, Long userId, UpdateType updateType, String description) {
        super(source);
        this.userId = userId;
        this.updateType = updateType;
        this.description = description;
    }
    
    /**
     * 更新类型枚举
     */
    public enum UpdateType {
        TOPIC_UPLOADED("题目上传"),
        TOPIC_APPROVED("题目审核通过"),
        TOPIC_REJECTED("题目审核拒绝"),
        TOPIC_DELETED("题目删除"),
        BATCH_UPLOAD("批量上传");
        
        private final String description;
        
        UpdateType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
