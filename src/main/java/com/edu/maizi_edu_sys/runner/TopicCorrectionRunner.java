package com.edu.maizi_edu_sys.runner;

import com.edu.maizi_edu_sys.service.TopicCorrectionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Qualifier;
import java.time.Duration;
import com.edu.maizi_edu_sys.config.CorrectionProperties;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.core.task.TaskExecutor;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import javax.annotation.PreDestroy;

@Component
public class TopicCorrectionRunner implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(TopicCorrectionRunner.class);
    // 调度间隔统一通过 CorrectionProperties 获取
    

    @Autowired
    private CorrectionProperties correctionProperties;

    @Autowired
    private TopicCorrectionService topicCorrectionService;

    @Autowired
    @Qualifier("correctionExecutor")
    private TaskExecutor correctionExecutor;

    private final AtomicBoolean taskRunning = new AtomicBoolean(false);
    private volatile boolean shuttingDown = false;

    @Override
    public void run(String... args) {
        // 检查系统总开关
        if (!correctionProperties.isEnabled()) {
            logger.info("题目校对系统已禁用 (correction.enabled=false)，服务组件已加载但不会执行任何校对任务");
            return;
        }
        
        long intervalMinutes = correctionProperties.getRunner().getInterval().toMinutes();
        logger.info("题目自动校对独立服务启动，调度间隔 {} 分钟，批大小 {}", intervalMinutes, correctionProperties.getBatch().getSize());
    }

    /**
     * 每次任务执行完毕后，等待配置的 Duration 间隔再触发下一次。
     */
    @Scheduled(fixedDelayString = "#{@correctionProperties.getRunner().getInterval().toMillis()}")
    public void schedule() {
        // 检查系统总开关
        if (!correctionProperties.isEnabled()) {
            logger.debug("题目校对系统已禁用，跳过本次调度");
            return;
        }
        
        if (shuttingDown) {
            return;
        }
        if (!taskRunning.compareAndSet(false, true)) {
            logger.warn("上一批次仍在进行，跳过本次调度");
            return;
        }
        correctionExecutor.execute(() -> {
            try {
                Map<String, Object> result = topicCorrectionService.checkAndProcessOneBatch();
                boolean success = (Boolean) result.getOrDefault("success", false);
                int processedCount = (Integer) result.getOrDefault("processedCount", 0);
                
                if (success && processedCount > 0) {
                    logger.info("本批次处理完成，处理了 {} 个题目，服务将在 {} 分钟后处理下一批。", processedCount, correctionProperties.getRunner().getInterval().toMinutes());
                } else if (success && processedCount == 0) {
                    logger.info("当前没有更多题目需要处理，服务将在 {} 分钟后再次检查。", correctionProperties.getRunner().getInterval().toMinutes());
                } else {
                    String message = (String) result.getOrDefault("message", "未知错误");
                    logger.warn("批次处理失败: {}，服务将在 {} 分钟后重试。", message, correctionProperties.getRunner().getInterval().toMinutes());
                }
            } catch (Exception e) {
                logger.error("校对任务执行过程中发生未捕获的异常，服务将在 {} 分钟后重试。", correctionProperties.getRunner().getInterval().toMinutes(), e);
            } finally {
                taskRunning.set(false);
            }
        });
    }

    @PreDestroy
    public void onDestroy() {
        shuttingDown = true;
        logger.info("TopicCorrectionRunner 即将关闭，等待正在执行的任务完成...");
        // 调度线程已停止，等待余下执行的任务完结由线程池自身决定
    }
}
