package com.edu.maizi_edu_sys.runner;

import com.edu.maizi_edu_sys.service.DocumentParseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 文档解析任务定时调度器
 *
 * 注意：此服务已被禁用，因为数据库表 'document_parse_task' 不存在
 * 配置项：mineru.scheduler.enabled=false
 *
 * <AUTHOR> Integration
 * @since 2025-01-20
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "mineru.scheduler.enabled", havingValue = "true", matchIfMissing = false)
public class DocumentParseTaskScheduler {

    @Autowired
    private DocumentParseService documentParseService;

    /**
     * 构造函数 - 记录组件加载状态
     */
    public DocumentParseTaskScheduler() {
        log.warn("DocumentParseTaskScheduler 组件已加载，但应该被 @ConditionalOnProperty 禁用");
        log.warn("如果看到此消息，请检查 mineru.scheduler.enabled 配置");
    }

    /**
     * 定时同步任务状态
     * 每5分钟执行一次
     *
     * 注意：此方法应该被 @ConditionalOnProperty 禁用，不应该执行
     */
    @Scheduled(fixedRate = 300000) // 5分钟 = 300,000毫秒
    public void syncTaskStatus() {
        log.error("DocumentParseTaskScheduler.syncTaskStatus() 被意外调用！");
        log.error("此服务应该被禁用，因为数据库表 'document_parse_task' 不存在");
        log.error("请检查 mineru.scheduler.enabled 配置是否正确设置为 false");

        // 为了安全起见，直接返回，不执行任何操作
        return;

        /*
        try {
            log.debug("开始执行文档解析任务状态同步");
            documentParseService.syncTaskStatus();
            log.debug("文档解析任务状态同步完成");
        } catch (Exception e) {
            log.error("定时同步文档解析任务状态失败", e);
        }
        */
    }

    /**
     * 清理过期任务
     * 每天凌晨2点执行
     *
     * 注意：此方法应该被 @ConditionalOnProperty 禁用，不应该执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredTasks() {
        log.error("DocumentParseTaskScheduler.cleanupExpiredTasks() 被意外调用！");
        log.error("此服务应该被禁用，因为数据库表 'document_parse_task' 不存在");
        log.error("请检查 mineru.scheduler.enabled 配置是否正确设置为 false");

        // 为了安全起见，直接返回，不执行任何操作
        return;

        /*
        try {
            log.info("开始清理过期的文档解析任务");
            // 这里可以添加清理逻辑，比如删除30天前的已完成任务
            // documentParseService.cleanupExpiredTasks();
            log.info("过期任务清理完成");
        } catch (Exception e) {
            log.error("清理过期任务失败", e);
        }
        */
    }
}
