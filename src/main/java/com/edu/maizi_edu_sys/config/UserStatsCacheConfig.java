package com.edu.maizi_edu_sys.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.time.Duration;
import java.util.Collections;

/**
 * 用户统计缓存配置
 * 
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@Configuration
@EnableCaching
@RequiredArgsConstructor
public class UserStatsCacheConfig {
    
    private final UserStatsProperties userStatsProperties;
    
    /**
     * 用户统计专用缓存管理器
     */
    @Bean("userStatsCacheManager")
    @Primary
    public CacheManager userStatsCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();

        UserStatsProperties.Cache cacheConfig = userStatsProperties.getCache();

        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(cacheConfig.getMaxSize())
            .expireAfterWrite(Duration.ofMinutes(cacheConfig.getExpireMinutes()))
            .recordStats() // 启用统计信息
        );

        // 设置缓存名称
        cacheManager.setCacheNames(Collections.singleton("userStats"));

        log.info("用户统计缓存管理器初始化完成: maxSize={}, expireMinutes={}",
            cacheConfig.getMaxSize(), cacheConfig.getExpireMinutes());

        return cacheManager;
    }
    
    /**
     * 缓存预热配置
     */
    @Bean
    public CacheWarmupService cacheWarmupService() {
        return new CacheWarmupService();
    }
    
    /**
     * 缓存预热服务
     */
    public static class CacheWarmupService {
        
        /**
         * 应用启动后预热缓存
         */
        public void warmupCache() {
            log.info("开始预热用户统计缓存...");
            // 这里可以预加载一些热点用户的统计数据
            // 暂时留空，根据需要实现
            log.info("用户统计缓存预热完成");
        }
    }
}
