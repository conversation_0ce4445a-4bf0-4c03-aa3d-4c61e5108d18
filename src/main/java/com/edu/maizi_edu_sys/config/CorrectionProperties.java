package com.edu.maizi_edu_sys.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.Duration;
import java.time.LocalTime;

@Component
@ConfigurationProperties("correction")
public class CorrectionProperties {

    /** 是否启用整个题目校对系统 */
    private boolean enabled = false;
    
    private Runner runner = new Runner();
    private Batch batch = new Batch();
    private Executor executor = new Executor();
    private Api api = new Api();
    private Approval approval = new Approval();
    private Schedule schedule = new Schedule();

    public Runner getRunner() {
        return runner;
    }

    public void setRunner(Runner runner) {
        this.runner = runner;
    }

    public Batch getBatch() {
        return batch;
    }

    public void setBatch(Batch batch) {
        this.batch = batch;
    }

    public Executor getExecutor() {
        return executor;
    }

    public void setExecutor(Executor executor) {
        this.executor = executor;
    }

    public Api getApi() {
        return api;
    }

    public void setApi(Api api) {
        this.api = api;
    }

    public Approval getApproval() {
        return approval;
    }

    public void setApproval(Approval approval) {
        this.approval = approval;
    }

    public Schedule getSchedule() {
        return schedule;
    }

    public void setSchedule(Schedule schedule) {
        this.schedule = schedule;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public static class Runner {
        /** 调度间隔 */
        private Duration interval = Duration.ofMinutes(1);

        public Duration getInterval() {
            return interval;
        }

        public void setInterval(Duration interval) {
            this.interval = interval;
        }
    }

    public static class Batch {
        /** 每批处理题目数量 */
        private int size = 5;

        public int getSize() {
            return size;
        }

        public void setSize(int size) {
            this.size = size;
        }
    }

    public static class Executor {
        private int coreSize = 2;
        private int maxSize = 4;
        private int queueCapacity = 20;

        public int getCoreSize() {
            return coreSize;
        }

        public void setCoreSize(int coreSize) {
            this.coreSize = coreSize;
        }

        public int getMaxSize() {
            return maxSize;
        }

        public void setMaxSize(int maxSize) {
            this.maxSize = maxSize;
        }

        public int getQueueCapacity() {
            return queueCapacity;
        }

        public void setQueueCapacity(int queueCapacity) {
            this.queueCapacity = queueCapacity;
        }
    }

    public static class Api {
        /** AI API调用超时时间（秒） */
        private int timeoutSeconds = 60; // 默认60秒，防止长时间阻塞
        
        /** 最大并发AI调用数 */
        private int maxConcurrent = 4; // 限制并发，防止CPU过载
        
        /** 失败重试次数 */
        private int retryCount = 3;
        
        /** 重试延迟（秒） */
        private int retryDelaySeconds = 30;

        public int getTimeoutSeconds() {
            return timeoutSeconds;
        }

        public void setTimeoutSeconds(int timeoutSeconds) {
            this.timeoutSeconds = timeoutSeconds;
        }
        
        public int getMaxConcurrent() {
            return maxConcurrent;
        }
        
        public void setMaxConcurrent(int maxConcurrent) {
            this.maxConcurrent = maxConcurrent;
        }
        
        public int getRetryCount() {
            return retryCount;
        }
        
        public void setRetryCount(int retryCount) {
            this.retryCount = retryCount;
        }
        
        public int getRetryDelaySeconds() {
            return retryDelaySeconds;
        }
        
        public void setRetryDelaySeconds(int retryDelaySeconds) {
            this.retryDelaySeconds = retryDelaySeconds;
        }
    }

    /**
     * 审核配置
     */
    public static class Approval {
        /** 是否启用人工审核模式 */
        private boolean enabled = true;
        
        /** 自动审核超时时间（小时），超过此时间未审核的修正将被自动拒绝 */
        private int autoRejectHours = 2160; // 三个月

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public int getAutoRejectHours() {
            return autoRejectHours;
        }

        public void setAutoRejectHours(int autoRejectHours) {
            this.autoRejectHours = autoRejectHours;
        }
    }

    /**
     * 时间调度配置
     */
    public static class Schedule {
        /** 是否启用时间限制 */
        private boolean enabled = false;
        
        /** 允许自动更新的开始时间 */
        @DateTimeFormat(pattern = "HH:mm")
        private LocalTime startTime = LocalTime.of(2, 0); // 默认凌晨2点
        
        /** 允许自动更新的结束时间 */
        @DateTimeFormat(pattern = "HH:mm")
        private LocalTime endTime = LocalTime.of(6, 0); // 默认凌晨6点

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public LocalTime getStartTime() {
            return startTime;
        }

        public void setStartTime(LocalTime startTime) {
            this.startTime = startTime;
        }

        public LocalTime getEndTime() {
            return endTime;
        }

        public void setEndTime(LocalTime endTime) {
            this.endTime = endTime;
        }

        /**
         * 检查当前时间是否在允许的更新时间窗口内
         */
        public boolean isInAllowedTimeWindow() {
            LocalTime now = LocalTime.now();
            if (startTime.isBefore(endTime)) {
                // 正常情况：如 02:00-06:00
                return !now.isBefore(startTime) && !now.isAfter(endTime);
            } else {
                // 跨天情况：如 22:00-06:00
                return !now.isBefore(startTime) || !now.isAfter(endTime);
            }
        }
    }
}
