package com.edu.maizi_edu_sys.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 用户统计系统配置属性
 * 
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
@Component
@ConfigurationProperties(prefix = "user-stats")
public class UserStatsProperties {

    /**
     * 内存安全模式配置
     */
    private MemorySafeMode memorySafeMode = new MemorySafeMode();

    /**
     * 缓存配置
     */
    private Cache cache = new Cache();

    @Data
    public static class MemorySafeMode {
        /**
         * 是否启用内存安全模式
         */
        private boolean enabled = true;

        /**
         * 内存使用率阈值（超过此值启用简化计算）
         */
        private double memoryThreshold = 0.85;

        /**
         * 单次查询最大记录数
         */
        private int maxQueryLimit = 5000;

        /**
         * 排名计算模式: full, simplified, disabled
         */
        private String rankingCalculation = "simplified";
    }

    @Data
    public static class Cache {
        /**
         * 是否启用缓存
         */
        private boolean enabled = true;

        /**
         * 缓存过期时间（分钟）
         */
        private int expireMinutes = 30;

        /**
         * 最大缓存条目数
         */
        private int maxSize = 1000;

        /**
         * 是否自动刷新活跃用户缓存
         */
        private boolean autoRefresh = true;

        /**
         * 缓存刷新间隔（分钟）
         */
        private int refreshIntervalMinutes = 30;
    }
}
