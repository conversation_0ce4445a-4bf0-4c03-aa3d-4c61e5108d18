package com.edu.maizi_edu_sys.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * MineRU API配置属性
 * 
 * <AUTHOR> Integration
 * @since 2025-01-20
 */
@Data
@Component
@ConfigurationProperties(prefix = "mineru")
public class MineRuProperties {

    /**
     * API配置
     */
    private Api api = new Api();

    /**
     * 解析配置
     */
    private Parse parse = new Parse();

    /**
     * 调度器配置
     */
    private Scheduler scheduler = new Scheduler();

    /**
     * 回调配置
     */
    private Callback callback = new Callback();

    /**
     * 缓存配置
     */
    private Cache cache = new Cache();

    /**
     * 监控配置
     */
    private Monitoring monitoring = new Monitoring();

    @Data
    public static class Api {
        /**
         * API基础URL
         */
        private String baseUrl = "https://mineru.net";

        /**
         * API请求超时时间(秒)
         */
        private int timeout = 60;

        /**
         * 最大重试次数
         */
        private int maxRetries = 3;

        /**
         * 重试延迟(毫秒)
         */
        private long retryDelay = 2000;

        /**
         * 多密钥配置
         */
        private java.util.List<TokenConfig> tokens = new java.util.ArrayList<>();

        /**
         * 负载均衡配置
         */
        private LoadBalance loadBalance = new LoadBalance();

        @Data
        public static class TokenConfig {
            /**
             * API密钥
             */
            private String key;

            /**
             * 密钥别名
             */
            private String name;

            /**
             * 优先级 (1=最高)
             */
            private int priority = 1;

            /**
             * 每日高优先级页数限制
             */
            private int dailyLimit = 2000;

            /**
             * 是否启用
             */
            private boolean enabled = true;
        }

        @Data
        public static class LoadBalance {
            /**
             * 轮询策略: round-robin, priority, least-used
             */
            private String strategy = "round-robin";

            /**
             * 启用故障转移
             */
            private boolean failoverEnabled = true;

            /**
             * 健康检查间隔(毫秒)
             */
            private long healthCheckInterval = 300000;
        }
    }

    @Data
    public static class Parse {
        /**
         * 默认解析参数
         */
        private Default defaultConfig = new Default();

        /**
         * 文件配置
         */
        private File file = new File();

        @Data
        public static class Default {
            /**
             * 默认启用OCR
             */
            private boolean isOcr = true;

            /**
             * 默认启用公式识别
             */
            private boolean enableFormula = true;

            /**
             * 默认启用表格识别
             */
            private boolean enableTable = true;

            /**
             * 默认语言 (ch=中文, en=英文, auto=自动识别)
             */
            private String language = "ch";

            /**
             * 模型版本 (v1/v2, 推荐使用v2获得更好效果)
             */
            private String modelVersion = "v2";

            /**
             * 默认输出格式 (支持数组格式: ["markdown", "json"])
             */
            private java.util.List<String> outputFormat = java.util.Arrays.asList("markdown", "json");

            /**
             * 额外输出格式 (可选: docx, html, latex)
             */
            private java.util.List<String> extraFormats = java.util.Arrays.asList("docx", "html");
        }

        @Data
        public static class File {
            /**
             * 单文件最大大小 (官方限制200MB)
             */
            private String maxSize = "200MB";

            /**
             * 最大页数限制 (官方限制600页)
             */
            private int maxPages = 600;

            /**
             * 官方支持的文件类型
             */
            private String allowedTypes = "pdf,doc,docx,ppt,pptx,png,jpg,jpeg";

            /**
             * 批量上传最大文件数 (官方限制200个)
             */
            private int batchMaxCount = 200;
        }
    }

    @Data
    public static class Scheduler {
        /**
         * 状态同步间隔(毫秒)
         */
        private long syncInterval = 300000; // 5分钟

        /**
         * 清理任务cron表达式
         */
        private String cleanupCron = "0 0 2 * * ?"; // 每天凌晨2点

        /**
         * 清理过期天数
         */
        private int cleanupExpireDays = 30;

        /**
         * 最大并发同步任务数
         */
        private int maxConcurrentSync = 10;
    }

    @Data
    public static class Callback {
        /**
         * 是否启用回调
         */
        private boolean enabled = true;

        /**
         * 回调验证密钥
         */
        private String secret = "mineru_callback_2025";

        /**
         * 回调超时时间(秒)
         */
        private int timeout = 30;
    }

    @Data
    public static class Cache {
        /**
         * 是否启用缓存
         */
        private boolean enabled = true;

        /**
         * 缓存TTL(秒)
         */
        private long ttl = 3600; // 1小时

        /**
         * 最大缓存条目数
         */
        private int maxSize = 1000;
    }

    @Data
    public static class Monitoring {
        /**
         * 是否启用监控
         */
        private boolean enabled = true;

        /**
         * 指标收集间隔(秒)
         */
        private int metricsInterval = 60;

        /**
         * 是否记录慢请求
         */
        private boolean logSlowRequests = true;

        /**
         * 慢请求阈值(毫秒)
         */
        private long slowRequestThreshold = 10000;
    }
}
