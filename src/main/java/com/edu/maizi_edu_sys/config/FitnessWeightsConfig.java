package com.edu.maizi_edu_sys.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 适应度权重配置类
 * 从application.yml中读取适应度权重配置
 */
@Component
@ConfigurationProperties(prefix = "algorithm.genetic.fitness-weights")
@Data
public class FitnessWeightsConfig {
    
    private double score = 0.21;
    private double quality = 0.20;
    private double difficultyDistribution = 0.15;
    private double cognitiveDistribution = 0.01;
    private double kpCoverage = 0.10;
    private double topicTypeDiversity = 0.05;
    private double kpTypeBalance = 0.03;
    private double tagDiversity = 0.25;
    
    /**
     * 获取权重映射
     */
    public Map<String, Double> getWeightsMap() {
        Map<String, Double> weights = new HashMap<>();
        weights.put("score", score);
        weights.put("quality", quality);
        weights.put("difficulty-distribution", difficultyDistribution);
        weights.put("cognitive-distribution", cognitiveDistribution);
        weights.put("kp-coverage", kpCoverage);
        weights.put("topic-type-diversity", topicTypeDiversity);
        weights.put("kp-type-balance", kpTypeBalance);
        weights.put("tag-diversity", tagDiversity);
        return weights;
    }
    
    /**
     * 验证权重总和是否为1.0
     */
    public boolean isValid() {
        double sum = score + quality + difficultyDistribution + 
                    cognitiveDistribution + kpCoverage + topicTypeDiversity + 
                    kpTypeBalance + tagDiversity;
        return Math.abs(sum - 1.0) < 0.001;
    }
    
    /**
     * 获取权重总和
     */
    public double getWeightSum() {
        return score + quality + difficultyDistribution + 
               cognitiveDistribution + kpCoverage + topicTypeDiversity + 
               kpTypeBalance + tagDiversity;
    }
}
