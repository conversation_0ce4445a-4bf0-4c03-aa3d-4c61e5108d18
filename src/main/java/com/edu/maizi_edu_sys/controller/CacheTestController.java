package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.dto.UserStatsDTO;
import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.service.UserStatsCacheService;
import com.edu.maizi_edu_sys.service.AuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 缓存测试控制器
 * 用于测试用户统计缓存功能
 * 
 * <AUTHOR>
 * @since 2025-07-27
 */
@RestController
@RequestMapping("/api/cache-test")
@RequiredArgsConstructor
@Slf4j
public class CacheTestController {
    
    private final UserStatsCacheService userStatsCacheService;
    private final AuthService authService;
    
    /**
     * 测试缓存功能
     */
    @GetMapping("/user-stats")
    public ApiResponse<Map<String, Object>> testUserStatsCache(HttpServletRequest request) {
        try {
            Long userId = authService.getCurrentUserId();
            if (userId == null) {
                return ApiResponse.error("用户未认证");
            }
            
            long startTime = System.currentTimeMillis();
            
            // 第一次调用（应该查询数据库）
            UserStatsDTO stats1 = userStatsCacheService.getUserStats(userId);
            long firstCallTime = System.currentTimeMillis() - startTime;
            
            startTime = System.currentTimeMillis();
            
            // 第二次调用（应该从缓存获取）
            UserStatsDTO stats2 = userStatsCacheService.getUserStats(userId);
            long secondCallTime = System.currentTimeMillis() - startTime;
            
            // 获取缓存统计信息
            UserStatsCacheService.CacheStats cacheStats = userStatsCacheService.getCacheStats();
            
            Map<String, Object> result = new HashMap<>();
            result.put("userId", userId);
            result.put("firstCallTime", firstCallTime + "ms");
            result.put("secondCallTime", secondCallTime + "ms");
            result.put("speedImprovement", firstCallTime > 0 ? (firstCallTime / (double) Math.max(secondCallTime, 1)) : 0);
            Map<String, Object> cacheStatsMap = new HashMap<>();
            cacheStatsMap.put("hitCount", cacheStats.getHitCount());
            cacheStatsMap.put("missCount", cacheStats.getMissCount());
            cacheStatsMap.put("hitRate", String.format("%.2f%%", cacheStats.getHitRate() * 100));
            result.put("cacheStats", cacheStatsMap);
            result.put("userStats", stats2);
            
            log.info("缓存测试完成: userId={}, 第一次调用={}ms, 第二次调用={}ms, 提升倍数={}", 
                userId, firstCallTime, secondCallTime, 
                firstCallTime > 0 ? (firstCallTime / (double) Math.max(secondCallTime, 1)) : 0);
            
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("缓存测试失败", e);
            return ApiResponse.error("缓存测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 清除缓存测试
     */
    @PostMapping("/clear-cache")
    public ApiResponse<String> testClearCache(HttpServletRequest request) {
        try {
            Long userId = authService.getCurrentUserId();
            if (userId == null) {
                return ApiResponse.error("用户未认证");
            }
            
            userStatsCacheService.clearUserStatsCache(userId);
            log.info("已清除用户{}的统计缓存", userId);
            
            return ApiResponse.success("缓存清除成功");
            
        } catch (Exception e) {
            log.error("清除缓存失败", e);
            return ApiResponse.error("清除缓存失败: " + e.getMessage());
        }
    }
}
