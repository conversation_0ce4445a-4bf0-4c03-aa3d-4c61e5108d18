package com.edu.maizi_edu_sys.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.service.AdminUserUploadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员用户上传统计控制器
 */
@RestController
@RequestMapping("/api/admin/user-upload")
@RequiredArgsConstructor
@Slf4j
public class AdminUserUploadController {
    
    private final AdminUserUploadService adminUserUploadService;
    
    /**
     * 获取用户上传统计概览
     */
    @GetMapping("/overview")
    public ApiResponse<Map<String, Object>> getUploadOverview() {
        try {
            Map<String, Object> overview = adminUserUploadService.getUploadOverview();
            return ApiResponse.success(overview);
        } catch (Exception e) {
            log.error("获取用户上传概览失败", e);
            return ApiResponse.error("获取用户上传概览失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户每日上传统计列表
     */
    @GetMapping("/daily-stats")
    public ApiResponse<Map<String, Object>> getDailyUploadStats(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false) String orderType) {
        try {
            IPage<?> result = adminUserUploadService.getDailyUploadStats(
                page, size, startDate, endDate, username, orderBy, orderType);
            
            Map<String, Object> data = new HashMap<>();
            data.put("list", result.getRecords());
            data.put("current", result.getCurrent());
            data.put("pages", result.getPages());
            data.put("total", result.getTotal());
            data.put("size", result.getSize());
            
            return ApiResponse.success(data);
        } catch (Exception e) {
            log.error("获取用户每日上传统计失败", e);
            return ApiResponse.error("获取用户每日上传统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取异常上传行为检测
     */
    @GetMapping("/anomaly-detection")
    public ApiResponse<List<?>> getAnomalyDetection(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        try {
            List<?> anomalies = adminUserUploadService.detectAnomalousUploads(date);
            return ApiResponse.success(anomalies);
        } catch (Exception e) {
            log.error("获取异常上传行为检测失败", e);
            return ApiResponse.error("获取异常上传行为检测失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户上传趋势分析
     */
    @GetMapping("/trend-analysis")
    public ApiResponse<Map<String, Object>> getTrendAnalysis(
            @RequestParam(required = false) Long userId,
            @RequestParam(defaultValue = "7") int days) {
        try {
            Map<String, Object> trend = adminUserUploadService.getUserUploadTrend(userId, days);
            return ApiResponse.success(trend);
        } catch (Exception e) {
            log.error("获取用户上传趋势分析失败", e);
            return ApiResponse.error("获取用户上传趋势分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户上传详情
     */
    @GetMapping("/user-detail/{userId}")
    public ApiResponse<Map<String, Object>> getUserUploadDetail(
            @PathVariable Long userId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        try {
            Map<String, Object> detail = adminUserUploadService.getUserUploadDetail(userId, date);
            return ApiResponse.success(detail);
        } catch (Exception e) {
            log.error("获取用户上传详情失败", e);
            return ApiResponse.error("获取用户上传详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页获取用户上传时间线
     */
    @GetMapping("/user-timeline/{userId}")
    public ApiResponse<Map<String, Object>> getUserUploadTimeline(
            @PathVariable Long userId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Map<String, Object> timeline = adminUserUploadService.getUserUploadTimeline(userId, date, page, size);
            return ApiResponse.success(timeline);
        } catch (Exception e) {
            log.error("获取用户上传时间线失败", e);
            return ApiResponse.error("获取用户上传时间线失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置用户上传限制
     */
    @PostMapping("/set-limit")
    public ApiResponse<String> setUserUploadLimit(
            @RequestBody Map<String, Object> request,
            HttpServletRequest httpRequest) {
        try {
            // 验证必需参数
            if (request.get("userId") == null) {
                return ApiResponse.error("用户ID不能为空");
            }
            if (request.get("dailyLimit") == null) {
                return ApiResponse.error("每日限制不能为空");
            }
            if (request.get("reason") == null || request.get("reason").toString().trim().isEmpty()) {
                return ApiResponse.error("限制原因不能为空");
            }

            // 安全解析参数
            Long userId;
            try {
                userId = Long.valueOf(request.get("userId").toString());
            } catch (NumberFormatException e) {
                return ApiResponse.error("用户ID格式无效");
            }

            Integer dailyLimit;
            try {
                String dailyLimitStr = request.get("dailyLimit").toString().trim();
                if (dailyLimitStr.isEmpty()) {
                    return ApiResponse.error("每日限制不能为空");
                }
                dailyLimit = Integer.valueOf(dailyLimitStr);
                if (dailyLimit < 0) {
                    return ApiResponse.error("每日限制不能为负数");
                }
            } catch (NumberFormatException e) {
                return ApiResponse.error("每日限制格式无效，请输入有效的数字");
            }

            String reason = request.get("reason").toString().trim();
            
            Long adminId = getCurrentAdminId(httpRequest);
            adminUserUploadService.setUserUploadLimit(userId, dailyLimit, reason, adminId);
            
            return ApiResponse.success("设置用户上传限制成功");
        } catch (Exception e) {
            log.error("设置用户上传限制失败", e);
            return ApiResponse.error("设置用户上传限制失败: " + e.getMessage());
        }
    }
    
    /**
     * 导出用户上传统计
     */
    @GetMapping("/export")
    public ApiResponse<String> exportUploadStats(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            HttpServletRequest request) {
        try {
            String exportUrl = adminUserUploadService.exportUploadStats(startDate, endDate);
            return ApiResponse.success(exportUrl);
        } catch (Exception e) {
            log.error("导出用户上传统计失败", e);
            return ApiResponse.error("导出用户上传统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前管理员ID
     */
    private Long getCurrentAdminId(HttpServletRequest request) {
        // TODO: 从JWT token中获取管理员ID
        String adminIdHeader = request.getHeader("X-Admin-Id");
        if (adminIdHeader != null) {
            try {
                return Long.parseLong(adminIdHeader);
            } catch (NumberFormatException e) {
                log.warn("无效的管理员ID格式: {}", adminIdHeader);
            }
        }
        // 默认返回管理员ID 1
        return 1L;
    }
} 