package com.edu.maizi_edu_sys.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 用户认证页面控制器
 * 处理用户登录、注册等页面访问
 */
@Controller
@RequestMapping("/auth")
public class AuthController {
    
    /**
     * 用户登录页面 - 支持多种访问路径
     */
    @GetMapping("/login")
    public String authLoginPage() {
        return "auth/login";
    }
    
    /**
     * 用户注册页面
     */
    @GetMapping("/register")
    public String registerPage() {
        return "auth/register";
    }
    
    /**
     * 找回密码页面
     */
    @GetMapping("/forgot-password")
    public String forgotPasswordPage() {
        return "auth/forgot-password";
    }
    
    /**
     * 重置密码页面
     */
    @GetMapping("/reset-password")
    public String resetPasswordPage() {
        return "auth/reset-password";
    }
}

/**
 * 兼容旧版本的登录页面控制器
 */
@Controller
class LegacyAuthController {
    /**
     * 兼容旧版本的登录页面访问
     */
    @GetMapping("/login")
    public String legacyLoginPage() {
        return "redirect:/auth/login";
    }
} 