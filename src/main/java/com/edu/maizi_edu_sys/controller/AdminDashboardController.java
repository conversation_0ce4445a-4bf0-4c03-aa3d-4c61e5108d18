package com.edu.maizi_edu_sys.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.edu.maizi_edu_sys.entity.TopicAudit;
import com.edu.maizi_edu_sys.entity.User;
import com.edu.maizi_edu_sys.mapper.UserMapper;
import com.edu.maizi_edu_sys.repository.TopicAuditMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员仪表板控制器
 */
@RestController
@RequestMapping("/api/admin/dashboard")
@RequiredArgsConstructor
public class AdminDashboardController {
    
    private final UserMapper userMapper;
    private final TopicAuditMapper topicAuditMapper;
    
    /**
     * 获取仪表板统计数据
     */
    @GetMapping("/stats")
    public ResponseEntity<?> getDashboardStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 用户总数
            Long totalUsers = userMapper.selectCount(new QueryWrapper<User>());
            
            // 题目总数
            Long totalTopics = topicAuditMapper.selectCount(new QueryWrapper<TopicAudit>());
            
            // 待审核题目
            Long pendingAudits = topicAuditMapper.selectCount(
                new QueryWrapper<TopicAudit>().eq("audit_status", TopicAudit.AuditStatus.PENDING)
            );
            
            // 在线用户（模拟数据）
            Long onlineUsers = (long) (Math.random() * 50) + 10;
            
            stats.put("totalUsers", totalUsers);
            stats.put("totalTopics", totalTopics);
            stats.put("pendingAudits", pendingAudits);
            stats.put("onlineUsers", onlineUsers);
            
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("获取仪表板统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取最近提交的题目
     */
    @GetMapping("/recent-topics")
    public ResponseEntity<?> getRecentTopics(@RequestParam(defaultValue = "10") int limit) {
        try {
            QueryWrapper<TopicAudit> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByDesc("submit_time").last("LIMIT " + limit);
            
            List<TopicAudit> recentTopics = topicAuditMapper.selectList(queryWrapper);
            
            List<Map<String, Object>> result = new ArrayList<>();
            for (TopicAudit topic : recentTopics) {
                Map<String, Object> item = new HashMap<>();
                item.put("id", topic.getId());
                item.put("title", topic.getTitle());
                item.put("type", topic.getType());
                item.put("status", topic.getAuditStatus());
                item.put("submitTime", topic.getSubmitTime());
                item.put("userId", topic.getUserId());
                result.add(item);
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("获取最近题目失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取系统日志
     */
    @GetMapping("/system-logs")
    public ResponseEntity<?> getSystemLogs(@RequestParam(defaultValue = "20") int limit) {
        try {
            List<Map<String, Object>> logs = new ArrayList<>();
            
            // 模拟系统日志数据
            String[] actions = {"用户登录", "题目提交", "审核通过", "审核拒绝", "用户注册"};
            String[] levels = {"INFO", "WARN", "ERROR"};
            
            for (int i = 0; i < limit; i++) {
                Map<String, Object> log = new HashMap<>();
                log.put("id", i + 1);
                log.put("action", actions[(int) (Math.random() * actions.length)]);
                log.put("level", levels[(int) (Math.random() * levels.length)]);
                log.put("message", "用户 " + (i + 1) + " 进行了" + actions[(int) (Math.random() * actions.length)]);
                log.put("timestamp", LocalDateTime.now().minusMinutes(i * 10));
                logs.add(log);
            }
            
            return ResponseEntity.ok(logs);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("获取系统日志失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取通知消息
     */
    @GetMapping("/notifications")
    public ResponseEntity<?> getNotifications(@RequestParam(defaultValue = "5") int limit) {
        try {
            List<Map<String, Object>> notifications = new ArrayList<>();
            
            // 模拟通知数据
            String[] types = {"audit", "system", "user"};
            String[] messages = {
                "有新的题目待审核",
                "系统将在今晚进行维护",
                "新用户注册需要审核",
                "发现可疑登录行为",
                "数据库备份完成"
            };
            
            for (int i = 0; i < limit; i++) {
                Map<String, Object> notification = new HashMap<>();
                notification.put("id", i + 1);
                notification.put("type", types[(int) (Math.random() * types.length)]);
                notification.put("message", messages[(int) (Math.random() * messages.length)]);
                notification.put("read", Math.random() > 0.5);
                notification.put("timestamp", LocalDateTime.now().minusHours(i));
                notifications.add(notification);
            }
            
            return ResponseEntity.ok(notifications);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("获取通知消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户活跃度图表数据
     */
    @GetMapping("/user-activity-chart")
    public ResponseEntity<?> getUserActivityChart(@RequestParam(defaultValue = "7") int days) {
        try {
            Map<String, Object> chartData = new HashMap<>();
            
            List<String> labels = new ArrayList<>();
            List<Integer> data = new ArrayList<>();
            
            for (int i = days - 1; i >= 0; i--) {
                LocalDateTime date = LocalDateTime.now().minusDays(i);
                labels.add(String.format("%02d-%02d", date.getMonthValue(), date.getDayOfMonth()));
                data.add((int) (Math.random() * 100) + 20); // 模拟数据
            }
            
            chartData.put("labels", labels);
            chartData.put("data", data);
            
            return ResponseEntity.ok(chartData);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("获取用户活跃度图表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取题目类型分布图表数据
     */
    @GetMapping("/topic-type-chart")
    public ResponseEntity<?> getTopicTypeChart() {
        try {
            Map<String, Object> chartData = new HashMap<>();
            
            // 统计题目类型分布
            List<TopicAudit> allTopics = topicAuditMapper.selectList(new QueryWrapper<>());
            Map<String, Integer> typeCount = new HashMap<>();
            
            for (TopicAudit topic : allTopics) {
                typeCount.put(topic.getType(), typeCount.getOrDefault(topic.getType(), 0) + 1);
            }
            
            List<String> labels = new ArrayList<>();
            List<Integer> data = new ArrayList<>();
            
            Map<String, String> typeNames = new HashMap<>();
            typeNames.put("single", "单选题");
            typeNames.put("multiple", "多选题");
            typeNames.put("judge", "判断题");
            typeNames.put("fill", "填空题");
            typeNames.put("short", "简答题");
            
            for (Map.Entry<String, Integer> entry : typeCount.entrySet()) {
                labels.add(typeNames.getOrDefault(entry.getKey(), entry.getKey()));
                data.add(entry.getValue());
            }
            
            chartData.put("labels", labels);
            chartData.put("data", data);
            
            return ResponseEntity.ok(chartData);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("获取题目类型分布图表失败: " + e.getMessage());
        }
    }
} 