package com.edu.maizi_edu_sys.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.edu.maizi_edu_sys.dto.AuditRequestDTO;
import com.edu.maizi_edu_sys.dto.TopicAuditDTO;
import com.edu.maizi_edu_sys.entity.SystemMessage;
import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.service.AuthService;
import com.edu.maizi_edu_sys.service.SystemMessageService;
import com.edu.maizi_edu_sys.service.TopicAuditService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * 题目审核控制器
 */
@RestController
@RequestMapping("/api/audit")
@Slf4j
public class TopicAuditController {

    @Autowired
    private TopicAuditService topicAuditService;

    @Autowired
    private SystemMessageService systemMessageService;

    @Autowired
    private AuthService authService;

    /**
     * 获取待审核题目列表（管理员权限）
     */
    @GetMapping("/pending")
    public ApiResponse<IPage<TopicAuditDTO>> getPendingAudits(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            // 检查审核权限
            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            IPage<TopicAuditDTO> audits = topicAuditService.getPendingAudits(pageNum, pageSize);
            return ApiResponse.success(audits);
        } catch (Exception e) {
            log.error("获取待审核题目列表失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取待审核题目列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的审核记录
     */
    @GetMapping("/my-audits")
    public ApiResponse<IPage<TopicAuditDTO>> getUserAudits(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            IPage<TopicAuditDTO> audits = topicAuditService.getUserAudits(currentUserId, pageNum, pageSize);
            return ApiResponse.success(audits);
        } catch (Exception e) {
            log.error("获取用户审核记录失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取用户审核记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取审核员的审核记录（管理员权限）
     */
    @GetMapping("/auditor-records")
    public ApiResponse<IPage<TopicAuditDTO>> getAuditorAudits(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            // 检查审核权限
            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            IPage<TopicAuditDTO> audits = topicAuditService.getAuditorAudits(currentUserId, pageNum, pageSize);
            return ApiResponse.success(audits);
        } catch (Exception e) {
            log.error("获取审核员记录失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取审核员记录失败: " + e.getMessage());
        }
    }

    /**
     * 审核题目（通过或拒绝）
     */
    @PostMapping("/review")
    public ApiResponse<?> auditTopic(@Valid @RequestBody AuditRequestDTO auditRequest) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            // 检查审核权限
            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            topicAuditService.auditTopic(auditRequest, currentUserId);
            
            String result = auditRequest.getAuditResult() == 1 ? "通过" : "拒绝";
            log.info("审核完成: 审核员={}, 结果={}", currentUserId, result);
            
            return ApiResponse.success("审核完成");
        } catch (IllegalArgumentException e) {
            log.warn("审核失败: {}", e.getMessage());
            return ApiResponse.error(e.getMessage());
        } catch (Exception e) {
            log.error("审核题目失败: {}", e.getMessage(), e);
            return ApiResponse.error("审核失败: " + e.getMessage());
        }
    }

    /**
     * 获取审核统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getAuditStatistics() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            // 检查审核权限
            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            Map<String, Object> statistics = topicAuditService.getAuditStatistics();
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            log.error("获取审核统计失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取审核统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户待审核题目数量
     */
    @GetMapping("/pending-count")
    public ApiResponse<Long> getUserPendingCount() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            Long count = topicAuditService.getUserPendingCount(currentUserId);
            return ApiResponse.success(count);
        } catch (Exception e) {
            log.error("获取用户待审核数量失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取待审核数量失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户消息列表
     */
    @GetMapping("/messages")
    public ApiResponse<IPage<SystemMessage>> getUserMessages(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            IPage<SystemMessage> messages = systemMessageService.getUserMessages(currentUserId, pageNum, pageSize);
            return ApiResponse.success(messages);
        } catch (Exception e) {
            log.error("获取用户消息失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取用户消息失败: " + e.getMessage());
        }
    }

    /**
     * 获取未读消息数量
     */
    @GetMapping("/messages/unread-count")
    public ApiResponse<Long> getUnreadCount() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            Long count = systemMessageService.getUnreadCount(currentUserId);
            return ApiResponse.success(count);
        } catch (Exception e) {
            log.error("获取未读消息数量失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取未读消息数量失败: " + e.getMessage());
        }
    }

    /**
     * 标记消息为已读
     */
    @PostMapping("/messages/{messageId}/read")
    public ApiResponse<?> markMessageAsRead(@PathVariable Long messageId) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            boolean success = systemMessageService.markAsRead(messageId, currentUserId);
            if (success) {
                return ApiResponse.success("标记成功");
            } else {
                return ApiResponse.error("标记失败");
            }
        } catch (Exception e) {
            log.error("标记消息已读失败: {}", e.getMessage(), e);
            return ApiResponse.error("标记失败: " + e.getMessage());
        }
    }

    /**
     * 标记所有消息为已读
     */
    @PostMapping("/messages/read-all")
    public ApiResponse<?> markAllMessagesAsRead() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            boolean success = systemMessageService.markAllAsRead(currentUserId);
            if (success) {
                return ApiResponse.success("全部标记成功");
            } else {
                return ApiResponse.error("标记失败");
            }
        } catch (Exception e) {
            log.error("标记所有消息已读失败: {}", e.getMessage(), e);
            return ApiResponse.error("标记失败: " + e.getMessage());
        }
    }
} 