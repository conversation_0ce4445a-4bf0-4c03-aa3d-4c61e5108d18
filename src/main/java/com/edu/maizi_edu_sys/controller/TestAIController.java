package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.util.DoubaoSyncUtil;
import com.edu.maizi_edu_sys.service.TopicCorrectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * AI API测试控制器
 * 用于测试和诊断AI校对功能
 */
@RestController
@RequestMapping("/api/test")
@RequiredArgsConstructor
@Slf4j
public class TestAIController {

    private final DoubaoSyncUtil doubaoSyncUtil;
    private final TopicCorrectionService topicCorrectionService;

    /**
     * 测试同步AI调用
     */
    @PostMapping("/ai/sync")
    public Map<String, Object> testSyncAI(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        String content = request.get("content");
        
        if (content == null || content.trim().isEmpty()) {
            content = "你好，请回复一个简单的JSON对象，例如：{\"status\": \"ok\", \"message\": \"测试成功\"}";
        }
        
        try {
            log.info("测试同步AI调用，输入内容长度: {}", content.length());
            long startTime = System.currentTimeMillis();
            
            String result = doubaoSyncUtil.syncRequest(content);
            
            long endTime = System.currentTimeMillis();
            
            response.put("success", true);
            response.put("inputLength", content.length());
            response.put("outputLength", result != null ? result.length() : 0);
            response.put("responseTime", endTime - startTime);
            response.put("result", result);
            
            log.info("同步AI调用完成，耗时: {}ms, 输出长度: {}", 
                    endTime - startTime, result != null ? result.length() : 0);
            
        } catch (Exception e) {
            log.error("同步AI调用失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("errorType", e.getClass().getSimpleName());
        }
        
        return response;
    }

    /**
     * 测试异步AI调用
     */
    @PostMapping("/ai/async")
    public Map<String, Object> testAsyncAI(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        String content = request.get("content");
        
        if (content == null || content.trim().isEmpty()) {
            content = "你好，请回复一个简单的JSON对象，例如：{\"status\": \"ok\", \"message\": \"测试成功\"}";
        }
        
        try {
            log.info("测试异步AI调用，输入内容长度: {}", content.length());
            long startTime = System.currentTimeMillis();
            
            CompletableFuture<String> future = doubaoSyncUtil.asyncStreamRequest(content);
            String result = future.get(); // 等待结果
            
            long endTime = System.currentTimeMillis();
            
            response.put("success", true);
            response.put("inputLength", content.length());
            response.put("outputLength", result != null ? result.length() : 0);
            response.put("responseTime", endTime - startTime);
            response.put("result", result);
            
            log.info("异步AI调用完成，耗时: {}ms, 输出长度: {}", 
                    endTime - startTime, result != null ? result.length() : 0);
            
        } catch (Exception e) {
            log.error("异步AI调用失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("errorType", e.getClass().getSimpleName());
        }
        
        return response;
    }

    /**
     * 测试题目校对格式
     */
    @PostMapping("/ai/correction")
    public Map<String, Object> testCorrectionFormat(@RequestBody(required = false) Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        // 构建测试题目
        String testTopics = "[\n" +
                "  {\n" +
                "    \"id\": 1,\n" +
                "    \"know_id\": 123,\n" +
                "    \"type\": \"choice\",\n" +
                "    \"title\": \"以下哪个是Java的关键字？\",\n" +
                "    \"tags\": \"Java,关键字\",\n" +
                "    \"options\": [\n" +
                "      {\"choice\": \"A\", \"text\": \"public\"},\n" +
                "      {\"choice\": \"B\", \"text\": \"private\"},\n" +
                "      {\"choice\": \"C\", \"text\": \"class\"},\n" +
                "      {\"choice\": \"D\", \"text\": \"main\"}\n" +
                "    ],\n" +
                "    \"subs\": [],\n" +
                "    \"answer\": \"ABC\",\n" +
                "    \"source\": \"测试\",\n" +
                "    \"parse\": \"ABC都是Java关键字。\",\n" +
                "    \"difficulty\": 0.3\n" +
                "  }\n" +
                "]";
        
        String prompt = "你是一位顶级的教育内容质量控制专家，任务是充当自动化校对系统，审查数据库中的教育题目。\n" +
                "\n" +
                "你的核心目标是找出题目中的任何错误，并按照严格的JSON格式返回需要修正的内容，以便程序能自动更新数据库。\n" +
                "\n" +
                "## 核心任务与输出格式规范\n" +
                "\n" +
                "你将收到一个包含多道题目的JSON数组。请仔细审查每一道题。\n" +
                "\n" +
                "### 1. 如果发现错误：\n" +
                "\n" +
                "你 **必须** 返回一个JSON数组，其中包含一个或多个对象。每个对象代表一道需要修正的题目。\n" +
                "每个对象 **必须** 包含三个键：\n" +
                "\n" +
                "- **\"id\"**: 题目的整数ID，用于定位数据库记录\n" +
                "- **\"reason\"**: 一个字符串，用一句话清晰地概括你做出修改的核心原因。例如：\"原答案错误\"、\"解析不准确\"、\"题干有错别字\"或\"题目表述不清\"\n" +
                "- **\"updates\"**: 一个对象，其中 **只包含** 需要被修改的字段和它们的新值。绝对不要包含没有改动的字段\n" +
                "\n" +
                "**重要**：updates 对象中可包含的字段为: `type`, `title`, `options`, `subs`, `answer`, `parse`, `difficulty`\n" +
                "\n" +
                "⚠\uFE0F **特别注意 `options` / `subs` 的格式**：\n" +
                "- 这两个字段最终会以 JSON 字符串形式存入数据库，但 **调用方在 updates 中应直接提供合法的 JSON 数组/对象**，系统会自动序列化。\n" +
                "- 正确示例：`\"options\": [{\"key\":\"A\",\"name\":\"选项A\"},{\"key\":\"B\",\"name\":\"选项B\"}]`\n" +
                "\n" +
                "**若 updates 对象为空（即没有字段需要修改），请不要返回该题。**\n" +
                "\n" +
                "### 输出示例：\n" +
                "\n" +
                "```json\n" +
                "[\n" +
                "  {\n" +
                "    \"id\": 275735,\n" +
                "    \"reason\": \"多选题答案字母顺序错误，且解析不够详细\",\n" +
                "    \"updates\": {\n" +
                "      \"answer\": \"ACD\",\n" +
                "      \"options\": [{\"key\":\"A\",\"name\":\"正确选项A\"},{\"key\":\"B\",\"name\":\"错误选项B\"},{\"key\":\"C\",\"name\":\"正确选项C\"},{\"key\":\"D\",\"name\":\"正确选项D\"}],\n" +
                "      \"parse\": \"【修正后的解析】A项正确，信息具有价值相对性，其价值因人、因时、因地而异。B项错误，信息虽然可以共享，但在传播过程中可能会损耗或失真。C项正确，信息具有时效性，会随着时间推移而失去或降低价值。D项正确，信息具有载体依附性，必须通过某种形式的数据来承载。\"\n" +
                "    }\n" +
                "  }\n" +
                "]\n" +
                "```\n" +
                "\n" +
                "**失败示例（错误写法，请勿模仿）**：\n" +
                "\n" +
                "```json\n" +
                "{\n" +
                "  \"id\": 1,\n" +
                "  \"reason\": \"示例\"\n" +
                "}\n" +
                "```\n" +
                "\n" +
                "### 2. 如果没有发现任何错误：\n" +
                "\n" +
                "如果输入的所有题目都完美无缺，你 **必须** 返回一个空的JSON数组 `[]`。\n" +
                "\n" +
                "## 核心审查清单\n" +
                "\n" +
                "### 内容与逻辑错误检查\n" +
                "\n" +
                "#### 题干 (title) 检查：\n" +
                "- 是否有错别字、语病、标点符号错误\n" +
                "- 表述是否清晰明确，避免歧义\n" +
                "- 提问方式是否恰当（\"以下哪个\"、\"关于...下列说法正确的是\"等）\n" +
                "- 是否包含不必要的重复词汇\n" +
                "\n" +
                "#### 选项 (options) 检查：\n" +
                "- 每个选项是否有错别字或语法错误\n" +
                "- 选项长度是否适中，表述是否简洁\n" +
                "- 单选题：是否只有一个明确正确答案\n" +
                "- 多选题：是否有2-4个正确选项，且选项间没有重复或矛盾\n" +
                "- 选项是否具有合理的迷惑性（错误选项应该看似合理）\n" +
                "- options格式：必须是有效的JSON数组格式\n" +
                "- **选项与答案必须匹配**（答案字母必须全部出现在 options.key 中）\n" +
                "\n" +
                "#### 答案 (answer) 检查：\n" +
                "- 答案是否正确回应了题干问题\n" +
                "- 答案与解析结论是否完全一致\n" +
                "- 格式规范：\n" +
                "  - 单选题 (choice)：必须是单个大写字母，如 \"A\"、\"B\"、\"C\"、\"D\"，或者 \"E\" **长度必须为 1**\n" +
                "  - 多选题 (multiple)：必须是大写字母且 **按字母升序排列**，如 \"ACD\"（不能是 \"CAD\"），**正确选项数量 2-4 个**\n" +
                "  - 判断题 (judge)：必须是 \"是\" 或 \"否\"（必须使用中文全角字符）\n" +
                "\n" +
                "#### 解析 (parse) 检查：\n" +
                "- 是否清晰解释了正确答案为什么正确\n" +
                "- 是否解释了错误选项为什么错误\n" +
                "- 引用的知识点、事实、数据是否准确\n" +
                "- 解析是否专业、详尽，能帮助学生理解\n" +
                "- 避免仅仅重复答案，要有教育价值\n" +
                "\n" +
                "### 格式与规范检查\n" +
                "\n" +
                "#### 题目类型 (type) 检查：\n" +
                "- **choice**：单选题，答案为单个字母\n" +
                "- **multiple**：多选题，答案为多个字母\n" +
                "- **judge**：判断题，答案为\"是\"或\"否\"\n" +
                "- type是否与实际题目形式匹配\n" +
                "\n" +
                "#### 难度 (difficulty) 评估：\n" +
                "- 取值范围：0.1 - 1.0（**保留一位小数**）\n" +
                "- 参考标准：\n" +
                "  - 0.1-0.3：基础概念题，直接记忆类\n" +
                "  - 0.4-0.6：理解应用题，需要思考分析\n" +
                "  - 0.7-0.9：综合分析题，需要深度理解\n" +
                "  - 1.0：极难题，需要创新思维\n" +
                "\n" +
                "### 常见错误类型示例\n" +
                "\n" +
                "1. **答案格式错误**：\n" +
                "   - 多选题答案 \"CAD\" → \"ACD\"\n" +
                "   - 单选题答案 \"ab\" → \"A\"\n" +
                "\n" +
                "2. **题型不匹配**：\n" +
                "   - 答案是 \"AC\" 但 type 是 \"choice\" → type 应为 \"multiple\"\n" +
                "\n" +
                "3. **解析质量问题**：\n" +
                "   - \"A正确，B错误，C错误，D错误\" → 详细解释每个选项的原因\n" +
                "\n" +
                "4. **逻辑不一致**：\n" +
                "   - 解析说A正确，但答案是B → 答案和解析必须一致\n" +
                "\n" +
                "### 重要限制\n" +
                "\n" +
                "**绝对不能修改的字段**：`id`, `know_id`, `tags`, `source`, `score`, `sort`, `created_at`\n" +
                "你的任务是修正题目内容本身，而不是元数据分类。\n" +
                "\n" +
                "### 质量标准\n" +
                "\n" +
                "每道题目修正后应达到：\n" +
                "- 内容准确无误，符合学科知识\n" +
                "- 格式规范统一，便于系统处理\n" +
                "- 解析详细专业，具有教育价值\n" +
                "- 难度设置合理，匹配题目复杂度\n" +
                "\n" +
                "---\n" +
                "⚠\uFE0F **输出必须是纯 JSON**：回答中只能包含合法的 JSON 数组，**前后不可夹带任何解释性文字、Markdown、代码块标记或其它字符**。\n" +
                "\n" +
                "现在，请根据以上所有规则，仔细审查以下这批题目：" + testTopics;
        
        try {
            log.info("测试校对格式，输入内容长度: {}", prompt.length());
            long startTime = System.currentTimeMillis();
            
            CompletableFuture<String> future = doubaoSyncUtil.asyncStreamRequest(prompt);
            String result = future.get();
            
            long endTime = System.currentTimeMillis();
            
            response.put("success", true);
            response.put("inputLength", prompt.length());
            response.put("outputLength", result != null ? result.length() : 0);
            response.put("responseTime", endTime - startTime);
            response.put("result", result);
            response.put("testPrompt", prompt);
            
            // 验证返回结果
            response.put("isValidJson", isValidJson(result));
            response.put("isEmpty", result == null || result.trim().isEmpty());
            response.put("isEmptyArray", "[]".equals(result != null ? result.trim() : ""));
            
            log.info("校对格式测试完成，耗时: {}ms, 输出长度: {}", 
                    endTime - startTime, result != null ? result.length() : 0);
            
        } catch (Exception e) {
            log.error("校对格式测试失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("errorType", e.getClass().getSimpleName());
        }
        
        return response;
    }
    
    /**
     * 检查字符串是否为有效JSON
     */
    private boolean isValidJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return false;
        }
        try {
            new com.fasterxml.jackson.databind.ObjectMapper().readTree(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 测试完整的题目校对流程
     * 手动触发一批题目的校对处理
     */
    @PostMapping("/correction/batch")
    public Map<String, Object> testCorrectionBatch() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("手动触发题目校对批处理测试");
            long startTime = System.currentTimeMillis();
            
            // 调用校对服务处理一批题目
            Map<String, Object> batchResult = topicCorrectionService.checkAndProcessOneBatch();
            
            long endTime = System.currentTimeMillis();
            
            boolean success = (Boolean) batchResult.getOrDefault("success", false);
            response.put("success", success);
            response.put("message", success ? "批处理执行成功" : "批处理执行失败或无新题目需要处理");
            response.put("processingTime", endTime - startTime);
            response.put("timestamp", System.currentTimeMillis());
            response.put("batchResult", batchResult); // 添加详细的批处理结果
            
            log.info("题目校对批处理测试完成，成功: {}, 耗时: {}ms", success, endTime - startTime);
            
        } catch (Exception e) {
            log.error("题目校对批处理测试失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("errorType", e.getClass().getSimpleName());
        }
        
        return response;
    }

    /**
     * 测试修正JSON格式解析
     * 验证系统能否正确解析AI返回的修正建议
     */
    @PostMapping("/correction/parse")
    public Map<String, Object> testCorrectionParsing(@RequestBody(required = false) Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        // 如果没有提供测试数据，使用默认的修正JSON
        String testJson = (request != null && request.containsKey("json")) ? 
                         request.get("json") : getDefaultCorrectionJson();
        
        try {
            log.info("测试修正JSON解析，输入长度: {}", testJson.length());
            
            // 验证JSON格式
            boolean isValidJson = isValidJson(testJson);
            response.put("isValidJson", isValidJson);
            
            if (!isValidJson) {
                response.put("success", false);
                response.put("message", "输入不是有效的JSON格式");
                return response;
            }
            
            // 尝试解析为修正建议对象
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            com.fasterxml.jackson.core.type.TypeReference<java.util.List<com.edu.maizi_edu_sys.dto.TopicCorrectionDTO>> typeRef = 
                new com.fasterxml.jackson.core.type.TypeReference<java.util.List<com.edu.maizi_edu_sys.dto.TopicCorrectionDTO>>() {};
            
            java.util.List<com.edu.maizi_edu_sys.dto.TopicCorrectionDTO> corrections = mapper.readValue(testJson, typeRef);
            
            // 统计解析结果
            int totalCount = corrections.size();
            int validCount = (int) corrections.stream().filter(c -> c.isValid()).count();
            
            response.put("success", true);
            response.put("totalCorrections", totalCount);
            response.put("validCorrections", validCount);
            response.put("parseSuccessful", true);
            
            // 详细分析每个修正建议
            java.util.List<Map<String, Object>> correctionDetails = new java.util.ArrayList<>();
            for (int i = 0; i < corrections.size(); i++) {
                com.edu.maizi_edu_sys.dto.TopicCorrectionDTO correction = corrections.get(i);
                Map<String, Object> detail = new HashMap<>();
                detail.put("index", i);
                detail.put("id", correction.getId());
                detail.put("reason", correction.getReason());
                detail.put("isValid", correction.isValid());
                detail.put("updateFields", correction.getUpdates() != null ? correction.getUpdates().keySet() : null);
                correctionDetails.add(detail);
            }
            response.put("details", correctionDetails);
            
            log.info("修正JSON解析测试完成，总数: {}, 有效: {}", totalCount, validCount);
            
        } catch (Exception e) {
            log.error("修正JSON解析测试失败", e);
            response.put("success", false);
            response.put("parseSuccessful", false);
            response.put("error", e.getMessage());
            response.put("errorType", e.getClass().getSimpleName());
        }
        
        response.put("inputJson", testJson);
        return response;
    }

    /**
     * 获取默认的修正JSON测试数据
     */
    private String getDefaultCorrectionJson() {
        return "[\n" +
                "  {\n" +
                "    \"id\": 999999,\n" +
                "    \"reason\": \"测试修正：答案格式错误，多选题字母顺序需要调整\",\n" +
                "    \"updates\": {\n" +
                "      \"answer\": \"ACD\",\n" +
                "      \"options\": \"[{\\\"key\\\":\\\"A\\\",\\\"name\\\":\\\"正确选项A\\\"},{\\\"key\\\":\\\"B\\\",\\\"name\\\":\\\"错误选项B\\\"},{\\\"key\\\":\\\"C\\\",\\\"name\\\":\\\"正确选项C\\\"},{\\\"key\\\":\\\"D\\\",\\\"name\\\":\\\"正确选项D\\\"}]\",\n" +
                "      \"parse\": \"【测试修正解析】A项正确，原因1。B项错误，原因2。C项正确，原因3。D项正确，原因4。\",\n" +
                "      \"difficulty\": 0.6\n" +
                "    }\n" +
                "  }\n" +
                "]";
    }

    /**
     * 测试真实数据库格式的题目校对
     * 使用实际数据库中的题目格式进行测试
     */
    @PostMapping("/correction/real-data")
    public Map<String, Object> testRealDataCorrection() {
        Map<String, Object> response = new HashMap<>();
        
        // 使用实际数据库记录格式构建测试题目
        String realTopicJson = "[]";
        
        try {
            log.info("测试真实数据库格式的题目校对");
            
            // 验证JSON格式
            boolean isValidJson = isValidJson(realTopicJson);
            response.put("isValidJson", isValidJson);
            
            if (!isValidJson) {
                response.put("success", false);
                response.put("message", "构建的真实题目JSON格式无效");
                return response;
            }
            
            // 分析题目格式
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            com.fasterxml.jackson.core.type.TypeReference<java.util.List<java.util.Map<String, Object>>> typeRef = 
                new com.fasterxml.jackson.core.type.TypeReference<java.util.List<java.util.Map<String, Object>>>() {};
            
            java.util.List<java.util.Map<String, Object>> topics = mapper.readValue(realTopicJson, typeRef);
            
            if (!topics.isEmpty()) {
                Map<String, Object> topic = topics.get(0);
                
                // 分析options字段
                Object options = topic.get("options");
                boolean optionsIsArray = options instanceof java.util.List;
                int optionsCount = 0;
                if (optionsIsArray) {
                    optionsCount = ((java.util.List<?>) options).size();
                }
                
                // 分析subs字段
                Object subs = topic.get("subs");
                boolean subsIsArray = subs instanceof java.util.List;
                int subsCount = 0;
                if (subsIsArray) {
                    subsCount = ((java.util.List<?>) subs).size();
                }
                
                Map<String, Object> topicAnalysis = new HashMap<>();
                topicAnalysis.put("id", topic.get("id"));
                topicAnalysis.put("type", topic.get("type"));
                topicAnalysis.put("answer", topic.get("answer"));
                topicAnalysis.put("difficulty", topic.get("difficulty"));
                topicAnalysis.put("optionsIsArray", optionsIsArray);
                topicAnalysis.put("optionsCount", optionsCount);
                topicAnalysis.put("subsIsArray", subsIsArray);
                topicAnalysis.put("subsCount", subsCount);
                topicAnalysis.put("titleLength", topic.get("title") != null ? topic.get("title").toString().length() : 0);
                topicAnalysis.put("parseLength", topic.get("parse") != null ? topic.get("parse").toString().length() : 0);
                response.put("topicAnalysis", topicAnalysis);
            }
            
            // 构建校对提示词
            String correctionPrompt = "你是一位教育内容质量专家。请检查以下题目是否有错误。如果没有错误请返回空数组[]，如果有错误请按照JSON格式返回修正建议。\n\n" +
                    "题目数据：\n" + realTopicJson;
            
            // 调用AI进行校对
            long startTime = System.currentTimeMillis();
            CompletableFuture<String> future = doubaoSyncUtil.asyncStreamRequest(correctionPrompt);
            String aiResult = future.get();
            long endTime = System.currentTimeMillis();
            
            Map<String, Object> aiCall = new HashMap<>();
            aiCall.put("success", true);
            aiCall.put("inputLength", correctionPrompt.length());
            aiCall.put("outputLength", aiResult != null ? aiResult.length() : 0);
            aiCall.put("responseTime", endTime - startTime);
            aiCall.put("result", aiResult);
            response.put("aiCall", aiCall);
            
            // 验证AI返回结果
            boolean aiResultValid = isValidJson(aiResult);
            boolean isEmptyArray = "[]".equals(aiResult != null ? aiResult.trim() : "");
            
            Map<String, Object> aiResultAnalysis = new HashMap<>();
            aiResultAnalysis.put("isValidJson", aiResultValid);
            aiResultAnalysis.put("isEmptyArray", isEmptyArray);
            aiResultAnalysis.put("needsCorrection", !isEmptyArray);
            aiResultAnalysis.put("resultPreview", aiResult != null && aiResult.length() > 100 ? 
                aiResult.substring(0, 100) + "..." : aiResult);
            response.put("aiResultAnalysis", aiResultAnalysis);
            
            // 如果AI返回了修正建议，尝试解析
            if (aiResultValid && !isEmptyArray) {
                try {
                    com.fasterxml.jackson.core.type.TypeReference<java.util.List<com.edu.maizi_edu_sys.dto.TopicCorrectionDTO>> correctionTypeRef = 
                        new com.fasterxml.jackson.core.type.TypeReference<java.util.List<com.edu.maizi_edu_sys.dto.TopicCorrectionDTO>>() {};
                    
                    java.util.List<com.edu.maizi_edu_sys.dto.TopicCorrectionDTO> corrections = mapper.readValue(aiResult, correctionTypeRef);
                    Map m = new HashMap<>();    
                    m.put("parseSuccessful", true);
                    m.put("correctionCount", corrections.size());
                    m.put("validCorrectionCount", corrections.stream().mapToInt(c -> c.isValid() ? 1 : 0).sum());
                    response.put("correctionParsing", m);
                    
                } catch (Exception e) {
                    Map<String, Object> errorMap = new HashMap<>();
                    errorMap.put("parseSuccessful", false);
                    errorMap.put("error", e.getMessage());
                    response.put("correctionParsing", errorMap);
                }
            }
            
            response.put("success", true);
            response.put("message", "真实数据格式测试完成");
            
            log.info("真实数据库格式校对测试完成，AI响应时间: {}ms", endTime - startTime);
            
        } catch (Exception e) {
            log.error("真实数据库格式校对测试失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("errorType", e.getClass().getSimpleName());
        }
        
        response.put("testTopicJson", realTopicJson);
        return response;
    }
}