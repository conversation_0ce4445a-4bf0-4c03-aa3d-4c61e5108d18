package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.entity.Paper;
import com.edu.maizi_edu_sys.service.PaperService;
import com.edu.maizi_edu_sys.dto.PaperStatisticsDTO;
import com.edu.maizi_edu_sys.dto.BatchEditDTO;
import com.edu.maizi_edu_sys.service.AuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;

import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import com.edu.maizi_edu_sys.dto.ApiResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 历史试卷管理控制器
 * 提供试卷的历史记录管理、批量操作、统计分析等功能
 */
@Controller
@RequestMapping("/paper-history")
public class PaperHistoryController {

    private static final Logger logger = LoggerFactory.getLogger(PaperHistoryController.class);
    
    private final PaperService paperService;
    private final AuthService authService;

    @Autowired
    public PaperHistoryController(PaperService paperService, AuthService authService) {
        this.paperService = paperService;
        this.authService = authService;
    }

    /**
     * 显示历史试卷管理页面
     */
    @GetMapping
    public String historyPage(Model model) {
        // 获取基本统计信息
        Long userId = authService.getCurrentUserId();
        PaperStatisticsDTO statistics = paperService.getPaperStatisticsByUser(userId);
        model.addAttribute("statistics", statistics);

        return "paper/history";
    }

    /**
     * 获取历史试卷列表（API接口）
     */
    @GetMapping("/papers/history")
    @ResponseBody
    public ResponseEntity<ApiResponse<Page<Paper>>> getPaperHistory(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search,
            @RequestParam(defaultValue = "createTime-desc") String sort,
            @RequestParam(required = false) String dateFilter,
            @RequestParam(required = false) String scoreFilter) {

        try {
            // 构建排序
            Sort sortObj = buildSort(sort);
            Pageable pageable = PageRequest.of(page, size, sortObj);

            // 构建查询条件
            Map<String, Object> filters = new HashMap<>();
            if (search != null && !search.trim().isEmpty()) {
                filters.put("search", search.trim());
            }
            if (dateFilter != null && !dateFilter.isEmpty()) {
                filters.put("dateFilter", dateFilter);
            }
            if (scoreFilter != null && !scoreFilter.isEmpty()) {
                filters.put("scoreFilter", scoreFilter);
            }

            // 获取当前用户ID（从session或认证信息中获取）
            Long userId = authService.getCurrentUserId();
            filters.put("userId", userId);

            Page<Paper> papers = paperService.findPapersWithFilters(filters, pageable);

            return ResponseEntity.ok(new ApiResponse<>(true, "获取试卷列表成功", papers));

        } catch (Exception e) {
            logger.error("获取试卷列表失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "获取试卷列表失败", null));
        }
    }

    /**
     * 获取试卷统计信息
     */
    @GetMapping("/statistics")
    @ResponseBody
    public ResponseEntity<ApiResponse<PaperStatisticsDTO>> getPaperStatistics() {
        try {
            Long userId = authService.getCurrentUserId();
            PaperStatisticsDTO statistics = paperService.getPaperStatisticsByUser(userId);
            return ResponseEntity.ok(new ApiResponse<>(true, "获取统计信息成功", statistics));
        } catch (Exception e) {
            logger.error("获取统计信息失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "获取统计信息失败", null));
        }
    }

    /**
     * 批量编辑试卷
     */
    @PostMapping("/batch-edit")
    @ResponseBody
    public ResponseEntity<ApiResponse<Map<String, Object>>> batchEditPapers(
            @RequestBody BatchEditDTO batchEditDTO) {

        try {
            Long userId = authService.getCurrentUserId();

            // 验证试卷所有权
            List<Long> paperIds = batchEditDTO.getPaperIds();
            if (!paperService.validatePaperOwnership(paperIds, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new ApiResponse<>(false, "您没有权限编辑部分试卷", null));
            }

            int updatedCount = paperService.batchEditPapers(batchEditDTO);

            Map<String, Object> result = new HashMap<>();
            result.put("updatedCount", updatedCount);

            return ResponseEntity.ok(new ApiResponse<>(true, "批量编辑成功", result));

        } catch (Exception e) {
            logger.error("批量编辑失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "批量编辑失败：" + e.getMessage(), null));
        }
    }

    /**
     * 批量导出试卷数据
     */
    @PostMapping("/batch-export")
    @ResponseBody
    public ResponseEntity<ApiResponse<String>> batchExportPapers(
            @RequestParam List<Long> paperIds) {

        try {
            Long userId = authService.getCurrentUserId();

            // 验证试卷所有权
            if (!paperService.validatePaperOwnership(paperIds, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new ApiResponse<>(false, "您没有权限下载部分试卷", null));
            }

            // 这里可以将文件保存到临时目录，然后返回文件的URL
            // 或者直接返回base64编码的文件内容
            // 为了简化，我们这里只返回一个成功的消息
            paperService.exportPapersToExcel(paperIds);
            return ResponseEntity.ok(new ApiResponse<>(true, "导出任务已创建", null));

        } catch (Exception e) {
            logger.error("批量导出失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "批量导出失败: " + e.getMessage(), null));
        }
    }

    /**
     * 克隆试卷
     */
    @PostMapping("/{id}/clone")
    @ResponseBody
    public ResponseEntity<ApiResponse<Map<String, Object>>> clonePaper(
            @PathVariable Long id) {

        try {
            Long userId = authService.getCurrentUserId();

            // 验证试卷所有权
            if (!paperService.validatePaperOwnership(Arrays.asList(id), userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new ApiResponse<>(false, "您没有权限克隆此试卷", null));
            }

            Paper clonedPaper = paperService.clonePaper(id, userId);

            Map<String, Object> result = new HashMap<>();
            result.put("newPaperId", clonedPaper.getId());
            result.put("newPaperTitle", clonedPaper.getTitle());

            return ResponseEntity.ok(new ApiResponse<>(true, "试卷克隆成功", result));

        } catch (Exception e) {
            logger.error("克隆试卷失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "试卷克隆失败：" + e.getMessage(), null));
        }
    }

    /**
     * 获取试卷详情
     */
    @GetMapping("/{id}/detail")
    @ResponseBody
    public ResponseEntity<ApiResponse<Paper>> getPaperDetail(
            @PathVariable Long id) {

        try {
            Long userId = authService.getCurrentUserId();

            // 验证试卷所有权
            if (!paperService.validatePaperOwnership(Arrays.asList(id), userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new ApiResponse<>(false, "您没有权限查看此试卷", null));
            }

            Paper paper = paperService.getPaperById(id);
            if (paper == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new ApiResponse<>(false, "试卷不存在", null));
            }

            return ResponseEntity.ok(new ApiResponse<>(true, "获取试卷详情成功", paper));

        } catch (Exception e) {
            logger.error("获取试卷详情失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "获取试卷详情失败", null));
        }
    }

    /**
     * 删除单个试卷
     */
    @DeleteMapping("/{id}")
    @ResponseBody
    public ResponseEntity<ApiResponse<String>> deletePaper(
            @PathVariable Long id) {

        try {
            Long userId = authService.getCurrentUserId();

            // 验证试卷所有权
            if (!paperService.validatePaperOwnership(Arrays.asList(id), userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new ApiResponse<>(false, "您没有权限删除此试卷", null));
            }

            boolean deleted = paperService.deletePaper(id);

            if (deleted) {
                return ResponseEntity.ok(new ApiResponse<>(true, "试卷删除成功", "操作完成"));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new ApiResponse<>(false, "试卷删除失败", null));
            }

        } catch (Exception e) {
            logger.error("删除试卷失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "试卷删除失败：" + e.getMessage(), null));
        }
    }

    /**
     * 批量删除试卷
     */
    @PostMapping("/batch-delete")
    @ResponseBody
    public ResponseEntity<ApiResponse<Map<String, Object>>> batchDeletePapers(
            @RequestBody List<Long> paperIds) {

        try {
            Long userId = authService.getCurrentUserId();

            // 验证试卷所有权
            if (!paperService.validatePaperOwnership(paperIds, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new ApiResponse<>(false, "您没有权限删除部分试卷", null));
            }

            int deletedCount = paperService.batchDeletePapers(paperIds, userId);

            Map<String, Object> result = new HashMap<>();
            result.put("deletedCount", deletedCount);

            return ResponseEntity.ok(new ApiResponse<>(true, "批量删除成功", result));

        } catch (Exception e) {
            logger.error("批量删除失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "批量删除失败：" + e.getMessage(), null));
        }
    }

    /**
     * 批量下载试卷
     */
    @PostMapping("/batch-download")
    @ResponseBody
    public ResponseEntity<ApiResponse<String>> batchDownloadPapers(
            @RequestParam("paperIds") Long[] paperIds,
            @RequestParam(defaultValue = "pdf") String format,
            @RequestParam(defaultValue = "standard") String version) {

        try {
            Long userId = authService.getCurrentUserId();
            
            // 转换数组为List
            List<Long> paperIdList = Arrays.asList(paperIds);

            // 验证试卷所有权
            if (!paperService.validatePaperOwnership(paperIdList, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new ApiResponse<>(false, "您没有权限下载部分试卷", null));
            }

            // 同上，返回成功消息或文件URL
            paperService.batchDownloadPapers(paperIdList, format, version, userId);
            return ResponseEntity.ok(new ApiResponse<>(true, "下载任务已创建", null));

        } catch (Exception e) {
            logger.error("批量下载失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "批量下载失败: " + e.getMessage(), null));
        }
    }

    /**
     * 试卷预览页面
     */
    @GetMapping("/{id}/preview")
    @ResponseBody
    public ResponseEntity<ApiResponse<Paper>> previewPaper(@PathVariable Long id) {
        try {
            Long userId = authService.getCurrentUserId();

            // 验证试卷所有权
            if (!paperService.validatePaperOwnership(Arrays.asList(id), userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new ApiResponse<>(false, "您没有权限预览此试卷", null));
            }

            Paper paper = paperService.getPaperById(id);
            if (paper == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new ApiResponse<>(false, "试卷不存在", null));
            }

            return ResponseEntity.ok(new ApiResponse<>(true, "获取试卷预览成功", paper));

        } catch (Exception e) {
            logger.error("预览试卷异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "预览试卷失败: " + e.getMessage(), null));
        }
    }

    /**
     * 试卷编辑页面
     */
    @GetMapping("/{id}/edit")
    @ResponseBody
    public ResponseEntity<ApiResponse<Paper>> editPaper(@PathVariable Long id) {
        try {
            Long userId = authService.getCurrentUserId();

            // 验证试卷所有权
            if (!paperService.validatePaperOwnership(Arrays.asList(id), userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new ApiResponse<>(false, "您没有权限编辑此试卷", null));
            }

            Paper paper = paperService.getPaperById(id);
            if (paper == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new ApiResponse<>(false, "试卷不存在", null));
            }

            return ResponseEntity.ok(new ApiResponse<>(true, "获取试卷编辑信息成功", paper));

        } catch (Exception e) {
            logger.error("编辑试卷异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "编辑试卷失败: " + e.getMessage(), null));
        }
    }

    /**
     * 构建排序对象
     */
    private Sort buildSort(String sortStr) {
        if (sortStr == null || sortStr.isEmpty()) {
            return Sort.by(Sort.Direction.DESC, "createTime");
        }

        String[] parts = sortStr.split("-");
        if (parts.length != 2) {
            return Sort.by(Sort.Direction.DESC, "createTime");
        }

        String field = parts[0];
        String direction = parts[1];

        Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) ? Sort.Direction.ASC : Sort.Direction.DESC;

        // 验证字段名，防止SQL注入
        Set<String> allowedFields = new HashSet<>(
                Arrays.asList("createTime", "updateTime", "title", "totalScore", "downloadCount"));
        if (!allowedFields.contains(field)) {
            field = "createTime";
        }

        return Sort.by(sortDirection, field);
    }

    /**
     * 处理异常的通用方法
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public ResponseEntity<ApiResponse<String>> handleException(Exception e) {
        logger.error("系统捕获到未处理异常", e);

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(false, "系统错误：" + e.getMessage(), null));
    }
}