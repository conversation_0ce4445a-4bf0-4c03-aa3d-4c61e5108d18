package com.edu.maizi_edu_sys.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.edu.maizi_edu_sys.dto.AuditRequestDTO;
import com.edu.maizi_edu_sys.dto.TopicAuditDTO;
import com.edu.maizi_edu_sys.dto.UserDailyAuditGroupDTO;
import com.edu.maizi_edu_sys.entity.TopicAudit;
import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.service.AuthService;
import com.edu.maizi_edu_sys.service.TopicAuditService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * 管理员审核控制器
 * 专门处理管理员后台的审核管理功能
 */
@RestController
@RequestMapping("/api/admin/audit")
@Slf4j
public class AdminAuditController {

    @Autowired
    private TopicAuditService topicAuditService;

    @Autowired
    private AuthService authService;

    /**
     * 获取待审核题目列表（分页）
     */
    @GetMapping("/pending")
    public ApiResponse<IPage<TopicAuditDTO>> getPendingAudits(
            @RequestParam(name = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
            @RequestParam(name = "keyword", required = false) String keyword,
            @RequestParam(name = "submitter", required = false) String submitter,
            @RequestParam(name = "knowledge", required = false) String knowledge) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            // 使用带搜索和筛选的方法
            IPage<TopicAuditDTO> audits;
            if ((keyword != null && !keyword.trim().isEmpty()) || 
                (submitter != null && !submitter.trim().isEmpty()) || 
                (knowledge != null && !knowledge.trim().isEmpty())) {
                // 有搜索或筛选条件
                audits = topicAuditService.getPendingAudits(pageNum, pageSize, keyword, submitter, knowledge);
            } else {
                // 无搜索条件，使用原方法
                audits = topicAuditService.getPendingAudits(pageNum, pageSize);
            }
            
            return ApiResponse.success(audits);
        } catch (Exception e) {
            log.error("获取待审核题目失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取待审核题目失败: " + e.getMessage());
        }
    }

    /**
     * 获取审核通过的题目列表（分页）
     */
    @GetMapping("/approved")
    public ApiResponse<IPage<TopicAuditDTO>> getApprovedAudits(
            @RequestParam(name = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(name = "pageSize", defaultValue = "10") int pageSize) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            IPage<TopicAuditDTO> audits = topicAuditService.getApprovedAudits(pageNum, pageSize);
            return ApiResponse.success(audits);
        } catch (Exception e) {
            log.error("获取审核通过题目失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取审核通过题目失败: " + e.getMessage());
        }
    }

    /**
     * 获取审核拒绝的题目列表（分页）
     */
    @GetMapping("/rejected")
    public ApiResponse<IPage<TopicAuditDTO>> getRejectedAudits(
            @RequestParam(name = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(name = "pageSize", defaultValue = "10") int pageSize) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            IPage<TopicAuditDTO> audits = topicAuditService.getRejectedAudits(pageNum, pageSize);
            return ApiResponse.success(audits);
        } catch (Exception e) {
            log.error("获取审核拒绝题目失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取审核拒绝题目失败: " + e.getMessage());
        }
    }

    /**
     * 获取审核员的审核记录（分页）
     */
    @GetMapping("/auditor-records")
    public ApiResponse<IPage<TopicAuditDTO>> getAuditorRecords(
            @RequestParam(name = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(name = "pageSize", defaultValue = "10") int pageSize) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            IPage<TopicAuditDTO> audits = topicAuditService.getAuditorAudits(currentUserId, pageNum, pageSize);
            return ApiResponse.success(audits);
        } catch (Exception e) {
            log.error("获取审核员记录失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取审核员记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取审核详情
     */
    @GetMapping("/detail/{auditId}")
    public ApiResponse<TopicAuditDTO> getAuditDetail(@PathVariable Long auditId) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            TopicAuditDTO audit = topicAuditService.getAuditDetail(auditId);
            if (audit == null) {
                return ApiResponse.error("审核记录不存在");
            }
            
            return ApiResponse.success(audit);
        } catch (Exception e) {
            log.error("获取审核详情失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取审核详情失败: " + e.getMessage());
        }
    }

    /**
     * 提交审核结果
     */
    @PostMapping("/submit")
    public ApiResponse<?> submitAudit(@Valid @RequestBody AuditRequestDTO auditRequest) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            topicAuditService.auditTopic(auditRequest, currentUserId);
            
            String result = auditRequest.getAuditResult() == 1 ? "通过" : "拒绝";
            log.info("审核完成: 审核员={}, 结果={}", currentUserId, result);
            
            return ApiResponse.success("审核完成");
        } catch (IllegalArgumentException e) {
            log.warn("审核失败: {}", e.getMessage());
            return ApiResponse.error(e.getMessage());
        } catch (Exception e) {
            log.error("审核题目失败: {}", e.getMessage(), e);
            return ApiResponse.error("审核失败: " + e.getMessage());
        }
    }

    /**
     * 快速审核通过
     */
    @PostMapping("/quick-approve/{auditId}")
    public ApiResponse<?> quickApprove(@PathVariable Long auditId, @RequestParam(required = false) String comment) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            AuditRequestDTO auditRequest = new AuditRequestDTO();
            auditRequest.setAuditId(auditId);
            auditRequest.setAuditResult(1); // 通过
            auditRequest.setAuditComment(comment != null ? comment : "快速通过");

            topicAuditService.auditTopic(auditRequest, currentUserId);
            
            log.info("快速审核通过: 审核员={}, 审核ID={}", currentUserId, auditId);
            return ApiResponse.success("审核通过");
        } catch (Exception e) {
            log.error("快速审核通过失败: {}", e.getMessage(), e);
            return ApiResponse.error("审核失败: " + e.getMessage());
        }
    }

    /**
     * 快速审核拒绝
     */
    @PostMapping("/quick-reject/{auditId}")
    public ApiResponse<?> quickReject(@PathVariable Long auditId, @RequestParam String reason) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            if (reason == null || reason.trim().isEmpty()) {
                return ApiResponse.error("拒绝原因不能为空");
            }

            AuditRequestDTO auditRequest = new AuditRequestDTO();
            auditRequest.setAuditId(auditId);
            auditRequest.setAuditResult(2); // 拒绝
            auditRequest.setAuditComment(reason);

            topicAuditService.auditTopic(auditRequest, currentUserId);
            
            log.info("快速审核拒绝: 审核员={}, 审核ID={}, 原因={}", currentUserId, auditId, reason);
            return ApiResponse.success("审核拒绝");
        } catch (Exception e) {
            log.error("快速审核拒绝失败: {}", e.getMessage(), e);
            return ApiResponse.error("审核失败: " + e.getMessage());
        }
    }

    /**
     * 撤销审核
     */
    @PostMapping("/revert/{auditId}")
    public ApiResponse<?> revertAudit(@PathVariable Long auditId) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            topicAuditService.revertAudit(auditId, currentUserId);
            
            log.info("撤销审核成功: 操作员={}, 审核ID={}", currentUserId, auditId);
            return ApiResponse.success("撤销审核成功");
        } catch (IllegalArgumentException e) {
            log.warn("撤销审核失败: {}", e.getMessage());
            return ApiResponse.error(e.getMessage());
        } catch (Exception e) {
            log.error("撤销审核失败: {}", e.getMessage(), e);
            return ApiResponse.error("撤销审核失败: " + e.getMessage());
        }
    }

    /**
     * 审核通过（兼容性API）
     */
    @PostMapping("/approve")
    public ApiResponse<?> approve(@Valid @RequestBody AuditRequestDTO auditRequest) {
        auditRequest.setAuditResult(1); // 强制设置为通过
        return submitAudit(auditRequest);
    }

    /**
     * 批量审核
     */
    @PostMapping("/batch")
    public ApiResponse<?> batchAudit(
            @RequestParam String action,
            @RequestParam(required = false) String comment,
            @RequestBody(required = false) List<Long> auditIds) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            if (!"approve".equals(action) && !"reject".equals(action)) {
                return ApiResponse.error("无效的操作类型");
            }

            if ("reject".equals(action) && (comment == null || comment.trim().isEmpty())) {
                return ApiResponse.error("批量拒绝时必须提供原因");
            }

            // ⚠️ 修复安全漏洞：必须提供具体的审核ID列表，不允许空列表
            if (auditIds == null || auditIds.isEmpty()) {
                return ApiResponse.error("请选择要审核的题目");
            }

            // 验证审核ID列表大小，支持大批量操作但设置合理上限
            if (auditIds.size() > 2000) {
                return ApiResponse.error("单次批量操作不能超过1000个题目，请分批处理");
            }
            
            // 对于超大批量操作给出优化建议
            if (auditIds.size() > 1000) {
                log.warn("大批量审核操作: {} 个题目，审核员: {}", auditIds.size(), currentUserId);
            }

            int auditResult = "approve".equals(action) ? 1 : 2;
            String auditComment = comment != null ? comment : ("批量" + ("approve".equals(action) ? "通过" : "拒绝"));

            // 🚀 使用优化的批量审核方法
            Map<String, Integer> result = topicAuditService.batchAuditByIds(auditIds, auditResult, auditComment, currentUserId);
            
            int successCount = result.get("success");
            int failureCount = result.get("failure");
            
            String resultMessage = String.format("批量审核完成：成功 %d 个，失败 %d 个", successCount, failureCount);
            log.info("批量审核结果: {}, 操作: {}, 审核员: {}", resultMessage, action, currentUserId);
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("message", resultMessage);
            responseData.put("successCount", successCount);
            responseData.put("failureCount", failureCount);
            responseData.put("totalCount", auditIds.size());
            
            return ApiResponse.success(responseData);
        } catch (Exception e) {
            log.error("批量审核失败: {}", e.getMessage(), e);
            return ApiResponse.error("批量审核失败: " + e.getMessage());
        }
    }

    /**
     * 超大批量审核 - 支持异步处理和进度追踪
     */
    @PostMapping("/batch-large")
    public ApiResponse<?> largeBatchAudit(
            @RequestParam String action,
            @RequestParam(required = false) String comment,
            @RequestBody List<Long> auditIds) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            if (!"approve".equals(action) && !"reject".equals(action)) {
                return ApiResponse.error("无效的操作类型");
            }

            if ("reject".equals(action) && (comment == null || comment.trim().isEmpty())) {
                return ApiResponse.error("批量拒绝时必须提供原因");
            }

            // 对于超大批量操作，必须提供审核ID列表
            if (auditIds == null || auditIds.isEmpty()) {
                return ApiResponse.error("请选择要审核的题目");
            }

            // 支持更大的批量操作，但仍有上限
            if (auditIds.size() > 5000) {
                return ApiResponse.error("单次批量操作不能超过5000个题目，请联系系统管理员");
            }

            log.info("开始超大批量审核: {} 个题目，操作: {}, 审核员: {}", auditIds.size(), action, currentUserId);

            int auditResult = "approve".equals(action) ? 1 : 2;
            String auditComment = comment != null ? comment : ("超大批量" + ("approve".equals(action) ? "通过" : "拒绝"));

            // 🚀 使用优化的批量审核方法，支持分批处理
            Map<String, Integer> result = topicAuditService.batchAuditByIds(auditIds, auditResult, auditComment, currentUserId);
            
            int successCount = result.get("success");
            int failureCount = result.get("failure");
            double successRate = auditIds.size() > 0 ? (double) successCount / auditIds.size() * 100 : 0;
            
            String resultMessage = String.format("超大批量审核完成：成功 %d 个，失败 %d 个（成功率: %.1f%%）", 
                                                successCount, failureCount, successRate);
            log.info("超大批量审核结果: {}, 操作: {}, 审核员: {}", resultMessage, action, currentUserId);
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("message", resultMessage);
            responseData.put("successCount", successCount);
            responseData.put("failureCount", failureCount);
            responseData.put("totalCount", auditIds.size());
            responseData.put("successRate", successRate);
            responseData.put("batchSize", auditIds.size());
            
            return ApiResponse.success(responseData);
        } catch (Exception e) {
            log.error("超大批量审核失败: {}", e.getMessage(), e);
            return ApiResponse.error("超大批量审核失败: " + e.getMessage());
        }
    }

    /**
     * 获取审核统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getStatistics() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            Map<String, Object> stats = topicAuditService.getAuditStatistics();
            return ApiResponse.success(stats);
        } catch (Exception e) {
            log.error("获取审核统计信息失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取审核统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取审核统计信息 (别名接口，兼容前端)
     */
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getStats() {
        return getStatistics();
    }

    /**
     * 获取按用户和日期分组的审核数据
     */
    @GetMapping("/user-daily-groups")
    public ApiResponse<IPage<UserDailyAuditGroupDTO>> getUserDailyAuditGroups(
            @RequestParam(name = "page", defaultValue = "1") int page,
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "keyword", required = false) String keyword,
            @RequestParam(name = "submitter", required = false) String submitter) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            // 如果指定了submitter（用户名或用户ID），优先作为搜索关键字
            if (submitter != null && !submitter.trim().isEmpty()) {
                keyword = submitter;
            }

            IPage<UserDailyAuditGroupDTO> groups = topicAuditService.getUserDailyAuditGroups(page, size, keyword);
            return ApiResponse.success(groups);
        } catch (Exception e) {
            log.error("获取用户每日审核分组失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取分组数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户某日的审核详情
     */
    @GetMapping("/user-daily-detail")
    public ApiResponse<List<TopicAuditDTO>> getUserDailyDetail(
            @RequestParam String userId,
            @RequestParam String date) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            List<TopicAuditDTO> audits = topicAuditService.getAuditsByUserAndDate(userId, date);
            return ApiResponse.success(audits);
        } catch (Exception e) {
            log.error("获取用户每日审核详情失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取详情失败: " + e.getMessage());
        }
    }

    /**
     * 批量审核用户某日的所有题目
     */
    @PostMapping("/batch-audit-user-daily")
    public ApiResponse<?> batchAuditUserDaily(
            @RequestParam String userId,
            @RequestParam String date,
            @RequestParam String action,
            @RequestParam(required = false) String comment) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            if (!"approve".equals(action) && !"reject".equals(action)) {
                return ApiResponse.error("无效的操作类型");
            }

            if ("reject".equals(action) && (comment == null || comment.trim().isEmpty())) {
                return ApiResponse.error("拒绝时必须提供原因");
            }

            int auditResult = "approve".equals(action) ? 1 : 2;
            String auditComment = comment != null ? comment : ("批量" + ("approve".equals(action) ? "通过" : "拒绝"));

            topicAuditService.batchAuditUserDaily(userId, date, auditResult, auditComment, currentUserId);

            String resultMessage = String.format("成功批量%s用户 %s 在 %s 的所有题目", 
                                                "approve".equals(action) ? "通过" : "拒绝", userId, date);
            log.info("批量审核用户每日题目成功: {}, 操作: {}, 审核员: {}", resultMessage, action, currentUserId);
            
            return ApiResponse.success(resultMessage);
        } catch (Exception e) {
            log.error("批量审核用户每日题目失败: {}", e.getMessage(), e);
            return ApiResponse.error("批量审核失败: " + e.getMessage());
        }
    }

    /**
     * 临时测试端点 - 不需要认证
     */
    @GetMapping("/test-user-daily-groups")
    public ApiResponse<IPage<UserDailyAuditGroupDTO>> testGetUserDailyAuditGroups(
            @RequestParam(name = "page", defaultValue = "1") int page,
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "keyword", required = false) String keyword) {
        try {
            log.info("测试获取用户每日审核分组 - 页码: {}, 大小: {}, 关键词: {}", page, size, keyword);
            
            IPage<UserDailyAuditGroupDTO> groups = topicAuditService.getUserDailyAuditGroups(page, size, keyword);
            log.info("测试查询结果 - 总数: {}, 记录数: {}", groups.getTotal(), groups.getRecords().size());
            
            return ApiResponse.success(groups);
        } catch (Exception e) {
            log.error("测试获取分组数据失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取分组数据失败: " + e.getMessage());
        }
    }

    /**
     * 临时测试端点 - 查看用户具体的数据
     */
    @GetMapping("/test-user-data/{userId}")
    public ApiResponse<List<Map<String, Object>>> testGetUserData(@PathVariable Long userId) {
        try {
            log.info("测试查询用户数据: {}", userId);
            
            // 直接查询数据库
            List<TopicAudit> audits = topicAuditService.list(
                new QueryWrapper<TopicAudit>()
                    .eq("user_id", userId)
                    .orderByDesc("submit_time")
                    .last("LIMIT 10")
            );
            
            List<Map<String, Object>> result = new ArrayList<>();
            for (TopicAudit audit : audits) {
                Map<String, Object> data = new HashMap<>();
                data.put("id", audit.getId());
                data.put("title", audit.getTitle());
                data.put("submit_time", audit.getSubmitTime());
                data.put("submit_date", audit.getSubmitTime() != null ? 
                    audit.getSubmitTime().toLocalDate().toString() : null);
                data.put("audit_status", audit.getAuditStatus());
                result.add(data);
                
                log.info("用户 {} 的数据: ID={}, 提交时间={}, 日期={}, 状态={}", 
                    userId, audit.getId(), audit.getSubmitTime(), 
                    audit.getSubmitTime() != null ? audit.getSubmitTime().toLocalDate() : null,
                    audit.getAuditStatus());
            }
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("测试查询用户数据失败: {}", e.getMessage(), e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 临时测试端点 - 查看所有待审核数据
     */
    @GetMapping("/test-all-pending")
    public ApiResponse<List<Map<String, Object>>> testGetAllPending() {
        try {
            log.info("测试查询所有待审核数据");
            
            // 查询所有待审核数据
            List<TopicAudit> audits = topicAuditService.list(
                new QueryWrapper<TopicAudit>()
                    .eq("audit_status", TopicAudit.AuditStatus.PENDING)
                    .orderByDesc("submit_time")
                    .last("LIMIT 20")
            );
            
            List<Map<String, Object>> result = new ArrayList<>();
            for (TopicAudit audit : audits) {
                Map<String, Object> data = new HashMap<>();
                data.put("id", audit.getId());
                data.put("user_id", audit.getUserId());
                data.put("user_id_type", audit.getUserId() != null ? audit.getUserId().getClass().getSimpleName() : "null");
                data.put("title", audit.getTitle());
                data.put("submit_time", audit.getSubmitTime());
                data.put("submit_date", audit.getSubmitTime() != null ? 
                    audit.getSubmitTime().toLocalDate().toString() : null);
                data.put("audit_status", audit.getAuditStatus());
                result.add(data);
                
                log.info("待审核数据: ID={}, 用户ID={} ({}), 提交时间={}, 日期={}, 状态={}", 
                    audit.getId(), audit.getUserId(), 
                    audit.getUserId() != null ? audit.getUserId().getClass().getSimpleName() : "null",
                    audit.getSubmitTime(), 
                    audit.getSubmitTime() != null ? audit.getSubmitTime().toLocalDate() : null,
                    audit.getAuditStatus());
            }
            
            log.info("总共找到 {} 条待审核数据", result.size());
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("测试查询所有待审核数据失败: {}", e.getMessage(), e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 简化的批量审核用户某日的所有题目 - 用于调试
     */
    @PostMapping("/simple-batch-audit-user-daily")
    public ApiResponse<?> simpleBatchAuditUserDaily(
            @RequestParam String userId,
            @RequestParam String date,
            @RequestParam String action,
            @RequestParam(required = false) String comment) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            if (!"approve".equals(action) && !"reject".equals(action)) {
                return ApiResponse.error("无效的操作类型");
            }

            if ("reject".equals(action) && (comment == null || comment.trim().isEmpty())) {
                return ApiResponse.error("拒绝时必须提供原因");
            }

            int auditResult = "approve".equals(action) ? 1 : 2;
            String auditComment = comment != null ? comment : ("简化批量" + ("approve".equals(action) ? "通过" : "拒绝"));

            topicAuditService.simpleBatchAuditUserDaily(userId, date, auditResult, auditComment, currentUserId);

            String resultMessage = String.format("简化方法成功批量%s用户 %s 在 %s 的所有题目", 
                                                "approve".equals(action) ? "通过" : "拒绝", userId, date);
            log.info("简化批量审核用户每日题目成功: {}, 操作: {}, 审核员: {}", resultMessage, action, currentUserId);
            
            return ApiResponse.success(resultMessage);
        } catch (Exception e) {
            log.error("简化批量审核用户每日题目失败: {}", e.getMessage(), e);
            return ApiResponse.error("简化批量审核失败: " + e.getMessage());
        }
    }

    /**
     * 智能审核分析
     */
    @PostMapping("/smart-audit")
    public ApiResponse<Map<String, Object>> smartAudit() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            // 获取待审核数据进行智能分析
            List<TopicAuditDTO> pendingAudits = topicAuditService.getAllPendingAudits();
            
            Map<String, Object> result = new HashMap<>();
            int approved = 0, rejected = 0, manual = 0;
            
            for (TopicAuditDTO audit : pendingAudits) {
                // 智能审核规则
                if (isSmartApprovalCandidate(audit)) {
                    approved++;
                } else if (isSmartRejectCandidate(audit)) {
                    rejected++;
                } else {
                    manual++;
                }
            }
            
            result.put("approved", approved);
            result.put("rejected", rejected);
            result.put("manual", manual);
            result.put("total", pendingAudits.size());
            
            log.info("智能审核分析完成: 建议通过={}, 建议拒绝={}, 需人工={}", approved, rejected, manual);
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("智能审核分析失败: {}", e.getMessage(), e);
            return ApiResponse.error("智能审核分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 应用智能审核结果
     */
    @PostMapping("/apply-smart-result")
    public ApiResponse<?> applySmartAuditResult(@RequestBody Map<String, String> request) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            String type = request.get("type");
            if (!"approved".equals(type) && !"rejected".equals(type)) {
                return ApiResponse.error("无效的操作类型");
            }

            List<TopicAuditDTO> pendingAudits = topicAuditService.getAllPendingAudits();
            List<Long> auditIds = new ArrayList<>();
            
            for (TopicAuditDTO audit : pendingAudits) {
                if ("approved".equals(type) && isSmartApprovalCandidate(audit)) {
                    // 将String类型的ID转换为Long类型
                    auditIds.add(Long.valueOf(audit.getId()));
                } else if ("rejected".equals(type) && isSmartRejectCandidate(audit)) {
                    // 将String类型的ID转换为Long类型
                    auditIds.add(Long.valueOf(audit.getId()));
                }
            }
            
            if (auditIds.isEmpty()) {
                return ApiResponse.error("没有符合条件的审核项");
            }
            
            // 执行批量审核
            int auditResult = "approved".equals(type) ? 1 : 2;
            String comment = "approved".equals(type) ? "智能审核自动通过" : "智能审核自动拒绝";
            
            for (Long auditId : auditIds) {
                AuditRequestDTO auditRequest = new AuditRequestDTO();
                auditRequest.setAuditId(auditId);
                auditRequest.setAuditResult(auditResult);
                auditRequest.setAuditComment(comment);
                
                topicAuditService.auditTopic(auditRequest, currentUserId);
            }
            
            log.info("智能审核结果应用完成: 类型={}, 处理数量={}", type, auditIds.size());
            
            return ApiResponse.success("智能审核结果已应用，共处理 " + auditIds.size() + " 个审核项");
        } catch (Exception e) {
            log.error("应用智能审核结果失败: {}", e.getMessage(), e);
            return ApiResponse.error("应用智能审核结果失败: " + e.getMessage());
        }
    }
    
    /**
     * 按用户批量通过
     */
    @PostMapping("/batch-approve-by-user")
    public ApiResponse<Map<String, Object>> batchApproveByUser(@RequestBody Map<String, Object> request) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            Long userId = Long.valueOf(request.get("userId").toString());
            
            // 获取该用户的所有待审核项
            List<TopicAuditDTO> userAudits = topicAuditService.getPendingAuditsByUserId(userId);
            
            if (userAudits.isEmpty()) {
                return ApiResponse.error("该用户没有待审核的题目");
            }
            
            int successCount = 0;
            for (TopicAuditDTO audit : userAudits) {
                try {
                    AuditRequestDTO auditRequest = new AuditRequestDTO();
                    // 将String类型的ID转换为Long类型
                    auditRequest.setAuditId(Long.valueOf(audit.getId()));
                    auditRequest.setAuditResult(1);
                    auditRequest.setAuditComment("批量通过");
                    
                    topicAuditService.auditTopic(auditRequest, currentUserId);
                    successCount++;
                } catch (Exception e) {
                    log.warn("批量审核单项失败: auditId={}, error={}", audit.getId(), e.getMessage());
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("count", successCount);
            result.put("total", userAudits.size());
            
            log.info("按用户批量通过完成: userId={}, 成功={}, 总数={}", userId, successCount, userAudits.size());
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("按用户批量通过失败: {}", e.getMessage(), e);
            return ApiResponse.error("批量通过失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户的审核详情
     */
    @GetMapping("/user-audits/{userId}")
    public ApiResponse<List<TopicAuditDTO>> getUserAudits(@PathVariable Long userId) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            List<TopicAuditDTO> audits = topicAuditService.getPendingAuditsByUserId(userId);
            return ApiResponse.success(audits);
        } catch (Exception e) {
            log.error("获取用户审核详情失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取用户审核详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 智能审核规则：判断是否建议通过
     */
    private boolean isSmartApprovalCandidate(TopicAuditDTO audit) {
        // 智能审核规则
        String title = audit.getTitle();
        String options = audit.getOptions();
        String answer = audit.getAnswer();
        
        // 基本完整性检查
        if (title == null || title.trim().isEmpty()) return false;
        if (options == null || options.trim().isEmpty()) return false;
        if (answer == null || answer.trim().isEmpty()) return false;
        
        // 标题长度合理
        if (title.length() < 5 || title.length() > 200) return false;
        
        // 选项格式检查（简单）
        if (options.contains("A.") && options.contains("B.") && 
            (options.contains("C.") || options.contains("D."))) {
            // 看起来是选择题格式
            return true;
        }
        
        // 填空题或简答题格式检查
        if (title.contains("___") || title.contains("填空") || 
            title.contains("简述") || title.contains("分析")) {
            return true;
        }
        
        // 包含关键词的题目可能质量较高
        String content = (title + " " + options).toLowerCase();
        String[] qualityKeywords = {"计算", "分析", "设计", "实现", "原理", "算法", "数据结构"};
        for (String keyword : qualityKeywords) {
            if (content.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 智能审核规则：判断是否建议拒绝
     */
    private boolean isSmartRejectCandidate(TopicAuditDTO audit) {
        String title = audit.getTitle();
        String options = audit.getOptions();
        
        // 明显的格式问题
        if (title == null || title.trim().isEmpty()) return true;
        if (options == null || options.trim().isEmpty()) return true;
        
        // 标题过短或过长
        if (title.length() < 3 || title.length() > 500) return true;
        
        // 包含明显错误内容
        String content = (title + " " + options).toLowerCase();
        String[] badKeywords = {"测试", "test", "aaa", "bbb", "111", "xxx", "待定", "暂无"};
        for (String keyword : badKeywords) {
            if (content.contains(keyword)) {
                return true;
            }
        }
        
        // 重复字符检查
        if (title.matches(".*(..)\\1{2,}.*")) { // 检查是否有连续重复的字符
            return true;
        }
        
        return false;
    }

    /**
     * 获取提交者列表
     */
    @GetMapping("/submitters")
    public ApiResponse<List<Map<String, Object>>> getSubmitters() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            List<Map<String, Object>> submitters = topicAuditService.getSubmitterList();
            return ApiResponse.success(submitters);
        } catch (Exception e) {
            log.error("获取提交者列表失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取提交者列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取知识点列表
     */
    @GetMapping("/knowledge-points")
    public ApiResponse<List<Map<String, Object>>> getKnowledgePoints() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            List<Map<String, Object>> knowledgePoints = topicAuditService.getKnowledgePointList();
            return ApiResponse.success(knowledgePoints);
        } catch (Exception e) {
            log.error("获取知识点列表失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取知识点列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 调试提交者数据 - 分析为什么返回空数据
     */
    @GetMapping("/debug-submitters")
    public ApiResponse<Map<String, Object>> debugSubmitters() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            // 调用Service实现类的调试方法
            if (topicAuditService instanceof com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl) {
                com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl serviceImpl = 
                    (com.edu.maizi_edu_sys.service.impl.TopicAuditServiceImpl) topicAuditService;
                Map<String, Object> debugInfo = serviceImpl.debugSubmitterData();
                return ApiResponse.success(debugInfo);
            } else {
                return ApiResponse.error("调试功能不可用");
            }
        } catch (Exception e) {
            log.error("调试提交者数据失败: {}", e.getMessage(), e);
            return ApiResponse.error("调试失败: " + e.getMessage());
        }
    }

    /**
     * 调试接口：检查topic_audit表的数据情况
     */
    @GetMapping("/debug/audit-data")
    public ApiResponse<Map<String, Object>> debugAuditData() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无审核权限");
            }

            Map<String, Object> debugInfo = new HashMap<>();
            
            // 查询总记录数
            long totalCount = topicAuditService.count();
            debugInfo.put("totalCount", totalCount);
            
            // 查询各状态的数量
            long pendingCount = topicAuditService.count(new QueryWrapper<TopicAudit>().eq("audit_status", 0));
            long approvedCount = topicAuditService.count(new QueryWrapper<TopicAudit>().eq("audit_status", 1));
            long rejectedCount = topicAuditService.count(new QueryWrapper<TopicAudit>().eq("audit_status", 2));
            
            debugInfo.put("pendingCount", pendingCount);
            debugInfo.put("approvedCount", approvedCount);
            debugInfo.put("rejectedCount", rejectedCount);
            
            // 查询最近几条记录
            List<TopicAudit> recentAudits = topicAuditService.list(
                new QueryWrapper<TopicAudit>()
                    .orderByDesc("submit_time")
                    .last("LIMIT 5")
            );
            
            List<Map<String, Object>> recentData = new ArrayList<>();
            for (TopicAudit audit : recentAudits) {
                Map<String, Object> auditInfo = new HashMap<>();
                auditInfo.put("id", audit.getId());
                auditInfo.put("userId", audit.getUserId());
                auditInfo.put("title", audit.getTitle());
                auditInfo.put("auditStatus", audit.getAuditStatus());
                auditInfo.put("submitTime", audit.getSubmitTime());
                recentData.add(auditInfo);
            }
            debugInfo.put("recentAudits", recentData);
            
            // 查询用户统计
            String userStatsQuery = "SELECT ta.user_id, COUNT(*) as count " +
                                  "FROM topic_audit ta " +
                                  "WHERE ta.audit_status = 0 " +
                                  "GROUP BY ta.user_id " +
                                  "ORDER BY count DESC LIMIT 10";
            
            List<Map<String, Object>> userStats = topicAuditService.getBaseMapper()
                .selectMaps(new QueryWrapper<TopicAudit>().apply(userStatsQuery));
            debugInfo.put("userStats", userStats);
            
            log.info("调试信息: {}", debugInfo);
            
            return ApiResponse.success(debugInfo);
        } catch (Exception e) {
            log.error("获取调试信息失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取调试信息失败: " + e.getMessage());
        }
    }

    /**
     * 导出审核数据
     */
    @GetMapping("/export")
    public void exportAuditData(
            @RequestParam(name = "keyword", required = false) String keyword,
            @RequestParam(name = "submitter", required = false) String submitter,
            @RequestParam(name = "knowledge", required = false) String knowledge,
            @RequestParam(name = "status", required = false) String status,
            HttpServletResponse response) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return;
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                return;
            }

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = "审核数据导出_" + java.time.LocalDate.now() + ".xlsx";
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + java.net.URLEncoder.encode(fileName, "UTF-8"));

            // 导出数据
            topicAuditService.exportAuditData(response.getOutputStream(), keyword, submitter, knowledge, status);
            
            log.info("审核数据导出成功: 操作员={}, 条件=keyword:{}, submitter:{}, knowledge:{}", 
                    currentUserId, keyword, submitter, knowledge);
                    
        } catch (Exception e) {
            log.error("导出审核数据失败: {}", e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 调试特定用户数据 - 查看为什么查询返回空结果
     */
    @GetMapping("/debug-specific-user/{userId}")
    public ApiResponse<Map<String, Object>> debugSpecificUserData(@PathVariable String userId) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无调试权限");
            }

            Map<String, Object> debugInfo = topicAuditService.debugSpecificUserData(userId);
            return ApiResponse.success(debugInfo);
        } catch (Exception e) {
            log.error("调试特定用户数据失败: {}", e.getMessage(), e);
            return ApiResponse.error("调试失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有有审核记录的用户列表（用于调试）
     */
    @GetMapping("/debug-all-users")
    public ApiResponse<Map<String, Object>> debugAllUsersWithAudits() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            if (!topicAuditService.hasAuditPermission(currentUserId)) {
                return ApiResponse.error("无调试权限");
            }

            Map<String, Object> debugInfo = topicAuditService.debugAllUsersWithAudits();
            return ApiResponse.success(debugInfo);
        } catch (Exception e) {
            log.error("调试所有用户数据失败: {}", e.getMessage(), e);
            return ApiResponse.error("调试失败: " + e.getMessage());
        }
    }
} 