package com.edu.maizi_edu_sys.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class ViewController {

    @Value("${app.features.daily-upload-stats:false}")
    private boolean dailyUploadStatsEnabled;



    // 上传题目页面 - 保留，这是独有的
    @GetMapping("/topics/upload-topics")
    public String uploadTopics(Model model) {
        model.addAttribute("dailyUploadStatsEnabled", dailyUploadStatsEnabled);
        return "topics/upload-topics";
    }



    //  试卷配置管理页面路由 - 保留，路径不冲突
    @GetMapping("/paper-configs")
    public String paperConfigsPage() {
        return "paper/config-management";
    }

    // 查重页面路由 - 保留，这是重定向功能
    @GetMapping("/paper/check")
    public String checkPage() {
        return "redirect:/papers/duplicate-check";
    }



    // 旧上传路径重定向 - 保留，这是重定向功能
    @GetMapping("/paper/upload")
    public String oldUploadPage() {
        return "redirect:/topics/upload-topics";
    }

    // 处理旧的 HTML 文件请求 - 保留，这是重定向功能
    @GetMapping("/login.html")
    public String loginHtml() {
        return "redirect:/auth/login";
    }

    @GetMapping("/chat.html")
    public String chatHtml() {
        return "redirect:/main/chat";
    }

    @GetMapping("/profile.html")
    public String profileHtml() {
        return "redirect:/user/profile";
    }



    // 认证功能测试页面 - 保留，这是测试功能
    @GetMapping("/test/auth")
    public String authTestPage() {
        return "test/auth-test";
    }

    // 数学公式渲染测试页面 - 保留，这是测试功能
    @GetMapping("/test/math")
    public String mathFormulaTestPage() {
        return "test/math-formula-test";
    }

    // PDF生成测试页面 - 保留，这是测试功能
    @GetMapping("/test/pdf")
    public String pdfTestPage() {
        return "test/pdf-test";
    }

    // 知识点删除功能测试页面 - 保留，这是测试功能
    @GetMapping("/test/knowledge-point-delete")
    public String knowledgePointDeleteTestPage() {
        return "test/knowledge-point-delete-test";
    }
}