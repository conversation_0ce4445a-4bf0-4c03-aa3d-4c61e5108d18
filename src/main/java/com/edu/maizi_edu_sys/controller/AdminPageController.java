package com.edu.maizi_edu_sys.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 管理员页面控制器
 * 处理管理员后台页面访问
 */
@Controller
@RequestMapping("/admin")
@Slf4j
public class AdminPageController {
    
    /**
     * 管理员登录页面
     * 支持以下访问路径：
     * - http://localhost:8081/admin/templates/admin-login.html
     * - http://localhost:8081/admin/login
     */
    @GetMapping({"/templates/admin-login.html", "/login"})
    public String adminLoginPage() {
        return "admin/templates/admin-login";
    }
    
    /**
     * 管理员仪表板页面
     * 支持以下访问路径：
     * - http://localhost:8081/admin/templates/dashboard.html
     * - http://localhost:8081/admin/dashboard
     */
    @GetMapping({"/templates/dashboard.html", "/dashboard"})
    public String adminDashboardPage(Model model) {
        model.addAttribute("activeTab", "dashboard");
        return "admin/templates/dashboard";
    }
    
    /**
     * 管理员用户管理页面
     */
    @GetMapping({"/templates/users.html", "/users", "/templates/user-management.html"})
    public String adminUsersPage(Model model) {
        model.addAttribute("activeTab", "users");
        return "admin/templates/user-management";
    }
    
    /**
     * 管理员题目管理页面
     */
    @GetMapping({"/templates/topics.html", "/topics", "/templates/topic-management.html"})
    public String adminTopicsPage(Model model) {
        model.addAttribute("activeTab", "topics");
        return "admin/templates/topic-management";
    }
    
    /**
     * 管理员审核管理页面
     */
    @GetMapping({"/templates/audit.html", "/audit", "/templates/audit-management.html"})
    public String adminAuditPage(Model model) {
        model.addAttribute("activeTab", "audit");
        return "admin/templates/audit-management";
    }
    
    /**
     * 管理员用户上传统计页面
     */
    @GetMapping({"/templates/user-upload-stats.html", "/user-upload-stats", "/templates/user-upload-statistics.html"})
    public String adminUserUploadStatsPage(Model model) {
        model.addAttribute("activeTab", "user-upload-stats");
        return "admin/templates/user-upload-statistics";
    }
    
    /**
     * 管理员知识点管理页面
     */
    @GetMapping({"/templates/knowledge.html", "/knowledge", "/templates/knowledge-management.html"})
    public String adminKnowledgePage(Model model) {
        model.addAttribute("activeTab", "knowledge");
        return "admin/templates/knowledge-management";
    }
    
    /**
     * 管理员考试管理页面
     */
    @GetMapping({"/templates/exams.html", "/exams", "/templates/exam-management.html"})
    public String adminExamsPage(Model model) {
        model.addAttribute("activeTab", "exams");
        return "admin/templates/exam-management";
    }
    
    /**
     * 管理员数据报表页面
     */
    @GetMapping({"/templates/reports.html", "/reports"})
    public String adminReportsPage(Model model) {
        model.addAttribute("activeTab", "reports");
        return "admin/templates/reports";
    }
    
    /**
     * 管理员系统监控页面
     */
    @GetMapping({"/templates/system.html", "/system", "/templates/system-monitoring.html"})
    public String adminSystemPage(Model model) {
        model.addAttribute("activeTab", "system");
        return "admin/templates/system-monitoring";
    }
    
    /**
     * 管理员修正审核页面
     */
    @GetMapping({"/templates/correction_approval.html", "/correction_approval"})
    public String adminCorrectionApprovalPage(Model model) {
        // 重定向到有完整实现的审核控制器
        return "redirect:/admin/correction/approval";
    }
    
    /**
     * 管理员系统设置页面
     */
    @GetMapping({"/templates/settings.html", "/settings"})
    public String adminSettingsPage(Model model) {
        model.addAttribute("activeTab", "settings");
        return "admin/templates/settings";
    }
    
    /**
     * 管理员日志查看页面
     */
    @GetMapping({"/templates/logs.html", "/logs"})
    public String adminLogsPage(Model model) {
        model.addAttribute("activeTab", "logs");
        return "admin/templates/logs";
    }
    
    /**
     * 管理员用户调试页面
     */
    @GetMapping({"/templates/user-debug.html", "/user-debug"})
    public String adminUserDebugPage(Model model) {
        model.addAttribute("activeTab", "debug");
        return "admin/templates/user-debug";
    }
    
    /**
     * 管理员修正审核调试页面
     */
    @GetMapping({"/templates/audit-debug.html", "/audit-debug", "/templates/audit-debug-management.html"})
    public String adminAuditDebugPage(Model model) {
        model.addAttribute("activeTab", "audit-debug");
        return "admin/templates/audit-debug-management";
    }
    
    /**
     * 头像功能演示页面
     */
    @GetMapping({"/templates/avatar-demo.html", "/avatar-demo"})
    public String avatarDemoPage(Model model) {
        model.addAttribute("activeTab", "avatar-demo");
        return "admin/templates/avatar-demo";
    }
    
    /**
     * 审核分组功能测试页面
     */
    @GetMapping("/audit/test-page")
    public String auditTestPage(Model model) {
        model.addAttribute("activeTab", "audit-test");
        return "admin/test-group-audit";
    }
    
    /**
     * 重定向到管理员仪表板（默认页面）
     */
    @GetMapping({"", "/"})
    public String adminIndexPage() {
        return "redirect:/admin/dashboard";
    }
} 