package com.edu.maizi_edu_sys.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import lombok.extern.slf4j.Slf4j;

/**
 * 统一页面路由控制器
 * 处理系统中各种页面的访问路由
 */
@Controller
@Slf4j
public class PageRouteController {

    /**
     * 题目相关页面
     */
    @GetMapping("/topics/bank")
    public String topicBankPage() {
        log.info("访问题目库页面");
        return "topics/bank";
    }
    
    @GetMapping("/topics/create")
    public String topicCreatePage() {
        log.info("访问题目创建页面");
        return "topics/create";
    }
    
    @GetMapping("/topics/manage")
    public String topicManagePage() {
        log.info("访问题目管理页面");
        return "topics/manage";
    }

    /**
     * 试卷相关页面
     */
    @GetMapping("/paper/generate")
    public String paperGeneratePage(Model model) {
        log.info("访问试卷生成页面");
        model.addAttribute("activePage", "generate");
        return "paper/generate";
    }
    
    @GetMapping("/paper/list")
    public String paperListPage() {
        log.info("访问试卷列表页面");
        return "paper/list";
    }
    
    @GetMapping("/paper/config")
    public String paperConfigPage() {
        log.info("访问试卷配置页面");
        return "paper/config";
    }

    /**
     * 用户相关页面
     */
    @GetMapping("/user/profile")
    public String userProfilePage() {
        log.info("访问用户资料页面");
        return "user/profile";
    }
    
    @GetMapping("/user/settings")
    public String userSettingsPage() {
        log.info("访问用户设置页面");
        return "user/settings";
    }

    /**
     * 聊天页面
     */
    @GetMapping("/main/chat")
    public String chatPage() {
        log.info("访问聊天页面");
        return "main/chat";
    }

    /**
     * 系统相关页面
     */
    @GetMapping("/about")
    public String aboutPage() {
        log.info("访问关于页面");
        return "about";
    }
    
    @GetMapping("/help")
    public String helpPage() {
        log.info("访问帮助页面");
        return "help";
    }
    
    @GetMapping("/contact")
    public String contactPage() {
        log.info("访问联系我们页面");
        return "contact";
    }

    /**
     * 错误页面处理
     */
    @GetMapping("/error/403")
    public String forbiddenPage() {
        return "error/403";
    }
    
    @GetMapping("/error/404")
    public String notFoundPage() {
        return "error/404";
    }
    
    @GetMapping("/error/500")
    public String serverErrorPage() {
        return "error/500";
    }

    /**
     * 通用页面访问记录
     */
    private void logPageAccess(String pageName, String description) {
        log.info("页面访问: {} - {}", pageName, description);
    }
}