package com.edu.maizi_edu_sys.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.maizi_edu_sys.entity.KnowledgePoint;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.service.AuthService;
import com.edu.maizi_edu_sys.service.KnowledgePointService;
import com.edu.maizi_edu_sys.service.TopicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 管理员知识点管理控制器
 */
@RestController
@RequestMapping("/api/admin")
@Slf4j
public class AdminKnowledgeController {

    @Autowired
    private KnowledgePointService knowledgePointService;

    @Autowired
    private TopicService topicService;

    @Autowired
    private AuthService authService;

    /**
     * 获取知识点列表
     */
    @GetMapping("/knowledge-points")
    public ApiResponse<List<Map<String, Object>>> getKnowledgePoints() {
        try {
            QueryWrapper<KnowledgePoint> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_deleted", false)
                       .orderByAsc("group_name")
                       .orderByAsc("sort");
            
            List<KnowledgePoint> knowledgePoints = knowledgePointService.list(queryWrapper);
            
            // 统计每个知识点的题目数量
            List<Map<String, Object>> result = knowledgePoints.stream().map(kp -> {
                // 统计该知识点的题目数量
                QueryWrapper<Topic> topicQuery = new QueryWrapper<>();
                topicQuery.eq("know_id", kp.getKnowledgeId());
                long topicCount = topicService.count(topicQuery);
                
                Map<String, Object> map = new HashMap<>();
                map.put("knowledgeId", kp.getKnowledgeId());
                map.put("knowledgeName", kp.getName());
                map.put("description", kp.getGroupName());
                map.put("topicCount", topicCount);
                return map;
            }).collect(Collectors.toList());
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取知识点列表失败", e);
            return ApiResponse.error("获取知识点列表失败: " + e.getMessage());
        }
    }



    /**
     * 获取知识点映射
     */
    private Map<Integer, String> getKnowledgePointMap() {
        QueryWrapper<KnowledgePoint> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_deleted", false);
        
        List<KnowledgePoint> knowledgePoints = knowledgePointService.list(queryWrapper);
        return knowledgePoints.stream()
            .collect(Collectors.toMap(
                KnowledgePoint::getKnowledgeId,
                KnowledgePoint::getName,
                (existing, replacement) -> existing // 如果有重复key，保留现有值
            ));
    }

    /**
     * 获取知识点统计信息
     */
    @GetMapping("/knowledge/stats")
    public ApiResponse<Map<String, Object>> getKnowledgeStats() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            Map<String, Object> stats = new HashMap<>();
            
            // 总知识点数
            QueryWrapper<KnowledgePoint> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_deleted", false);
            long totalCount = knowledgePointService.count(queryWrapper);
            stats.put("total", totalCount);
            
            // 按group_name分组统计
            QueryWrapper<KnowledgePoint> groupQuery = new QueryWrapper<>();
            groupQuery.select("group_name", "count(*) as count")
                     .eq("is_deleted", false)
                     .groupBy("group_name");
            
            List<Map<String, Object>> groupStats = knowledgePointService.listMaps(groupQuery);
            stats.put("groupStats", groupStats);
            
            return ApiResponse.success(stats);
        } catch (Exception e) {
            log.error("获取知识点统计失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取知识点统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取知识点列表（分页）
     */
    @GetMapping("/knowledge")
    public ApiResponse<Map<String, Object>> getKnowledgePointList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String groupName) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            Page<KnowledgePoint> pageInfo = new Page<>(page, size);
            QueryWrapper<KnowledgePoint> queryWrapper = new QueryWrapper<>();
            
            queryWrapper.eq("is_deleted", false);
            
            // 搜索条件
            if (StringUtils.hasText(search)) {
                queryWrapper.like("knowledge_name", search);
            }
            
            // 分组筛选
            if (StringUtils.hasText(groupName)) {
                queryWrapper.eq("group_name", groupName);
            }
            
            queryWrapper.orderByAsc("group_name").orderByAsc("sort").orderByAsc("knowledge_id");
            
            IPage<KnowledgePoint> result = knowledgePointService.page(pageInfo, queryWrapper);
            
            // 转换为DTO
            List<Map<String, Object>> knowledgeList = result.getRecords().stream().map(knowledge -> {
                Map<String, Object> knowledgeMap = new HashMap<>();
                knowledgeMap.put("id", knowledge.getId());
                knowledgeMap.put("knowledgeId", knowledge.getKnowledgeId());
                knowledgeMap.put("knowledgeName", knowledge.getName());
                knowledgeMap.put("groupName", knowledge.getGroupName());
                knowledgeMap.put("isFree", knowledge.getIsFree());
                knowledgeMap.put("sort", knowledge.getSort());
                knowledgeMap.put("createdAt", knowledge.getCreateTime());
                return knowledgeMap;
            }).collect(Collectors.toList());
            
            Map<String, Object> response = new HashMap<>();
            response.put("data", knowledgeList);
            response.put("total", result.getTotal());
            response.put("pages", result.getPages());
            response.put("current", result.getCurrent());
            response.put("size", result.getSize());
            
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取知识点列表失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取知识点列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取单个知识点详情
     */
    @GetMapping("/knowledge/{id}")
    public ApiResponse<KnowledgePoint> getKnowledgePointById(@PathVariable Long id) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            KnowledgePoint knowledge = knowledgePointService.getById(id);
            if (knowledge == null || knowledge.getIsDeleted()) {
                return ApiResponse.error("知识点不存在");
            }
            
            return ApiResponse.success(knowledge);
        } catch (Exception e) {
            log.error("获取知识点详情失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取知识点详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建知识点
     */
    @PostMapping("/knowledge")
    public ApiResponse<?> createKnowledgePoint(@RequestBody KnowledgePoint knowledgePoint) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            // 检查知识点ID是否已存在
            QueryWrapper<KnowledgePoint> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("knowledge_id", knowledgePoint.getKnowledgeId())
                       .eq("is_deleted", false);
            
            KnowledgePoint existing = knowledgePointService.getOne(queryWrapper);
            if (existing != null) {
                return ApiResponse.error("知识点ID已存在");
            }

            knowledgePoint.setIsDeleted(false);
            boolean success = knowledgePointService.save(knowledgePoint);
            
            if (success) {
                return ApiResponse.success("知识点创建成功");
            } else {
                return ApiResponse.error("知识点创建失败");
            }
        } catch (Exception e) {
            log.error("创建知识点失败: {}", e.getMessage(), e);
            return ApiResponse.error("创建知识点失败: " + e.getMessage());
        }
    }

    /**
     * 更新知识点
     */
    @PutMapping("/knowledge/{id}")
    public ApiResponse<?> updateKnowledgePoint(@PathVariable Long id, @RequestBody KnowledgePoint knowledgePoint) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            KnowledgePoint existing = knowledgePointService.getById(id);
            if (existing == null || existing.getIsDeleted()) {
                return ApiResponse.error("知识点不存在");
            }

            knowledgePoint.setId(id);
            boolean success = knowledgePointService.updateById(knowledgePoint);
            
            if (success) {
                return ApiResponse.success("知识点更新成功");
            } else {
                return ApiResponse.error("知识点更新失败");
            }
        } catch (Exception e) {
            log.error("更新知识点失败: {}", e.getMessage(), e);
            return ApiResponse.error("更新知识点失败: " + e.getMessage());
        }
    }

    /**
     * 删除知识点
     */
    @DeleteMapping("/knowledge/{id}")
    public ApiResponse<?> deleteKnowledgePoint(@PathVariable Long id) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            KnowledgePoint knowledge = knowledgePointService.getById(id);
            if (knowledge == null) {
                return ApiResponse.error("知识点不存在");
            }

            // 软删除
            knowledge.setIsDeleted(true);
            boolean success = knowledgePointService.updateById(knowledge);
            
            if (success) {
                return ApiResponse.success("知识点删除成功");
            } else {
                return ApiResponse.error("知识点删除失败");
            }
        } catch (Exception e) {
            log.error("删除知识点失败: {}", e.getMessage(), e);
            return ApiResponse.error("删除知识点失败: " + e.getMessage());
        }
    }

    /**
     * 获取分组名称列表
     */
    @GetMapping("/knowledge/groups")
    public ApiResponse<List<String>> getGroupNames() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            QueryWrapper<KnowledgePoint> queryWrapper = new QueryWrapper<>();
            queryWrapper.select("DISTINCT group_name")
                       .eq("is_deleted", false)
                       .orderByAsc("group_name");
            
            List<KnowledgePoint> groups = knowledgePointService.list(queryWrapper);
            List<String> groupNames = groups.stream()
                .map(KnowledgePoint::getGroupName)
                .distinct()
                .collect(Collectors.toList());
            
            return ApiResponse.success(groupNames);
        } catch (Exception e) {
            log.error("获取分组名称失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取分组名称失败: " + e.getMessage());
        }
    }
} 