package com.edu.maizi_edu_sys.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.maizi_edu_sys.entity.User;
import com.edu.maizi_edu_sys.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 管理员用户管理控制器
 */
@RestController
@RequestMapping("/api/admin/users")
@RequiredArgsConstructor
@Slf4j
public class AdminUserController {
    
    private final UserMapper userMapper;
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    /**
     * 获取用户列表（分页）
     */
    @GetMapping
    public ResponseEntity<?> getUserList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) Integer role,
            @RequestParam(required = false) Integer status) {
        try {
            Page<User> pageInfo = new Page<>(page, size);
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            
            // 搜索条件
            if (StringUtils.hasText(search)) {
                queryWrapper.and(wrapper -> wrapper
                    .like("username", search)
                    .or()
                    .like("email", search)
                    .or()
                    .like("phone", search));
            }
            
            // 角色筛选
            if (role != null) {
                queryWrapper.eq("role", role);
            }
            
            // 状态筛选
            if (status != null) {
                queryWrapper.eq("status", status);
            }
            
            // 排除已删除的用户
            queryWrapper.eq("deleted", 0);
            queryWrapper.orderByDesc("created_at");
            
            IPage<User> result = userMapper.selectPage(pageInfo, queryWrapper);
            
            // 转换用户数据，隐藏敏感信息
            List<Map<String, Object>> userList = result.getRecords().stream().map(user -> {
                Map<String, Object> userMap = new HashMap<>();
                userMap.put("id", user.getId().toString()); // 确保ID作为字符串返回，避免前端精度丢失
                userMap.put("username", user.getUsername());
                userMap.put("email", user.getEmail());
                userMap.put("phone", user.getPhone());
                userMap.put("role", user.getRole());
                userMap.put("roleName", getRoleName(user.getRole()));
                userMap.put("status", user.getStatus());
                userMap.put("statusName", user.getStatus() == 1 ? "正常" : "禁用");
                userMap.put("avatar", generateRandomAvatar(user));
                userMap.put("bio", user.getBio());
                userMap.put("createTime", user.getCreatedAt());
                userMap.put("lastLoginTime", user.getLastLoginTime());
                userMap.put("lastLoginIp", user.getLastLoginIp());
                return userMap;
            }).collect(Collectors.toList());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", userList);
            response.put("total", result.getTotal());
            response.put("pages", result.getPages());
            response.put("current", result.getCurrent());
            response.put("size", result.getSize());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取用户列表失败", e);
            return ResponseEntity.badRequest().body(buildErrorResponse("获取用户列表失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取单个用户信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getUserById(@PathVariable String id) {
        try {
            // 日志记录接收到的ID（作为字符串）
            log.info("接收到的用户ID（字符串）: '{}'", id);
            
            // 验证ID格式
            if (!StringUtils.hasText(id)) {
                log.warn("用户ID为空");
                return ResponseEntity.badRequest().body(buildErrorResponse("用户ID不能为空"));
            }
            
            // 尝试转换为Long
            Long userId;
            try {
                userId = Long.parseLong(id);
                log.info("ID转换成功: {} -> {}", id, userId);
            } catch (NumberFormatException e) {
                log.error("用户ID格式无效: '{}'", id, e);
                return ResponseEntity.badRequest().body(buildErrorResponse("用户ID格式无效"));
            }
            
            // 查询用户
            User user = userMapper.selectById(userId);
            log.info("数据库查询结果: {}", user != null ? "找到用户" : "未找到用户");
            
            if (user == null) {
                log.warn("用户不存在，ID: {}", userId);
                return ResponseEntity.badRequest().body(buildErrorResponse("用户不存在 (ID: " + id + ")"));
            }
            
            if (user.getDeleted() == 1) {
                log.warn("用户已被删除，ID: {}", userId);
                return ResponseEntity.badRequest().body(buildErrorResponse("用户已被删除"));
            }
            
            Map<String, Object> userMap = new HashMap<>();
            userMap.put("id", user.getId().toString()); // 确保返回字符串形式的ID
            userMap.put("username", user.getUsername());
            userMap.put("email", user.getEmail());
            userMap.put("phone", user.getPhone());
            userMap.put("role", user.getRole());
            userMap.put("roleName", getRoleName(user.getRole()));
            userMap.put("status", user.getStatus());
            userMap.put("statusName", user.getStatus() == 1 ? "正常" : "禁用");
            userMap.put("avatar", generateRandomAvatar(user));
            userMap.put("bio", user.getBio());
            userMap.put("createTime", user.getCreatedAt());
            userMap.put("lastLoginTime", user.getLastLoginTime());
            userMap.put("lastLoginIp", user.getLastLoginIp());
            
            return ResponseEntity.ok(buildSuccessResponse(userMap));
        } catch (Exception e) {
            log.error("获取用户信息失败，ID: '{}'", id, e);
            return ResponseEntity.badRequest().body(buildErrorResponse("获取用户信息失败: " + e.getMessage()));
        }
    }
    
    /**
     * 添加用户
     */
    @PostMapping
    public ResponseEntity<?> addUser(@RequestBody AddUserRequest request) {
        try {
            // 验证请求参数
            if (!StringUtils.hasText(request.getUsername())) {
                return ResponseEntity.badRequest().body(buildErrorResponse("用户名不能为空"));
            }
            if (!StringUtils.hasText(request.getEmail())) {
                return ResponseEntity.badRequest().body(buildErrorResponse("邮箱不能为空"));
            }
            if (!StringUtils.hasText(request.getPassword())) {
                return ResponseEntity.badRequest().body(buildErrorResponse("密码不能为空"));
            }
            if (request.getRole() == null) {
                return ResponseEntity.badRequest().body(buildErrorResponse("角色不能为空"));
            }
            
            // 检查用户名是否已存在
            QueryWrapper<User> usernameQuery = new QueryWrapper<>();
            usernameQuery.eq("username", request.getUsername()).eq("deleted", 0);
            if (userMapper.selectCount(usernameQuery) > 0) {
                return ResponseEntity.badRequest().body(buildErrorResponse("用户名已存在"));
            }
            
            // 检查邮箱是否已存在
            QueryWrapper<User> emailQuery = new QueryWrapper<>();
            emailQuery.eq("email", request.getEmail()).eq("deleted", 0);
            if (userMapper.selectCount(emailQuery) > 0) {
                return ResponseEntity.badRequest().body(buildErrorResponse("邮箱已存在"));
            }
            
            // 创建新用户
            User user = new User();
            user.setUsername(request.getUsername());
            user.setPassword(passwordEncoder.encode(request.getPassword()));
            user.setEmail(request.getEmail());
            user.setPhone(request.getPhone());
            user.setRole(request.getRole());
            user.setStatus(request.getStatus() != null ? request.getStatus() : 1);
            user.setBio(request.getBio());
            user.setCreatedAt(LocalDateTime.now());
            user.setUpdatedAt(LocalDateTime.now());
            user.setDeleted(0);
            
            int result = userMapper.insert(user);
            if (result > 0) {
                log.info("管理员添加用户成功: {}", request.getUsername());
                return ResponseEntity.ok(buildSuccessResponse("用户添加成功"));
            } else {
                return ResponseEntity.badRequest().body(buildErrorResponse("用户添加失败"));
            }
        } catch (Exception e) {
            log.error("添加用户失败", e);
            return ResponseEntity.badRequest().body(buildErrorResponse("添加用户失败: " + e.getMessage()));
        }
    }
    
    /**
     * 更新用户信息
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> updateUser(@PathVariable String id, @RequestBody UpdateUserRequest request) {
        try {
            log.info("接收到更新用户请求，ID（字符串）: '{}'", id);
            
            // 验证并转换ID
            Long userId;
            try {
                userId = Long.parseLong(id);
                log.info("更新用户ID转换成功: {} -> {}", id, userId);
            } catch (NumberFormatException e) {
                log.error("更新用户ID格式无效: '{}'", id, e);
                return ResponseEntity.badRequest().body(buildErrorResponse("用户ID格式无效"));
            }
            
            User user = userMapper.selectById(userId);
            if (user == null || user.getDeleted() == 1) {
                log.warn("更新失败：用户不存在或已删除，ID: {}", userId);
                return ResponseEntity.badRequest().body(buildErrorResponse("用户不存在"));
            }
            
            // 检查邮箱是否已被其他用户使用
            if (StringUtils.hasText(request.getEmail()) && !request.getEmail().equals(user.getEmail())) {
                QueryWrapper<User> emailQuery = new QueryWrapper<>();
                emailQuery.eq("email", request.getEmail()).eq("deleted", 0).ne("id", userId);
                if (userMapper.selectCount(emailQuery) > 0) {
                    return ResponseEntity.badRequest().body(buildErrorResponse("邮箱已被其他用户使用"));
                }
                user.setEmail(request.getEmail());
            }
            
            // 更新用户信息
            if (StringUtils.hasText(request.getPhone())) {
                user.setPhone(request.getPhone());
            }
            if (request.getRole() != null) {
                user.setRole(request.getRole());
            }
            if (request.getStatus() != null) {
                user.setStatus(request.getStatus());
            }
            if (StringUtils.hasText(request.getBio())) {
                user.setBio(request.getBio());
            }
            if (StringUtils.hasText(request.getPassword())) {
                user.setPassword(passwordEncoder.encode(request.getPassword()));
            }
            
            user.setUpdatedAt(LocalDateTime.now());
            
            int result = userMapper.updateById(user);
            if (result > 0) {
                log.info("管理员更新用户成功: {} (ID: {})", user.getUsername(), userId);
                return ResponseEntity.ok(buildSuccessResponse("用户信息更新成功"));
            } else {
                return ResponseEntity.badRequest().body(buildErrorResponse("用户信息更新失败"));
            }
        } catch (Exception e) {
            log.error("更新用户信息失败，ID: '{}'", id, e);
            return ResponseEntity.badRequest().body(buildErrorResponse("更新用户信息失败: " + e.getMessage()));
        }
    }
    
    /**
     * 删除用户（软删除）
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteUser(@PathVariable String id) {
        try {
            log.info("接收到删除用户请求，ID（字符串）: '{}'", id);
            
            // 验证并转换ID
            Long userId;
            try {
                userId = Long.parseLong(id);
                log.info("删除用户ID转换成功: {} -> {}", id, userId);
            } catch (NumberFormatException e) {
                log.error("删除用户ID格式无效: '{}'", id, e);
                return ResponseEntity.badRequest().body(buildErrorResponse("用户ID格式无效"));
            }
            
            // 查询用户删除前状态
            User user = userMapper.selectById(userId);
            log.info("删除前用户状态 - ID: {}, deleted: {}, username: {}", 
                    userId, user != null ? user.getDeleted() : "null", 
                    user != null ? user.getUsername() : "null");
            
            if (user == null) {
                log.warn("删除失败：用户不存在，ID: {}", userId);
                return ResponseEntity.badRequest().body(buildErrorResponse("用户不存在"));
            }
            
            if (user.getDeleted() == 1) {
                log.warn("删除失败：用户已被删除，ID: {}", userId);
                return ResponseEntity.badRequest().body(buildErrorResponse("用户已被删除"));
            }
            
            // 不能删除管理员账户
            if (user.getRole() == 1) {
                return ResponseEntity.badRequest().body(buildErrorResponse("不能删除管理员账户"));
            }
            
            // 记录删除前的原始状态
            log.info("准备删除用户 - ID: {}, username: {}, 当前deleted值: {}", 
                    userId, user.getUsername(), user.getDeleted());
            
            // ✅ 使用MyBatis-Plus标准删除方法（会自动处理逻辑删除）
            int result = userMapper.deleteById(userId);
            log.info("MyBatis-Plus删除结果 - 影响行数: {}", result);
            
            if (result > 0) {
                // 验证删除结果（使用原生查询避免逻辑删除影响）
                User deletedUser = userMapper.selectById(userId);
                log.info("删除后验证 - ID: {}, 用户是否仍可查询: {}", 
                        userId, deletedUser != null ? "是，逻辑删除可能失败" : "否，逻辑删除成功");
                
                log.info("管理员删除用户成功: {} (ID: {})", user.getUsername(), userId);
                return ResponseEntity.ok(buildSuccessResponse("用户删除成功"));
            } else {
                log.error("删除失败 - MyBatis-Plus deleteById返回0，ID: {}", userId);
                return ResponseEntity.badRequest().body(buildErrorResponse("用户删除失败：数据库操作无效"));
            }
        } catch (Exception e) {
            log.error("删除用户失败，ID: '{}', 异常: ", id, e);
            return ResponseEntity.badRequest().body(buildErrorResponse("删除用户失败: " + e.getMessage()));
        }
    }
    
    /**
     * 批量删除用户
     */
    @DeleteMapping("/batch")
    public ResponseEntity<?> batchDeleteUsers(@RequestBody BatchDeleteRequest request) {
        try {
            if (request.getIds() == null || request.getIds().isEmpty()) {
                return ResponseEntity.badRequest().body(buildErrorResponse("请选择要删除的用户"));
            }
            
            log.info("接收到批量删除请求，ID列表: {}", request.getIds());
            
            // 转换字符串ID为Long ID
            List<Long> longIds = new ArrayList<>();
            for (String idStr : request.getIds()) {
                try {
                    longIds.add(Long.parseLong(idStr));
                } catch (NumberFormatException e) {
                    log.error("批量删除中发现无效ID: '{}'", idStr, e);
                    return ResponseEntity.badRequest().body(buildErrorResponse("包含无效的用户ID: " + idStr));
                }
            }
            
            // 检查用户是否存在以及权限
            List<User> users = userMapper.selectBatchIds(longIds);
            log.info("查询到 {} 个用户，请求删除 {} 个用户", users.size(), longIds.size());
            
            // 检查是否包含管理员账户
            boolean hasAdmin = users.stream().anyMatch(user -> user.getRole() == 1);
            if (hasAdmin) {
                return ResponseEntity.badRequest().body(buildErrorResponse("不能删除管理员账户"));
            }
            
            // ✅ 使用MyBatis-Plus标准批量删除方法
            int result = userMapper.deleteBatchIds(longIds);
            log.info("批量删除结果 - 影响行数: {}", result);
            
            if (result > 0) {
                log.info("管理员批量删除用户成功: {} 个", result);
                return ResponseEntity.ok(buildSuccessResponse("成功删除 " + result + " 个用户"));
            } else {
                log.warn("批量删除无影响 - 可能用户不存在或已被删除");
                return ResponseEntity.badRequest().body(buildErrorResponse("批量删除失败：无用户被删除"));
            }
        } catch (Exception e) {
            log.error("批量删除用户失败", e);
            return ResponseEntity.badRequest().body(buildErrorResponse("批量删除用户失败: " + e.getMessage()));
        }
    }
    
    /**
     * 重置用户密码
     */
    @PutMapping("/{id}/reset-password")
    public ResponseEntity<?> resetPassword(@PathVariable String id, @RequestBody ResetPasswordRequest request) {
        try {
            log.info("接收到重置密码请求，ID（字符串）: '{}'", id);
            
            // 验证并转换ID
            Long userId;
            try {
                userId = Long.parseLong(id);
                log.info("重置密码ID转换成功: {} -> {}", id, userId);
            } catch (NumberFormatException e) {
                log.error("重置密码ID格式无效: '{}'", id, e);
                return ResponseEntity.badRequest().body(buildErrorResponse("用户ID格式无效"));
            }
            
            User user = userMapper.selectById(userId);
            if (user == null || user.getDeleted() == 1) {
                log.warn("重置密码失败：用户不存在或已删除，ID: {}", userId);
                return ResponseEntity.badRequest().body(buildErrorResponse("用户不存在"));
            }
            
            String newPassword = StringUtils.hasText(request.getNewPassword()) ? 
                request.getNewPassword() : "123456"; // 默认密码
            
            user.setPassword(passwordEncoder.encode(newPassword));
            user.setUpdatedAt(LocalDateTime.now());
            
            int result = userMapper.updateById(user);
            if (result > 0) {
                log.info("管理员重置用户密码成功: {} (ID: {})", user.getUsername(), userId);
                return ResponseEntity.ok(buildSuccessResponse("密码重置成功，新密码为: " + newPassword));
            } else {
                return ResponseEntity.badRequest().body(buildErrorResponse("密码重置失败"));
            }
        } catch (Exception e) {
            log.error("重置用户密码失败，ID: '{}'", id, e);
            return ResponseEntity.badRequest().body(buildErrorResponse("重置用户密码失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取用户统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<?> getUserStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 总用户数
            QueryWrapper<User> totalQuery = new QueryWrapper<>();
            totalQuery.eq("deleted", 0);
            Long totalUsers = userMapper.selectCount(totalQuery);
            
            // 正常用户数
            QueryWrapper<User> activeQuery = new QueryWrapper<>();
            activeQuery.eq("deleted", 0).eq("status", 1);
            Long activeUsers = userMapper.selectCount(activeQuery);
            
            // 禁用用户数
            QueryWrapper<User> disabledQuery = new QueryWrapper<>();
            disabledQuery.eq("deleted", 0).eq("status", 0);
            Long disabledUsers = userMapper.selectCount(disabledQuery);
            
            // 管理员数量
            QueryWrapper<User> adminQuery = new QueryWrapper<>();
            adminQuery.eq("deleted", 0).in("role", 2, 3);
            Long adminUsers = userMapper.selectCount(adminQuery);
            
            stats.put("totalUsers", totalUsers);
            stats.put("activeUsers", activeUsers);
            stats.put("disabledUsers", disabledUsers);
            stats.put("adminUsers", adminUsers);
            
            return ResponseEntity.ok(buildSuccessResponse(stats));
        } catch (Exception e) {
            log.error("获取用户统计信息失败", e);
            return ResponseEntity.badRequest().body(buildErrorResponse("获取用户统计信息失败: " + e.getMessage()));
        }
    }
    
    /**
     * 调试用户数据查询
     */
    @GetMapping("/debug")
    public ResponseEntity<?> debugUsers() {
        try {
            Map<String, Object> debugInfo = new HashMap<>();
            
            // 测试基本查询
            List<User> allUsers = userMapper.selectList(null);
            debugInfo.put("totalUsersInDB", allUsers.size());
            
            // 测试删除条件查询
            QueryWrapper<User> activeQuery = new QueryWrapper<>();
            activeQuery.eq("deleted", 0);
            List<User> activeUsers = userMapper.selectList(activeQuery);
            debugInfo.put("activeUsers", activeUsers.size());
            debugInfo.put("deletedUsers", allUsers.size() - activeUsers.size());
            
            // ✅ 添加原生SQL查询验证删除状态
            try {
                QueryWrapper<User> rawQuery = new QueryWrapper<>();
                rawQuery.select("id", "username", "deleted", "updated_at")
                       .last("ORDER BY updated_at DESC LIMIT 10");
                List<Map<String, Object>> recentUsers = userMapper.selectMaps(rawQuery);
                debugInfo.put("recentUsersWithDeletedStatus", recentUsers);
                
                // 专门查询deleted=1的用户
                QueryWrapper<User> deletedQuery = new QueryWrapper<>();
                deletedQuery.select("id", "username", "deleted", "updated_at")
                          .eq("deleted", 1)
                          .orderByDesc("updated_at");
                List<Map<String, Object>> deletedUsers = userMapper.selectMaps(deletedQuery);
                debugInfo.put("explicitlyDeletedUsers", deletedUsers);
                debugInfo.put("explicitlyDeletedCount", deletedUsers.size());
            } catch (Exception e) {
                debugInfo.put("rawQueryError", e.getMessage());
            }
            
            if (!allUsers.isEmpty()) {
                User firstUser = allUsers.get(0);
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("id", firstUser.getId());
                userInfo.put("idType", firstUser.getId().getClass().getSimpleName());
                userInfo.put("username", firstUser.getUsername());
                userInfo.put("email", firstUser.getEmail());
                userInfo.put("role", firstUser.getRole());
                userInfo.put("status", firstUser.getStatus());
                userInfo.put("deleted", firstUser.getDeleted());
                userInfo.put("createdAt", firstUser.getCreatedAt());
                userInfo.put("updatedAt", firstUser.getUpdatedAt());
                debugInfo.put("firstUser", userInfo);
                
                // 测试按ID查询
                Long testId = firstUser.getId();
                User userById = userMapper.selectById(testId);
                debugInfo.put("queryByIdWorking", userById != null);
                debugInfo.put("testId", testId);
                debugInfo.put("testIdString", testId.toString());
                
                // 大ID测试
                if (testId > 1000000000000000000L) {
                    debugInfo.put("hasLargeId", true);
                    debugInfo.put("idWarning", "检测到大ID值，可能影响前端JavaScript精度");
                } else {
                    debugInfo.put("hasLargeId", false);
                }
            }
            
            // 测试分页查询
            QueryWrapper<User> pageQuery = new QueryWrapper<>();
            pageQuery.eq("deleted", 0).orderByDesc("created_at");
            List<User> recentUsers = userMapper.selectList(pageQuery);
            debugInfo.put("recentUsersCount", recentUsers.size());
            
            // 添加前几个用户的ID信息
            if (recentUsers.size() > 0) {
                List<Map<String, Object>> userIds = recentUsers.stream()
                    .limit(5)
                    .map(user -> {
                        Map<String, Object> idInfo = new HashMap<>();
                        idInfo.put("id", user.getId());
                        idInfo.put("idString", user.getId().toString());
                        idInfo.put("username", user.getUsername());
                        idInfo.put("deleted", user.getDeleted());
                        return idInfo;
                    })
                    .collect(Collectors.toList());
                debugInfo.put("sampleUserIds", userIds);
            }
            
            // MyBatis-Plus版本信息和逻辑删除状态
            debugInfo.put("mapperType", userMapper.getClass().getSimpleName());
            debugInfo.put("logicDeleteEnabled", "逻辑删除已配置：deleted字段，删除值=1，未删除值=0");
            
            return ResponseEntity.ok(buildSuccessResponse(debugInfo));
        } catch (Exception e) {
            log.error("调试用户查询失败", e);
            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("error", e.getMessage());
            errorInfo.put("errorClass", e.getClass().getSimpleName());
            errorInfo.put("stackTrace", e.getStackTrace());
            return ResponseEntity.badRequest().body(buildErrorResponse("调试失败: " + e.getMessage()));
        }
    }
    
    // 辅助方法
    private String getRoleName(Integer role) {
        switch (role) {
            case 1: return "管理员";
            case 2: return "普通用户";
            case 3: return "教师";
            default: return "未知";
        }
    }
    
    /**
     * 生成随机头像URL
     * 根据用户信息生成唯一的头像
     */
    private String generateRandomAvatar(User user) {
        if (user == null) {
            return null; // 不返回默认头像，让前端处理
        }
        
        // 只返回用户实际上传的头像
        if (user.getAvatar() != null && !user.getAvatar().trim().isEmpty()) {
            // 确保头像路径格式正确
            String avatar = user.getAvatar();
            if (avatar.startsWith("avatars/")) {
                return "/uploads/" + avatar;
            } else if (avatar.startsWith("main/avatars/")) {
                return "/uploads/avatars/" + avatar.replace("main/avatars/", "");
            } else if (!avatar.startsWith("http") && !avatar.startsWith("/")) {
                return "/uploads/avatars/" + avatar;
            }
            return avatar;
        }
        
        // 没有头像时返回null，让前端根据用户名生成首字母头像
        return null;
    }
    
    /**
     * 备用头像生成方案：UI Avatars
     */
    private String generateUIAvatar(User user) {
        if (user == null || user.getUsername() == null) {
            return "/static/images/default-avatar.png";
        }
        
        try {
            String username = user.getUsername();
            String[] colors = {"3498db", "9b59b6", "e74c3c", "2ecc71", "f39c12", "1abc9c", "34495e", "e67e22"};
            int colorIndex = Math.abs(username.hashCode()) % colors.length;
            String bgColor = colors[colorIndex];
            
            String initials = username.length() >= 2 ? 
                             username.substring(0, 2).toUpperCase() : 
                             username.substring(0, 1).toUpperCase();
            
            return String.format("https://ui-avatars.com/api/?name=%s&background=%s&color=fff&size=128", 
                               java.net.URLEncoder.encode(initials, "UTF-8"), 
                               bgColor);
        } catch (Exception e) {
            return "/static/images/default-avatar.png";
        }
    }
    
    private Map<String, Object> buildSuccessResponse(Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", data);
        return response;
    }
    
    private Map<String, Object> buildErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        return response;
    }
    
    // 请求DTO类
    public static class AddUserRequest {
        private String username;
        private String password;
        private String email;
        private String phone;
        private Integer role;
        private Integer status;
        private String bio;
        
        // Getters and Setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public Integer getRole() { return role; }
        public void setRole(Integer role) { this.role = role; }
        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }
        public String getBio() { return bio; }
        public void setBio(String bio) { this.bio = bio; }
    }
    
    public static class UpdateUserRequest {
        private String email;
        private String phone;
        private Integer role;
        private Integer status;
        private String bio;
        private String password;
        
        // Getters and Setters
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public Integer getRole() { return role; }
        public void setRole(Integer role) { this.role = role; }
        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }
        public String getBio() { return bio; }
        public void setBio(String bio) { this.bio = bio; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
    }
    
    public static class BatchDeleteRequest {
        private List<String> ids;
        
        public List<String> getIds() { return ids; }
        public void setIds(List<String> ids) { this.ids = ids; }
    }
    
    public static class ResetPasswordRequest {
        private String newPassword;
        
        public String getNewPassword() { return newPassword; }
        public void setNewPassword(String newPassword) { this.newPassword = newPassword; }
    }
} 