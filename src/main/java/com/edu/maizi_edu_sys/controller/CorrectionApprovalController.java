package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.entity.CorrectionApproval;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.service.CorrectionApprovalService;
import com.edu.maizi_edu_sys.config.CorrectionProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/admin/correction")
public class CorrectionApprovalController {

    private static final Logger logger = LoggerFactory.getLogger(CorrectionApprovalController.class);

    @Autowired
    private CorrectionApprovalService approvalService;

    @Autowired
    private CorrectionProperties correctionProperties;

    /**
     * 审核管理主页面
     */
    @GetMapping("/approval")
    public String approvalPage(Model model) {
        try {
            // 设置导航标签
            model.addAttribute("activeTab", "correction");
            
            // 获取待审核列表
            List<CorrectionApproval> pendingApprovals = approvalService.getPendingApprovals();
            model.addAttribute("pendingApprovals", pendingApprovals);
            
            // 获取统计信息
            Map<String, Object> statistics = approvalService.getApprovalStatistics();
            model.addAttribute("statistics", statistics);
            
            // 配置信息
            Map<String, Object> config = new HashMap<>();
            config.put("approvalEnabled", correctionProperties.getApproval().isEnabled());
            config.put("scheduleEnabled", correctionProperties.getSchedule().isEnabled());
            config.put("autoRejectHours", correctionProperties.getApproval().getAutoRejectHours());
            model.addAttribute("config", config);
            
            return "admin/templates/correction_approval";
        } catch (Exception e) {
            logger.error("加载审核页面失败", e);
            model.addAttribute("error", "加载审核页面失败: " + e.getMessage());
            return "error/500";
        }
    }

    /**
     * 获取待审核列表 (AJAX)
     */
    @GetMapping("/api/pending")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getPendingApprovals(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String status) {
        try {
            Map<String, Object> result = approvalService.getPendingApprovalsWithPagination(page, size, search, status);
            result.put("success", true);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取待审核列表失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取待审核列表失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 获取审核详情 (AJAX)
     */
    @GetMapping("/api/detail/{id}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getApprovalDetail(@PathVariable Long id) {
        try {
            List<CorrectionApproval> approvals = approvalService.getPendingApprovals();
            CorrectionApproval approval = approvals.stream()
                    .filter(a -> a.getId().equals(id))
                    .findFirst()
                    .orElse(null);
            
            Map<String, Object> result = new HashMap<>();
            if (approval != null) {
                result.put("success", true);
                result.put("data", approval);
                
                // 解析修正内容
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    Object correctionData = mapper.readValue(approval.getCorrectionJson(), Object.class);
                    result.put("correctionData", correctionData);
                    
                    if (approval.getStatistics() != null && !approval.getStatistics().isEmpty()) {
                        Object statisticsData = mapper.readValue(approval.getStatistics(), Object.class);
                        result.put("statisticsData", statisticsData);
                    }
                } catch (Exception e) {
                    logger.warn("解析修正详情JSON失败", e);
                }
            } else {
                result.put("success", false);
                result.put("message", "审核记录不存在");
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取审核详情失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取审核详情失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 审核通过 (AJAX)
     */
    @PostMapping("/api/approve/{id}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> approveCorrection(
            @PathVariable Long id,
            @RequestParam String approver,
            @RequestParam(defaultValue = "") String comment) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = approvalService.approveCorrection(id, approver, comment);
            if (success) {
                result.put("success", true);
                result.put("message", "审核通过成功");
                logger.info("管理员 {} 审核通过修正记录 {}", approver, id);
            } else {
                result.put("success", false);
                result.put("message", "审核通过失败，请检查记录状态");
            }
        } catch (Exception e) {
            logger.error("审核通过操作失败", e);
            result.put("success", false);
            result.put("message", "审核通过失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 审核拒绝 (AJAX)
     */
    @PostMapping("/api/reject/{id}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> rejectCorrection(
            @PathVariable Long id,
            @RequestParam String approver,
            @RequestParam String comment) {
        
        Map<String, Object> result = new HashMap<>();
        
        if (comment == null || comment.trim().isEmpty()) {
            result.put("success", false);
            result.put("message", "拒绝审核必须填写原因");
            return ResponseEntity.ok(result);
        }
        
        try {
            boolean success = approvalService.rejectCorrection(id, approver, comment);
            if (success) {
                result.put("success", true);
                result.put("message", "审核拒绝成功");
                logger.info("管理员 {} 拒绝修正记录 {}，原因: {}", approver, id, comment);
            } else {
                result.put("success", false);
                result.put("message", "审核拒绝失败，请检查记录状态");
            }
        } catch (Exception e) {
            logger.error("审核拒绝操作失败", e);
            result.put("success", false);
            result.put("message", "审核拒绝失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 批量审核通过 (AJAX)
     */
    @PostMapping("/api/batch/approve")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> batchApproveCorrections(
            @RequestParam String ids,
            @RequestParam String approver,
            @RequestParam(defaultValue = "") String comment) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            String[] idArray = ids.split(",");
            int successCount = 0;
            int failCount = 0;
            
            for (String idStr : idArray) {
                try {
                    Long id = Long.parseLong(idStr.trim());
                    boolean success = approvalService.approveCorrection(id, approver, comment);
                    if (success) {
                        successCount++;
                        logger.info("管理员 {} 批量审核通过修正记录 {}", approver, id);
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    logger.error("批量审核通过单个记录失败: {}", idStr, e);
                    failCount++;
                }
            }
            
            result.put("success", true);
            result.put("message", String.format("批量审核完成！成功 %d 项，失败 %d 项", successCount, failCount));
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            
        } catch (Exception e) {
            logger.error("批量审核通过操作失败", e);
            result.put("success", false);
            result.put("message", "批量审核通过失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 批量审核拒绝 (AJAX)
     */
    @PostMapping("/api/batch/reject")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> batchRejectCorrections(
            @RequestParam String ids,
            @RequestParam String approver,
            @RequestParam String comment) {
        
        Map<String, Object> result = new HashMap<>();
        
        if (comment == null || comment.trim().isEmpty()) {
            result.put("success", false);
            result.put("message", "批量拒绝审核必须填写原因");
            return ResponseEntity.ok(result);
        }
        
        try {
            String[] idArray = ids.split(",");
            int successCount = 0;
            int failCount = 0;
            
            for (String idStr : idArray) {
                try {
                    Long id = Long.parseLong(idStr.trim());
                    boolean success = approvalService.rejectCorrection(id, approver, comment);
                    if (success) {
                        successCount++;
                        logger.info("管理员 {} 批量拒绝修正记录 {}，原因: {}", approver, id, comment);
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    logger.error("批量审核拒绝单个记录失败: {}", idStr, e);
                    failCount++;
                }
            }
            
            result.put("success", true);
            result.put("message", String.format("批量审核完成！成功 %d 项，失败 %d 项", successCount, failCount));
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            
        } catch (Exception e) {
            logger.error("批量审核拒绝操作失败", e);
            result.put("success", false);
            result.put("message", "批量审核拒绝失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 回撤审核 (AJAX)
     */
    @PostMapping("/api/withdraw/{id}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> withdrawCorrection(
            @PathVariable Long id,
            @RequestParam(defaultValue = "") String reason) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = approvalService.withdrawCorrection(id, reason);
            if (success) {
                result.put("success", true);
                result.put("message", "回撤成功");
                logger.info("审核回撤成功，ID: {}, 原因: {}", id, reason);
            } else {
                result.put("success", false);
                result.put("message", "回撤失败，请检查记录状态");
            }
        } catch (Exception e) {
            logger.error("回撤审核操作失败", e);
            result.put("success", false);
            result.put("message", "回撤失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 获取审核统计信息 (AJAX)
     */
    @GetMapping("/api/statistics")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getStatistics() {
        try {
            Map<String, Object> statistics = approvalService.getApprovalStatistics();
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", statistics);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取统计信息失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 根据日期查询审核记录 (AJAX)
     */
    @GetMapping("/api/history")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getApprovalHistory(
            @RequestParam(defaultValue = "") String date) {
        
        try {
            String queryDate = date.isEmpty() ? 
                    LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE) : date;
            
            List<CorrectionApproval> approvals = approvalService.getApprovalsByDate(queryDate);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", approvals);
            result.put("date", queryDate);
            result.put("total", approvals.size());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取审核历史失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取审核历史失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 获取原始题目数据用于对比 (AJAX)
     */
    @GetMapping("/api/original-topic/{topicId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getOriginalTopic(@PathVariable Long topicId) {
        try {
            Topic originalTopic = approvalService.getOriginalTopicById(topicId);
            Map<String, Object> result = new HashMap<>();
            
            if (originalTopic != null) {
                result.put("success", true);
                result.put("data", originalTopic);
            } else {
                result.put("success", false);
                result.put("message", "未找到原始题目数据");
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取原始题目数据失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取原始题目数据失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
}