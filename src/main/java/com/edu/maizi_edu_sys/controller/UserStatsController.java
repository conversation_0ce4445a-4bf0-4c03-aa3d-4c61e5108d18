package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.dto.UserStatsDTO;
import com.edu.maizi_edu_sys.dto.UserTopicTypeDistributionDTO;
import com.edu.maizi_edu_sys.dto.UserTrendDTO;
import com.edu.maizi_edu_sys.service.UserStatsService;
import com.edu.maizi_edu_sys.service.UserStatsCacheService;
import com.edu.maizi_edu_sys.service.AuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户统计控制器
 */
@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
@Slf4j
public class UserStatsController {
    
    private final UserStatsService userStatsService;
    private final UserStatsCacheService userStatsCacheService;
    private final AuthService authService;
    
    /**
     * 获取用户个人上传统计数据
     */
    @GetMapping("/upload/stats/personal")
    public ApiResponse<Map<String, Object>> getPersonalUploadStats(HttpServletRequest request) {
        try {
            Long userId = authService.getCurrentUserId();
            if (userId == null) {
                log.warn("获取当前用户ID失败，用户可能未认证");
                return ApiResponse.error("用户未认证");
            }
            
            log.info("获取用户{}的个人上传统计数据", userId);
            // 使用缓存服务获取统计数据，提高性能
            UserStatsDTO stats = userStatsCacheService.getUserStats(userId);
            
            // 转换为前端期望的格式
            Map<String, Object> response = new HashMap<>();
            response.put("totalUploads", stats.getTotalUploaded());
            response.put("approvedUploads", stats.getApprovedCount());
            response.put("pendingUploads", stats.getPendingCount());
            response.put("weeklyUploads", stats.getWeeklyUploaded());
            
            // 成就数据
            Map<String, Object> achievements = new HashMap<>();
            achievements.put("consecutiveDays", stats.getConsecutiveDays());
            achievements.put("maxDailyUploads", stats.getMaxDailyUpload());
            achievements.put("avgAuditTime", String.format("%.1f", stats.getAverageAuditTime()));
            response.put("achievements", achievements);
            
            // 排名数据
            Map<String, Object> rankings = new HashMap<>();
            rankings.put("totalUploadRank", stats.getRanking());
            rankings.put("monthlyRank", stats.getRanking() + 2); // 模拟月度排名
            rankings.put("qualityScore", Math.round(stats.getQualityScore()));
            response.put("rankings", rankings);
            
            // 题目类型分布
            List<UserTopicTypeDistributionDTO> typeDistList = userStatsService.getUserTopicTypeDistribution(userId);
            Map<String, Object> typeDistribution = new HashMap<>();
            typeDistList.forEach(dist -> {
                typeDistribution.put(dist.getType(), dist.getCount());
            });
            // 补充缺失的题目类型
            String[] types = {"choice", "multiple", "judge", "fill", "short", "group"};
            for (String type : types) {
                typeDistribution.putIfAbsent(type, 0);
            }
            response.put("typeDistribution", typeDistribution);
            
            log.info("成功获取用户{}的统计数据: 总上传{}, 已通过{}, 待审核{}", 
                userId, stats.getTotalUploaded(), stats.getApprovedCount(), stats.getPendingCount());
            
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取用户个人统计失败", e);
            return ApiResponse.error("获取用户个人统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户上传趋势图表数据
     */
    @GetMapping("/upload/stats/chart")
    public ApiResponse<Map<String, Object>> getUploadStatsChart(
            @RequestParam(defaultValue = "day") String type,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            HttpServletRequest request) {
        try {
            Long userId = authService.getCurrentUserId();
            if (userId == null) {
                log.warn("获取当前用户ID失败，用户可能未认证");
                return ApiResponse.error("用户未认证");
            }
            
            // 根据type参数确定天数
            int days;
            if ("week".equals(type)) {
                days = 7;
            } else if ("month".equals(type)) {
                days = 30;
            } else {
                days = 7; // day
            }
            
            log.info("获取用户{}的上传趋势数据，类型: {}, 天数: {}", userId, type, days);
            UserTrendDTO trend = userStatsService.getUserUploadTrend(userId, days);
            
            // 转换为前端期望的格式
            Map<String, Object> response = new HashMap<>();
            response.put("labels", trend.getDates());
            response.put("uploadData", trend.getUploadCounts());
            
            // 计算通过数据（模拟实现）
            List<Integer> approvedData = trend.getUploadCounts().stream()
                .map(count -> Math.max(0, (int)(count * 0.85))) // 假设85%通过率
                .collect(Collectors.toList());
            response.put("approvedData", approvedData);
            
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取用户上传趋势失败", e);
            return ApiResponse.error("获取用户上传趋势失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户个人统计数据（原有路径保持兼容）
     */
    @GetMapping("/stats")
    public ResponseEntity<?> getUserStats(HttpServletRequest request) {
        try {
            Long userId = authService.getCurrentUserId();
            if (userId == null) {
                return ResponseEntity.badRequest().body("用户未认证");
            }
            UserStatsDTO stats = userStatsService.getUserStats(userId);
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("获取用户统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户上传趋势数据（原有路径保持兼容）
     */
    @GetMapping("/stats/trend")
    public ResponseEntity<?> getUserUploadTrend(
            @RequestParam(defaultValue = "7") int days,
            HttpServletRequest request) {
        try {
            Long userId = authService.getCurrentUserId();
            if (userId == null) {
                return ResponseEntity.badRequest().body("用户未认证");
            }
            UserTrendDTO trend = userStatsService.getUserUploadTrend(userId, days);
            return ResponseEntity.ok(trend);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("获取用户上传趋势失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户题目类型分布（原有路径保持兼容）
     */
    @GetMapping("/stats/type-distribution")
    public ResponseEntity<?> getUserTopicTypeDistribution(HttpServletRequest request) {
        try {
            Long userId = authService.getCurrentUserId();
            if (userId == null) {
                return ResponseEntity.badRequest().body("用户未认证");
            }
            List<UserTopicTypeDistributionDTO> distribution = userStatsService.getUserTopicTypeDistribution(userId);
            return ResponseEntity.ok(distribution);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("获取用户题目类型分布失败: " + e.getMessage());
        }
    }

    /**
     * 刷新用户统计缓存
     */
    @PostMapping("/stats/refresh")
    public ApiResponse<String> refreshUserStatsCache(HttpServletRequest request) {
        try {
            Long userId = authService.getCurrentUserId();
            if (userId == null) {
                return ApiResponse.error("用户未认证");
            }

            log.info("手动刷新用户{}的统计缓存", userId);
            userStatsCacheService.refreshUserStatsCache(userId);

            return ApiResponse.success("缓存刷新成功");
        } catch (Exception e) {
            log.error("刷新用户统计缓存失败", e);
            return ApiResponse.error("刷新缓存失败: " + e.getMessage());
        }
    }

    /**
     * 获取缓存统计信息（管理员接口）
     */
    @GetMapping("/stats/cache-info")
    public ApiResponse<UserStatsCacheService.CacheStats> getCacheStats() {
        try {
            UserStatsCacheService.CacheStats stats = userStatsCacheService.getCacheStats();
            return ApiResponse.success(stats);
        } catch (Exception e) {
            log.error("获取缓存统计信息失败", e);
            return ApiResponse.error("获取缓存信息失败: " + e.getMessage());
        }
    }
}