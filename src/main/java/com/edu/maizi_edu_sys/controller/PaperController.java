package com.edu.maizi_edu_sys.controller;

import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

import com.edu.maizi_edu_sys.dto.*;
import com.edu.maizi_edu_sys.entity.Paper;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.PaperDownload;

import com.edu.maizi_edu_sys.service.PaperGenerationService;
import com.edu.maizi_edu_sys.service.AuthService;

import com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl; // Added import

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;

import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.LocalDateTime;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Arrays;

import com.edu.maizi_edu_sys.repository.TopicMapper;
import com.edu.maizi_edu_sys.util.TopicTypeMapper;

@Controller
@RequestMapping("/")
@Slf4j
public class PaperController {

    private final PaperGenerationService paperService;
    private final ObjectMapper objectMapper;
    private final AuthService authService;
    private final TopicMapper topicMapper;

    /**
     * Sanitizes a filename by removing or replacing characters that are invalid in filenames
     * across common operating systems
     * 
     * @param input The original filename to sanitize
     * @return A sanitized filename safe for use in file systems
     */
    private String sanitizeFilename(String input) {
        if (input == null || input.trim().isEmpty()) {
            return "";
        }
        
        // Replace invalid filename characters with underscores
        String sanitized = input.replaceAll("[\\/:*?\"<>|]|\\s+", "_");
        
        // Trim to reasonable length to avoid very long filenames
        int maxLength = 100;
        if (sanitized.length() > maxLength) {
            sanitized = sanitized.substring(0, maxLength);
        }
        
        return sanitized;
    }
    
    @SuppressWarnings("unused")
    private List<Long> parseTopicIds(String topicIds) {
        if (topicIds == null || topicIds.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        return Stream.of(topicIds.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    public PaperController(
            PaperGenerationService paperService,
            ObjectMapper objectMapper,
            AuthService authService,
            TopicMapper topicMapper) {
        this.paperService = paperService;
        this.objectMapper = objectMapper;
        this.authService = authService;
        this.topicMapper = topicMapper;
    }

    /**
     * 显示试卷生成页面
     */
    @GetMapping("/papers/generate")
    public String showPaperGenerationPage(Model model) {
        return "paper/generate";
    }

    /**
     * 智能组卷接口
     */
    @PostMapping("/api/papers/generate")
    @ResponseBody
    public ResponseEntity<ApiResponse<PaperGenerationResponse>> generatePaper(
            @Valid @RequestBody PaperGenerationRequest request,
            BindingResult result) {
        if (result.hasErrors()) {
            String errorMsg = result.getFieldErrors().stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .collect(Collectors.joining(", "));
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "参数验证失败: " + errorMsg, null));
        }
        log.info("Received request to generate paper: {}", request.getTitle());
        try {
            PaperGenerationResponse response = paperService.generatePaper(request);
            if (response.getSuccess()) {
                 return ResponseEntity.ok(new ApiResponse<>(true, "试卷生成成功", response));
            } else {
                return ResponseEntity.badRequest().body(new ApiResponse<>(false, response.getErrorMessage(), response));
            }
        } catch (Exception e) {
            log.error("Error generating paper: ", e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "试卷生成失败: " + e.getMessage(), null));
        }
    }

    /**
     *  批量生成试卷接口
     */
    @PostMapping("/api/papers/generate-batch")
    @ResponseBody
    public ResponseEntity<ApiResponse<BatchPaperGenerationResponse>> generateBatchPapers(
            @Valid @RequestBody BatchPaperGenerationRequest request,
            BindingResult result) {
        if (result.hasErrors()) {
            String errorMsg = result.getFieldErrors().stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .collect(Collectors.joining(", "));
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "参数验证失败: " + errorMsg, null));
        }

        log.info("Received request to generate {} papers with title: {}",
                request.getPaperCount(), request.getTitle());

        try {
            BatchPaperGenerationResponse response = paperService.generateBatchPapers(request);
            if (response.getOverallSuccess()) {
                return ResponseEntity.ok(new ApiResponse<>(true, "批量生成完成", response));
            } else {
                return ResponseEntity.badRequest().body(new ApiResponse<>(false, response.getErrorMessage(), response));
            }
        } catch (Exception e) {
            log.error("Error generating batch papers: ", e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "批量生成失败: " + e.getMessage(), null));
        }
    }

    /**
     * 保存试卷接口
     */
    @PostMapping("/api/papers")
    @ResponseBody
    public ResponseEntity<ApiResponse<Paper>> savePaper(@Valid @RequestBody Paper paper) {
        try {
            if (paper.getPaperType() == null) {
                paper.setPaperType("regular");
            }
            if (paper.getFileFormat() == null) {
                paper.setFileFormat("pdf");
            }

            // 设置创建用户ID
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId != null) {
                paper.setUserId(currentUserId);
            }

            Paper savedPaper = paperService.savePaper(paper);
            return ResponseEntity.ok(new ApiResponse<>(true, "试卷保存成功", savedPaper));
        } catch (Exception e) {
            log.error("Error saving paper: ", e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "试卷保存失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取试卷列表接口 (支持分页、搜索、筛选和排序)
     */
    @GetMapping("/api/papers")
    @ResponseBody
    public ResponseEntity<ApiResponse<PaginationResponse<Paper>>> getAllPapers(
        @RequestParam(required = false) String search,
        @RequestParam(required = false) Integer type,
        @RequestParam(defaultValue = "createTime,desc") String sort,
        @RequestParam(defaultValue = "0") Integer page,
        @RequestParam(defaultValue = "10") Integer size
    ) {
        try {
            log.info("Fetching papers with search='{}', type={}, sort='{}', page={}, size={}",
                    search, type, sort, page, size);

            String[] sortParams = sort.split(",");
            String sortField = "createTime";
            boolean isAsc = false;

            if (sortParams.length == 2) {
                sortField = sortParams[0];
                isAsc = "asc".equalsIgnoreCase(sortParams[1]);
            }

            if ("time".equals(sortField)) {
                sortField = "create_time";
            } else if ("score".equals(sortField)) {
                sortField = "total_score";
            } else if ("title".equals(sortField)) {
                sortField = "title";
            } else {
                sortField = "create_time";
            }

            PaginationResponse<Paper> paginatedPapers = paperService.getPapersWithPagination(
                search,
                type,
                sortField,
                isAsc,
                page,
                size);

            return ResponseEntity.ok(new ApiResponse<>(true, "获取试卷列表成功", paginatedPapers));
        } catch (Exception e) {
            log.error("Error retrieving papers: ", e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "获取试卷列表失败: " + e.getMessage(), null));
        }
    }

    /**
     * 获取试卷详情接口
     */
    @GetMapping("/api/papers/{id:\\d+}")
    @ResponseBody
    public ResponseEntity<ApiResponse<PaperDetailDTO>> getPaperById(@PathVariable Long id) {
        try {
            PaperDetailDTO paper = paperService.getPaperDetail(id);
            if (paper != null) {
                return ResponseEntity.ok(new ApiResponse<>(true, "获取试卷详情成功", paper));
            } else {
                return ResponseEntity
                        .status(HttpStatus.NOT_FOUND)
                        .body(new ApiResponse<>(false, "未找到指定试卷", null));
            }
        } catch (Exception e) {
            log.error("Error retrieving paper details for id {}: ", id, e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "获取试卷详情失败: " + e.getMessage(), null));
        }
    }

    /**
     * 删除试卷接口
     */
    @DeleteMapping("/api/papers/{id}")
    @ResponseBody
    public ResponseEntity<ApiResponse<Void>> deletePaper(@PathVariable Long id) {
        try {
            boolean deleted = paperService.deletePaper(id);
            if (deleted) {
                return ResponseEntity.ok(new ApiResponse<>(true, "试卷删除成功", null));
            } else {
                return ResponseEntity
                        .status(HttpStatus.NOT_FOUND)
                        .body(new ApiResponse<>(false, "未找到要删除的指定试卷", null));
            }
        } catch (Exception e) {
            log.error("Error deleting paper id {}: ", id, e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "试卷删除失败: " + e.getMessage(), null));
        }
    }

    /**
     * 试卷预览页面 (HTML page)
     */
    @GetMapping("/papers/preview/{id}")
    public String previewPaper(@PathVariable Long id, Model model) {
        log.info("Request to preview paper with id: {}", id);
        PaperDetailDTO paperDetail = paperService.getPaperDetail(id);
        if (paperDetail == null) {
            log.warn("Paper not found for preview, id: {}", id);
            return "error/404";
        }

        if (paperDetail.getTopics() != null) {
            for (com.edu.maizi_edu_sys.entity.Topic topic : paperDetail.getTopics()) {
                model.addAttribute("parsedOptions" + topic.getId(), parseOptionsFromTopic(topic));
            }
        }

        model.addAttribute("paper", paperDetail);
        model.addAttribute("pageTitle", paperDetail.getTitle() != null ? paperDetail.getTitle() : "试卷预览");

        // Enhanced logging for debugging
        try {
            log.info("PaperDetail being sent to template: {}", objectMapper.writeValueAsString(paperDetail));
            if (paperDetail.getTopics() != null) {
                for (com.edu.maizi_edu_sys.entity.Topic topicLogger : paperDetail.getTopics()) {
                    Object parsedOptionsObj = model.getAttribute("parsedOptions" + topicLogger.getId());
                    String optionsToLog;
                    if (parsedOptionsObj == null) {
                        optionsToLog = "null";
                    } else if (parsedOptionsObj instanceof String) {
                        optionsToLog = (String) parsedOptionsObj;
                    } else {
                        optionsToLog = objectMapper.writeValueAsString(parsedOptionsObj);
                    }
                    log.info("Parsed options for topic ID {}: {}", topicLogger.getId(), optionsToLog);
                }
            }
        } catch (com.fasterxml.jackson.core.JsonProcessingException e) {
            log.error("Error serializing paperDetail or options for logging", e);
        }

        return "paper/preview";
    } // End of previewPaper method

    private List<Map<String, String>> parseOptionsFromTopic(com.edu.maizi_edu_sys.entity.Topic topic) {
        if (topic.getOptions() == null || topic.getOptions().isEmpty()) {
            return Collections.emptyList();
        }
        try {
            return objectMapper.readValue(topic.getOptions(), new com.fasterxml.jackson.core.type.TypeReference<List<Map<String, String>>>() {});
        } catch (Exception e) {
            log.error("Error parsing options for topic id {}: {}", topic.getId(), topic.getOptions(), e);
            return Collections.emptyList();
        }
    }


    @ModelAttribute("objectMapper")
    public ObjectMapper getObjectMapper() {
        return objectMapper;
    }

    /**
     * 获取试卷难度分布
     */
    @GetMapping("/api/papers/{id}/difficulty-distribution")
    @ResponseBody
    public ResponseEntity<ApiResponse<Map<String, Object>>> getPaperDifficultyDistribution(@PathVariable Long id) {
        try {
            Paper paper = paperService.getPaperById(id);
            if (paper == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ApiResponse<>(false, "试卷不存在", null));
            }

            // 获取试卷详情（包含题目信息）
            PaperDetailDTO paperDetail = paperService.getPaperDetail(id);
            if (paperDetail == null || paperDetail.getTopics() == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ApiResponse<>(false, "试卷详情不存在", null));
            }

            // 按难度分类题目
            List<Map<String, Object>> easyQuestions = new ArrayList<>();
            List<Map<String, Object>> mediumQuestions = new ArrayList<>();
            List<Map<String, Object>> hardQuestions = new ArrayList<>();

            for (com.edu.maizi_edu_sys.entity.Topic topic : paperDetail.getTopics()) {
                Double difficultyValue = topic.getDifficulty();
                String difficulty = "medium"; // 默认中等难度

                // 根据难度数值转换为难度级别
                if (difficultyValue != null) {
                    if (difficultyValue <= 0.4) {
                        difficulty = "easy";
                    } else if (difficultyValue >= 0.71) {
                        difficulty = "hard";
                    } else {
                        difficulty = "medium";
                    }
                }

                Map<String, Object> questionInfo = new HashMap<>();
                questionInfo.put("title", topic.getTitle());
                questionInfo.put("type", topic.getType());
                questionInfo.put("difficulty", difficulty);

                switch (difficulty.toLowerCase()) {
                    case "easy":
                        easyQuestions.add(questionInfo);
                        break;
                    case "hard":
                        hardQuestions.add(questionInfo);
                        break;
                    default:
                        mediumQuestions.add(questionInfo);
                        break;
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("paperTitle", paper.getTitle());
            result.put("easy", easyQuestions);
            result.put("medium", mediumQuestions);
            result.put("hard", hardQuestions);
            result.put("totalQuestions", paperDetail.getTopics().size());

            return ResponseEntity.ok(new ApiResponse<>(true, "获取难度分布成功", result));
        } catch (Exception e) {
            log.error("获取试卷难度分布失败: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(false, "获取难度分布失败: " + e.getMessage(), null));
        }
    }


    /**
     * 试卷下载接口 (API endpoint)
     *  优化以兼容下载代理工具（迅雷、IDM等）
     */
    @GetMapping("/api/papers/download/{id}")
    public ResponseEntity<Resource> downloadPaper(
            @PathVariable Long id,
            @RequestParam(defaultValue = "pdf") String format,
            @RequestParam(defaultValue = "regular") String paperType,
            HttpServletRequest request) {

        //  检测是否为下载代理工具的预检请求
        String userAgent = request.getHeader("User-Agent");
        boolean isDownloadManager = isDownloadManagerRequest(userAgent);

        log.info("下载请求 - paperId: {}, format: {}, paperType: {}, userAgent: {}, isDownloadManager: {}",
                id, format, paperType, userAgent, isDownloadManager);

        // 对于下载代理工具，不进行连接状态检查，因为它们会快速断开重连
        if (!isDownloadManager && isClientDisconnected(request)) {
            log.warn("客户端连接已断开，取消下载 - paperId: {}", id);
            return null;
        }

        try {
            // 检查格式参数
            if (!format.equalsIgnoreCase("pdf") && !format.equalsIgnoreCase("word") && !format.equalsIgnoreCase("docx")) {
                log.warn("不支持的下载格式: {}, 默认使用PDF", format);
                format = "pdf";
            }

            // 获取试卷信息
            Paper paper = paperService.getPaperById(id);
            if (paper == null) {
                log.error("试卷不存在, id: {}", id);
                return ResponseEntity.notFound().build();
            }

            // 记录所选择的格式
            paper.setFileFormat(format.toLowerCase());
            paperService.savePaper(paper);

            Resource resource;
            String contentType;
            String filename;

            if (format.equalsIgnoreCase("word") || format.equalsIgnoreCase("docx")) {
                // 生成Word文档
                resource = paperService.generateWordResource(id, paperType);
                contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
                String safeTitle = sanitizeFilename(paper.getTitle());
                filename = (safeTitle != null && !safeTitle.isEmpty())
                    ? safeTitle + ".docx"
                    : "paper_" + id + ".docx";
            } else {
                // 生成PDF文档
                resource = paperService.generatePaperResource(id, paperType);
                contentType = MediaType.APPLICATION_PDF_VALUE;
                String safeTitle = sanitizeFilename(paper.getTitle());
                filename = (safeTitle != null && !safeTitle.isEmpty())
                    ? safeTitle + ".pdf"
                    : "paper_" + id + ".pdf";
            }

            if (resource == null) {
                log.error("Failed to generate {} document for paper id: {}. Resource is null.", format, id);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
            }

            // 记录下载记录
            recordPaperDownload(id, format.toLowerCase(), request);

            // 更新试卷下载计数和最后下载时间
            updatePaperDownloadStats(paper);

            //  修复文件名编码和响应头
            String encodedFilename = java.net.URLEncoder.encode(filename, "UTF-8").replaceAll("\\+", "%20");

            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_TYPE, contentType);
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"; filename*=UTF-8''" + encodedFilename);

            // 针对下载代理工具的优化
            if (isDownloadManager) {
                headers.add(HttpHeaders.CACHE_CONTROL, "max-age=3600");
                headers.add("Accept-Ranges", "bytes");
            } else {
                headers.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
                headers.add(HttpHeaders.PRAGMA, "no-cache");
                headers.add(HttpHeaders.EXPIRES, "0");
            }

            // 设置文件大小
            try {
                long contentLength = resource.contentLength();
                if (contentLength > 0) {
                    headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(contentLength));
                }
            } catch (Exception e) {
                log.warn("无法获取文件大小: {}", e.getMessage());
            }

            log.info("成功生成{}文件 - paperId: {}, filename: {}, contentLength: {}",
                    format, id, filename, resource.contentLength());

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);

        } catch (Exception e) {
            log.error("试卷下载失败 - paperId: {}, format: {}: ", id, format, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    /**
     * 试卷下载接口 (POST方法支持) - 解决word下载内容为空的问题
     */
    @PostMapping("/api/papers/download/{id}")
    @ResponseBody
    public ResponseEntity<?> downloadPaperPost(
            @PathVariable Long id,
            @RequestBody Map<String, String> requestBody,
            HttpServletRequest request) {
        
        try {
            String format = requestBody.getOrDefault("format", "pdf");
            String paperType = requestBody.getOrDefault("paperType", "regular");
            
            log.info("POST下载请求 - paperId: {}, format: {}, paperType: {}", id, format, paperType);

            // 获取试卷信息
            Paper paper = paperService.getPaperById(id);
            if (paper == null) {
                log.error("试卷不存在, id: {}", id);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ApiResponse<>(false, "试卷不存在", null));
            }

            // 检查格式参数
            if (!format.equalsIgnoreCase("pdf") && !format.equalsIgnoreCase("word") && !format.equalsIgnoreCase("docx")) {
                log.warn("不支持的下载格式: {}, 默认使用PDF", format);
                format = "pdf";
            }

            // 记录所选择的格式
            paper.setFileFormat(format.toLowerCase());
            paperService.savePaper(paper);

            Resource resource;
            String contentType;
            String filename;

            if (format.equalsIgnoreCase("word") || format.equalsIgnoreCase("docx")) {
                // 生成Word文档 - 修复内容为空的问题
                resource = paperService.generateWordResource(id, paperType);
                contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
                String safeTitle = sanitizeFilename(paper.getTitle());
                filename = (safeTitle != null && !safeTitle.isEmpty())
                    ? safeTitle + ".docx"
                    : "paper_" + id + ".docx";
            } else {
                // 生成PDF文档
                resource = paperService.generatePaperResource(id, paperType);
                contentType = MediaType.APPLICATION_PDF_VALUE;
                String safeTitle = sanitizeFilename(paper.getTitle());
                filename = (safeTitle != null && !safeTitle.isEmpty())
                    ? safeTitle + ".pdf"
                    : "paper_" + id + ".pdf";
            }

            if (resource == null) {
                log.error("Failed to generate {} document for paper id: {}. Resource is null.", format, id);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "文件生成失败", null));
            }

            // 验证资源内容不为空
            try {
                long contentLength = resource.contentLength();
                if (contentLength <= 0) {
                    log.error("Generated {} document is empty for paper id: {}", format, id);
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new ApiResponse<>(false, "生成的文件内容为空", null));
                }
                log.info("Generated {} document size: {} bytes", format, contentLength);
            } catch (Exception e) {
                log.warn("无法获取文件大小: {}", e.getMessage());
            }

            // 记录下载记录
            recordPaperDownload(id, format.toLowerCase(), request);

            // 更新试卷下载计数和最后下载时间
            updatePaperDownloadStats(paper);

            // 生成临时下载URL或直接返回文件流
            // 这里返回下载信息，前端通过GET请求下载
            Map<String, Object> downloadInfo = new HashMap<>();
            downloadInfo.put("downloadUrl", "/api/papers/download/" + id + "?format=" + format + "&paperType=" + paperType);
            downloadInfo.put("filename", filename);
            downloadInfo.put("contentType", contentType);
            downloadInfo.put("paperId", id);

            return ResponseEntity.ok(new ApiResponse<>(true, "文件准备完成", downloadInfo));

        } catch (Exception e) {
            log.error("POST试卷下载失败 - paperId: {}: ", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(false, "下载失败: " + e.getMessage(), null));
        }
    }

    /**
     * 批量删除试卷接口
     */
    @DeleteMapping("/api/papers/batch-delete")
    @ResponseBody
    public ResponseEntity<ApiResponse<Map<String, Object>>> batchDeletePapers(
            @RequestBody Map<String, List<Long>> requestBody) {
        try {
            List<Long> paperIds = requestBody.get("paperIds");
            if (paperIds == null || paperIds.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "请提供要删除的试卷ID列表", null));
            }

            log.info("批量删除试卷请求 - paperIds: {}", paperIds);

            int successCount = 0;
            int failCount = 0;
            List<String> errors = new ArrayList<>();

            for (Long paperId : paperIds) {
                try {
                    boolean deleted = paperService.deletePaper(paperId);
                    if (deleted) {
                        successCount++;
                        log.info("成功删除试卷 ID: {}", paperId);
                    } else {
                        failCount++;
                        errors.add("试卷 " + paperId + " 不存在或已被删除");
                        log.warn("试卷删除失败 - paperId: {} (不存在)", paperId);
                    }
                } catch (Exception e) {
                    failCount++;
                    errors.add("试卷 " + paperId + " 删除时发生错误: " + e.getMessage());
                    log.error("试卷删除异常 - paperId: {}: ", paperId, e);
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", paperIds.size());
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("errors", errors);

            String message = String.format("批量删除完成：成功 %d 个，失败 %d 个", successCount, failCount);
            boolean overallSuccess = failCount == 0;

            return ResponseEntity.ok(new ApiResponse<>(overallSuccess, message, result));

        } catch (Exception e) {
            log.error("批量删除试卷失败: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(false, "批量删除失败: " + e.getMessage(), null));
        }
    }

    /**
     * 批量下载试卷接口
     */
    @PostMapping("/api/papers/batch-download")
    @ResponseBody
    public ResponseEntity<?> batchDownloadPapers(
            @RequestBody Map<String, Object> requestBody) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> paperIds = (List<Long>) requestBody.get("paperIds");
            String format = (String) requestBody.getOrDefault("format", "pdf");
            String version = (String) requestBody.getOrDefault("version", "regular");

            if (paperIds == null || paperIds.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "请提供要下载的试卷ID列表", null));
            }

            log.info("批量下载试卷请求 - paperIds: {}, format: {}, version: {}", paperIds, format, version);

            // 检查格式参数
            if (!format.equalsIgnoreCase("pdf") && !format.equalsIgnoreCase("word") && !format.equalsIgnoreCase("docx")) {
                format = "pdf";
            }

            try {
                // 调用批量下载服务
                Resource zipResource = paperService.generateBatchDownloadResource(paperIds, format, version);
                
                if (zipResource == null) {
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new ApiResponse<>(false, "批量下载文件生成失败", null));
                }

                // 生成下载文件名
                String timestamp = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                String filename = String.format("试卷批量下载_%s_%s.zip", format, timestamp);

                // 直接返回文件流而不是downloadUrl，避免需要额外的GET接口
                HttpHeaders headers = new HttpHeaders();
                headers.add(HttpHeaders.CONTENT_TYPE, "application/zip");
                headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"");
                headers.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
                headers.add(HttpHeaders.PRAGMA, "no-cache");
                headers.add(HttpHeaders.EXPIRES, "0");

                // 设置文件大小
                try {
                    long contentLength = zipResource.contentLength();
                    if (contentLength > 0) {
                        headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(contentLength));
                    }
                } catch (Exception e) {
                    log.warn("无法获取ZIP文件大小: {}", e.getMessage());
                }

                log.info("批量下载ZIP生成完成 - filename: {}, paperCount: {}, size: {} bytes", 
                    filename, paperIds.size(), zipResource.contentLength());

                return ResponseEntity.ok()
                    .headers(headers)
                    .body(zipResource);

            } catch (Exception e) {
                log.error("批量下载处理失败: ", e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, "批量下载处理失败: " + e.getMessage(), null));
            }

        } catch (Exception e) {
            log.error("批量下载请求处理失败: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(false, "批量下载请求处理失败: " + e.getMessage(), null));
        }
    }

    /**
     * 检查客户端是否已断开连接
     */
    private boolean isClientDisconnected(HttpServletRequest request) {
        try {
            // 检查请求是否已被取消或连接是否已断开
            if (request.isAsyncStarted()) {
                return false; // 异步请求正在处理中
            }

            // 尝试获取远程地址，如果连接断开可能会抛出异常
            String remoteAddr = request.getRemoteAddr();
            return remoteAddr == null;
        } catch (Exception e) {
            // 如果获取请求信息时出现异常，可能是连接已断开
            log.debug("检查客户端连接状态时出现异常: {}", e.getMessage());
            return true;
        }
    }

    /**
     * 检查异常是否是客户端断开连接导致的
     */
    private boolean isClientAbortException(Throwable throwable) {
        if (throwable == null) {
            return false;
        }

        // 检查异常类型
        if (throwable instanceof org.apache.catalina.connector.ClientAbortException) {
            return true;
        }

        // 检查异常消息
        String message = throwable.getMessage();
        if (message != null) {
            String lowerMessage = message.toLowerCase();
            if (lowerMessage.contains("connection reset") ||
                lowerMessage.contains("broken pipe") ||
                lowerMessage.contains("connection aborted") ||
                lowerMessage.contains("你的主机中的软件中止了一个已建立的连接") ||
                lowerMessage.contains("远程主机强迫关闭了一个现有的连接")) {
                return true;
            }
        }

        // 检查根本原因
        Throwable cause = throwable.getCause();
        if (cause != null && cause != throwable) {
            return isClientAbortException(cause);
        }

        return false;
    }

    /**
     *  检测是否为下载代理工具的请求
     * 下载代理工具（如迅雷、IDM、FDM等）会发起特殊的请求
     */
    private boolean isDownloadManagerRequest(String userAgent) {
        if (userAgent == null || userAgent.isEmpty()) {
            return false;
        }

        String lowerUserAgent = userAgent.toLowerCase();

        // 常见下载代理工具的User-Agent特征
        return lowerUserAgent.contains("thunder") ||           // 迅雷
               lowerUserAgent.contains("xunlei") ||            // 迅雷
               lowerUserAgent.contains("idm") ||               // Internet Download Manager
               lowerUserAgent.contains("fdm") ||               // Free Download Manager
               lowerUserAgent.contains("eagleget") ||          // EagleGet
               lowerUserAgent.contains("jdownloader") ||       // JDownloader
               lowerUserAgent.contains("aria2") ||             // Aria2
               lowerUserAgent.contains("wget") ||              // wget
               lowerUserAgent.contains("curl") ||              // curl
               lowerUserAgent.contains("download") ||          // 通用下载工具
               lowerUserAgent.contains("downloader") ||        // 下载器
               lowerUserAgent.contains("range") ||             // 支持断点续传的工具
               userAgent.startsWith("Mozilla/4.0 (compatible; MSIE") && // 某些下载工具伪装的IE
               !lowerUserAgent.contains("trident");           // 排除真正的IE
    }

    //  防重复下载缓存（内存缓存，避免短时间内重复记录）
    private static final Map<String, Long> downloadAttemptCache = new ConcurrentHashMap<>();
    private static final long DUPLICATE_THRESHOLD_MS = 10000; // 10秒内的重复请求视为重复

    /**
     *  检查是否为重复下载
     */
    private boolean isDuplicateDownload(String duplicateKey) {
        Long lastAttemptTime = downloadAttemptCache.get(duplicateKey);
        if (lastAttemptTime == null) {
            return false;
        }

        long currentTime = System.currentTimeMillis();
        boolean isDuplicate = (currentTime - lastAttemptTime) < DUPLICATE_THRESHOLD_MS;

        if (isDuplicate) {
            log.debug("检测到重复下载请求 - key: {}, 距离上次: {}ms",
                    duplicateKey, currentTime - lastAttemptTime);
        }

        return isDuplicate;
    }

    /**
     *  记录下载尝试时间
     */
    private void recordDownloadAttempt(String duplicateKey) {
        downloadAttemptCache.put(duplicateKey, System.currentTimeMillis());

        //  清理过期的缓存条目（避免内存泄漏）
        cleanupExpiredCacheEntries();
    }

    /**
     *  清理过期的缓存条目
     */
    private void cleanupExpiredCacheEntries() {
        long currentTime = System.currentTimeMillis();
        downloadAttemptCache.entrySet().removeIf(entry ->
            (currentTime - entry.getValue()) > DUPLICATE_THRESHOLD_MS * 2);
    }

    /**
     *  异步记录下载统计（用于下载代理工具）
     * 延迟记录避免重复计数，因为下载代理工具会发起多次请求
     */
    private void scheduleDownloadRecording(Long paperId, String format, HttpServletRequest request, Paper paper) {
        // 使用异步方式延迟记录，给下载代理工具足够时间完成下载
        new Thread(() -> {
            try {
                // 延迟3秒记录，避免重复计数
                Thread.sleep(3000);

                log.info("延迟记录下载统计 - paperId: {}, format: {}", paperId, format);

                // 记录下载历史
                recordPaperDownload(paperId, format, request);

                // 更新试卷统计（如果paper对象可用）
                if (paper != null) {
                    updatePaperDownloadStats(paper);
                } else {
                    // 重新获取paper对象并更新统计
                    Paper currentPaper = paperService.getPaperById(paperId);
                    if (currentPaper != null) {
                        updatePaperDownloadStats(currentPaper);
                    }
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("下载统计记录被中断 - paperId: {}", paperId);
            } catch (Exception e) {
                log.error("延迟记录下载统计失败 - paperId: {}, error: {}", paperId, e.getMessage());
            }
        }).start();
    }

    /**
     * 记录试卷下载记录（带防重复机制）
     */
    private void recordPaperDownload(Long paperId, String format, HttpServletRequest request) {
        try {
            // 获取当前用户ID
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                log.warn("未能记录下载历史 - 用户未登录，paperId={}, format={}, ip={}",
                    paperId, format, getClientIpAddress(request));
                return;
            }

            //  防重复机制：检查最近是否已有相同的下载记录
            String duplicateKey = String.format("%d_%d_%s", paperId, currentUserId, format.toLowerCase());
            if (isDuplicateDownload(duplicateKey)) {
                log.debug("跳过重复下载记录 - paperId={}, userId={}, format={}", paperId, currentUserId, format);
                return;
            }

            // 创建下载记录
            PaperDownload download = new PaperDownload();
            download.setPaperId(paperId);
            download.setUserId(currentUserId);
            download.setFileFormat(format.toLowerCase());
            download.setDownloadTime(LocalDateTime.now());
            download.setIpAddress(getClientIpAddress(request));

            // 保存下载记录
            paperService.savePaperDownload(download);

            //  记录到防重复缓存
            recordDownloadAttempt(duplicateKey);

            log.info("成功记录试卷下载: paperId={}, userId={}, format={}, ip={}",
                paperId, currentUserId, format, getClientIpAddress(request));
        } catch (Exception e) {
            log.error("记录试卷下载历史失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 更新试卷的下载统计信息
     */
    private void updatePaperDownloadStats(Paper paper) {
        try {
            if (paper != null) {
                paper.setDownloadCount(paper.getDownloadCount() != null ? paper.getDownloadCount() + 1 : 1);
                paper.setLastDownloadTime(LocalDateTime.now());
                paperService.savePaper(paper);
            }
        } catch (Exception e) {
            log.error("更新试卷下载统计失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}