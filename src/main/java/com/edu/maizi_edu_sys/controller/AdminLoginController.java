package com.edu.maizi_edu_sys.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.edu.maizi_edu_sys.entity.User;
import com.edu.maizi_edu_sys.mapper.UserMapper;
import com.edu.maizi_edu_sys.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 管理员登录控制器
 */
@RestController
@RequestMapping("/api/admin")
@RequiredArgsConstructor
public class AdminLoginController {
    
    private final UserMapper userMapper;
    private final JwtUtil jwtUtil;
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    /**
     * 管理员登录
     */
    @PostMapping("/login")
    public ResponseEntity<?> adminLogin(@RequestBody Map<String, String> loginData) {
        try {
            String username = loginData.get("username");
            String password = loginData.get("password");
            String captcha = loginData.get("captcha");
            
            if (username == null || username.trim().isEmpty()) {
                return buildErrorResponse("用户名不能为空");
            }
            
            if (password == null || password.trim().isEmpty()) {
                return buildErrorResponse("密码不能为空");
            }
            
            // 查询用户
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("username", username)
                       .eq("deleted", 0); // 未删除的用户
            
            User user = userMapper.selectOne(queryWrapper);
            
            if (user == null) {
                return buildErrorResponse("用户不存在");
            }
            
            // 检查用户状态
            if (user.getStatus() != 1) {
                return buildErrorResponse("账户已被禁用，请联系管理员");
            }
            
            // 检查用户权限：只有管理员（role=1）才能登录后台
            if (user.getRole() != 1) {
                return buildErrorResponse("权限不足，只有管理员才能登录后台管理系统");
            }
            
            // 验证密码（如果密码是加密的）
            boolean passwordMatch = false;
            if (user.getPassword().startsWith("$2a$") || user.getPassword().startsWith("$2b$")) {
                // BCrypt加密的密码
                passwordMatch = passwordEncoder.matches(password, user.getPassword());
            } else {
                // 明文密码（开发环境）
                passwordMatch = password.equals(user.getPassword());
            }
            
            if (!passwordMatch) {
                return buildErrorResponse("用户名或密码错误");
            }
            
            // 更新最后登录时间和IP（这里简化处理）
            user.setLastLoginTime(LocalDateTime.now());
            user.setLastLoginIp("127.0.0.1"); // 实际项目中应该获取真实IP
            userMapper.updateById(user);
            
            // 登录成功，生成JWT token
            String jwtToken = jwtUtil.generateToken(user);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "登录成功");
            response.put("token", jwtToken);
            
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("username", user.getUsername());
            userInfo.put("role", user.getRole());
            userInfo.put("roleName", getRoleName(user.getRole()));
            userInfo.put("email", user.getEmail());
            userInfo.put("avatar", generateRandomAvatar(user));
            userInfo.put("bio", user.getBio());
            response.put("user", userInfo);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            return buildErrorResponse("登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理GET方法访问登录API的情况
     * 提供友好的提示信息和登录页面重定向
     */
    @GetMapping("/login")
    public ResponseEntity<?> adminLoginInfo() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "管理员登录需要使用POST方法提交用户名和密码");
        response.put("method", "POST");
        response.put("endpoint", "/api/admin/login");
        response.put("loginPage", "/admin/templates/admin-login.html");
        response.put("requiredFields", new String[]{"username", "password"});
        
        Map<String, String> exampleRequest = new HashMap<>();
        exampleRequest.put("username", "admin");
        exampleRequest.put("password", "your_password");
        response.put("exampleRequest", exampleRequest);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 生成验证码
     */
    @GetMapping("/captcha")
    public ResponseEntity<?> generateCaptcha() {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("captcha", "1234"); // 简化的验证码，实际项目中应该生成图片验证码
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("生成验证码失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前管理员信息
     */
    @GetMapping("/info")
    public ResponseEntity<?> getAdminInfo(@RequestHeader(value = "Authorization", required = false) String token) {
        try {
            if (token == null || token.trim().isEmpty()) {
                return ResponseEntity.badRequest().body("未提供认证token");
            }
            
            // 移除Bearer前缀
            if (token.toLowerCase().startsWith("bearer ")) {
                token = token.substring(7);
            }
            
            // 验证JWT token
            if (!jwtUtil.validateToken(token)) {
                return ResponseEntity.badRequest().body("token无效或已过期");
            }
            
            // 从token中获取用户名
            String username = jwtUtil.getUsernameFromToken(token);
            
            // 查询用户信息
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("username", username).eq("deleted", 0);
            User user = userMapper.selectOne(queryWrapper);
            
            if (user == null || user.getRole() != 1) {
                return ResponseEntity.badRequest().body("用户不存在或权限不足");
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("id", user.getId());
            response.put("username", user.getUsername());
            response.put("role", user.getRole());
            response.put("roleName", getRoleName(user.getRole()));
            response.put("email", user.getEmail());
            response.put("avatar", generateRandomAvatar(user));
            response.put("bio", user.getBio());
            response.put("lastLoginTime", user.getLastLoginTime());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("获取管理员信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 管理员退出登录
     */
    @PostMapping("/logout")
    public ResponseEntity<?> adminLogout() {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "退出登录成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "退出登录失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取角色名称
     */
    private String getRoleName(Integer role) {
        switch (role) {
            case 1: return "管理员";
            case 2: return "普通用户";
            case 3: return "教师";
            default: return "未知角色";
        }
    }
    
    /**
     * 构建错误响应
     */
    private ResponseEntity<?> buildErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        return ResponseEntity.badRequest().body(response);
    }
    
    /**
     * 生成随机头像URL
     */
    private String generateRandomAvatar(User user) {
        if (user == null) {
            return null; // 不返回默认头像，让前端处理
        }
        
        // 只返回用户实际上传的头像
        if (user.getAvatar() != null && !user.getAvatar().trim().isEmpty()) {
            // 确保头像路径格式正确
            String avatar = user.getAvatar();
            if (avatar.startsWith("avatars/")) {
                return "/uploads/" + avatar;
            } else if (avatar.startsWith("main/avatars/")) {
                return "/uploads/avatars/" + avatar.replace("main/avatars/", "");
            } else if (!avatar.startsWith("http") && !avatar.startsWith("/")) {
                return "/uploads/avatars/" + avatar;
            }
            return avatar;
        }
        
        // 没有头像时返回null，让前端根据用户名生成首字母头像
        return null;
    }
    
    /**
     * 备用头像生成方案
     */
    private String generateUIAvatar(User user) {
        if (user == null || user.getUsername() == null) {
            return "/static/images/default-avatar.png";
        }
        
        try {
            String username = user.getUsername();
            String[] colors = {"3498db", "9b59b6", "e74c3c", "2ecc71", "f39c12", "1abc9c", "34495e", "e67e22"};
            int colorIndex = Math.abs(username.hashCode()) % colors.length;
            String bgColor = colors[colorIndex];
            
            String initials = username.length() >= 2 ? 
                             username.substring(0, 2).toUpperCase() : 
                             username.substring(0, 1).toUpperCase();
            
            return String.format("https://ui-avatars.com/api/?name=%s&background=%s&color=fff&size=128", 
                               java.net.URLEncoder.encode(initials, "UTF-8"), 
                               bgColor);
        } catch (Exception e) {
            return "/static/images/default-avatar.png";
        }
    }

    /**
     * 获取当前管理员信息
     */
    @GetMapping("/current")
    public ResponseEntity<?> getCurrentAdmin() {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "当前管理员信息");
            response.put("user", new HashMap<String, Object>() {{
                put("id", 1);
                put("username", "admin");
                put("role", "admin");
            }});
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return buildErrorResponse("获取管理员信息失败: " + e.getMessage());
        }
    }

    /**
     * 检查管理员权限
     */
    @GetMapping("/auth/check")
    public ResponseEntity<?> checkAdminAuth(@RequestHeader(value = "Authorization", required = false) String authHeader) {
        try {
            // 从Authorization header获取token
            String token = null;
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                token = authHeader.substring(7);
            }
            
            if (token == null || token.trim().isEmpty()) {
                return buildErrorResponse("未提供有效的认证token");
            }
            
            // 验证token有效性
            if (!jwtUtil.validateToken(token)) {
                return buildErrorResponse("token无效或已过期");
            }
            
            // 从token中获取用户名
            String username = jwtUtil.getUsernameFromToken(token);
            if (username == null) {
                return buildErrorResponse("无法从token中获取用户信息");
            }
            
            // 查询用户信息并验证管理员权限
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("username", username)
                       .eq("deleted", 0)
                       .eq("status", 1); // 只查询活跃用户
            
            User user = userMapper.selectOne(queryWrapper);
            
            if (user == null) {
                return buildErrorResponse("用户不存在或已被禁用");
            }
            
            // 检查是否为管理员
            if (user.getRole() != 1) {
                return buildErrorResponse("权限不足，需要管理员权限");
            }
            
            // 权限验证通过
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "管理员权限验证通过");
            
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("username", user.getUsername());
            userInfo.put("role", user.getRole());
            response.put("user", userInfo);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            return buildErrorResponse("权限验证失败: " + e.getMessage());
        }
    }
} 