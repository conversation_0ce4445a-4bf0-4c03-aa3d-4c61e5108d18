package com.edu.maizi_edu_sys.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.maizi_edu_sys.dto.TopicDTO;
import com.edu.maizi_edu_sys.entity.KnowledgePoint;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.service.AuthService;
import com.edu.maizi_edu_sys.service.KnowledgePointService;
import com.edu.maizi_edu_sys.service.TopicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

/**
 * 管理员题目管理控制器
 */
@RestController
@RequestMapping("/api/admin/topics")
@Slf4j
public class AdminTopicController {

    @Autowired
    private TopicService topicService;

    @Autowired
    private AuthService authService;

    @Autowired
    private KnowledgePointService knowledgePointService;

    /**
     * 获取题目统计信息
     */
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getTopicStats() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            // 获取各类型题目统计
            Map<String, Object> stats = new HashMap<>();
            
            // 获取总数统计
            QueryWrapper<Topic> queryWrapper = new QueryWrapper<>();
            long totalCount = topicService.count(queryWrapper);
            stats.put("totalTopics", totalCount);
            
            // 按类型统计
            QueryWrapper<Topic> choiceQuery = new QueryWrapper<>();
            choiceQuery.eq("type", "choice");
            stats.put("choiceCount", topicService.count(choiceQuery));
            
            QueryWrapper<Topic> multipleQuery = new QueryWrapper<>();
            multipleQuery.eq("type", "multiple");
            stats.put("multipleCount", topicService.count(multipleQuery));
            
            QueryWrapper<Topic> judgeQuery = new QueryWrapper<>();
            judgeQuery.eq("type", "judge");
            stats.put("judgeCount", topicService.count(judgeQuery));
            
            QueryWrapper<Topic> fillQuery = new QueryWrapper<>();
            fillQuery.eq("type", "fill");
            stats.put("fillCount", topicService.count(fillQuery));
            
            QueryWrapper<Topic> shortQuery = new QueryWrapper<>();
            shortQuery.eq("type", "short");
            stats.put("shortCount", topicService.count(shortQuery));
            
            return ApiResponse.success(stats);
        } catch (Exception e) {
            log.error("获取题目统计失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取题目统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取题目列表（分页）
     */
    @GetMapping
    public ApiResponse<Map<String, Object>> getTopicList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String difficulty,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String knowledgeId,
            @RequestParam(required = false) String ids) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            Page<Topic> pageInfo = new Page<>(page, size);
            QueryWrapper<Topic> queryWrapper = new QueryWrapper<>();
            
            // ID搜索（支持多个ID，逗号分割）
            if (StringUtils.hasText(ids)) {
                String[] idArray = ids.split(",");
                List<Long> topicIds = new ArrayList<>();
                for (String id : idArray) {
                    try {
                        topicIds.add(Long.parseLong(id.trim()));
                    } catch (NumberFormatException e) {
                        // 忽略无效的ID
                        log.warn("Invalid topic ID: {}", id);
                    }
                }
                if (!topicIds.isEmpty()) {
                    queryWrapper.in("id", topicIds);
                }
            } else {
                // 只有在不按ID搜索时才应用其他条件
                
                // 知识点筛选
                if (StringUtils.hasText(knowledgeId)) {
                    try {
                        Long knowId = Long.parseLong(knowledgeId);
                        queryWrapper.eq("know_id", knowId);
                    } catch (NumberFormatException e) {
                        log.warn("Invalid knowledgeId: {}", knowledgeId);
                    }
                }
                
                // 标题搜索
                if (StringUtils.hasText(search)) {
                    queryWrapper.like("title", search);
                }
                
                // 类型筛选
                if (StringUtils.hasText(type)) {
                    queryWrapper.eq("type", type);
                }
                
                // 难度筛选
                if (StringUtils.hasText(difficulty)) {
                    try {
                        Integer difficultyInt = Integer.parseInt(difficulty);
                        queryWrapper.eq("difficulty", difficultyInt);
                    } catch (NumberFormatException e) {
                        log.warn("Invalid difficulty: {}", difficulty);
                    }
                }
                
                // 状态筛选
                if (StringUtils.hasText(status)) {
                    try {
                        Integer statusInt = Integer.parseInt(status);
                        queryWrapper.eq("status", statusInt);
                    } catch (NumberFormatException e) {
                        log.warn("Invalid status: {}", status);
                    }
                }
            }
            
            queryWrapper.orderByDesc("created_at");
            
            IPage<Topic> result = topicService.page(pageInfo, queryWrapper);
            
            // 获取所有知识点信息，用于名称映射
            Map<Integer, KnowledgePoint> knowledgeMap = new HashMap<>();
            if (!result.getRecords().isEmpty()) {
                List<Integer> knowledgeIds = result.getRecords().stream()
                    .filter(topic -> topic.getKnowId() != null)
                    .map(Topic::getKnowId)
                    .distinct()
                    .collect(Collectors.toList());
                
                if (!knowledgeIds.isEmpty()) {
                    QueryWrapper<KnowledgePoint> kpQuery = new QueryWrapper<>();
                    kpQuery.in("knowledge_id", knowledgeIds)
                           .eq("is_deleted", false);
                    
                    List<KnowledgePoint> knowledgePoints = knowledgePointService.list(kpQuery);
                    knowledgeMap = knowledgePoints.stream()
                        .collect(Collectors.toMap(KnowledgePoint::getKnowledgeId, kp -> kp, (k1, k2) -> k1));
                }
            }
            
            // 转换为DTO
            final Map<Integer, KnowledgePoint> finalKnowledgeMap = knowledgeMap;
            List<Map<String, Object>> topicList = result.getRecords().stream().map(topic -> {
                Map<String, Object> topicMap = new HashMap<>();
                topicMap.put("id", topic.getId());
                topicMap.put("title", topic.getTitle());
                topicMap.put("type", topic.getType());
                topicMap.put("difficulty", topic.getDifficulty());
                topicMap.put("status", 1); // 默认状态为正常
                topicMap.put("knowId", topic.getKnowId());
                
                // 获取真实的知识点名称
                String knowledgePointName = "未分类";
                if (topic.getKnowId() != null) {
                    KnowledgePoint kp = finalKnowledgeMap.get(topic.getKnowId());
                    if (kp != null) {
                        knowledgePointName = kp.getGroupName() + " - " + kp.getName();
                    } else {
                        knowledgePointName = "知识点" + topic.getKnowId();
                    }
                }
                topicMap.put("knowledgePointName", knowledgePointName);
                
                topicMap.put("score", topic.getScore() != null ? topic.getScore() : 3);
                topicMap.put("tags", topic.getTags() != null ? topic.getTags() : "");
                topicMap.put("createdAt", topic.getCreatedAt());
                return topicMap;
            }).collect(Collectors.toList());
            
            Map<String, Object> response = new HashMap<>();
            response.put("data", topicList);  // 移除嵌套的success字段
            response.put("total", result.getTotal());
            response.put("pages", result.getPages());
            response.put("current", result.getCurrent());
            response.put("size", result.getSize());
            
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取题目列表失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取题目列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取单个题目详情
     */
    @GetMapping("/{id}")
    public ApiResponse<TopicDTO> getTopicById(@PathVariable Long id) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            Topic topic = topicService.getById(id);
            if (topic == null) {
                return ApiResponse.error("题目不存在");
            }

            // 转换为DTO
            TopicDTO topicDTO = convertToDTO(topic);
            
            return ApiResponse.success(topicDTO);
        } catch (Exception e) {
            log.error("获取题目详情失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取题目详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建新题目
     */
    @PostMapping
    public ApiResponse<TopicDTO> createTopic(@RequestBody Map<String, Object> topicData) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            // 验证必填字段
            if (!topicData.containsKey("title") || topicData.get("title") == null || 
                topicData.get("title").toString().trim().isEmpty()) {
                return ApiResponse.error("题目标题不能为空");
            }
            
            if (!topicData.containsKey("type") || topicData.get("type") == null) {
                return ApiResponse.error("题目类型不能为空");
            }
            
            if (!topicData.containsKey("knowId") || topicData.get("knowId") == null) {
                return ApiResponse.error("知识点不能为空");
            }
            
            if (!topicData.containsKey("answer") || topicData.get("answer") == null || 
                topicData.get("answer").toString().trim().isEmpty()) {
                return ApiResponse.error("正确答案不能为空");
            }

            // 创建Topic对象
            Topic topic = new Topic();
            topic.setTitle(topicData.get("title").toString().trim());
            topic.setType(topicData.get("type").toString());
            topic.setKnowId(Integer.parseInt(topicData.get("knowId").toString()));
            topic.setAnswer(topicData.get("answer").toString().trim());
            
            // 设置可选字段
            if (topicData.containsKey("options") && topicData.get("options") != null) {
                Object optionsObj = topicData.get("options");
                if (optionsObj instanceof String) {
                    // 如果是字符串，直接设置
                    topic.setOptions((String) optionsObj);
                } else {
                    // 如果是其他类型（如List），转换为JSON字符串
                    try {
                        ObjectMapper mapper = new ObjectMapper();
                        topic.setOptions(mapper.writeValueAsString(optionsObj));
                    } catch (Exception e) {
                        log.warn("Failed to convert options to JSON for topic creation: {}", e.getMessage());
                        topic.setOptions("[]");
                    }
                }
            }
            
            if (topicData.containsKey("parse") && topicData.get("parse") != null) {
                topic.setParse(topicData.get("parse").toString());
            }
            
            if (topicData.containsKey("score") && topicData.get("score") != null) {
                topic.setScore(Integer.parseInt(topicData.get("score").toString()));
            } else {
                topic.setScore(3); // 默认分值
            }
            
            if (topicData.containsKey("difficulty") && topicData.get("difficulty") != null) {
                topic.setDifficulty(Double.parseDouble(topicData.get("difficulty").toString()));
            } else {
                topic.setDifficulty(2.0); // 默认中等难度
            }
            
            if (topicData.containsKey("tags") && topicData.get("tags") != null) {
                topic.setTags(topicData.get("tags").toString());
            }
            
            if (topicData.containsKey("source") && topicData.get("source") != null) {
                topic.setSource(topicData.get("source").toString());
            }
            
            topic.setSort(1); // 默认排序
            topic.setCorrected(0); // 默认未校对

            // 保存题目
            boolean success = topicService.save(topic);
            
            if (success) {
                TopicDTO topicDTO = convertToDTO(topic);
                log.info("创建题目成功, id: {}, title: {}", topic.getId(), topic.getTitle());
                return ApiResponse.success(topicDTO);
            } else {
                return ApiResponse.error("题目创建失败");
            }
        } catch (NumberFormatException e) {
            log.error("题目数据格式错误: {}", e.getMessage(), e);
            return ApiResponse.error("题目数据格式错误: " + e.getMessage());
        } catch (Exception e) {
            log.error("创建题目失败: {}", e.getMessage(), e);
            return ApiResponse.error("创建题目失败: " + e.getMessage());
        }
    }

    /**
     * 更新题目
     */
    @PutMapping("/{id}")
    public ApiResponse<TopicDTO> updateTopic(@PathVariable Long id, @RequestBody Map<String, Object> topicData) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            // 查找现有题目
            Topic existingTopic = topicService.getById(id);
            if (existingTopic == null) {
                return ApiResponse.error("题目不存在");
            }

            // 验证必填字段
            if (topicData.containsKey("title") && 
                (topicData.get("title") == null || topicData.get("title").toString().trim().isEmpty())) {
                return ApiResponse.error("题目标题不能为空");
            }
            
            if (topicData.containsKey("answer") && 
                (topicData.get("answer") == null || topicData.get("answer").toString().trim().isEmpty())) {
                return ApiResponse.error("正确答案不能为空");
            }

            // 更新字段
            if (topicData.containsKey("title") && topicData.get("title") != null) {
                existingTopic.setTitle(topicData.get("title").toString().trim());
            }
            
            if (topicData.containsKey("type") && topicData.get("type") != null) {
                existingTopic.setType(topicData.get("type").toString());
            }
            
            if (topicData.containsKey("knowId") && topicData.get("knowId") != null) {
                existingTopic.setKnowId(Integer.parseInt(topicData.get("knowId").toString()));
            }
            
            if (topicData.containsKey("answer") && topicData.get("answer") != null) {
                existingTopic.setAnswer(topicData.get("answer").toString().trim());
            }
            
            if (topicData.containsKey("options")) {
                Object optionsObj = topicData.get("options");
                if (optionsObj == null) {
                    existingTopic.setOptions(null);
                } else if (optionsObj instanceof String) {
                    // 如果是字符串，直接设置
                    existingTopic.setOptions((String) optionsObj);
                } else {
                    // 如果是其他类型（如List），转换为JSON字符串
                    try {
                        ObjectMapper mapper = new ObjectMapper();
                        existingTopic.setOptions(mapper.writeValueAsString(optionsObj));
                    } catch (Exception e) {
                        log.warn("Failed to convert options to JSON for topic {}: {}", id, e.getMessage());
                        existingTopic.setOptions("[]");
                    }
                }
            }
            
            if (topicData.containsKey("parse")) {
                existingTopic.setParse(topicData.get("parse") != null ? topicData.get("parse").toString() : null);
            }
            
            if (topicData.containsKey("score") && topicData.get("score") != null) {
                existingTopic.setScore(Integer.parseInt(topicData.get("score").toString()));
            }
            
            if (topicData.containsKey("difficulty") && topicData.get("difficulty") != null) {
                existingTopic.setDifficulty(Double.parseDouble(topicData.get("difficulty").toString()));
            }
            
            if (topicData.containsKey("tags")) {
                existingTopic.setTags(topicData.get("tags") != null ? topicData.get("tags").toString() : null);
            }
            
            if (topicData.containsKey("source")) {
                existingTopic.setSource(topicData.get("source") != null ? topicData.get("source").toString() : null);
            }

            // 更新题目
            boolean success = topicService.updateById(existingTopic);
            
            if (success) {
                TopicDTO topicDTO = convertToDTO(existingTopic);
                log.info("更新题目成功, id: {}, title: {}", existingTopic.getId(), existingTopic.getTitle());
                return ApiResponse.success(topicDTO);
            } else {
                return ApiResponse.error("题目更新失败");
            }
        } catch (NumberFormatException e) {
            log.error("题目数据格式错误: {}", e.getMessage(), e);
            return ApiResponse.error("题目数据格式错误: " + e.getMessage());
        } catch (Exception e) {
            log.error("更新题目失败: {}", e.getMessage(), e);
            return ApiResponse.error("更新题目失败: " + e.getMessage());
        }
    }

    /**
     * 删除题目
     */
    @DeleteMapping("/{id}")
    public ApiResponse<?> deleteTopic(@PathVariable Long id) {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            Topic topic = topicService.getById(id);
            if (topic == null) {
                return ApiResponse.error("题目不存在");
            }

            // 由于Topic实体没有deleted字段，直接物理删除
            boolean success = topicService.removeById(id);
            
            if (success) {
                log.info("删除题目成功, id: {}, title: {}", id, topic.getTitle());
                return ApiResponse.success("题目删除成功");
            } else {
                return ApiResponse.error("题目删除失败");
            }
        } catch (Exception e) {
            log.error("删除题目失败: {}", e.getMessage(), e);
            return ApiResponse.error("删除题目失败: " + e.getMessage());
        }
    }

    /**
     * 导出题目
     */
    @PostMapping("/export")
    public ApiResponse<Map<String, Object>> exportTopics() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            // 这里可以实现题目导出逻辑
            Map<String, Object> result = new HashMap<>();
            result.put("url", "/api/admin/topics/download/export.xlsx");
            result.put("filename", "题目导出_" + System.currentTimeMillis() + ".xlsx");
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("导出题目失败: {}", e.getMessage(), e);
            return ApiResponse.error("导出题目失败: " + e.getMessage());
        }
    }

    /**
     * 下载模板
     */
    @GetMapping("/template")
    public ApiResponse<String> downloadTemplate() {
        try {
            // 这里可以实现模板下载逻辑
            return ApiResponse.success("模板下载功能暂未实现");
        } catch (Exception e) {
            log.error("下载模板失败: {}", e.getMessage(), e);
            return ApiResponse.error("下载模板失败: " + e.getMessage());
        }
    }

    /**
     * 获取知识点分组列表（二级分类）
     */
    @GetMapping("/knowledge-groups")
    public ApiResponse<List<Map<String, Object>>> getKnowledgeGroups() {
        try {
            Long currentUserId = authService.getCurrentUserId();
            if (currentUserId == null) {
                return ApiResponse.error("用户未登录");
            }

            // 查询所有知识点，按group_name分组
            QueryWrapper<KnowledgePoint> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_deleted", false)
                       .orderByAsc("group_name")
                       .orderByAsc("sort")
                       .orderByAsc("knowledge_id");
            
            List<KnowledgePoint> allKnowledgePoints = knowledgePointService.list(queryWrapper);
            
            // 按group_name分组
            Map<String, List<KnowledgePoint>> groupedByGroupName = allKnowledgePoints.stream()
                .collect(Collectors.groupingBy(KnowledgePoint::getGroupName));
            
            // 构建分组结果
            List<Map<String, Object>> groups = new ArrayList<>();
            
            for (Map.Entry<String, List<KnowledgePoint>> entry : groupedByGroupName.entrySet()) {
                String groupName = entry.getKey();
                List<KnowledgePoint> knowledgePoints = entry.getValue();
                
                Map<String, Object> group = new HashMap<>();
                group.put("groupName", groupName);
                
                // 构建该分组下的知识点列表
                List<Map<String, Object>> knowledgeList = knowledgePoints.stream().map(kp -> {
                    Map<String, Object> knowledge = new HashMap<>();
                    knowledge.put("knowledgeId", kp.getKnowledgeId());
                    knowledge.put("knowledgeName", kp.getName());
                    
                    // 查询该知识点关联的题目数量
                    QueryWrapper<Topic> topicQuery = new QueryWrapper<>();
                    topicQuery.eq("know_id", kp.getKnowledgeId());
                    long topicCount = topicService.count(topicQuery);
                    knowledge.put("topicCount", topicCount);
                    
                    return knowledge;
                }).collect(Collectors.toList());
                
                group.put("knowledgePoints", knowledgeList);
                
                // 计算该分组下所有知识点的题目总数
                long totalTopicCount = knowledgeList.stream()
                    .mapToLong(k -> (Long) k.get("topicCount"))
                    .sum();
                group.put("totalTopicCount", totalTopicCount);
                
                groups.add(group);
            }
            
            return ApiResponse.success(groups);
        } catch (Exception e) {
            log.error("获取知识点分组失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取知识点分组失败: " + e.getMessage());
        }
    }

    /**
     * 转换为DTO
     */
    private TopicDTO convertToDTO(Topic topic) {
        TopicDTO dto = new TopicDTO();
        dto.setId(topic.getId());
        dto.setTitle(topic.getTitle());
        dto.setType(topic.getType());
        dto.setDifficulty(topic.getDifficulty());
        dto.setKnowId(topic.getKnowId());
        dto.setAnswer(topic.getAnswer());
        dto.setParse(topic.getParse());
        dto.setScore(topic.getScore());
        dto.setTags(topic.getTags());
        dto.setSource(topic.getSource());
        dto.setSort(topic.getSort());
        dto.setSubs(topic.getSubs());
        
        // 处理选项信息 - 将JSON字符串转换为OptionDTO列表
        if (topic.getOptions() != null && !topic.getOptions().trim().isEmpty()) {
            try {
                // 尝试解析JSON字符串为OptionDTO列表
                ObjectMapper mapper = new ObjectMapper();
                TypeReference<List<Map<String, String>>> typeRef = 
                    new TypeReference<List<Map<String, String>>>() {};
                
                List<Map<String, String>> optionMaps = mapper.readValue(topic.getOptions(), typeRef);
                List<TopicDTO.OptionDTO> optionDTOs = optionMaps.stream().map(map -> {
                    TopicDTO.OptionDTO optionDTO = new TopicDTO.OptionDTO();
                    optionDTO.setKey(map.get("key"));
                    optionDTO.setName(map.get("name"));
                    return optionDTO;
                }).collect(Collectors.toList());
                
                dto.setOptions(optionDTOs);
            } catch (Exception e) {
                log.warn("Failed to parse options JSON for topic {}: {}. Using empty options list.", topic.getId(), e.getMessage());
                dto.setOptions(new ArrayList<>());
            }
        } else {
            dto.setOptions(new ArrayList<>());
        }
        
        return dto;
    }
} 