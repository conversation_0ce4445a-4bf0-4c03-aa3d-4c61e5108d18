package com.edu.maizi_edu_sys.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;

/**
 * URL映射控制器
 * 用于展示和测试系统中的所有URL映射
 */
@Controller
@Slf4j
public class UrlMappingController {

    @Value("${server.port:8081}")
    private String serverPort;

    /**
     * 展示所有URL映射信息
     */
    @GetMapping("/url-mapping")
    @ResponseBody
    public Map<String, Object> showUrlMappings() {
        Map<String, Object> mappings = new HashMap<>();
        
        // 基础URL
        String baseUrl = "http://localhost:" + serverPort;
        
        // 页面访问URL
        Map<String, String> pageUrls = new HashMap<>();
        pageUrls.put("系统首页", baseUrl + "/");
        pageUrls.put("用户登录", baseUrl + "/auth/login");
        pageUrls.put("用户注册", baseUrl + "/auth/register");
        pageUrls.put("管理员登录", baseUrl + "/admin/login");
        pageUrls.put("管理员仪表板", baseUrl + "/admin/dashboard");
        pageUrls.put("用户管理", baseUrl + "/admin/users");
        pageUrls.put("题目管理", baseUrl + "/admin/topics");
        pageUrls.put("审核管理", baseUrl + "/admin/audit");
        pageUrls.put("题目库", baseUrl + "/topics/bank");
        pageUrls.put("试卷生成", baseUrl + "/paper/generate");
        pageUrls.put("聊天页面", baseUrl + "/main/chat");
        pageUrls.put("教材资源", baseUrl + "/main/books");
        
        // API接口URL
        Map<String, String> apiUrls = new HashMap<>();
        apiUrls.put("管理员登录API", baseUrl + "/api/admin/login");
        apiUrls.put("管理员仪表板API", baseUrl + "/api/admin/dashboard/stats");
        apiUrls.put("用户管理API", baseUrl + "/api/admin/users");
        apiUrls.put("审核管理API", baseUrl + "/api/admin/audit/stats");
        apiUrls.put("用户统计API", baseUrl + "/api/user/stats");
        apiUrls.put("题目相关API", baseUrl + "/api/topics");
        apiUrls.put("试卷配置API", baseUrl + "/api/paper-configs");
        apiUrls.put("聊天API", baseUrl + "/api/chat");
        apiUrls.put("知识点API", baseUrl + "/api/knowledge");
        
        // 静态资源URL
        Map<String, String> staticUrls = new HashMap<>();
        staticUrls.put("CSS资源", baseUrl + "/css/");
        staticUrls.put("JavaScript资源", baseUrl + "/js/");
        staticUrls.put("图片资源", baseUrl + "/images/");
        staticUrls.put("头像资源", baseUrl + "/avatars/");
        staticUrls.put("管理员资源", baseUrl + "/admin/assets/");
        
        // AI 测试 API
        List<String> aiTestApis = new ArrayList<>();
        aiTestApis.add("POST http://localhost:" + serverPort + "/api/test/ai/sync - 测试同步AI调用");
        aiTestApis.add("POST http://localhost:" + serverPort + "/api/test/ai/async - 测试异步AI调用");
        aiTestApis.add("POST http://localhost:" + serverPort + "/api/test/ai/correction - 测试题目校对格式");
        aiTestApis.add("POST http://localhost:" + serverPort + "/api/test/correction/batch - 测试完整校对流程");
        aiTestApis.add("POST http://localhost:" + serverPort + "/api/test/correction/parse - 测试修正JSON解析");
        
        // 错误页面
        List<String> errorPages = new ArrayList<>();
        errorPages.add("http://localhost:" + serverPort + "/error/404 - 404错误页面");
        errorPages.add("http://localhost:" + serverPort + "/error/500 - 500错误页面");
        
        mappings.put("baseUrl", baseUrl);
        mappings.put("pageUrls", pageUrls);
        mappings.put("apiUrls", apiUrls);
        mappings.put("staticUrls", staticUrls);
        mappings.put("AI测试API", aiTestApis);
        mappings.put("错误页面", errorPages);
        mappings.put("timestamp", System.currentTimeMillis());
        
        log.info("URL映射信息已生成，共 {} 个页面URL，{} 个API URL", 
                pageUrls.size(), apiUrls.size());
        
        return mappings;
    }

    /**
     * URL映射测试页面
     */
    @GetMapping("/url-test")
    public String urlTestPage(Model model) {
        String baseUrl = "http://localhost:" + serverPort;
        
        // 创建测试链接列表
        List<Map<String, String>> testLinks = new ArrayList<>();
        
        // 页面链接
        addTestLink(testLinks, "用户登录页面", baseUrl + "/auth/login", "page");
        addTestLink(testLinks, "用户注册页面", baseUrl + "/auth/register", "page");
        addTestLink(testLinks, "管理员登录页面", baseUrl + "/admin/login", "page");
        addTestLink(testLinks, "管理员仪表板", baseUrl + "/admin/dashboard", "page");
        addTestLink(testLinks, "用户管理页面", baseUrl + "/admin/users", "page");
        addTestLink(testLinks, "题目库页面", baseUrl + "/topics/bank", "page");
        addTestLink(testLinks, "聊天页面", baseUrl + "/main/chat", "page");
        
        // API链接
        addTestLink(testLinks, "管理员仪表板API", baseUrl + "/api/admin/dashboard/stats", "api");
        addTestLink(testLinks, "用户管理API", baseUrl + "/api/admin/users", "api");
        addTestLink(testLinks, "用户统计API", baseUrl + "/api/user/stats", "api");
        addTestLink(testLinks, "知识点API", baseUrl + "/api/knowledge/all", "api");
        addTestLink(testLinks, "URL映射API", baseUrl + "/url-mapping", "api");
        
        model.addAttribute("testLinks", testLinks);
        model.addAttribute("baseUrl", baseUrl);
        
        return "test/url-test";
    }
    
    /**
     * 快速跳转到管理员登录页面
     */
    @GetMapping("/quick-admin")
    public String quickAdminLogin() {
        return "redirect:/admin/login";
    }
    
    /**
     * 快速跳转到用户登录页面
     */
    @GetMapping("/quick-login")
    public String quickUserLogin() {
        return "redirect:/auth/login";
    }
    
    /**
     * 添加测试链接辅助方法
     */
    private void addTestLink(List<Map<String, String>> testLinks, String name, String url, String type) {
        Map<String, String> link = new HashMap<>();
        link.put("name", name);
        link.put("url", url);
        link.put("type", type);
        testLinks.add(link);
    }
} 