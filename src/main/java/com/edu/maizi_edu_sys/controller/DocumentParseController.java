package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.dto.DocumentParseRequest;
import com.edu.maizi_edu_sys.dto.DocumentParseResponse;
import com.edu.maizi_edu_sys.entity.DocumentParseTask;
import com.edu.maizi_edu_sys.service.DocumentParseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文档解析控制器
 * 
 * <AUTHOR> Integration
 * @since 2025-01-20
 */
@Slf4j
@RestController
@RequestMapping("/api/document/parse")
@Tag(name = "文档解析服务")
public class DocumentParseController {

    @Autowired
    private DocumentParseService documentParseService;

    @PostMapping("/single")
    @Operation(summary = "创建单文件解析任务")
    public ResponseEntity<Map<String, Object>> createSingleTask(
            @Valid @RequestBody DocumentParseRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            // 从请求中获取用户ID (这里假设从session或token中获取)
            Long userId = getCurrentUserId(httpRequest);
            
            DocumentParseResponse response = documentParseService.createSingleTask(request, userId);
            
            return ResponseEntity.ok(buildSuccessResponse(response));
            
        } catch (Exception e) {
            log.error("创建单文件解析任务失败", e);
            return ResponseEntity.badRequest().body(buildErrorResponse("创建解析任务失败: " + e.getMessage()));
        }
    }

    @PostMapping("/batch/url")
    @Operation(summary = "创建批量URL解析任务")
    public ResponseEntity<Map<String, Object>> createBatchUrlTask(
            @Valid @RequestBody DocumentParseRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            Long userId = getCurrentUserId(httpRequest);
            
            DocumentParseResponse response = documentParseService.createBatchUrlTask(request, userId);
            
            return ResponseEntity.ok(buildSuccessResponse(response));
            
        } catch (Exception e) {
            log.error("创建批量URL解析任务失败", e);
            return ResponseEntity.badRequest().body(buildErrorResponse("创建批量解析任务失败: " + e.getMessage()));
        }
    }

    @PostMapping("/batch/upload")
    @Operation(summary = "申请批量文件上传链接")
    public ResponseEntity<Map<String, Object>> createBatchUploadUrls(
            @Valid @RequestBody DocumentParseRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            Long userId = getCurrentUserId(httpRequest);
            
            DocumentParseResponse response = documentParseService.createBatchUploadUrls(request, userId);
            
            return ResponseEntity.ok(buildSuccessResponse(response));
            
        } catch (Exception e) {
            log.error("申请批量上传链接失败", e);
            return ResponseEntity.badRequest().body(buildErrorResponse("申请上传链接失败: " + e.getMessage()));
        }
    }

    @GetMapping("/task/{taskId}")
    @Operation(summary = "查询单个任务结果")
    public ResponseEntity<Map<String, Object>> getTaskResult(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        
        try {
            DocumentParseResponse response = documentParseService.getTaskResult(taskId);
            
            return ResponseEntity.ok(buildSuccessResponse(response));
            
        } catch (Exception e) {
            log.error("查询任务结果失败", e);
            return ResponseEntity.badRequest().body(buildErrorResponse("查询任务结果失败: " + e.getMessage()));
        }
    }

    @GetMapping("/batch/{batchId}")
    @Operation(summary = "查询批量任务结果")
    public ResponseEntity<Map<String, Object>> getBatchResults(
            @Parameter(description = "批次ID") @PathVariable String batchId) {
        
        try {
            DocumentParseResponse response = documentParseService.getBatchResults(batchId);
            
            return ResponseEntity.ok(buildSuccessResponse(response));
            
        } catch (Exception e) {
            log.error("查询批量任务结果失败", e);
            return ResponseEntity.badRequest().body(buildErrorResponse("查询批量任务结果失败: " + e.getMessage()));
        }
    }

    @GetMapping("/user/tasks")
    @Operation(summary = "查询用户任务列表")
    public ResponseEntity<Map<String, Object>> getUserTasks(
            @Parameter(description = "任务状态") @RequestParam(required = false) String status,
            HttpServletRequest httpRequest) {
        
        try {
            Long userId = getCurrentUserId(httpRequest);
            
            List<DocumentParseTask> tasks = documentParseService.getUserTasks(userId, status);
            
            return ResponseEntity.ok(buildSuccessResponse(tasks));
            
        } catch (Exception e) {
            log.error("查询用户任务列表失败", e);
            return ResponseEntity.badRequest().body(buildErrorResponse("查询任务列表失败: " + e.getMessage()));
        }
    }

    @PostMapping("/callback/{taskId}")
    @Operation(summary = "MineRU回调接口")
    public ResponseEntity<Map<String, Object>> handleCallback(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @RequestBody Map<String, Object> callbackData) {
        
        try {
            String status = (String) callbackData.get("status");
            String resultData = callbackData.toString();
            
            documentParseService.handleCallback(taskId, status, resultData);
            
            return ResponseEntity.ok(buildSuccessResponse("回调处理成功"));
            
        } catch (Exception e) {
            log.error("处理MineRU回调失败", e);
            return ResponseEntity.badRequest().body(buildErrorResponse("回调处理失败: " + e.getMessage()));
        }
    }

    @PostMapping("/sync")
    @Operation(summary = "手动同步状态")
    public ResponseEntity<Map<String, Object>> syncTaskStatus() {
        try {
            documentParseService.syncTaskStatus();
            
            return ResponseEntity.ok(buildSuccessResponse("同步任务已启动"));
            
        } catch (Exception e) {
            log.error("同步任务状态失败", e);
            return ResponseEntity.badRequest().body(buildErrorResponse("同步失败: " + e.getMessage()));
        }
    }

    // 私有辅助方法

    /**
     * 获取当前用户ID
     * 这里需要根据实际的认证机制来实现
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        // 示例实现，实际需要根据项目的认证方式来获取用户ID
        
        // 方式1: 从session获取
        Object userId = request.getSession().getAttribute("userId");
        if (userId != null) {
            return Long.valueOf(userId.toString());
        }
        
        // 方式2: 从JWT token获取
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            // 解析JWT token获取用户ID
            // return JwtUtil.getUserIdFromToken(token.substring(7));
        }
        
        // 方式3: 从请求参数获取 (仅用于测试)
        String userIdParam = request.getParameter("userId");
        if (userIdParam != null) {
            return Long.valueOf(userIdParam);
        }
        
        // 默认返回测试用户ID
        return 1L;
    }

    /**
     * 构建成功响应
     */
    private Map<String, Object> buildSuccessResponse(Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("code", 0);
        response.put("message", "操作成功");
        response.put("data", data);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    /**
     * 构建错误响应
     */
    private Map<String, Object> buildErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("code", -1);
        response.put("message", message);
        response.put("data", null);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
}
