package com.edu.maizi_edu_sys.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.MineRuApiUsage;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * MineRU API使用情况Repository
 * 
 * <AUTHOR> Integration
 * @since 2025-01-20
 */
@Repository
@Mapper
public interface MineRuApiUsageRepository extends BaseMapper<MineRuApiUsage> {
    
    /**
     * 根据密钥名称和日期查询使用情况
     */
    @Select("SELECT * FROM mineru_api_usage WHERE token_name = #{tokenName} AND usage_date = #{usageDate}")
    MineRuApiUsage findByTokenNameAndUsageDate(@Param("tokenName") String tokenName, @Param("usageDate") LocalDate usageDate);
    
    /**
     * 获取指定日期所有密钥的使用情况
     */
    @Select("SELECT * FROM mineru_api_usage WHERE usage_date = #{usageDate} ORDER BY token_name")
    List<MineRuApiUsage> findByUsageDate(@Param("usageDate") LocalDate usageDate);
    
    /**
     * 获取指定密钥的历史使用情况
     */
    @Select("SELECT * FROM mineru_api_usage WHERE token_name = #{tokenName} " +
            "ORDER BY usage_date DESC LIMIT #{limit}")
    List<MineRuApiUsage> findByTokenNameOrderByUsageDateDesc(@Param("tokenName") String tokenName, @Param("limit") int limit);
    
    /**
     * 更新API使用统计
     */
    @Update("UPDATE mineru_api_usage SET " +
            "parsed_pages = parsed_pages + #{parsedPages}, " +
            "request_count = request_count + 1, " +
            "success_count = success_count + #{isSuccess}, " +
            "failure_count = failure_count + #{isFailure}, " +
            "last_used_at = NOW(), " +
            "updated_at = NOW() " +
            "WHERE token_name = #{tokenName} AND usage_date = #{usageDate}")
    int updateUsageStats(@Param("tokenName") String tokenName, 
                        @Param("usageDate") LocalDate usageDate,
                        @Param("parsedPages") int parsedPages,
                        @Param("isSuccess") int isSuccess,
                        @Param("isFailure") int isFailure);
    
    /**
     * 标记密钥已超出高优先级限制
     */
    @Update("UPDATE mineru_api_usage SET exceeded_priority_limit = TRUE, updated_at = NOW() " +
            "WHERE token_name = #{tokenName} AND usage_date = #{usageDate}")
    int markExceededPriorityLimit(@Param("tokenName") String tokenName, @Param("usageDate") LocalDate usageDate);
    
    /**
     * 获取当日使用量最少的可用密钥
     */
    @Select("SELECT * FROM mineru_api_usage WHERE usage_date = #{usageDate} " +
            "ORDER BY parsed_pages ASC, request_count ASC LIMIT 1")
    MineRuApiUsage findLeastUsedToken(@Param("usageDate") LocalDate usageDate);
    
    /**
     * 清理过期的使用记录
     */
    @Delete("DELETE FROM mineru_api_usage WHERE usage_date < #{expireDate}")
    int deleteExpiredRecords(@Param("expireDate") LocalDate expireDate);
    
    /**
     * 获取密钥使用统计摘要
     */
    @Select("SELECT token_name, " +
            "SUM(parsed_pages) as total_pages, " +
            "SUM(request_count) as total_requests, " +
            "SUM(success_count) as total_success, " +
            "SUM(failure_count) as total_failures, " +
            "MAX(last_used_at) as last_used " +
            "FROM mineru_api_usage " +
            "WHERE usage_date >= #{startDate} AND usage_date <= #{endDate} " +
            "GROUP BY token_name " +
            "ORDER BY total_pages DESC")
    @Results({
        @Result(property = "tokenName", column = "token_name"),
        @Result(property = "totalPages", column = "total_pages"),
        @Result(property = "totalRequests", column = "total_requests"),
        @Result(property = "totalSuccess", column = "total_success"),
        @Result(property = "totalFailures", column = "total_failures"),
        @Result(property = "lastUsed", column = "last_used")
    })
    List<TokenUsageSummary> getUsageSummary(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    /**
     * 密钥使用统计摘要DTO
     */
    class TokenUsageSummary {
        private String tokenName;
        private Long totalPages;
        private Long totalRequests;
        private Long totalSuccess;
        private Long totalFailures;
        private java.time.LocalDateTime lastUsed;
        
        // Getters and Setters
        public String getTokenName() { return tokenName; }
        public void setTokenName(String tokenName) { this.tokenName = tokenName; }
        
        public Long getTotalPages() { return totalPages; }
        public void setTotalPages(Long totalPages) { this.totalPages = totalPages; }
        
        public Long getTotalRequests() { return totalRequests; }
        public void setTotalRequests(Long totalRequests) { this.totalRequests = totalRequests; }
        
        public Long getTotalSuccess() { return totalSuccess; }
        public void setTotalSuccess(Long totalSuccess) { this.totalSuccess = totalSuccess; }
        
        public Long getTotalFailures() { return totalFailures; }
        public void setTotalFailures(Long totalFailures) { this.totalFailures = totalFailures; }
        
        public java.time.LocalDateTime getLastUsed() { return lastUsed; }
        public void setLastUsed(java.time.LocalDateTime lastUsed) { this.lastUsed = lastUsed; }
    }
}
