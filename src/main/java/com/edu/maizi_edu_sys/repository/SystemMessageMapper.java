package com.edu.maizi_edu_sys.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.maizi_edu_sys.entity.SystemMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 系统消息Mapper接口
 */
@Mapper
public interface SystemMessageMapper extends BaseMapper<SystemMessage> {

    /**
     * 查询用户的消息（分页）
     */
    @Select("SELECT * FROM system_messages WHERE user_id = #{userId} ORDER BY created_at DESC")
    IPage<SystemMessage> selectByUserId(Page<?> page, @Param("userId") Long userId);

    /**
     * 查询用户的未读消息数量
     */
    @Select("SELECT COUNT(*) FROM system_messages WHERE user_id = #{userId} AND is_read = 0")
    Long countUnreadByUserId(@Param("userId") Long userId);

    /**
     * 标记消息为已读
     */
    @Update("UPDATE system_messages SET is_read = 1, read_time = NOW() WHERE id = #{messageId} AND user_id = #{userId}")
    int markAsRead(@Param("messageId") Long messageId, @Param("userId") Long userId);

    /**
     * 标记用户所有消息为已读
     */
    @Update("UPDATE system_messages SET is_read = 1, read_time = NOW() WHERE user_id = #{userId} AND is_read = 0")
    int markAllAsRead(@Param("userId") Long userId);
} 