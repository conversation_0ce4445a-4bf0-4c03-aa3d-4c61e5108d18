package com.edu.maizi_edu_sys.repository;

import com.edu.maizi_edu_sys.entity.Book;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BookRepository extends BaseMapper<Book> {

    /**
     * Find books by title containing the search keyword (case insensitive)
     */
    @Select("SELECT * FROM books WHERE LOWER(title) LIKE LOWER(CONCAT('%', #{title}, '%'))")
    List<Book> findByTitleContainingIgnoreCase(@Param("title") String title);

    /**
     * Find books by title or type containing the search keyword
     */
    @Select("SELECT * FROM books WHERE " +
           "LOWER(title) LIKE LOWER(CONCAT('%', #{keyword}, '%')) OR " +
           "LOWER(type) LIKE LOWER(CONCAT('%', #{keyword}, '%')) OR " +
           "LOWER(description) LIKE LOWER(CONCAT('%', #{keyword}, '%'))")
    List<Book> searchBooks(@Param("keyword") String keyword);

    /**
     * Alternative search method using native SQL for better performance
     */
    @Select("SELECT * FROM books WHERE " +
           "LOWER(title) LIKE LOWER(CONCAT('%', #{keyword}, '%')) OR " +
           "LOWER(type) LIKE LOWER(CONCAT('%', #{keyword}, '%')) OR " +
           "LOWER(description) LIKE LOWER(CONCAT('%', #{keyword}, '%'))")
    List<Book> searchBooksNative(@Param("keyword") String keyword);

    /**
     * Find books by user id
     */
    @Select("SELECT * FROM books WHERE user_id = #{userId}")
    List<Book> findByUserId(@Param("userId") Long userId);

    /**
     * Find books by type
     */
    @Select("SELECT * FROM books WHERE type = #{type}")
    List<Book> findByType(@Param("type") String type);

    /**
     * Find books by title or description containing the search query
     */
    @Select("SELECT * FROM books WHERE " +
           "title LIKE CONCAT('%', #{titleQuery}, '%') OR " +
           "description LIKE CONCAT('%', #{descQuery}, '%')")
    List<Book> findByTitleContainingOrDescriptionContaining(@Param("titleQuery") String titleQuery, @Param("descQuery") String descQuery);
}