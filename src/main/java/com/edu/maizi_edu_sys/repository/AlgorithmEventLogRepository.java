package com.edu.maizi_edu_sys.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.AlgorithmEventLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.Date;
import java.util.List;

/**
 * 算法事件日志Repository接口
 */
@Mapper
public interface AlgorithmEventLogRepository extends BaseMapper<AlgorithmEventLog> {
    
    /**
     * 根据执行ID查找事件日志
     */
    @Select("SELECT * FROM algorithm_event_log WHERE execution_id = #{executionId} ORDER BY event_time DESC")
    List<AlgorithmEventLog> findByExecutionIdOrderByEventTimeDesc(@Param("executionId") Long executionId);
    
    /**
     * 根据执行ID和事件类型查找事件日志
     */
    @Select("SELECT * FROM algorithm_event_log WHERE execution_id = #{executionId} AND event_type = #{eventType} ORDER BY event_time DESC")
    List<AlgorithmEventLog> findByExecutionIdAndEventType(@Param("executionId") Long executionId, @Param("eventType") String eventType);
    
    /**
     * 获取最新的事件日志
     */
    @Select("SELECT * FROM algorithm_event_log WHERE execution_id = #{executionId} ORDER BY event_time DESC LIMIT 1")
    AlgorithmEventLog findLatestEventByExecutionId(@Param("executionId") Long executionId);
    
    /**
     * 根据时间范围查找事件日志
     */
    @Select("SELECT * FROM algorithm_event_log WHERE execution_id = #{executionId} AND event_time BETWEEN #{startTime} AND #{endTime} ORDER BY event_time DESC")
    List<AlgorithmEventLog> findByExecutionIdAndEventTimeBetween(@Param("executionId") Long executionId, 
                                                                 @Param("startTime") Date startTime, 
                                                                 @Param("endTime") Date endTime);
    
    /**
     * 统计事件类型数量
     */
    @Select("SELECT event_type, COUNT(*) FROM algorithm_event_log WHERE execution_id = #{executionId} GROUP BY event_type")
    List<Object[]> countEventsByType(@Param("executionId") Long executionId);
    
    /**
     * 获取错误事件日志
     */
    @Select("SELECT * FROM algorithm_event_log WHERE execution_id = #{executionId} AND event_type = 'ERROR' ORDER BY event_time DESC")
    List<AlgorithmEventLog> findErrorEventsByExecutionId(@Param("executionId") Long executionId);
    
    /**
     * 删除指定执行ID的所有事件日志
     */
    @Delete("DELETE FROM algorithm_event_log WHERE execution_id = #{executionId}")
    void deleteByExecutionId(@Param("executionId") Long executionId);
    
    /**
     * 根据事件类型统计执行次数
     */
    @Select("SELECT COUNT(DISTINCT execution_id) FROM algorithm_event_log WHERE event_type = #{eventType}")
    long countDistinctExecutionsByEventType(@Param("eventType") String eventType);
}
