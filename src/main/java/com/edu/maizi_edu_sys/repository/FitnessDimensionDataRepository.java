package com.edu.maizi_edu_sys.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.FitnessDimensionData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

/**
 * 多维度适应度数据Repository接口
 */
@Mapper
public interface FitnessDimensionDataRepository extends BaseMapper<FitnessDimensionData> {
    
    /**
     * 根据执行ID查找适应度维度数据
     */
    @Select("SELECT * FROM fitness_dimension_data WHERE execution_id = #{executionId} ORDER BY generation ASC, dimension_name ASC")
    List<FitnessDimensionData> findByExecutionIdOrderByGenerationAscDimensionNameAsc(@Param("executionId") Long executionId);
    
    /**
     * 根据执行ID和代数查找数据
     */
    @Select("SELECT * FROM fitness_dimension_data WHERE execution_id = #{executionId} AND generation = #{generation}")
    List<FitnessDimensionData> findByExecutionIdAndGeneration(@Param("executionId") Long executionId, @Param("generation") Integer generation);
    
    /**
     * 根据执行ID和维度名称查找数据
     */
    @Select("SELECT * FROM fitness_dimension_data WHERE execution_id = #{executionId} AND dimension_name = #{dimensionName} ORDER BY generation")
    List<FitnessDimensionData> findByExecutionIdAndDimensionNameOrderByGeneration(
            @Param("executionId") Long executionId, @Param("dimensionName") String dimensionName);
    
    /**
     * 获取最新代数的适应度维度数据
     */
    @Select("SELECT * FROM fitness_dimension_data fdd " +
           "WHERE fdd.execution_id = #{executionId} " +
           "AND fdd.generation = (SELECT MAX(fdd2.generation) FROM fitness_dimension_data fdd2 " +
           "WHERE fdd2.execution_id = #{executionId})")
    List<FitnessDimensionData> findLatestDimensionData(@Param("executionId") Long executionId);
    
    /**
     * 获取雷达图数据
     */
    @Select("SELECT dimension_name, dimension_value, weight " +
           "FROM fitness_dimension_data " +
           "WHERE execution_id = #{executionId} AND generation = #{generation} " +
           "ORDER BY dimension_name")
    List<Object[]> getRadarChartData(@Param("executionId") Long executionId, 
                                    @Param("generation") Integer generation);
    
    /**
     * 获取维度进化趋势数据
     */
    @Select("SELECT generation, dimension_name, dimension_value " +
           "FROM fitness_dimension_data " +
           "WHERE execution_id = #{executionId} " +
           "ORDER BY generation, dimension_name")
    List<Object[]> getDimensionTrendData(@Param("executionId") Long executionId);
    
    /**
     * 获取维度统计数据
     */
    @Select("SELECT dimension_name, " +
           "MIN(dimension_value), MAX(dimension_value), AVG(dimension_value) " +
           "FROM fitness_dimension_data " +
           "WHERE execution_id = #{executionId} " +
           "GROUP BY dimension_name")
    List<Object[]> getDimensionStatistics(@Param("executionId") Long executionId);
    
    /**
     * 获取加权适应度数据
     */
    @Select("SELECT generation, SUM(dimension_value * weight) " +
           "FROM fitness_dimension_data " +
           "WHERE execution_id = #{executionId} " +
           "GROUP BY generation " +
           "ORDER BY generation")
    List<Object[]> getWeightedFitnessData(@Param("executionId") Long executionId);
    
    /**
     * 获取所有维度名称
     */
    @Select("SELECT DISTINCT dimension_name FROM fitness_dimension_data " +
           "WHERE execution_id = #{executionId}")
    List<String> findDistinctDimensionNames(@Param("executionId") Long executionId);
    
    /**
     * 删除指定执行ID的所有数据
     */
    @Delete("DELETE FROM fitness_dimension_data WHERE execution_id = #{executionId}")
    void deleteByExecutionId(@Param("executionId") Long executionId);
}
