package com.edu.maizi_edu_sys.repository;

import com.edu.maizi_edu_sys.entity.PaperConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 试卷配置Repository
 */
@Repository
@Mapper
public interface PaperConfigRepository extends BaseMapper<PaperConfig> {

    /**
     * 根据用户ID查找配置列表
     */
    @Select("SELECT * FROM paper_configs WHERE user_id = #{userId} ORDER BY last_used_at DESC, created_at DESC")
    List<PaperConfig> findByUserIdOrderByLastUsedAtDescCreatedAtDesc(@Param("userId") Long userId);

    /**
     * 查找用户的配置和公共配置
     */
    @Select("SELECT * FROM paper_configs WHERE user_id = #{userId} OR is_public = true ORDER BY last_used_at DESC, created_at DESC")
    List<PaperConfig> findByUserIdOrPublicOrderByLastUsedAtDescCreatedAtDesc(@Param("userId") Long userId);

    /**
     * 根据配置名称和用户ID查找（用于检查重名）
     */
    @Select("SELECT * FROM paper_configs WHERE config_name = #{configName} AND user_id = #{userId} LIMIT 1")
    PaperConfig findByConfigNameAndUserId(@Param("configName") String configName, @Param("userId") Long userId);

    /**
     * 查找用户的默认配置
     */
    @Select("SELECT * FROM paper_configs WHERE user_id = #{userId} AND is_default = true LIMIT 1")
    PaperConfig findByUserIdAndIsDefaultTrue(@Param("userId") Long userId);

    /**
     * 查找用户的所有默认配置（用于清理重复默认配置）
     */
    @Select("SELECT * FROM paper_configs WHERE user_id = #{userId} AND is_default = true")
    List<PaperConfig> findAllByUserIdAndIsDefaultTrue(@Param("userId") Long userId);

    /**
     * 查找公共配置
     */
    @Select("SELECT * FROM paper_configs WHERE is_public = true ORDER BY usage_count DESC, created_at DESC")
    List<PaperConfig> findByIsPublicTrueOrderByUsageCountDescCreatedAtDesc();

    /**
     * 根据配置名称模糊搜索用户配置
     */
    @Select("SELECT * FROM paper_configs WHERE user_id = #{userId} AND config_name LIKE CONCAT('%', #{keyword}, '%') ORDER BY last_used_at DESC, created_at DESC")
    List<PaperConfig> findByUserIdAndConfigNameContainingOrderByLastUsedAtDescCreatedAtDesc(@Param("userId") Long userId, @Param("keyword") String keyword);

    /**
     * 根据配置名称模糊搜索用户配置和公共配置
     */
    @Select("SELECT * FROM paper_configs WHERE (user_id = #{userId} OR is_public = true) AND config_name LIKE CONCAT('%', #{keyword}, '%') ORDER BY last_used_at DESC, created_at DESC")
    List<PaperConfig> findByUserIdOrPublicAndConfigNameContainingOrderByLastUsedAtDescCreatedAtDesc(@Param("userId") Long userId, @Param("keyword") String keyword);

    /**
     * 统计用户配置数量
     */
    @Select("SELECT COUNT(*) FROM paper_configs WHERE user_id = #{userId}")
    long countByUserId(@Param("userId") Long userId);

    /**
     * 统计公共配置数量
     */
    @Select("SELECT COUNT(*) FROM paper_configs WHERE is_public = true")
    long countByIsPublicTrue();



    /**
     * 分页查找用户配置
     */
    @Select("SELECT * FROM paper_configs WHERE user_id = #{userId} ORDER BY last_used_at DESC, created_at DESC")
    Page<PaperConfig> findByUserIdOrderByLastUsedAtDescCreatedAtDescPaged(@Param("userId") Long userId, Page<PaperConfig> page);

    /**
     * 分页查找用户配置和公共配置
     */
    @Select("SELECT * FROM paper_configs WHERE user_id = #{userId} OR is_public = true ORDER BY last_used_at DESC, created_at DESC")
    Page<PaperConfig> findByUserIdOrPublicOrderByLastUsedAtDescCreatedAtDescPaged(@Param("userId") Long userId, Page<PaperConfig> page);

    /**
     * 查找最近使用的配置 - 简化版本
     */
    @Select("SELECT * FROM paper_configs WHERE user_id = #{userId} AND last_used_at IS NOT NULL ORDER BY last_used_at DESC LIMIT #{limit}")
    List<PaperConfig> findRecentlyUsedByUserId(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 查找最常用的配置 - 简化版本
     */
    @Select("SELECT * FROM paper_configs WHERE user_id = #{userId} ORDER BY usage_count DESC, last_used_at DESC LIMIT #{limit}")
    List<PaperConfig> findMostUsedByUserId(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 删除用户的所有配置
     */
    @Delete("DELETE FROM paper_configs WHERE user_id = #{userId}")
    void deleteByUserId(@Param("userId") Long userId);

    /**
     * 检查配置名称是否存在（排除指定ID）
     */
    @Select("SELECT COUNT(*) > 0 FROM paper_configs WHERE config_name = #{configName} AND user_id = #{userId} AND id != #{excludeId}")
    boolean existsByConfigNameAndUserIdAndIdNot(@Param("configName") String configName, @Param("userId") Long userId, @Param("excludeId") Long excludeId);
}
