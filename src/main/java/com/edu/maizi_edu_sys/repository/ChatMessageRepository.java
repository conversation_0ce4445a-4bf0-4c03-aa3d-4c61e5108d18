package com.edu.maizi_edu_sys.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.ChatMessageEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

@Mapper
public interface ChatMessageRepository extends BaseMapper<ChatMessageEntity> {
    
    @Select("SELECT * FROM chat_messages WHERE session_id = #{sessionId} ORDER BY created_at ASC")
    List<ChatMessageEntity> findBySessionIdOrderByCreatedAt(@Param("sessionId") Long sessionId);
    
    @Delete("DELETE FROM chat_messages WHERE session_id = #{sessionId}")
    void deleteAllBySessionId(@Param("sessionId") Long sessionId);
} 