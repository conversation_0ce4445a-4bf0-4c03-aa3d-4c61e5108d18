package com.edu.maizi_edu_sys.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.maizi_edu_sys.dto.UserDailyAuditGroupDTO;
import com.edu.maizi_edu_sys.entity.TopicAudit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 题目审核Mapper接口
 */
@Mapper
public interface TopicAuditMapper extends BaseMapper<TopicAudit> {

    /**
     * 查询待审核题目列表（分页）
     */
    @Select("SELECT * FROM topic_audit WHERE audit_status = 0 ORDER BY submit_time ASC")
    IPage<TopicAudit> selectPendingAudits(Page<?> page);

    /**
     * 查询待审核题目列表（分页，支持搜索和筛选）
     */
    IPage<TopicAudit> selectPendingAuditsWithFilters(Page<?> page, @Param("keyword") String keyword, 
                                                    @Param("submitter") String submitter, 
                                                    @Param("knowledge") String knowledge);

    /**
     * 查询用户的审核记录（分页）
     */
    @Select("SELECT * FROM topic_audit WHERE user_id = #{userId} ORDER BY submit_time DESC")
    IPage<TopicAudit> selectByUserId(Page<?> page, @Param("userId") Long userId);

    /**
     * 统计审核状态数量
     */
    @Select("SELECT audit_status, COUNT(*) as count FROM topic_audit GROUP BY audit_status")
    List<Map<String, Object>> countByAuditStatus();

    /**
     * 查询用户提交的待审核题目数量
     */
    @Select("SELECT COUNT(*) FROM topic_audit WHERE user_id = #{userId} AND audit_status = 0")
    Long countPendingByUserId(@Param("userId") Long userId);

    /**
     * 查询审核员的审核记录
     */
    @Select("SELECT * FROM topic_audit WHERE auditor_id = #{auditorId} ORDER BY audit_time DESC")
    IPage<TopicAudit> selectByAuditorId(Page<?> page, @Param("auditorId") Long auditorId);

    /**
     * 查询按用户和日期分组的审核数据
     */
    IPage<UserDailyAuditGroupDTO> selectUserDailyAuditGroups(Page<?> page, @Param("keyword") String keyword);

    /**
     * 批量更新审核状态
     * @param auditIds 审核ID列表
     * @param auditStatus 审核状态
     * @param auditorId 审核员ID
     * @param auditTime 审核时间
     * @param auditComment 审核意见
     * @return 影响行数
     */
    int batchUpdateAuditStatus(@Param("auditIds") List<Long> auditIds,
                              @Param("auditStatus") int auditStatus,
                              @Param("auditorId") Long auditorId,
                              @Param("auditTime") LocalDateTime auditTime,
                              @Param("auditComment") String auditComment);
} 