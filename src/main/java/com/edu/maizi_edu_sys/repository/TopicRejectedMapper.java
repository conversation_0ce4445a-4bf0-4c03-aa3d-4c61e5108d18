package com.edu.maizi_edu_sys.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.maizi_edu_sys.entity.TopicRejected;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 被拒绝题目Mapper接口
 */
@Mapper
public interface TopicRejectedMapper extends BaseMapper<TopicRejected> {

    /**
     * 查询用户的被拒绝题目（分页）
     */
    @Select("SELECT * FROM topic_rejected WHERE user_id = #{userId} ORDER BY reject_time DESC")
    IPage<TopicRejected> selectByUserId(Page<?> page, @Param("userId") Long userId);

    /**
     * 查询审核员拒绝的题目（分页）
     */
    @Select("SELECT * FROM topic_rejected WHERE auditor_id = #{auditorId} ORDER BY reject_time DESC")
    IPage<TopicRejected> selectByAuditorId(Page<?> page, @Param("auditorId") Long auditorId);
} 