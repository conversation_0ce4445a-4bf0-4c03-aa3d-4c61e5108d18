package com.edu.maizi_edu_sys.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.AlgorithmGenerationData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.List;
import java.util.Optional;

/**
 * 算法代数进化数据Repository接口
 */
@Mapper
public interface AlgorithmGenerationDataRepository extends BaseMapper<AlgorithmGenerationData> {
    
    /**
     * 根据执行ID查找代数数据
     */
    @Select("SELECT * FROM algorithm_generation_data WHERE execution_id = #{executionId} ORDER BY generation")
    List<AlgorithmGenerationData> findByExecutionIdOrderByGeneration(@Param("executionId") Long executionId);
    
    /**
     * 根据执行ID和代数查找数据
     */
    @Select("SELECT * FROM algorithm_generation_data WHERE execution_id = #{executionId} AND generation = #{generation}")
    Optional<AlgorithmGenerationData> findByExecutionIdAndGeneration(@Param("executionId") Long executionId, @Param("generation") Integer generation);
    
    /**
     * 获取最新的代数数据
     */
    @Select("SELECT * FROM algorithm_generation_data WHERE execution_id = #{executionId} ORDER BY generation DESC LIMIT 1")
    Optional<AlgorithmGenerationData> findFirstByExecutionIdOrderByGenerationDesc(@Param("executionId") Long executionId);
    
    /**
     * 获取最佳适应度大于阈值的代数数据
     */
    @Select("SELECT * FROM algorithm_generation_data WHERE execution_id = #{executionId} AND best_fitness > #{threshold}")
    List<AlgorithmGenerationData> findByExecutionIdAndBestFitnessGreaterThan(@Param("executionId") Long executionId, @Param("threshold") Double threshold);
    
    /**
     * 获取收敛曲线数据
     */
    @Select("SELECT generation, best_fitness, avg_fitness, diversity_index " +
           "FROM algorithm_generation_data " +
           "WHERE execution_id = #{executionId} " +
           "ORDER BY generation")
    List<Object[]> getConvergenceData(@Param("executionId") Long executionId);
    
    /**
     * 获取适应度统计数据
     */
    @Select("SELECT MIN(best_fitness), MAX(best_fitness), AVG(best_fitness), " +
           "MIN(avg_fitness), MAX(avg_fitness), AVG(avg_fitness) " +
           "FROM algorithm_generation_data " +
           "WHERE execution_id = #{executionId}")
    Object[] getFitnessStatistics(@Param("executionId") Long executionId);
    
    /**
     * 获取约束违反统计
     */
    @Select("SELECT SUM(constraint_violations) FROM algorithm_generation_data " +
           "WHERE execution_id = #{executionId}")
    Long getTotalConstraintViolations(@Param("executionId") Long executionId);
    
    /**
     * 获取收敛速度数据
     */
    @Select("SELECT generation, convergence_speed FROM algorithm_generation_data " +
           "WHERE execution_id = #{executionId} AND convergence_speed IS NOT NULL " +
           "ORDER BY generation")
    List<Object[]> getConvergenceSpeedData(@Param("executionId") Long executionId);
    
    /**
     * 查找收敛代数
     */
    @Select("SELECT MIN(generation) FROM algorithm_generation_data " +
           "WHERE execution_id = #{executionId} AND best_fitness >= #{threshold}")
    Integer findConvergenceGeneration(@Param("executionId") Long executionId, 
                                     @Param("threshold") Double threshold);
    
    /**
     * 获取代数范围内的数据
     */
    @Select("SELECT * FROM algorithm_generation_data WHERE execution_id = #{executionId} AND generation BETWEEN #{startGeneration} AND #{endGeneration}")
    List<AlgorithmGenerationData> findByExecutionIdAndGenerationBetween(
            @Param("executionId") Long executionId, @Param("startGeneration") Integer startGeneration, @Param("endGeneration") Integer endGeneration);
    
    /**
     * 删除指定执行ID的所有数据
     */
    @Delete("DELETE FROM algorithm_generation_data WHERE execution_id = #{executionId}")
    void deleteByExecutionId(@Param("executionId") Long executionId);
}
