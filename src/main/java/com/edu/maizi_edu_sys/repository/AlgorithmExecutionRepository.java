package com.edu.maizi_edu_sys.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.AlgorithmExecution;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 算法执行记录Repository接口
 */
@Mapper
public interface AlgorithmExecutionRepository extends BaseMapper<AlgorithmExecution> {
    
    /**
     * 根据试卷ID查找执行记录
     */
    @Select("SELECT * FROM algorithm_execution WHERE paper_id = #{paperId}")
    AlgorithmExecution findByPaperId(String paperId);
    
    /**
     * 根据状态查找执行记录
     */
    @Select("SELECT * FROM algorithm_execution WHERE status = #{status}")
    List<AlgorithmExecution> findByStatus(@Param("status") String status);
    
    /**
     * 根据日期范围查找执行记录
     */
    @Select("SELECT * FROM algorithm_execution WHERE start_time BETWEEN #{startDate} AND #{endDate}")
    List<AlgorithmExecution> findByStartTimeBetween(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
    
    /**
     * 根据状态和日期范围查找执行记录
     */
    @Select("SELECT * FROM algorithm_execution WHERE status = #{status} AND start_time BETWEEN #{startDate} AND #{endDate}")
    List<AlgorithmExecution> findByStatusAndStartTimeBetween(
            @Param("status") String status, 
            @Param("startDate") Date startDate, 
            @Param("endDate") Date endDate);
    
    /**
     * 根据创建时间范围查找执行记录（替代findTodayExecutions）
     */
    @Select("SELECT * FROM algorithm_execution WHERE created_at BETWEEN #{startDate} AND #{endDate}")
    List<AlgorithmExecution> findByCreatedAtBetween(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
    
    /**
     * 统计各状态的执行记录数量
     */
    @Select("SELECT status, COUNT(*) FROM algorithm_execution GROUP BY status")
    List<Object[]> countByStatus();
    
    /**
     * 统计指定状态的执行记录数量
     */
    @Select("SELECT COUNT(*) FROM algorithm_execution WHERE status = #{status}")
    long countByStatus(@Param("status") String status);
    
    /**
     * 统计指定时间范围内的执行记录数量
     */
    @Select("SELECT COUNT(*) FROM algorithm_execution WHERE start_time BETWEEN #{startDate} AND #{endDate}")
    long countByStartTimeBetween(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
    
    /**
     * 获取平均收敛代数
     */
    @Select("SELECT AVG(COALESCE((SELECT MAX(agd.generation) FROM algorithm_generation_data agd WHERE agd.execution_id = ae.id), 0)) " +
           "FROM algorithm_execution ae WHERE ae.status = #{status}")
    Double getAverageGenerationsByStatus(@Param("status") String status);
    
    /**
     * 获取平均收敛代数（所有状态）
     */
    @Select("SELECT AVG(COALESCE((SELECT MAX(agd.generation) FROM algorithm_generation_data agd WHERE agd.execution_id = ae.id), 0)) " +
           "FROM algorithm_execution ae")
    Double getAverageGenerations();
    
    /**
     * 获取最近的执行记录
     */
    @Select("SELECT * FROM algorithm_execution ORDER BY created_at DESC LIMIT 10")
    List<AlgorithmExecution> findTop10ByOrderByCreatedAtDesc();
    
    /**
     * 获取最近的执行记录（按开始时间排序）
     */
    @Select("SELECT * FROM algorithm_execution ORDER BY start_time DESC LIMIT 10")
    List<AlgorithmExecution> findTop10ByOrderByStartTimeDesc();
    
    /**
     * 根据试卷名称模糊查询
     */
    @Select("SELECT * FROM algorithm_execution WHERE paper_name LIKE CONCAT('%', #{paperName}, '%')")
    List<AlgorithmExecution> findByPaperNameContaining(@Param("paperName") String paperName);
    
    /**
     * 删除指定日期前的历史记录
     */
    @Delete("DELETE FROM algorithm_execution WHERE created_at < #{date}")
    void deleteByCreatedAtBefore(@Param("date") Date date);
    
    /**
     * 统计执行记录总数
     */
    @Select("SELECT COUNT(*) FROM algorithm_execution WHERE created_at BETWEEN #{startDate} AND #{endDate}")
    long countByCreatedAtBetween(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
    
    /**
     * 获取指定状态的执行记录
     */
    @Select("SELECT * FROM algorithm_execution WHERE status = #{status} ORDER BY start_time DESC")
    List<AlgorithmExecution> findByStatusOrderByStartTimeDesc(@Param("status") String status);
}
