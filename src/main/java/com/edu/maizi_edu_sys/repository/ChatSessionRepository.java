package com.edu.maizi_edu_sys.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.ChatSession;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

@Mapper
public interface ChatSessionRepository extends BaseMapper<ChatSession> {
    
    @Select("SELECT * FROM chat_sessions WHERE user_id = #{userId} AND deleted = #{deleted} ORDER BY updated_at DESC")
    List<ChatSession> findByUserIdAndDeletedOrderByUpdatedAtDesc(@Param("userId") Long userId, @Param("deleted") Boolean deleted);
} 