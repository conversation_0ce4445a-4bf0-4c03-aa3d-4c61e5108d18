package com.edu.maizi_edu_sys.util;

import com.edu.maizi_edu_sys.config.MineRuProperties;
import com.edu.maizi_edu_sys.service.MineRuTokenManager;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * MineRU API客户端
 * 
 * <AUTHOR> Integration
 * @since 2025-01-20
 */
@Slf4j
@Component
public class MineRuApiClient {

    @Autowired
    private MineRuProperties mineRuProperties;
    
    @Autowired
    private MineRuTokenManager tokenManager;

    private OkHttpClient httpClient;
    private final ObjectMapper objectMapper;

    public MineRuApiClient() {
        this.objectMapper = new ObjectMapper();
    }

    @PostConstruct
    public void init() {
        // 验证配置
        validateConfiguration();
        
        // 初始化HTTP客户端
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(mineRuProperties.getApi().getTimeout(), TimeUnit.SECONDS)
                .writeTimeout(mineRuProperties.getApi().getTimeout(), TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
        
        log.info("MineRU API客户端初始化完成，baseUrl: {}, timeout: {}s", 
                mineRuProperties.getApi().getBaseUrl(), 
                mineRuProperties.getApi().getTimeout());
    }

    /**
     * 验证配置参数
     */
    private void validateConfiguration() {
        if (mineRuProperties.getApi().getTokens().isEmpty()) {
            throw new IllegalArgumentException("MineRU API tokens不能为空，请在配置文件中配置至少一个API密钥");
        }
        
        boolean hasValidToken = mineRuProperties.getApi().getTokens().stream()
                .anyMatch(token -> token.isEnabled() && StringUtils.hasText(token.getKey()));
        
        if (!hasValidToken) {
            throw new IllegalArgumentException("没有有效的MineRU API密钥，请检查配置");
        }
        
        if (!StringUtils.hasText(mineRuProperties.getApi().getBaseUrl())) {
            throw new IllegalArgumentException("MineRU API baseUrl不能为空");
        }
    }

    /**
     * 创建单文件解析任务
     */
    public MineRuResponse createTask(String requestBody, int estimatedPages) throws IOException {
        String url = mineRuProperties.getApi().getBaseUrl() + "/api/v4/extract/task";
        return sendPostRequestWithTokenSelection(url, requestBody, estimatedPages);
    }

    /**
     * 创建批量URL解析任务
     */
    public MineRuResponse createBatchTask(String requestBody, int estimatedPages) throws IOException {
        String url = mineRuProperties.getApi().getBaseUrl() + "/api/v4/extract/task/batch";
        return sendPostRequestWithTokenSelection(url, requestBody, estimatedPages);
    }

    /**
     * 申请批量文件上传链接
     */
    public MineRuResponse createBatchUploadUrls(String requestBody, int estimatedPages) throws IOException {
        String url = mineRuProperties.getApi().getBaseUrl() + "/api/v4/file-urls/batch";
        return sendPostRequestWithTokenSelection(url, requestBody, estimatedPages);
    }

    /**
     * 查询单个任务结果
     */
    public MineRuResponse getTaskResult(String taskId) throws IOException {
        String url = mineRuProperties.getApi().getBaseUrl() + "/api/v4/extract/task/" + taskId;
        return sendGetRequest(url);
    }

    /**
     * 查询批量任务结果
     */
    public MineRuResponse getBatchResults(String batchId) throws IOException {
        String url = mineRuProperties.getApi().getBaseUrl() + "/api/v4/extract-results/batch/" + batchId;
        return sendGetRequest(url);
    }

    /**
     * 上传文件到指定URL
     */
    public boolean uploadFile(String uploadUrl, byte[] fileData) throws IOException {
        RequestBody body = RequestBody.create(MediaType.parse("application/octet-stream"), fileData);
        Request request = new Request.Builder()
                .url(uploadUrl)
                .put(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            boolean success = response.isSuccessful();
            if (!success) {
                log.error("文件上传失败: {}, 响应码: {}", uploadUrl, response.code());
            }
            return success;
        }
    }

    /**
     * 发送POST请求（带密钥选择）
     */
    private MineRuResponse sendPostRequestWithTokenSelection(String url, String requestBody, int estimatedPages) throws IOException {
        MineRuProperties.Api.TokenConfig selectedToken = tokenManager.getNextAvailableToken(estimatedPages);
        
        if (selectedToken == null) {
            throw new IOException("没有可用的MineRU API密钥");
        }
        
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), requestBody);
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + selectedToken.getKey())
                .addHeader("Content-Type", "application/json")
                .addHeader("Accept", "*/*")
                .post(body)
                .build();

        return executeRequestWithUsageTracking(request, selectedToken.getName(), estimatedPages);
    }
    
    /**
     * 发送POST请求（使用默认密钥）
     */
    private MineRuResponse sendPostRequest(String url, String requestBody) throws IOException {
        return sendPostRequestWithTokenSelection(url, requestBody, 1); // 默认1页
    }

    /**
     * 发送GET请求
     */
    private MineRuResponse sendGetRequest(String url) throws IOException {
        // GET请求使用第一个可用密钥
        MineRuProperties.Api.TokenConfig selectedToken = tokenManager.getNextAvailableToken(0);
        
        if (selectedToken == null) {
            throw new IOException("没有可用的MineRU API密钥");
        }
        
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + selectedToken.getKey())
                .addHeader("Accept", "*/*")
                .get()
                .build();

        return executeRequestWithUsageTracking(request, selectedToken.getName(), 0);
    }

    /**
     * 执行请求并解析响应（带使用跟踪）
     */
    private MineRuResponse executeRequestWithUsageTracking(Request request, String tokenName, int estimatedPages) throws IOException {
        long startTime = System.currentTimeMillis();
        boolean success = false;
        MineRuResponse result = null;
        
        try {
            result = executeRequest(request);
            success = result.isSuccess();
            
            // 记录API使用情况
            tokenManager.recordApiUsage(tokenName, estimatedPages, success);
            
            // 如果请求失败且启用故障转移，标记密钥不可用
            if (!success && mineRuProperties.getApi().getLoadBalance().isFailoverEnabled()) {
                if (result.getHttpStatus() == 401 || result.getHttpStatus() == 403) {
                    tokenManager.markTokenUnavailable(tokenName, "认证失败: HTTP " + result.getHttpStatus());
                } else if (result.getHttpStatus() >= 500) {
                    tokenManager.markTokenUnavailable(tokenName, "服务器错误: HTTP " + result.getHttpStatus());
                }
            }
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("MineRU API调用完成: token={}, pages={}, success={}, duration={}ms", 
                    tokenName, estimatedPages, success, duration);
            
            return result;
            
        } catch (IOException e) {
            // 记录失败的API调用
            tokenManager.recordApiUsage(tokenName, 0, false);
            
            // 网络错误时标记密钥不可用
            if (mineRuProperties.getApi().getLoadBalance().isFailoverEnabled()) {
                tokenManager.markTokenUnavailable(tokenName, "网络错误: " + e.getMessage());
            }
            
            long duration = System.currentTimeMillis() - startTime;
            log.error("MineRU API调用异常: token={}, duration={}ms", tokenName, duration, e);
            
            throw e;
        }
    }
    
    /**
     * 执行请求并解析响应
     */
    private MineRuResponse executeRequest(Request request) throws IOException {
        try (Response response = httpClient.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "";
            
            log.info("MineRU API请求: {} {}", request.method(), request.url());
            log.debug("响应状态: {}, 响应体: {}", response.code(), responseBody);

            MineRuResponse result = new MineRuResponse();
            result.setHttpStatus(response.code());
            result.setSuccess(response.isSuccessful());

            if (response.isSuccessful() && !responseBody.isEmpty()) {
                try {
                    JsonNode jsonNode = objectMapper.readTree(responseBody);
                    result.setCode(jsonNode.path("code").asInt());
                    result.setMessage(jsonNode.path("msg").asText());
                    result.setTraceId(jsonNode.path("trace_id").asText());
                    result.setData(jsonNode.path("data"));
                    result.setRawResponse(responseBody);
                } catch (JsonProcessingException e) {
                    log.error("解析MineRU API响应失败", e);
                    result.setSuccess(false);
                    result.setMessage("响应解析失败: " + e.getMessage());
                }
            } else {
                result.setSuccess(false);
                result.setMessage("请求失败: HTTP " + response.code());
                result.setRawResponse(responseBody);
            }

            return result;
        }
    }

    /**
     * MineRU API响应包装类
     */
    public static class MineRuResponse {
        private int httpStatus;
        private boolean success;
        private int code;
        private String message;
        private String traceId;
        private JsonNode data;
        private String rawResponse;

        // Getters and Setters
        public int getHttpStatus() { return httpStatus; }
        public void setHttpStatus(int httpStatus) { this.httpStatus = httpStatus; }

        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

        public int getCode() { return code; }
        public void setCode(int code) { this.code = code; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public String getTraceId() { return traceId; }
        public void setTraceId(String traceId) { this.traceId = traceId; }

        public JsonNode getData() { return data; }
        public void setData(JsonNode data) { this.data = data; }

        public String getRawResponse() { return rawResponse; }
        public void setRawResponse(String rawResponse) { this.rawResponse = rawResponse; }

        public boolean isApiSuccess() {
            return success && code == 0;
        }
    }
}
