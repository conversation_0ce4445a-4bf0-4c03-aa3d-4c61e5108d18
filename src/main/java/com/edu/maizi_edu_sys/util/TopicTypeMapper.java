package com.edu.maizi_edu_sys.util;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 统一的题型映射工具类
 * 处理前端、后端、数据库之间的题型格式转换
 * 只支持明确定义的三种格式，不包含模糊匹配
 */
@Slf4j
public class TopicTypeMapper {

    // 数据库存储的标准题型（作为内部标准）
    public static final String DB_SINGLE_CHOICE = "choice";
    public static final String DB_MULTIPLE_CHOICE = "multiple";
    public static final String DB_JUDGMENT = "judge";
    public static final String DB_FILL_BLANK = "fill";
    public static final String DB_SHORT_ANSWER = "short";
    public static final String DB_SUBJECTIVE = "subjective";
    public static final String DB_GROUP = "group";

    // 前端使用的题型格式
    public static final String FRONTEND_SINGLE_CHOICE = "SINGLE_CHOICE";
    public static final String FRONTEND_MULTIPLE_CHOICE = "MULTIPLE_CHOICE";
    public static final String FRONTEND_JUDGMENT = "JUDGE";
    public static final String FRONTEND_FILL_BLANK = "FILL";
    public static final String FRONTEND_SHORT_ANSWER = "SHORT";
    public static final String FRONTEND_SUBJECTIVE = "SUBJECTIVE";
    public static final String FRONTEND_GROUP = "GROUP";

    // 驼峰命名格式（用于算法内部）
    public static final String CAMEL_SINGLE_CHOICE = "singleChoice";
    public static final String CAMEL_MULTIPLE_CHOICE = "multipleChoice";
    public static final String CAMEL_JUDGMENT = "judgment";
    public static final String CAMEL_FILL_BLANK = "fillBlank";
    public static final String CAMEL_SHORT_ANSWER = "shortAnswer";
    public static final String CAMEL_SUBJECTIVE = "subjective";
    public static final String CAMEL_GROUP_QUESTION = "groupQuestion";

    // 中文名称映射
    private static final Map<String, String> CHINESE_NAMES = new HashMap<>();
    static {
        CHINESE_NAMES.put(DB_SINGLE_CHOICE, "单选题");
        CHINESE_NAMES.put(DB_MULTIPLE_CHOICE, "多选题");
        CHINESE_NAMES.put(DB_JUDGMENT, "判断题");
        CHINESE_NAMES.put(DB_FILL_BLANK, "填空题");
        CHINESE_NAMES.put(DB_SHORT_ANSWER, "简答题");
        CHINESE_NAMES.put(DB_SUBJECTIVE, "主观题");
        CHINESE_NAMES.put(DB_GROUP, "组合题");
    }

    // 中文名称到驼峰格式的映射
    private static final Map<String, String> CHINESE_TO_CAMEL = new HashMap<>();
    static {
        CHINESE_TO_CAMEL.put("单选题", CAMEL_SINGLE_CHOICE);
        CHINESE_TO_CAMEL.put("多选题", CAMEL_MULTIPLE_CHOICE);
        CHINESE_TO_CAMEL.put("判断题", CAMEL_JUDGMENT);
        CHINESE_TO_CAMEL.put("填空题", CAMEL_FILL_BLANK);
        CHINESE_TO_CAMEL.put("简答题", CAMEL_SHORT_ANSWER);
        CHINESE_TO_CAMEL.put("主观题", CAMEL_SUBJECTIVE);
        CHINESE_TO_CAMEL.put("组合题", CAMEL_GROUP_QUESTION);
        // 兼容其他可能的中文名称变体
        CHINESE_TO_CAMEL.put("选择题", CAMEL_SINGLE_CHOICE);
        CHINESE_TO_CAMEL.put("单项选择题", CAMEL_SINGLE_CHOICE);
        CHINESE_TO_CAMEL.put("多项选择题", CAMEL_MULTIPLE_CHOICE);
        CHINESE_TO_CAMEL.put("多重选择题", CAMEL_MULTIPLE_CHOICE);
        CHINESE_TO_CAMEL.put("是非题", CAMEL_JUDGMENT);
        CHINESE_TO_CAMEL.put("真假题", CAMEL_JUDGMENT);
        CHINESE_TO_CAMEL.put("对错题", CAMEL_JUDGMENT);
        CHINESE_TO_CAMEL.put("补充题", CAMEL_FILL_BLANK);
        CHINESE_TO_CAMEL.put("完形填空", CAMEL_FILL_BLANK);
        CHINESE_TO_CAMEL.put("问答题", CAMEL_SHORT_ANSWER);
        CHINESE_TO_CAMEL.put("论述题", CAMEL_SHORT_ANSWER);
        CHINESE_TO_CAMEL.put("计算题", CAMEL_SHORT_ANSWER);
        // 加强题型变体支持
        CHINESE_TO_CAMEL.put("分析题", CAMEL_SHORT_ANSWER);
        CHINESE_TO_CAMEL.put("应用题", CAMEL_SHORT_ANSWER);
        CHINESE_TO_CAMEL.put("案例题", CAMEL_SHORT_ANSWER);
        CHINESE_TO_CAMEL.put("综合题", CAMEL_GROUP_QUESTION);
        CHINESE_TO_CAMEL.put("复合题", CAMEL_GROUP_QUESTION);
    }

    // 驼峰格式到数据库格式的映射
    private static final Map<String, String> CAMEL_TO_DB = new HashMap<>();
    static {
        CAMEL_TO_DB.put(CAMEL_SINGLE_CHOICE, DB_SINGLE_CHOICE);
        CAMEL_TO_DB.put(CAMEL_MULTIPLE_CHOICE, DB_MULTIPLE_CHOICE);
        CAMEL_TO_DB.put(CAMEL_JUDGMENT, DB_JUDGMENT);
        CAMEL_TO_DB.put(CAMEL_FILL_BLANK, DB_FILL_BLANK);
        CAMEL_TO_DB.put(CAMEL_SHORT_ANSWER, DB_SHORT_ANSWER);
        CAMEL_TO_DB.put(CAMEL_SUBJECTIVE, DB_SUBJECTIVE);
        CAMEL_TO_DB.put(CAMEL_GROUP_QUESTION, DB_GROUP);
    }

    // 驼峰格式到前端格式的映射
    private static final Map<String, String> CAMEL_TO_FRONTEND = new HashMap<>();
    static {
        CAMEL_TO_FRONTEND.put(CAMEL_SINGLE_CHOICE, FRONTEND_SINGLE_CHOICE);
        CAMEL_TO_FRONTEND.put(CAMEL_MULTIPLE_CHOICE, FRONTEND_MULTIPLE_CHOICE);
        CAMEL_TO_FRONTEND.put(CAMEL_JUDGMENT, FRONTEND_JUDGMENT);
        CAMEL_TO_FRONTEND.put(CAMEL_FILL_BLANK, FRONTEND_FILL_BLANK);
        CAMEL_TO_FRONTEND.put(CAMEL_SHORT_ANSWER, FRONTEND_SHORT_ANSWER);
        CAMEL_TO_FRONTEND.put(CAMEL_SUBJECTIVE, FRONTEND_SUBJECTIVE);
        CAMEL_TO_FRONTEND.put(CAMEL_GROUP_QUESTION, FRONTEND_GROUP);
    }

    /**
     * 标准化题型（兼容原有TopicTypeUtils.normalize方法）
     * 将前端格式或数据库格式转换为驼峰命名格式
     * 
     * @param typeKey 前端格式或数据库格式的题型标识
     * @return 驼峰命名格式的题型标识
     */
    public static String normalize(String typeKey) {
        if (typeKey == null || typeKey.trim().isEmpty()) {
            log.warn("题型为空，返回默认值");
            return CAMEL_SINGLE_CHOICE;
        }

        String input = typeKey.trim();
        
        // 1. 数据库格式匹配
        switch (input.toLowerCase()) {
            case DB_SINGLE_CHOICE: return CAMEL_SINGLE_CHOICE;
            case DB_MULTIPLE_CHOICE: return CAMEL_MULTIPLE_CHOICE;
            case DB_JUDGMENT: return CAMEL_JUDGMENT;
            case DB_FILL_BLANK: return CAMEL_FILL_BLANK;
            case DB_SHORT_ANSWER: return CAMEL_SHORT_ANSWER;
            case DB_SUBJECTIVE: return CAMEL_SUBJECTIVE;
            case DB_GROUP: return CAMEL_GROUP_QUESTION;
        }

        // 2. 前端格式匹配
        switch (input.toUpperCase()) {
            case FRONTEND_SINGLE_CHOICE: return CAMEL_SINGLE_CHOICE;
            case FRONTEND_MULTIPLE_CHOICE: return CAMEL_MULTIPLE_CHOICE;
            case FRONTEND_JUDGMENT: return CAMEL_JUDGMENT;
            case FRONTEND_FILL_BLANK: return CAMEL_FILL_BLANK;
            case FRONTEND_SHORT_ANSWER: return CAMEL_SHORT_ANSWER;
            case FRONTEND_SUBJECTIVE: return CAMEL_SUBJECTIVE;
            case FRONTEND_GROUP: return CAMEL_GROUP_QUESTION;
        }

        // 3. 驼峰格式匹配
        switch (input) {
            case CAMEL_SINGLE_CHOICE: return CAMEL_SINGLE_CHOICE;
            case CAMEL_MULTIPLE_CHOICE: return CAMEL_MULTIPLE_CHOICE;
            case CAMEL_JUDGMENT: return CAMEL_JUDGMENT;
            case CAMEL_FILL_BLANK: return CAMEL_FILL_BLANK;
            case CAMEL_SHORT_ANSWER: return CAMEL_SHORT_ANSWER;
            case CAMEL_SUBJECTIVE: return CAMEL_SUBJECTIVE;
            case CAMEL_GROUP_QUESTION: return CAMEL_GROUP_QUESTION;
        }

        // 4. 中文名称匹配
        String chineseResult = CHINESE_TO_CAMEL.get(input);
        if (chineseResult != null) {
            log.debug("中文题型映射成功: '{}' -> '{}'", input, chineseResult);
            return chineseResult;
        }

        // 5. 尝试去除常见前缀后再匹配中文名称
        String cleanInput = input.replaceAll("^[一二三四五六七八九十][、.]", "")
                                 .replaceAll("^\\d+[、.)]", "")
                                 .replaceAll("^[（(].*[）)]", "")
                                 .replaceAll("^[A-Z][、.):]", "")
                                 .replaceAll("^第[一二三四五六七八九十\\d]+[题部分章节]", "")
                                 .replaceAll("^[\\s\\u00A0]+", "") // 清理空白字符
                                 .trim();
        
        if (!cleanInput.equals(input)) {
            String cleanResult = CHINESE_TO_CAMEL.get(cleanInput);
            if (cleanResult != null) {
                log.debug("清理前缀后中文题型映射成功: '{}' -> '{}' -> '{}'", input, cleanInput, cleanResult);
                return cleanResult;
            }
        }

        log.warn("未识别的题型格式: '{}', 返回默认单选题", input);
        return CAMEL_SINGLE_CHOICE;
    }

    /**
     * 将任意格式的题型转换为数据库标准格式
     * 
     * @param typeKey 任意格式的题型标识
     * @return 数据库标准格式的题型标识
     */
    public static String toDbFormat(String typeKey) {
        String camelCase = normalize(typeKey);
        return CAMEL_TO_DB.getOrDefault(camelCase, DB_SINGLE_CHOICE);
    }

    /**
     * 将任意格式的题型转换为前端格式
     * 
     * @param inputType 输入题型
     * @return 前端格式的题型
     */
    public static String toFrontendFormat(String inputType) {
        String camelCase = normalize(inputType);
        return CAMEL_TO_FRONTEND.getOrDefault(camelCase, FRONTEND_SINGLE_CHOICE);
    }

    /**
     * 将前端格式转换为驼峰格式（兼容原有TopicTypeUtils.fromFrontendType方法）
     * 
     * @param frontendType 前端格式的题型
     * @return 驼峰格式的题型
     */
    public static String fromFrontendType(String frontendType) {
        return normalize(frontendType);
    }

    /**
     * 将驼峰格式转换为前端格式（兼容原有TopicTypeUtils.toFrontendType方法）
     * 
     * @param normalizedType 驼峰格式的题型
     * @return 前端格式的题型
     */
    public static String toFrontendType(String normalizedType) {
        return toFrontendFormat(normalizedType);
    }

    /**
     * 获取题型的中文显示名称（兼容原有TopicTypeUtils.getDisplayName方法）
     * 
     * @param normalizedType 标准化后的题型
     * @return 中文显示名称
     */
    public static String getDisplayName(String normalizedType) {
        if (normalizedType == null || normalizedType.trim().isEmpty()) {
            return "未知题型";
        }

        String dbType = toDbFormat(normalizedType);
        return CHINESE_NAMES.getOrDefault(dbType, normalizedType);
    }

    /**
     * 获取题型的中文名称
     * 
     * @param dbType 数据库格式的题型
     * @return 中文名称
     */
    public static String getChineseName(String dbType) {
        return CHINESE_NAMES.getOrDefault(dbType, "未知题型");
    }

    /**
     * 验证题型是否有效
     * 
     * @param typeKey 题型标识
     * @return 是否为有效题型
     */
    public static boolean isValidType(String typeKey) {
        if (typeKey == null || typeKey.trim().isEmpty()) {
            return false;
        }

        String dbType = toDbFormat(typeKey);
        return CHINESE_NAMES.containsKey(dbType);
    }

    /**
     * 获取所有支持的数据库格式题型
     * 
     * @return 数据库格式题型数组
     */
    public static String[] getAllDbTypes() {
        return new String[]{
            DB_SINGLE_CHOICE, DB_MULTIPLE_CHOICE, DB_JUDGMENT, 
            DB_FILL_BLANK, DB_SHORT_ANSWER, DB_SUBJECTIVE, DB_GROUP
        };
    }

    /**
     * 获取所有支持的前端格式题型
     * 
     * @return 前端格式题型数组
     */
    public static String[] getAllFrontendTypes() {
        return new String[]{
            FRONTEND_SINGLE_CHOICE, FRONTEND_MULTIPLE_CHOICE, FRONTEND_JUDGMENT,
            FRONTEND_FILL_BLANK, FRONTEND_SHORT_ANSWER, FRONTEND_SUBJECTIVE, FRONTEND_GROUP
        };
    }

    /**
     * 获取所有支持的驼峰格式题型
     * 
     * @return 驼峰格式题型数组
     */
    public static String[] getAllCamelTypes() {
        return new String[]{
            CAMEL_SINGLE_CHOICE, CAMEL_MULTIPLE_CHOICE, CAMEL_JUDGMENT,
            CAMEL_FILL_BLANK, CAMEL_SHORT_ANSWER, CAMEL_SUBJECTIVE, CAMEL_GROUP_QUESTION
        };
    }
}
