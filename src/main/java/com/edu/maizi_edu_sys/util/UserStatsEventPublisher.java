package com.edu.maizi_edu_sys.util;

import com.edu.maizi_edu_sys.event.UserStatsUpdateEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 用户统计事件发布工具类
 * 
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserStatsEventPublisher {
    
    private final ApplicationEventPublisher eventPublisher;
    
    /**
     * 发布题目上传事件
     */
    public void publishTopicUploadedEvent(Long userId) {
        publishEvent(userId, UserStatsUpdateEvent.UpdateType.TOPIC_UPLOADED, "用户上传了新题目");
    }
    
    /**
     * 发布题目审核通过事件
     */
    public void publishTopicApprovedEvent(Long userId) {
        publishEvent(userId, UserStatsUpdateEvent.UpdateType.TOPIC_APPROVED, "题目审核通过");
    }
    
    /**
     * 发布题目审核拒绝事件
     */
    public void publishTopicRejectedEvent(Long userId) {
        publishEvent(userId, UserStatsUpdateEvent.UpdateType.TOPIC_REJECTED, "题目审核拒绝");
    }
    
    /**
     * 发布题目删除事件
     */
    public void publishTopicDeletedEvent(Long userId) {
        publishEvent(userId, UserStatsUpdateEvent.UpdateType.TOPIC_DELETED, "题目被删除");
    }
    
    /**
     * 发布批量上传事件
     */
    public void publishBatchUploadEvent(Long userId, int count) {
        publishEvent(userId, UserStatsUpdateEvent.UpdateType.BATCH_UPLOAD, 
            String.format("批量上传了%d个题目", count));
    }
    
    /**
     * 发布用户统计更新事件
     */
    private void publishEvent(Long userId, UserStatsUpdateEvent.UpdateType updateType, String description) {
        try {
            UserStatsUpdateEvent event = new UserStatsUpdateEvent(this, userId, updateType, description);
            eventPublisher.publishEvent(event);
            
            log.debug("发布用户统计更新事件: userId={}, type={}, description={}", 
                userId, updateType, description);
                
        } catch (Exception e) {
            log.error("发布用户统计更新事件失败: userId={}, type={}", userId, updateType, e);
        }
    }
}
