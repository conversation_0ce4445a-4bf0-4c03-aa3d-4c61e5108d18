package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;

/**
 * 算法事件日志实体类
 */
@TableName("algorithm_event_log")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlgorithmEventLog {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("execution_id")
    private Long executionId;
    
    @TableField("event_type")
    private EventType eventType;
    
    @TableField("event_message")
    private String eventMessage;
    
    @TableField("event_data")
    private String eventData;
    
    @TableField("generation")
    private Integer generation;
    
    @TableField(value = "event_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventTime = new Date();
    
    /**
     * 创建时设置时间
     */
    public void onCreate() {
        eventTime = new Date();
    }
    
    /**
     * 判断是否为错误事件
     */
    public boolean isErrorEvent() {
        return eventType == EventType.ERROR;
    }
    
    /**
     * 判断是否为里程碑事件
     */
    public boolean isMilestoneEvent() {
        return eventType == EventType.MILESTONE;
    }
    
    /**
     * 事件类型枚举
     */
    public enum EventType {
        START("开始"),
        PAUSE("暂停"),
        RESUME("恢复"),
        STOP("停止"),
        ERROR("错误"),
        MILESTONE("里程碑"),
        CONVERGENCE("收敛"),
        PARAMETER_CHANGE("参数变更");
        
        private final String displayName;
        
        EventType(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
}
