package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.PrePersist;
import java.util.Date;

/**
 * 多维度适应度数据实体类
 */
@TableName("fitness_dimension_data")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FitnessDimensionData {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("execution_id")
    private Long executionId;
    
    @TableField("generation")
    private Integer generation;
    
    @TableField("dimension_name")
    private String dimensionName;
    
    @TableField("dimension_value")
    private Double dimensionValue;
    
    @TableField("fitness_value")
    private Double fitnessValue;
    
    @TableField("weight")
    private Double weight = 1.0;
    
    @TableField(value = "recorded_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recordedAt = new Date();
    
    /**
     * 预持久化操作
     */
    @PrePersist
    protected void onCreate() {
        recordedAt = new Date();
    }
    
    /**
     * 获取加权适应度值
     */
    public double getWeightedValue() {
        return dimensionValue * weight;
    }
    
    /**
     * 适应度维度类型枚举
     */
    public enum DimensionType {
        DIFFICULTY_DISTRIBUTION("难度分布"),
        KNOWLEDGE_COVERAGE("知识点覆盖"),
        QUESTION_TYPE_BALANCE("题型平衡"),
        TIME_ALLOCATION("时间分配"),
        SCORE_DISTRIBUTION("分值分布"),
        COGNITIVE_LEVEL("认知层次"),
        REPETITION_CONTROL("重复度控制"),
        OVERALL_QUALITY("综合质量");
        
        private final String displayName;
        
        DimensionType(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public static DimensionType fromDisplayName(String displayName) {
            for (DimensionType type : DimensionType.values()) {
                if (type.displayName.equals(displayName)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown dimension: " + displayName);
        }
    }
}
