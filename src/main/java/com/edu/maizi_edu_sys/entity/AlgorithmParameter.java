package com.edu.maizi_edu_sys.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;

/**
 * 算法参数配置实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("algorithm_parameters")
public class AlgorithmParameter {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("execution_id")
    private Long executionId;
    
    @TableField("parameter_name")
    private String parameterName;
    
    @TableField("parameter_value")
    private String parameterValue;
    
    @TableField("parameter_type")
    private ParameterType parameterType;
    
    @TableField("description")
    private String description;
    
    @TableField("created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt = LocalDateTime.now();
    

    
    /**
     * 获取参数值（按类型转换）
     */
    public Object getTypedValue() {
        if (parameterValue == null) return null;
        
        try {
            switch (parameterType) {
                case INT:
                    return Integer.parseInt(parameterValue);
                case DOUBLE:
                    return Double.parseDouble(parameterValue);
                case BOOLEAN:
                    return Boolean.parseBoolean(parameterValue);
                case STRING:
                    return parameterValue;
                case JSON:
                    return parameterValue; // 需要额外的JSON解析
                default:
                    return parameterValue;
            }
        } catch (NumberFormatException e) {
            return parameterValue;
        }
    }
    
    /**
     * 设置参数值（自动检测类型）
     */
    public void setTypedValue(Object value) {
        if (value == null) {
            this.parameterValue = null;
            return;
        }
        
        if (value instanceof Integer) {
            this.parameterType = ParameterType.INT;
            this.parameterValue = value.toString();
        } else if (value instanceof Double || value instanceof Float) {
            this.parameterType = ParameterType.DOUBLE;
            this.parameterValue = value.toString();
        } else if (value instanceof Boolean) {
            this.parameterType = ParameterType.BOOLEAN;
            this.parameterValue = value.toString();
        } else if (value instanceof String) {
            this.parameterType = ParameterType.STRING;
            this.parameterValue = (String) value;
        } else {
            this.parameterType = ParameterType.JSON;
            this.parameterValue = value.toString();
        }
    }
    
    /**
     * 参数类型枚举
     */
    public enum ParameterType {
        INT("整数"),
        DOUBLE("浮点数"),
        STRING("字符串"),
        BOOLEAN("布尔值"),
        JSON("JSON对象");
        
        private final String displayName;
        
        ParameterType(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
}
