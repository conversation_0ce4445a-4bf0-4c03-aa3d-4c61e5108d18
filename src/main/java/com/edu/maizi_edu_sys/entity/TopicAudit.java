package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 题目审核实体类
 */
@Data
@TableName("topic_audit")
public class TopicAudit {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField("know_id")
    private Integer knowId;
    
    @TableField("type")
    private String type;
    
    @TableField("title")
    private String title;
    
    @TableField("options")
    private String options;
    
    @TableField("subs")
    private String subs;
    
    @TableField("answer")
    private String answer;
    
    @TableField("parse")
    private String parse;
    
    @TableField("score")
    private Integer score = 3;
    
    @TableField("source")
    private String source;
    
    @TableField("difficulty")
    private Double difficulty;
    
    @TableField("tags")
    private String tags;
    
    /**
     * 审核状态：0-待审核，1-审核通过，2-审核拒绝
     */
    @TableField("audit_status")
    private Integer auditStatus = 0;
    
    @TableField("auditor_id")
    private Long auditorId;
    
    @TableField("audit_time")
    private LocalDateTime auditTime;
    
    @TableField("audit_comment")
    private String auditComment;
    
    /**
     * 是否自动审核通过：0-否，1-是
     */
    @TableField("auto_approved")
    private Integer autoApproved = 0;
    
    @TableField("submit_time")
    private LocalDateTime submitTime;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    /**
     * 审核状态枚举
     */
    public static class AuditStatus {
        public static final int PENDING = 0;  // 待审核
        public static final int APPROVED = 1; // 审核通过
        public static final int REJECTED = 2; // 审核拒绝
    }
} 