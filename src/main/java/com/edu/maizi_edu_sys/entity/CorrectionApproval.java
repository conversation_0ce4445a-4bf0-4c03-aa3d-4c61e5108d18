package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * AI修正审核实体
 * 存储待审核的修正建议
 */
@Data
@TableName("correction_approval")
public class CorrectionApproval {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /** 修正日期 */
    private String correctionDate;
    
    /** 修正结果JSON */
    private String correctionJson;
    
    /** 修正统计信息 */
    private String statistics;
    
    /** 审核状态：0-待审核, 1-已通过, 2-已拒绝, 3-已过期 */
    private Integer status;
    
    /** 审核人 */
    private String approver;
    
    /** 审核意见 */
    private String approvalComment;
    
    /** 创建时间 */
    private LocalDateTime createdAt;
    
    /** 审核时间 */
    private LocalDateTime approvedAt;
    
    /** 过期时间 */
    private LocalDateTime expiresAt;
} 