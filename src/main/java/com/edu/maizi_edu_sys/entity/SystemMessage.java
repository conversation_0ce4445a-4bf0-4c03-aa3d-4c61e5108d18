package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 系统消息实体类
 */
@Data
@TableName("system_messages")
public class SystemMessage {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField("sender_id")
    private Long senderId;
    
    @TableField("message_type")
    private String messageType;
    
    @TableField("title")
    private String title;
    
    @TableField("content")
    private String content;
    
    @TableField("related_id")
    private Long relatedId;
    
    @TableField("related_type")
    private String relatedType;
    
    @TableField("is_read")
    private Integer isRead = 0;
    
    @TableField("read_time")
    private LocalDateTime readTime;
    
    @TableField("priority")
    private Integer priority = 1;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    /**
     * 消息类型枚举
     */
    public static class MessageType {
        public static final String AUDIT_APPROVED = "AUDIT_APPROVED";     // 审核通过
        public static final String AUDIT_REJECTED = "AUDIT_REJECTED";     // 审核拒绝
        public static final String SYSTEM_NOTICE = "SYSTEM_NOTICE";       // 系统通知
    }
    
    /**
     * 关联类型枚举
     */
    public static class RelatedType {
        public static final String TOPIC_AUDIT = "TOPIC_AUDIT";           // 题目审核
    }
    
    /**
     * 优先级枚举
     */
    public static class Priority {
        public static final int NORMAL = 1;    // 普通
        public static final int IMPORTANT = 2; // 重要
        public static final int URGENT = 3;    // 紧急
    }
} 