package com.edu.maizi_edu_sys.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * 约束处理统计实体类
 */
@Entity
@Table(name = "constraint_statistics")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConstraintStatistics {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "execution_id", nullable = false)
    private AlgorithmExecution execution;
    
    @Column(name = "generation", nullable = false)
    private Integer generation;
    
    @Column(name = "constraint_type", nullable = false, length = 64)
    private String constraintType;
    
    @Column(name = "violation_count")
    private Integer violationCount = 0;
    
    @Column(name = "repair_success_count")
    private Integer repairSuccessCount = 0;
    
    @Column(name = "repair_failure_count")
    private Integer repairFailureCount = 0;
    
    @Column(name = "penalty_value")
    private Double penaltyValue = 0.0;
    
    @Column(name = "recorded_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Temporal(TemporalType.TIMESTAMP)
    private Date recordedAt = new Date();
    
    /**
     * 预持久化操作
     */
    @PrePersist
    protected void onCreate() {
        recordedAt = new Date();
    }
    
    /**
     * 计算修复成功率
     */
    public double getRepairSuccessRate() {
        int totalRepairs = repairSuccessCount + repairFailureCount;
        return totalRepairs > 0 ? (double) repairSuccessCount / totalRepairs : 0.0;
    }
    
    /**
     * 获取约束违反率
     */
    public double getViolationRate() {
        // 这里需要根据具体业务逻辑计算
        return violationCount > 0 ? violationCount / 100.0 : 0.0;
    }
    
    /**
     * 约束类型枚举
     */
    public enum ConstraintType {
        DIFFICULTY_BALANCE("难度平衡"),
        KNOWLEDGE_COVERAGE("知识点覆盖"),
        TIME_LIMIT("时间限制"),
        SCORE_DISTRIBUTION("分值分布"),
        QUESTION_TYPE_RATIO("题型比例"),
        DUPLICATE_PREVENTION("重复防止");
        
        private final String displayName;
        
        ConstraintType(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public static ConstraintType fromDisplayName(String displayName) {
            for (ConstraintType type : ConstraintType.values()) {
                if (type.displayName.equals(displayName)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown constraint type: " + displayName);
        }
    }
}
