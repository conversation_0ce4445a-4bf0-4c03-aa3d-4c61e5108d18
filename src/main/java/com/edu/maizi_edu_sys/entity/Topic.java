package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("topic_bak")
public class Topic {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("know_id")
    private Integer knowId;

    @TableField("type")
    private String type;

    @TableField("title")
    private String title;

    @TableField("tags")
    private String tags;

    @TableField("options")
    private String options;

    @TableField("subs")
    private String subs;

    @TableField("answer")
    private String answer;

    @TableField("parse")
    private String parse;

    @TableField("score")
    private Integer score = 3;

    @TableField("source")
    private String source;

    @TableField("sort")
    private Integer sort = 1;

    @TableField("difficulty")
    private Double difficulty;

    /** 是否已校对：0=否，1=是 */
    @TableField("corrected")
    private Integer corrected;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 获取题目解析（与parse字段相同）
     * @return 题目解析内容
     */
    public String getAnalysis() {
        return this.parse;
    }
    
    /**
     * 设置题目解析（同时设置parse字段）
     * @param analysis 题目解析内容
     */
    public void setAnalysis(String analysis) {
        this.parse = analysis;
    }



    public Topic() {
        this.createdAt = LocalDateTime.now();
    }
} 