package com.edu.maizi_edu_sys.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * 种群个体数据实体类
 */
@Entity
@Table(name = "population_individual_data")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PopulationIndividualData {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "execution_id", nullable = false)
    private AlgorithmExecution execution;
    
    @Column(name = "generation", nullable = false)
    private Integer generation;
    
    @Column(name = "individual_id", nullable = false)
    private Integer individualId;
    
    @Column(name = "fitness_value", nullable = false)
    private Double fitnessValue;
    
    @Column(name = "gene_sequence", nullable = false, columnDefinition = "TEXT")
    private String geneSequence;
    
    @Column(name = "constraint_violations")
    private Integer constraintViolations = 0;
    
    @Column(name = "is_elite")
    private Boolean isElite = false;
    
    @Column(name = "recorded_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Temporal(TemporalType.TIMESTAMP)
    private Date recordedAt = new Date();
    
    /**
     * 预持久化操作
     */
    @PrePersist
    protected void onCreate() {
        recordedAt = new Date();
    }
    
    /**
     * 判断是否为可行解
     */
    public boolean isFeasible() {
        return constraintViolations == null || constraintViolations == 0;
    }
    
    /**
     * 获取适应度等级
     */
    public FitnessLevel getFitnessLevel() {
        if (fitnessValue == null) return FitnessLevel.VERY_LOW;
        
        if (fitnessValue >= 0.9) return FitnessLevel.EXCELLENT;
        if (fitnessValue >= 0.8) return FitnessLevel.GOOD;
        if (fitnessValue >= 0.7) return FitnessLevel.AVERAGE;
        if (fitnessValue >= 0.6) return FitnessLevel.BELOW_AVERAGE;
        return FitnessLevel.VERY_LOW;
    }
    
    /**
     * 适应度等级枚举
     */
    public enum FitnessLevel {
        EXCELLENT("优秀", 0.9),
        GOOD("良好", 0.8),
        AVERAGE("一般", 0.7),
        BELOW_AVERAGE("较差", 0.6),
        VERY_LOW("很差", 0.0);
        
        private final String displayName;
        private final double threshold;
        
        FitnessLevel(String displayName, double threshold) {
            this.displayName = displayName;
            this.threshold = threshold;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public double getThreshold() {
            return threshold;
        }
    }
}
