package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 文档解析结果实体类
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Data
@Accessors(chain = true)
@TableName("document_parse_result")
public class DocumentParseResult {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务ID
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 数据ID
     */
    @TableField("data_id")
    private String dataId;

    /**
     * 批次ID
     */
    @TableField("batch_id")
    private String batchId;

    /**
     * 文件名
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 文件URL
     */
    @TableField("file_url")
    private String fileUrl;

    /**
     * 解析状态
     */
    @TableField("status")
    private String status;

    /**
     * Markdown结果URL
     */
    @TableField("markdown_url")
    private String markdownUrl;

    /**
     * JSON结果URL
     */
    @TableField("json_url")
    private String jsonUrl;

    /**
     * HTML结果URL
     */
    @TableField("html_url")
    private String htmlUrl;

    /**
     * 完整ZIP包URL
     */
    @TableField("zip_url")
    private String zipUrl;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 实际页数
     */
    @TableField("actual_pages")
    private Integer actualPages;

    /**
     * 处理时长（秒）
     */
    @TableField("processing_duration")
    private Integer processingDuration;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 完成时间
     */
    @TableField("completed_at")
    private LocalDateTime completedAt;
}
