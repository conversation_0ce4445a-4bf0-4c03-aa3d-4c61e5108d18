package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 算法执行记录实体类
 */
@TableName("algorithm_execution")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlgorithmExecution {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("paper_id")
    private String paperId;
    
    @TableField("paper_name")
    private String paperName;
    
    @TableField("algorithm_type")
    private String algorithmType = "genetic";
    
    @TableField("status")
    private ExecutionStatus status;
    
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    
    @TableField("max_generations")
    private Integer maxGenerations;
    
    @TableField("target_fitness")
    private Double targetFitness = 0.95;
    
    @TableField("population_size")
    private Integer populationSize = 100;
    
    @TableField("mutation_rate")
    private Double mutationRate = 0.1;
    
    @TableField("crossover_rate")
    private Double crossoverRate = 0.8;
    
    @TableField("selection_method")
    private String selectionMethod = "tournament";
    
    @TableField("error_message")
    private String errorMessage;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt = new Date();
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt = new Date();
    
    // 一对多关系（非数据库字段）
    @TableField(exist = false)
    private List<AlgorithmGenerationData> generationData;
    
    @TableField(exist = false)
    private List<FitnessDimensionData> fitnessData;
    
    @TableField(exist = false)
    private List<PopulationIndividualData> populationData;
    
    @TableField(exist = false)
    private List<ConstraintStatistics> constraintStatistics;
    
    @TableField(exist = false)
    private List<AlgorithmPerformanceMetrics> performanceMetrics;
    
    @TableField(exist = false)
    private List<AlgorithmParameter> parameters;
    
    @TableField(exist = false)
    private List<AlgorithmEventLog> eventLogs;
    
    /**
     * 执行状态枚举
     */
    public enum ExecutionStatus {
        RUNNING("running"),
        PAUSED("paused"),
        COMPLETED("completed"),
        FAILED("failed"),
        STOPPED("stopped");
        
        private final String value;
        
        ExecutionStatus(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
        
        public static ExecutionStatus fromValue(String value) {
            for (ExecutionStatus status : ExecutionStatus.values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown status: " + value);
        }
    }
    
    /**
     * 获取运行时长（秒）
     */
    public long getRunningDurationSeconds() {
        if (startTime == null) return 0;
        Date endTime = this.endTime != null ? this.endTime : new Date();
        return (endTime.getTime() - startTime.getTime()) / 1000;
    }
    
    /**
     * 获取进度百分比
     */
    public double getProgressPercent() {
        if (generationData == null || generationData.isEmpty() || maxGenerations == null || maxGenerations <= 0) {
            return 0.0;
        }
        
        int currentGeneration = generationData.stream()
            .mapToInt(AlgorithmGenerationData::getGeneration)
            .max()
            .orElse(0);
            
        return Math.min(100.0, (double) currentGeneration / maxGenerations * 100.0);
    }
    
    /**
     * 判断是否已完成
     */
    public boolean isCompleted() {
        return status == ExecutionStatus.COMPLETED || 
               status == ExecutionStatus.FAILED || 
               status == ExecutionStatus.STOPPED;
    }
    
    /**
     * 判断是否正在运行
     */
    public boolean isRunning() {
        return status == ExecutionStatus.RUNNING;
    }
    
    /**
     * 设置创建时间
     */
    public void onCreate() {
        createdAt = new Date();
        updatedAt = new Date();
    }
    
    /**
     * 设置更新时间
     */
    public void onUpdate() {
        updatedAt = new Date();
    }
}
