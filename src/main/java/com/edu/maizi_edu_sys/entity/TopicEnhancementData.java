package com.edu.maizi_edu_sys.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;

/**
 * 题目增强数据实体类
 * 用于存储题目的认知层次等信息
 */
@Data
@TableName("topic_enhancement_data")
public class TopicEnhancementData {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 题目ID
     */
    @TableField("topic_id")
    private Integer topicId;

    /**
     * 认知层次
     * 可选值：理解、应用、分析、评价
     * @deprecated 此字段将在未来版本移除，保留是为了向后兼容
     */
    @Deprecated
    @TableField("cognitive_level")
    private String cognitiveLevel;

    /**
     * 使用次数
     */
    @TableField("usage_count")
    private Integer usageCount = 0;
    
    /**
     * 最后使用时间
     */
    @TableField("last_used_time")
    private LocalDateTime lastUsedTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime = LocalDateTime.now();

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime = LocalDateTime.now();
}