package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 试卷下载记录实体类
 */
@Data
@TableName("paper_downloads")
public class PaperDownload {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID - 对应数据库的bigint unsigned
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 试卷ID
     */
    @TableField("paper_id")
    private Long paperId;

    /**
     * 下载时间
     */
    @TableField("download_time")
    private LocalDateTime downloadTime;

    /**
     * 下载IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 下载格式：pdf, docx
     */
    @TableField("file_format")
    private String fileFormat;
}