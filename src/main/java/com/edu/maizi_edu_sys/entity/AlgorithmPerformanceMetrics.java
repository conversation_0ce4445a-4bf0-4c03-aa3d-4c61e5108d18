package com.edu.maizi_edu_sys.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * 算法性能监控实体类
 */
@Entity
@Table(name = "algorithm_performance_metrics")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlgorithmPerformanceMetrics {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "execution_id", nullable = false)
    private AlgorithmExecution execution;
    
    @Column(name = "generation", nullable = false)
    private Integer generation;
    
    @Column(name = "cpu_usage", nullable = false)
    private Double cpuUsage;
    
    @Column(name = "memory_usage", nullable = false)
    private Double memoryUsage;
    
    @Column(name = "generation_time", nullable = false)
    private Long generationTime;
    
    @Column(name = "evaluation_time", nullable = false)
    private Long evaluationTime;
    
    @Column(name = "selection_time", nullable = false)
    private Long selectionTime;
    
    @Column(name = "crossover_time", nullable = false)
    private Long crossoverTime;
    
    @Column(name = "mutation_time", nullable = false)
    private Long mutationTime;
    
    @Column(name = "recorded_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Temporal(TemporalType.TIMESTAMP)
    private Date recordedAt = new Date();
    
    /**
     * 预持久化操作
     */
    @PrePersist
    protected void onCreate() {
        recordedAt = new Date();
    }
    
    /**
     * 计算总执行时间
     */
    public long getTotalExecutionTime() {
        return evaluationTime + selectionTime + crossoverTime + mutationTime;
    }
    
    /**
     * 计算操作时间比例
     */
    public OperationTimeRatio getOperationTimeRatio() {
        long total = getTotalExecutionTime();
        if (total == 0) return new OperationTimeRatio(0, 0, 0, 0);
        
        return new OperationTimeRatio(
            (double) evaluationTime / total * 100,
            (double) selectionTime / total * 100,
            (double) crossoverTime / total * 100,
            (double) mutationTime / total * 100
        );
    }
    
    /**
     * 获取性能等级
     */
    public PerformanceLevel getPerformanceLevel() {
        if (generationTime < 100) return PerformanceLevel.EXCELLENT;
        if (generationTime < 500) return PerformanceLevel.GOOD;
        if (generationTime < 1000) return PerformanceLevel.AVERAGE;
        if (generationTime < 2000) return PerformanceLevel.BELOW_AVERAGE;
        return PerformanceLevel.POOR;
    }
    
    /**
     * 操作时间比例
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OperationTimeRatio {
        private double evaluationRatio;
        private double selectionRatio;
        private double crossoverRatio;
        private double mutationRatio;
    }
    
    /**
     * 性能等级枚举
     */
    public enum PerformanceLevel {
        EXCELLENT("优秀", 100),
        GOOD("良好", 500),
        AVERAGE("一般", 1000),
        BELOW_AVERAGE("较差", 2000),
        POOR("很差", Long.MAX_VALUE);
        
        private final String displayName;
        private final long threshold;
        
        PerformanceLevel(String displayName, long threshold) {
            this.displayName = displayName;
            this.threshold = threshold;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public long getThreshold() {
            return threshold;
        }
    }
}
