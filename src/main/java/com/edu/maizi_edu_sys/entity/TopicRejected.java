package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 被拒绝题目实体类
 */
@Data
@TableName("topic_rejected")
public class TopicRejected {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("audit_id")
    private Long auditId;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField("know_id")
    private Integer knowId;
    
    @TableField("type")
    private String type;
    
    @TableField("title")
    private String title;
    
    @TableField("options")
    private String options;
    
    @TableField("subs")
    private String subs;
    
    @TableField("answer")
    private String answer;
    
    @TableField("parse")
    private String parse;
    
    @TableField("score")
    private Integer score = 3;
    
    @TableField("source")
    private String source;
    
    @TableField("difficulty")
    private Double difficulty;
    
    @TableField("tags")
    private String tags;
    
    @TableField("auditor_id")
    private Long auditorId;
    
    @TableField("reject_reason")
    private String rejectReason;
    
    @TableField("reject_time")
    private LocalDateTime rejectTime;
    
    @TableField("submit_time")
    private LocalDateTime submitTime;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
} 