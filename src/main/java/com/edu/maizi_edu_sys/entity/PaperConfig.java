package com.edu.maizi_edu_sys.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;

/**
 * 试卷配置实体类
 */
@TableName("paper_configs")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaperConfig {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 配置名称（用户自定义）
     */
    @TableField("config_name")
    private String configName;

    /**
     * 配置描述
     */
    @TableField("description")
    private String description;

    /**
     * 用户ID（配置所有者）
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 试卷标题模板
     */
    @TableField("title_template")
    private String titleTemplate;

    /**
     * 试卷类型
     */
    @TableField("paper_type")
    private String paperType = "standard";

    /**
     * 生成套数
     */
    @TableField("paper_count")
    private Integer paperCount = 1;

    /**
     * 单选题配置
     */
    @TableField("single_choice_count")
    private Integer singleChoiceCount = 0;

    @TableField("single_choice_score")
    private Integer singleChoiceScore = 0;

    /**
     * 多选题配置
     */
    @TableField("multiple_choice_count")
    private Integer multipleChoiceCount = 0;

    @TableField("multiple_choice_score")
    private Integer multipleChoiceScore = 0;

    /**
     * 判断题配置
     */
    @TableField("judgment_count")
    private Integer judgmentCount = 0;

    @TableField("judgment_score")
    private Integer judgmentScore = 0;

    /**
     * 填空题配置
     */
    @TableField("fill_count")
    private Integer fillCount = 0;

    @TableField("fill_score")
    private Integer fillScore = 0;

    /**
     * 简答题配置
     */
    @TableField("short_answer_count")
    private Integer shortAnswerCount = 0;

    @TableField("short_answer_score")
    private Integer shortAnswerScore = 0;

    /**
     * 难度分布配置（JSON格式）
     */
    @TableField("difficulty_distribution")
    private String difficultyDistribution;

    /**
     * 知识点配置（JSON格式）
     */
    @TableField("knowledge_point_configs")
    private String knowledgePointConfigs;

    /**
     * 是否为默认配置
     */
    @TableField("is_default")
    private Boolean isDefault = false;

    /**
     * 是否为公共配置（所有用户可见）
     */
    @TableField("is_public")
    private Boolean isPublic = false;

    /**
     * 使用次数
     */
    @TableField("usage_count")
    private Integer usageCount = 0;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 最后使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("last_used_at")
    private LocalDateTime lastUsedAt;

    /**
     * 计算总分
     */
    public Integer getTotalScore() {
        return (singleChoiceCount * singleChoiceScore) +
               (multipleChoiceCount * multipleChoiceScore) +
               (judgmentCount * judgmentScore) +
               (fillCount * fillScore) +
               (shortAnswerCount * shortAnswerScore);
    }

    /**
     * 计算总题数
     */
    public Integer getTotalQuestions() {
        return singleChoiceCount + multipleChoiceCount + judgmentCount + fillCount + shortAnswerCount;
    }
}
