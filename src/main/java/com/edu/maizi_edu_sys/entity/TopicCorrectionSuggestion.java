package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 题目修正建议实体类
 */
@Data
@TableName("topic_correction_suggestion")
public class TopicCorrectionSuggestion {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("topic_id")
    private Long topicId;
    
    @TableField("correction_reason")
    private String correctionReason;
    
    @TableField("suggested_updates")
    private String suggestedUpdates;
    
    @TableField("ai_response")
    private String aiResponse;
    
    @TableField("batch_id")
    private String batchId;
    
    @TableField("status")
    private String status;
    
    @TableField("created_at")
    private LocalDateTime createdAt;
    
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}