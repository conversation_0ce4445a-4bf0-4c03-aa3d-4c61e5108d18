package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * MineRU API使用情况跟踪实体
 * 
 * <AUTHOR> Integration
 * @since 2025-01-20
 */
@Data
@TableName("mineru_api_usage")
public class MineRuApiUsage {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * API密钥名称
     */
    @TableField("token_name")
    private String tokenName;
    
    /**
     * 使用日期
     */
    @TableField("usage_date")
    private LocalDate usageDate;
    
    /**
     * 当日已解析页数
     */
    @TableField("parsed_pages")
    private Integer parsedPages;
    
    /**
     * 当日请求次数
     */
    @TableField("request_count")
    private Integer requestCount;
    
    /**
     * 当日成功次数
     */
    @TableField("success_count")
    private Integer successCount;
    
    /**
     * 当日失败次数
     */
    @TableField("failure_count")
    private Integer failureCount;
    
    /**
     * 是否超出高优先级限制
     */
    @TableField("exceeded_priority_limit")
    private Boolean exceededPriorityLimit;
    
    /**
     * 最后使用时间
     */
    @TableField("last_used_at")
    private LocalDateTime lastUsedAt;
    
    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    /**
     * 备注信息
     */
    @TableField("remarks")
    private String remarks;
}
