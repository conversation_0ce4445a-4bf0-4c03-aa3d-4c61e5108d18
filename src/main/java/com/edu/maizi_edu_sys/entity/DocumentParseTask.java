package com.edu.maizi_edu_sys.entity;

import lombok.Data;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

/**
 * 文档解析任务实体
 * 
 * <AUTHOR> Integration
 * @since 2025-01-20
 */
@Data
@Accessors(chain = true)
@TableName("document_parse_task")
public class DocumentParseTask {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务ID (MineRU API返回)
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 数据ID (MineRU API返回)
     */
    @TableField("data_id")
    private String dataId;

    /**
     * 批次ID (批量处理时)
     */
    @TableField("batch_id")
    private String batchId;

    /**
     * 文件URL
     */
    @TableField("file_url")
    private String fileUrl;

    /**
     * 文件名
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 文件类型
     */
    @TableField("file_type")
    private String fileType;

    /**
     * 预估页数
     */
    @TableField("estimated_pages")
    private Integer estimatedPages;

    /**
     * 实际页数
     */
    @TableField("actual_pages")
    private Integer actualPages;

    /**
     * 任务状态
     */
    @TableField("status")
    private String status;

    /**
     * 解析配置（JSON）
     */
    @TableField("parse_config")
    private String parseConfig;

    /**
     * 结果数据（JSON）
     */
    @TableField("result_data")
    private String resultData;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 结果文件URL
     */
    @TableField("result_url")
    private String resultUrl;

    /**
     * 压缩包URL
     */
    @TableField("zip_url")
    private String zipUrl;

    /**
     * 使用的API密钥
     */
    @TableField("used_token")
    private String usedToken;

    /**
     * 处理开始时间
     */
    @TableField("started_at")
    private LocalDateTime startedAt;

    /**
     * 处理完成时间
     */
    @TableField("completed_at")
    private LocalDateTime completedAt;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Boolean isDeleted;
}
