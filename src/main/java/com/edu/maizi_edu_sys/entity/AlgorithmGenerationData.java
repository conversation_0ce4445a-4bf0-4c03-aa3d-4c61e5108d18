package com.edu.maizi_edu_sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;

/**
 * 算法代数进化数据实体类
 */
@TableName("algorithm_generation_data")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlgorithmGenerationData {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("execution_id")
    private Long executionId;
    
    @TableField(exist = false)
    private AlgorithmExecution execution;
    
    @TableField("generation")
    private Integer generation;
    
    @TableField("best_fitness")
    private Double bestFitness;
    
    @TableField("avg_fitness")
    private Double avgFitness;
    
    @TableField("worst_fitness")
    private Double worstFitness;
    
    @TableField("diversity_index")
    private Double diversityIndex;
    
    @TableField("convergence_speed")
    private Double convergenceSpeed;
    
    @TableField("constraint_violations")
    private Integer constraintViolations = 0;
    
    @TableField("fitness_std_dev")
    private Double fitnessStdDev;
    
    @TableField(value = "recorded_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recordedAt = new Date();
    
    /**
     * 设置记录时间
     */
    public void onCreate() {
        recordedAt = new Date();
    }
    
    /**
     * 计算适应度改进率
     */
    public double getFitnessImprovement() {
        if (worstFitness == null || bestFitness == null || worstFitness == 0) {
            return 0.0;
        }
        return (bestFitness - worstFitness) / worstFitness;
    }
    
    /**
     * 判断是否收敛
     */
    public boolean isConverged(double threshold) {
        return bestFitness >= threshold;
    }
}
