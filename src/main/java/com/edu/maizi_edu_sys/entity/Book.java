package com.edu.maizi_edu_sys.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.time.LocalDateTime;

@Data
@TableName("books")
public class Book {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("know_id")
    private Integer knowId;

    @TableField("title")
    private String title;

    @TableField("type")
    private String type;

    @TableField("url")
    private String url;

    @TableField("description")
    private String description;

    @TableField("user_id")
    private Long userId;

    @JsonIgnore
    @TableField(exist = false)
    private User user;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("created_at")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}