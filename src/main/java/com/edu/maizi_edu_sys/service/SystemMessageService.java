package com.edu.maizi_edu_sys.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.edu.maizi_edu_sys.entity.SystemMessage;

/**
 * 系统消息服务接口
 */
public interface SystemMessageService extends IService<SystemMessage> {

    /**
     * 发送审核通过消息
     */
    void sendAuditApprovedMessage(Long userId, Long auditId, String topicTitle);

    /**
     * 发送审核拒绝消息
     */
    void sendAuditRejectedMessage(Long userId, Long auditId, String topicTitle, String rejectReason);

    /**
     * 发送系统通知消息
     */
    void sendSystemNotice(Long userId, String title, String content);

    /**
     * 获取用户消息列表（分页）
     */
    IPage<SystemMessage> getUserMessages(Long userId, int pageNum, int pageSize);

    /**
     * 获取用户未读消息数量
     */
    Long getUnreadCount(Long userId);

    /**
     * 标记消息为已读
     */
    boolean markAsRead(Long messageId, Long userId);

    /**
     * 标记所有消息为已读
     */
    boolean markAllAsRead(Long userId);
} 