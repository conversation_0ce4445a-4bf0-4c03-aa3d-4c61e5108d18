package com.edu.maizi_edu_sys.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.edu.maizi_edu_sys.dto.AuditRequestDTO;
import com.edu.maizi_edu_sys.dto.TopicAuditDTO;
import com.edu.maizi_edu_sys.dto.TopicDTO;
import com.edu.maizi_edu_sys.dto.UserDailyAuditGroupDTO;
import com.edu.maizi_edu_sys.entity.TopicAudit;

import java.util.List;
import java.util.Map;

/**
 * 题目审核服务接口
 */
public interface TopicAuditService extends IService<TopicAudit> {

    /**
     * 提交题目审核
     */
    void submitTopicForAudit(TopicDTO topicDTO, Long userId);

    /**
     * 批量提交题目审核
     */
    void submitTopicsForAudit(List<TopicDTO> topicDTOs, Long userId);

    /**
     * 获取待审核题目列表（分页）
     */
    IPage<TopicAuditDTO> getPendingAudits(int pageNum, int pageSize);

    /**
     * 获取待审核题目列表（分页，支持搜索和筛选）
     */
    IPage<TopicAuditDTO> getPendingAudits(int pageNum, int pageSize, String keyword, String submitter, String knowledge);

    /**
     * 获取用户的审核记录（分页）
     */
    IPage<TopicAuditDTO> getUserAudits(Long userId, int pageNum, int pageSize);

    /**
     * 获取审核员的审核记录（分页）
     */
    IPage<TopicAuditDTO> getAuditorAudits(Long auditorId, int pageNum, int pageSize);

    /**
     * 获取已通过的审核记录（分页）
     */
    IPage<TopicAuditDTO> getApprovedAudits(int pageNum, int pageSize);

    /**
     * 获取已拒绝的审核记录（分页）
     */
    IPage<TopicAuditDTO> getRejectedAudits(int pageNum, int pageSize);

    /**
     * 获取审核详情
     */
    TopicAuditDTO getAuditDetail(Long auditId);

    /**
     * 获取所有待审核的题目ID列表
     */
    List<Long> getAllPendingAuditIds();

    /**
     * 审核题目（通过或拒绝）
     */
    void auditTopic(AuditRequestDTO auditRequest, Long auditorId);

    /**
     * 获取审核统计信息
     */
    Map<String, Object> getAuditStatistics();

    /**
     * 获取用户待审核题目数量
     */
    Long getUserPendingCount(Long userId);

    /**
     * 检查用户是否有审核权限
     */
    boolean hasAuditPermission(Long userId);

    /**
     * 获取按用户和日期分组的审核数据
     */
    IPage<UserDailyAuditGroupDTO> getUserDailyAuditGroups(int pageNum, int pageSize, String keyword);

    /**
     * 根据用户和日期获取审核详情
     */
    List<TopicAuditDTO> getAuditsByUserAndDate(String userId, String date);

    /**
     * 批量审核用户某日的所有题目
     */
    void batchAuditUserDaily(String userId, String date, int auditResult, String comment, Long auditorId);

    /**
     * 简化的批量审核方法 - 用于调试
     */
    void simpleBatchAuditUserDaily(String userId, String date, int auditResult, String comment, Long auditorId);

    /**
     * 获取所有待审核题目（不分页）
     */
    List<TopicAuditDTO> getAllPendingAudits();

    /**
     * 根据用户ID获取待审核题目
     */
    List<TopicAuditDTO> getPendingAuditsByUserId(Long userId);

    /**
     * 优化的批量审核方法 - 通过审核ID列表
     * @param auditIds 审核ID列表
     * @param auditResult 审核结果 (1-通过, 2-拒绝)
     * @param comment 审核意见
     * @param auditorId 审核员ID
     * @return 包含成功和失败数量的结果Map
     */
    Map<String, Integer> batchAuditByIds(List<Long> auditIds, int auditResult, String comment, Long auditorId);

    /**
     * 获取提交者列表
     * @return 提交者列表，包含id、username、count等信息
     */
    List<Map<String, Object>> getSubmitterList();

    /**
     * 获取知识点列表
     * @return 知识点列表，包含id、name、count等信息
     */
    List<Map<String, Object>> getKnowledgePointList();

    /**
     * 导出审核数据
     * @param outputStream 输出流
     * @param keyword 关键词
     * @param submitter 提交者
     * @param knowledge 知识点
     * @param status 状态
     */
    void exportAuditData(java.io.OutputStream outputStream, String keyword, String submitter, String knowledge, String status);

    /**
     * 调试特定用户的数据 - 查看为什么查询返回空结果
     * @param userId 用户ID
     * @return 调试信息
     */
    Map<String, Object> debugSpecificUserData(String userId);

    /**
     * 调试所有有审核记录的用户数据
     * @return 调试信息
     */
    Map<String, Object> debugAllUsersWithAudits();

    /**
     * 撤销审核
     * @param auditId 审核记录ID
     * @param operatorId 操作员ID
     * @throws IllegalArgumentException 如果审核记录不存在或状态不允许撤销
     * @throws RuntimeException 如果撤销过程中发生错误
     */
    void revertAudit(Long auditId, Long operatorId);
} 