package com.edu.maizi_edu_sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.maizi_edu_sys.dto.*;
import com.edu.maizi_edu_sys.entity.TopicAudit;
import com.edu.maizi_edu_sys.entity.User;
import com.edu.maizi_edu_sys.repository.TopicAuditMapper;
import com.edu.maizi_edu_sys.mapper.UserMapper;
import com.edu.maizi_edu_sys.service.AdminAuditService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 管理员审核服务实现类
 */
@Service
@RequiredArgsConstructor
public class AdminAuditServiceImpl implements AdminAuditService {
    
    private final TopicAuditMapper topicAuditMapper;
    private final UserMapper userMapper;
    
    @Override
    public AdminAuditStatsDTO getAuditStats() {
        AdminAuditStatsDTO stats = new AdminAuditStatsDTO();
        
        // 待审核数量
        Long pending = topicAuditMapper.selectCount(
            new QueryWrapper<TopicAudit>().eq("audit_status", TopicAudit.AuditStatus.PENDING)
        );
        
        // 已通过数量
        Long approved = topicAuditMapper.selectCount(
            new QueryWrapper<TopicAudit>().eq("audit_status", TopicAudit.AuditStatus.APPROVED)
        );
        
        // 已拒绝数量
        Long rejected = topicAuditMapper.selectCount(
            new QueryWrapper<TopicAudit>().eq("audit_status", TopicAudit.AuditStatus.REJECTED)
        );
        
        stats.setPending(pending);
        stats.setApproved(approved);
        stats.setRejected(rejected);
        stats.setTotal(pending + approved + rejected);
        
        return stats;
    }
    
    @Override
    public IPage<AdminAuditItemDTO> getPendingAudits(int pageNum, int pageSize, String search) {
        return getAuditsByStatus(TopicAudit.AuditStatus.PENDING, pageNum, pageSize, search);
    }
    
    @Override
    public IPage<AdminAuditItemDTO> getApprovedAudits(int pageNum, int pageSize, String search) {
        return getAuditsByStatus(TopicAudit.AuditStatus.APPROVED, pageNum, pageSize, search);
    }
    
    @Override
    public IPage<AdminAuditItemDTO> getRejectedAudits(int pageNum, int pageSize, String search) {
        return getAuditsByStatus(TopicAudit.AuditStatus.REJECTED, pageNum, pageSize, search);
    }
    
    @Override
    public IPage<AdminAuditItemDTO> getAuditRecords(int pageNum, int pageSize, String search) {
        Page<TopicAudit> page = new Page<>(pageNum, pageSize);
        QueryWrapper<TopicAudit> queryWrapper = new QueryWrapper<>();
        
        // 搜索条件
        if (StringUtils.hasText(search)) {
            queryWrapper.and(wrapper -> 
                wrapper.like("title", search)
                       .or()
                       .like("tags", search)
            );
        }
        
        queryWrapper.orderByDesc("submit_time");
        
        IPage<TopicAudit> auditPage = topicAuditMapper.selectPage(page, queryWrapper);
        return convertToAdminAuditItemDTO(auditPage);
    }
    
    private IPage<AdminAuditItemDTO> getAuditsByStatus(int status, int pageNum, int pageSize, String search) {
        Page<TopicAudit> page = new Page<>(pageNum, pageSize);
        QueryWrapper<TopicAudit> queryWrapper = new QueryWrapper<>();
        
        queryWrapper.eq("audit_status", status);
        
        // 搜索条件
        if (StringUtils.hasText(search)) {
            queryWrapper.and(wrapper -> 
                wrapper.like("title", search)
                       .or()
                       .like("tags", search)
            );
        }
        
        queryWrapper.orderByDesc("submit_time");
        
        IPage<TopicAudit> auditPage = topicAuditMapper.selectPage(page, queryWrapper);
        return convertToAdminAuditItemDTO(auditPage);
    }
    
    private IPage<AdminAuditItemDTO> convertToAdminAuditItemDTO(IPage<TopicAudit> auditPage) {
        // 获取所有相关用户信息
        List<Long> userIds = auditPage.getRecords().stream()
            .flatMap(audit -> Stream.of(audit.getUserId(), audit.getAuditorId()))
            .filter(id -> id != null)
            .distinct()
            .collect(Collectors.toList());
        
        List<User> users = userMapper.selectBatchIds(userIds);
        Map<Long, User> userMap = users.stream()
            .collect(Collectors.toMap(User::getId, Function.identity()));
        
        // 转换为DTO
        Page<AdminAuditItemDTO> dtoPage = new Page<>(auditPage.getCurrent(), auditPage.getSize(), auditPage.getTotal());
        List<AdminAuditItemDTO> dtoList = auditPage.getRecords().stream()
            .map(audit -> convertToAdminAuditItemDTO(audit, userMap))
            .collect(Collectors.toList());
        
        dtoPage.setRecords(dtoList);
        return dtoPage;
    }
    
    private AdminAuditItemDTO convertToAdminAuditItemDTO(TopicAudit audit, Map<Long, User> userMap) {
        AdminAuditItemDTO dto = new AdminAuditItemDTO();
        
        dto.setId(audit.getId());
        dto.setTopicId(audit.getId()); // 题目ID等于审核ID
        dto.setTopicTitle(audit.getTitle());
        dto.setTopicContent(audit.getTitle()); // 题目内容使用标题
        dto.setTopicType(audit.getType());
        dto.setKnowledgePoint(getKnowledgePointName(audit.getKnowId()));
        dto.setSubmitterId(audit.getUserId());
        dto.setStatus(audit.getAuditStatus());
        dto.setAuditComment(audit.getAuditComment());
        dto.setAuditorId(audit.getAuditorId());
        dto.setSubmitTime(audit.getSubmitTime());
        dto.setAuditTime(audit.getAuditTime());
        dto.setAuditResult(audit.getAuditStatus());
        
        // 设置用户名
        User submitter = userMap.get(audit.getUserId());
        if (submitter != null) {
            dto.setSubmitterName(submitter.getUsername());
        }
        
        User auditor = userMap.get(audit.getAuditorId());
        if (auditor != null) {
            dto.setAuditorName(auditor.getUsername());
        }
        
        return dto;
    }
    
    private String getKnowledgePointName(Integer knowId) {
        // 这里应该查询知识点表获取名称，暂时返回ID
        return knowId != null ? "知识点" + knowId : "未分类";
    }
    
    @Override
    public AdminAuditItemDTO getAuditDetail(Long auditId) {
        TopicAudit audit = topicAuditMapper.selectById(auditId);
        if (audit == null) {
            throw new RuntimeException("审核记录不存在");
        }
        
        // 获取用户信息
        Map<Long, User> userMap = Collections.emptyMap();
        if (audit.getUserId() != null || audit.getAuditorId() != null) {
            List<Long> userIds = Stream.of(audit.getUserId(), audit.getAuditorId())
                .filter(id -> id != null).distinct().collect(Collectors.toList());
            List<User> users = userMapper.selectBatchIds(userIds);
            userMap = users.stream()
                .collect(Collectors.toMap(User::getId, Function.identity()));
        }
        
        return convertToAdminAuditItemDTO(audit, userMap);
    }
    
    @Override
    @Transactional
    public void submitAuditResult(AdminAuditSubmitDTO auditSubmitDTO, Long auditorId) {
        TopicAudit audit = topicAuditMapper.selectById(auditSubmitDTO.getAuditId());
        if (audit == null) {
            throw new RuntimeException("审核记录不存在");
        }
        
        if (audit.getAuditStatus() != TopicAudit.AuditStatus.PENDING) {
            throw new RuntimeException("该题目已审核，无法重复审核");
        }
        
        audit.setAuditStatus(auditSubmitDTO.getAuditResult());
        audit.setAuditComment(auditSubmitDTO.getAuditComment());
        audit.setAuditorId(auditorId);
        audit.setAuditTime(LocalDateTime.now());
        
        topicAuditMapper.updateById(audit);
    }
    
    @Override
    @Transactional
    public void quickApprove(Long auditId, Long auditorId) {
        AdminAuditSubmitDTO submitDTO = new AdminAuditSubmitDTO();
        submitDTO.setAuditId(auditId);
        submitDTO.setAuditResult(TopicAudit.AuditStatus.APPROVED);
        submitDTO.setAuditComment("快速通过");
        
        submitAuditResult(submitDTO, auditorId);
    }
    
    @Override
    @Transactional
    public void quickReject(Long auditId, String reason, Long auditorId) {
        AdminAuditSubmitDTO submitDTO = new AdminAuditSubmitDTO();
        submitDTO.setAuditId(auditId);
        submitDTO.setAuditResult(TopicAudit.AuditStatus.REJECTED);
        submitDTO.setAuditComment(reason);
        
        submitAuditResult(submitDTO, auditorId);
    }
    
    @Override
    @Transactional
    public void batchAudit(List<Long> auditIds, AdminBatchAuditDTO batchAuditDTO, Long auditorId) {
        if (auditIds == null || auditIds.isEmpty()) {
            throw new RuntimeException("审核ID列表不能为空");
        }
        
        Integer result = "approve".equals(batchAuditDTO.getAction()) ? 
            TopicAudit.AuditStatus.APPROVED : TopicAudit.AuditStatus.REJECTED;
        
        for (Long auditId : auditIds) {
            AdminAuditSubmitDTO submitDTO = new AdminAuditSubmitDTO();
            submitDTO.setAuditId(auditId);
            submitDTO.setAuditResult(result);
            submitDTO.setAuditComment(batchAuditDTO.getComment());
            
            try {
                submitAuditResult(submitDTO, auditorId);
            } catch (Exception e) {
                // 批量操作中的单个失败不影响其他项目
                System.err.println("批量审核失败，审核ID: " + auditId + ", 错误: " + e.getMessage());
            }
        }
    }
} 