package com.edu.maizi_edu_sys.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.edu.maizi_edu_sys.entity.SystemConfig;
import com.edu.maizi_edu_sys.mapper.SystemConfigMapper;
import com.edu.maizi_edu_sys.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;

/**
 * 系统配置服务实现类
 */
@Service
@Slf4j
public class SystemConfigServiceImpl extends ServiceImpl<SystemConfigMapper, SystemConfig> implements SystemConfigService {
    
    @Autowired
    private SystemConfigMapper systemConfigMapper;
    
    // 配置键常量
    private static final String TOPIC_CORRECTION_PROMPT = "topic_correction_prompt";
    private static final String TOPIC_CORRECTION_LAST_PROCESSED_ID = "topic_correction_last_processed_id";
    private static final String TOPIC_CORRECTION_RETRY_COUNT_PREFIX = "topic_correction_retry_count_";
    
    // 重试阈值常量
    private static final int MAX_RETRY_COUNT = 3;
    
    // 默认提示词
    private static final String DEFAULT_CORRECTION_PROMPT = "你是一位顶级的教育内容质量控制专家，任务是充当自动化校对系统，审查数据库中的教育题目。\n" +
            "\n" +
            "你的核心目标是找出题目中的任何错误，并按照严格的JSON格式返回需要修正的内容，以便程序能自动更新数据库。\n" +
            "\n" +
            "## 核心任务与输出格式规范\n" +
            "\n" +
            "你将收到一个包含多道题目的JSON数组。请仔细审查每一道题。\n" +
            "\n" +
            "### 1. 如果发现错误：\n" +
            "\n" +
            "你 **必须** 返回一个JSON数组，其中包含一个或多个对象。每个对象代表一道需要修正的题目。\n" +
            "每个对象 **必须** 包含三个键：\n" +
            "\n" +
            "- **\"id\"**: 题目的整数ID，用于定位数据库记录\n" +
            "- **\"reason\"**: 一个字符串，用一句话清晰地概括你做出修改的核心原因。例如：\"原答案错误\"、\"解析不准确\"、\"题干有错别字\"或\"题目表述不清\"\n" +
            "- **\"updates\"**: 一个对象，其中 **只包含** 需要被修改的字段和它们的新值。绝对不要包含没有改动的字段\n" +
            "\n" +
            "**重要**：updates 对象中可包含的字段为: `type`, `title`, `options`, `subs`, `answer`, `parse`, `difficulty`\n" +
            "\n" +
            "⚠\uFE0F **特别注意 `options` / `subs` 的格式**：\n" +
            "- 这两个字段最终会以 JSON 字符串形式存入数据库，但 **调用方在 updates 中应直接提供合法的 JSON 数组/对象**，系统会自动序列化。\n" +
            "- 正确示例：`\"options\": [{\"key\":\"A\",\"name\":\"选项A\"},{\"key\":\"B\",\"name\":\"选项B\"}]`\n" +
            "\n" +
            "**若 updates 对象为空（即没有字段需要修改），请不要返回该题。**\n" +
            "\n" +
            "### 输出示例：\n" +
            "\n" +
            "```json\n" +
            "[\n" +
            "  {\n" +
            "    \"id\": 275735,\n" +
            "    \"reason\": \"多选题答案字母顺序错误，且解析不够详细\",\n" +
            "    \"updates\": {\n" +
            "      \"answer\": \"ACD\",\n" +
            "      \"options\": [{\"key\":\"A\",\"name\":\"正确选项A\"},{\"key\":\"B\",\"name\":\"错误选项B\"},{\"key\":\"C\",\"name\":\"正确选项C\"},{\"key\":\"D\",\"name\":\"正确选项D\"}],\n" +
            "      \"parse\": \"【修正后的解析】A项正确，信息具有价值相对性，其价值因人、因时、因地而异。B项错误，信息虽然可以共享，但在传播过程中可能会损耗或失真。C项正确，信息具有时效性，会随着时间推移而失去或降低价值。D项正确，信息具有载体依附性，必须通过某种形式的数据来承载。\"\n" +
            "    }\n" +
            "  }\n" +
            "]\n" +
            "```\n" +
            "\n" +
            "**失败示例（错误写法，请勿模仿）**：\n" +
            "\n" +
            "```json\n" +
            "{\n" +
            "  \"id\": 1,\n" +
            "  \"reason\": \"示例\"\n" +
            "}\n" +
            "```\n" +
            "\n" +
            "### 2. 如果没有发现任何错误：\n" +
            "\n" +
            "如果输入的所有题目都完美无缺，你 **必须** 返回一个空的JSON数组 `[]`。\n" +
            "\n" +
            "## 核心审查清单\n" +
            "\n" +
            "### 内容与逻辑错误检查\n" +
            "\n" +
            "#### 题干 (title) 检查：\n" +
            "- 是否有错别字、语病、标点符号错误\n" +
            "- 表述是否清晰明确，避免歧义\n" +
            "- 提问方式是否恰当（\"以下哪个\"、\"关于...下列说法正确的是\"等）\n" +
            "- 是否包含不必要的重复词汇\n" +
            "\n" +
            "#### 选项 (options) 检查：\n" +
            "- 每个选项是否有错别字或语法错误\n" +
            "- 选项长度是否适中，表述是否简洁\n" +
            "- 单选题：是否只有一个明确正确答案\n" +
            "- 多选题：是否有2-4个正确选项，且选项间没有重复或矛盾\n" +
            "- 选项是否具有合理的迷惑性（错误选项应该看似合理）\n" +
            "- options格式：必须是有效的JSON数组格式\n" +
            "- **选项与答案必须匹配**（答案字母必须全部出现在 options.key 中）\n" +
            "\n" +
            "#### 答案 (answer) 检查：\n" +
            "- 答案是否正确回应了题干问题\n" +
            "- 答案与解析结论是否完全一致\n" +
            "- 格式规范：\n" +
            "  - 单选题 (choice)：必须是单个大写字母，如 \"A\"、\"B\"、\"C\"、\"D\"，或者 \"E\" **长度必须为 1**\n" +
            "  - 多选题 (multiple)：必须是大写字母且 **按字母升序排列**，如 \"ACD\"（不能是 \"CAD\"），**正确选项数量 2-5 个**\n" +
            "  - 判断题 (judge)：必须是 \"是\" 或 \"否\"（必须使用中文全角字符）\n" +
            "\n" +
            "#### 解析 (parse) 检查：\n" +
            "- 是否清晰解释了正确答案为什么正确\n" +
            "- 是否解释了错误选项为什么错误\n" +
            "- 引用的知识点、事实、数据是否准确\n" +
            "- 解析是否专业、详尽，能帮助学生理解\n" +
            "- 避免仅仅重复答案，要有教育价值\n" +
            "\n" +
            "### 格式与规范检查\n" +
            "\n" +
            "#### 题目类型 (type) 检查：\n" +
            "- **choice**：单选题，答案为单个字母\n" +
            "- **multiple**：多选题，答案为多个字母\n" +
            "- **judge**：判断题，答案为\"是\"或\"否\"\n" +
            "- type是否与实际题目形式匹配\n" +
            "\n" +
            "#### 难度 (difficulty) 评估：\n" +
            "- 取值范围：0.1 - 1.0（**保留一位小数**）\n" +
            "- 参考标准：\n" +
            "  - 0.1-0.3：基础概念题，直接记忆类\n" +
            "  - 0.4-0.6：理解应用题，需要思考分析\n" +
            "  - 0.7-0.9：综合分析题，需要深度理解\n" +
            "  - 1.0：极难题，需要创新思维\n" +
            "\n" +
            "### 常见错误类型示例\n" +
            "\n" +
            "1. **答案格式错误**：\n" +
            "   - 多选题答案 \"CAD\" → \"ACD\"\n" +
            "   - 单选题答案 \"ab\" → \"A\"\n" +
            "\n" +
            "2. **题型不匹配**：\n" +
            "   - 答案是 \"AC\" 但 type 是 \"choice\" → type 应为 \"multiple\"\n" +
            "\n" +
            "3. **解析质量问题**：\n" +
            "   - \"A正确，B错误，C错误，D错误\" → 详细解释每个选项的原因\n" +
            "\n" +
            "4. **逻辑不一致**：\n" +
            "   - 解析说A正确，但答案是B → 答案和解析必须一致\n" +
            "\n" +
            "### 重要限制\n" +
            "\n" +
            "**绝对不能修改的字段**：`id`, `know_id`, `tags`, `source`, `score`, `sort`, `created_at`\n" +
            "你的任务是修正题目内容本身，而不是元数据分类。\n" +
            "\n" +
            "### 质量标准\n" +
            "\n" +
            "每道题目修正后应达到：\n" +
            "- 内容准确无误，符合学科知识\n" +
            "- 格式规范统一，便于系统处理\n" +
            "- 解析详细专业，具有教育价值\n" +
            "- 难度设置合理，匹配题目复杂度\n" +
            "\n" +
            "---\n" +
            "⚠\uFE0F **输出必须是纯 JSON**：回答中只能包含合法的 JSON 数组，**前后不可夹带任何解释性文字、Markdown、代码块标记或其它字符**。\n" +
            "\n" +
            "现在，请根据以上所有规则，仔细审查以下这批题目：";
    
    @PostConstruct
    public void init() {
        initDefaultConfigs();
    }
    
    @Override
    public String getConfigValue(String configKey) {
        try {
            return systemConfigMapper.getConfigValue(configKey);
        } catch (Exception e) {
            log.error("获取配置值失败: {}", configKey, e);
            return null;
        }
    }
    
    @Override
    public String getConfigValue(String configKey, String defaultValue) {
        String value = getConfigValue(configKey);
        return value != null ? value : defaultValue;
    }
    
    @Override
    @Transactional
    public void setConfigValue(String configKey, String configValue, String description) {
        try {
            systemConfigMapper.insertOrUpdateConfig(configKey, configValue, description);
            log.info("设置配置成功: {} = {}", configKey, configValue);
        } catch (Exception e) {
            log.error("设置配置失败: {} = {}", configKey, configValue, e);
            throw new RuntimeException("设置配置失败", e);
        }
    }
    
    @Override
    @Transactional
    public void updateConfigValue(String configKey, String configValue) {
        try {
            int result = systemConfigMapper.updateConfigValue(configKey, configValue);
            if (result > 0) {
                log.info("更新配置成功: {} = {}", configKey, configValue);
            } else {
                log.warn("配置不存在，将创建新配置: {}", configKey);
                setConfigValue(configKey, configValue, "自动创建的配置");
            }
        } catch (Exception e) {
            log.error("更新配置失败: {} = {}", configKey, configValue, e);
            throw new RuntimeException("更新配置失败", e);
        }
    }
    
    @Override
    public String getCorrectionPrompt() {
        return getConfigValue(TOPIC_CORRECTION_PROMPT, DEFAULT_CORRECTION_PROMPT);
    }
    
    @Override
    @Transactional
    public void setCorrectionPrompt(String prompt) {
        setConfigValue(TOPIC_CORRECTION_PROMPT, prompt, "题目修正AI提示词");
    }
    
    @Override
    public Long getLastProcessedTopicId() {
        String value = getConfigValue(TOPIC_CORRECTION_LAST_PROCESSED_ID, "0");
        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            log.warn("解析上次处理ID失败，使用默认值0: {}", value);
            return 0L;
        }
    }
    
    @Override
    @Transactional
    public Long getLastProcessedTopicIdWithLock() {
        try {
            String value = systemConfigMapper.getConfigValueWithLock(TOPIC_CORRECTION_LAST_PROCESSED_ID);
            if (value == null) {
                // 如果配置不存在，先创建默认配置
                setConfigValue(TOPIC_CORRECTION_LAST_PROCESSED_ID, "0", "上次处理的题目ID");
                return 0L;
            }
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            log.warn("解析上次处理ID失败，使用默认值0", e);
            return 0L;
        } catch (Exception e) {
            log.error("获取上次处理ID失败（带锁）", e);
            throw new RuntimeException("获取上次处理ID失败", e);
        }
    }
    
    @Override
    @Transactional
    public void setLastProcessedTopicId(Long lastProcessedId) {
        updateConfigValue(TOPIC_CORRECTION_LAST_PROCESSED_ID, String.valueOf(lastProcessedId));
    }
    
    @Override
    @Transactional
    public void initDefaultConfigs() {
        try {
            // 初始化默认提示词
            if (getConfigValue(TOPIC_CORRECTION_PROMPT) == null) {
                setConfigValue(TOPIC_CORRECTION_PROMPT, DEFAULT_CORRECTION_PROMPT, "题目修正AI提示词");
                log.info("初始化默认修正提示词");
            }
            
            // 初始化默认处理进度
            if (getConfigValue(TOPIC_CORRECTION_LAST_PROCESSED_ID) == null) {
                setConfigValue(TOPIC_CORRECTION_LAST_PROCESSED_ID, "0", "上次处理的题目ID");
                log.info("初始化默认处理进度");
            }
        } catch (Exception e) {
            log.error("初始化默认配置失败", e);
        }
    }
    
    @Override
    public int getFailedBatchRetryCount(int lastFailedId) {
        String configKey = TOPIC_CORRECTION_RETRY_COUNT_PREFIX + lastFailedId;
        String value = getConfigValue(configKey, "0");
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            log.warn("解析重试计数失败，使用默认值0: {}", value);
            return 0;
        }
    }
    
    @Override
    @Transactional
    public int incrementFailedBatchRetryCount(int lastFailedId) {
        String configKey = TOPIC_CORRECTION_RETRY_COUNT_PREFIX + lastFailedId;
        int currentCount = getFailedBatchRetryCount(lastFailedId);
        int newCount = currentCount + 1;
        updateConfigValue(configKey, String.valueOf(newCount));
        log.info("批次{}重试计数增加: {} -> {}", lastFailedId, currentCount, newCount);
        return newCount;
    }
    
    @Override
    @Transactional
    public void clearFailedBatchRetryCount(int lastFailedId) {
        String configKey = TOPIC_CORRECTION_RETRY_COUNT_PREFIX + lastFailedId;
        try {
            systemConfigMapper.deleteConfig(configKey);
            log.info("清除批次{}的重试计数", lastFailedId);
        } catch (Exception e) {
            log.warn("清除重试计数失败: {}", configKey, e);
        }
    }
    
    /**
     * 获取最大重试次数
     * @return 最大重试次数
     */
    public int getMaxRetryCount() {
        return MAX_RETRY_COUNT;
    }
}