package com.edu.maizi_edu_sys.service.impl;

import com.edu.maizi_edu_sys.config.UserStatsProperties;
import com.edu.maizi_edu_sys.dto.UserStatsDTO;
import com.edu.maizi_edu_sys.service.UserStatsCacheService;
import com.edu.maizi_edu_sys.service.UserStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 用户统计缓存服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
//@Service  // 暂时禁用，使用简化版本
@RequiredArgsConstructor
public class UserStatsCacheServiceImpl implements UserStatsCacheService {
    
    private final UserStatsService userStatsService;
    private final UserStatsProperties userStatsProperties;
    
    // 缓存统计
    private final AtomicLong hitCount = new AtomicLong(0);
    private final AtomicLong missCount = new AtomicLong(0);
    private final ConcurrentHashMap<Long, LocalDateTime> lastAccessTime = new ConcurrentHashMap<>();
    
    @Override
    @Cacheable(value = "userStats", key = "#userId", unless = "#result == null")
    public UserStatsDTO getUserStats(Long userId) {
        log.debug("缓存未命中，从数据库获取用户统计数据, userId: {}", userId);
        missCount.incrementAndGet();
        lastAccessTime.put(userId, LocalDateTime.now());
        
        try {
            return userStatsService.getUserStats(userId);
        } catch (Exception e) {
            log.error("获取用户统计数据失败, userId: {}", userId, e);
            // 返回空统计数据，避免缓存null值
            return createEmptyStats();
        }
    }
    
    @Override
    @CacheEvict(value = "userStats", key = "#userId")
    public void refreshUserStatsCache(Long userId) {
        log.debug("刷新用户统计缓存, userId: {}", userId);
        lastAccessTime.put(userId, LocalDateTime.now());
    }
    
    @Override
    @CacheEvict(value = "userStats", key = "#userId")
    public void clearUserStatsCache(Long userId) {
        log.debug("清除用户统计缓存, userId: {}", userId);
        lastAccessTime.remove(userId);
    }
    
    @Override
    @Async
    public void refreshActiveUsersCache() {
        log.info("开始批量刷新活跃用户统计缓存");
        
        // 获取最近访问的用户（最近1小时内访问过的用户）
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        
        lastAccessTime.entrySet().stream()
            .filter(entry -> entry.getValue().isAfter(oneHourAgo))
            .forEach(entry -> {
                Long userId = entry.getKey();
                try {
                    // 异步刷新缓存
                    refreshUserStatsCache(userId);
                    getUserStats(userId); // 重新加载到缓存
                    log.debug("刷新活跃用户缓存完成, userId: {}", userId);
                } catch (Exception e) {
                    log.error("刷新活跃用户缓存失败, userId: {}", userId, e);
                }
            });
        
        log.info("批量刷新活跃用户统计缓存完成");
    }
    
    @Override
    public CacheStats getCacheStats() {
        long hits = hitCount.get();
        long misses = missCount.get();
        long size = lastAccessTime.size();
        
        return new CacheStats(hits, misses, size);
    }
    
    /**
     * 定时清理过期的访问记录
     * 每小时执行一次，清理超过24小时未访问的记录
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void cleanupExpiredAccessRecords() {
        LocalDateTime oneDayAgo = LocalDateTime.now().minusDays(1);
        
        lastAccessTime.entrySet().removeIf(entry -> {
            boolean expired = entry.getValue().isBefore(oneDayAgo);
            if (expired) {
                log.debug("清理过期访问记录, userId: {}", entry.getKey());
            }
            return expired;
        });
    }
    
    /**
     * 定时刷新活跃用户缓存
     * 每30分钟执行一次
     */
    @Scheduled(fixedRate = 1800000) // 30分钟
    public void scheduledRefreshActiveUsersCache() {
        if (userStatsProperties.getCache().isAutoRefresh()) {
            refreshActiveUsersCache();
        }
    }
    
    /**
     * 创建空的统计数据
     */
    private UserStatsDTO createEmptyStats() {
        UserStatsDTO stats = new UserStatsDTO();
        stats.setTotalUploaded(0L);
        stats.setApprovedCount(0L);
        stats.setPendingCount(0L);
        stats.setRejectedCount(0L);
        stats.setWeeklyUploaded(0L);
        stats.setConsecutiveDays(0);
        stats.setMaxDailyUpload(0L);
        stats.setAverageAuditTime(0.0);
        stats.setRanking(0);
        stats.setQualityScore(0.0);
        stats.setPassRate(0.0);
        return stats;
    }
    
    /**
     * 记录缓存命中
     */
    public void recordCacheHit() {
        hitCount.incrementAndGet();
    }
}
