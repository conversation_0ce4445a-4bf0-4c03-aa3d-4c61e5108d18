package com.edu.maizi_edu_sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.edu.maizi_edu_sys.dto.AuditRequestDTO;
import com.edu.maizi_edu_sys.dto.TopicAuditDTO;
import com.edu.maizi_edu_sys.dto.TopicDTO;
import com.edu.maizi_edu_sys.dto.UserDailyAuditGroupDTO;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicAudit;
import com.edu.maizi_edu_sys.entity.TopicRejected;
import com.edu.maizi_edu_sys.entity.User;
import com.edu.maizi_edu_sys.repository.TopicAuditMapper;
import com.edu.maizi_edu_sys.repository.TopicMapper;
import com.edu.maizi_edu_sys.repository.TopicRejectedMapper;
import com.edu.maizi_edu_sys.mapper.UserMapper;
import com.edu.maizi_edu_sys.mapper.KnowledgeMapper;
import com.edu.maizi_edu_sys.entity.Knowledge;
import com.edu.maizi_edu_sys.service.SystemMessageService;
import com.edu.maizi_edu_sys.service.TopicAuditService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 题目审核服务实现类
 */
@Service
@Slf4j
public class TopicAuditServiceImpl extends ServiceImpl<TopicAuditMapper, TopicAudit> implements TopicAuditService {

    @Autowired
    private TopicMapper topicMapper;
    
    @Autowired
    private TopicRejectedMapper topicRejectedMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private KnowledgeMapper knowledgeMapper;
    
    @Autowired
    private SystemMessageService systemMessageService;
    
    @Autowired
    private ObjectMapper objectMapper;

    @Override
    @Transactional
    public void submitTopicForAudit(TopicDTO topicDTO, Long userId) {
        log.info("提交题目审核, userId: {}, topicTitle: {}", userId, topicDTO.getTitle());
        
        TopicAudit audit = new TopicAudit();
        BeanUtils.copyProperties(topicDTO, audit);
        audit.setUserId(userId);
        audit.setSubmitTime(LocalDateTime.now());
        audit.setAuditStatus(TopicAudit.AuditStatus.PENDING);
        
        // 处理选项数据
        if (topicDTO.getOptions() != null && !topicDTO.getOptions().isEmpty()) {
            try {
                audit.setOptions(objectMapper.writeValueAsString(topicDTO.getOptions()));
            } catch (Exception e) {
                throw new IllegalArgumentException("选项格式转换失败: " + e.getMessage(), e);
            }
        } else {
            audit.setOptions("[]");
        }
        
        this.save(audit);
        log.info("题目提交审核成功, auditId: {}", audit.getId());
    }

    @Override
    @Transactional
    public void submitTopicsForAudit(List<TopicDTO> topicDTOs, Long userId) {
        log.info("批量提交题目审核, userId: {}, count: {}", userId, topicDTOs.size());
        
        if (topicDTOs == null || topicDTOs.isEmpty()) {
            log.warn("批量提交的题目列表为空");
            return;
        }
        
        LocalDateTime submitTime = LocalDateTime.now();
        List<TopicAudit> auditList = new ArrayList<>();
        
        // 预处理所有题目数据
        for (int i = 0; i < topicDTOs.size(); i++) {
            TopicDTO topicDTO = topicDTOs.get(i);
            try {
                TopicAudit audit = new TopicAudit();
                BeanUtils.copyProperties(topicDTO, audit);
                audit.setUserId(userId);
                audit.setSubmitTime(submitTime);
                audit.setAuditStatus(TopicAudit.AuditStatus.PENDING);
                
                // 处理选项数据
                if (topicDTO.getOptions() != null && !topicDTO.getOptions().isEmpty()) {
                    try {
                        audit.setOptions(objectMapper.writeValueAsString(topicDTO.getOptions()));
                    } catch (Exception e) {
                        throw new IllegalArgumentException("第" + (i + 1) + "个题目选项格式转换失败: " + e.getMessage(), e);
                    }
                } else {
                    audit.setOptions("[]");
                }
                
                auditList.add(audit);
                
            } catch (Exception e) {
                log.error("预处理第{}个题目时出错: {}", i + 1, e.getMessage());
                throw new IllegalArgumentException("第" + (i + 1) + "个题目数据有误: " + e.getMessage(), e);
            }
        }
        
        // 执行批量插入
        try {
            // 分批处理，避免单次插入过多数据
            int batchSize = 100; // 每批最多500条
            if (auditList.size() <= batchSize) {
                // 少量数据直接批量插入
                boolean success = this.saveBatch(auditList);
                if (!success) {
                    throw new RuntimeException("批量插入失败");
                }
                log.info("成功批量插入{}条审核记录", auditList.size());
            } else {
                // 大批量数据分批插入
                int totalBatches = (int) Math.ceil((double) auditList.size() / batchSize);
                log.info("大批量数据，分{}批处理，每批{}条", totalBatches, batchSize);
                
                for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                    int fromIndex = batchIndex * batchSize;
                    int toIndex = Math.min(fromIndex + batchSize, auditList.size());
                    List<TopicAudit> batchList = auditList.subList(fromIndex, toIndex);
                    
                    boolean batchSuccess = this.saveBatch(batchList);
                    if (!batchSuccess) {
                        throw new RuntimeException("第" + (batchIndex + 1) + "批插入失败");
                    }
                    log.info("第{}/{}批插入成功，本批{}条", batchIndex + 1, totalBatches, batchList.size());
                }
            }
            
            log.info("批量题目提交审核完成，共{}条", auditList.size());
            
        } catch (Exception e) {
            log.error("批量插入审核记录失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量提交失败: " + e.getMessage(), e);
        }
    }

    @Override
    public IPage<TopicAuditDTO> getPendingAudits(int pageNum, int pageSize) {
        Page<TopicAudit> page = new Page<>(pageNum, pageSize);
        IPage<TopicAudit> auditPage = baseMapper.selectPendingAudits(page);
        
        return convertToDTO(auditPage);
    }

    @Override
    public IPage<TopicAuditDTO> getPendingAudits(int pageNum, int pageSize, String keyword, String submitter, String knowledge) {
        log.info("获取待审核题目列表 - 页码: {}, 大小: {}, 关键词: {}, 提交者: {}, 知识点: {}", 
                pageNum, pageSize, keyword, submitter, knowledge);
        
        Page<TopicAudit> page = new Page<>(pageNum, pageSize);
        IPage<TopicAudit> auditPage = baseMapper.selectPendingAuditsWithFilters(page, keyword, submitter, knowledge);
        
        return convertToDTO(auditPage);
    }

    @Override
    public IPage<TopicAuditDTO> getUserAudits(Long userId, int pageNum, int pageSize) {
        Page<TopicAudit> page = new Page<>(pageNum, pageSize);
        IPage<TopicAudit> auditPage = baseMapper.selectByUserId(page, userId);
        
        return convertToDTO(auditPage);
    }

    @Override
    public IPage<TopicAuditDTO> getAuditorAudits(Long auditorId, int pageNum, int pageSize) {
        Page<TopicAudit> page = new Page<>(pageNum, pageSize);
        IPage<TopicAudit> auditPage = baseMapper.selectByAuditorId(page, auditorId);
        
        return convertToDTO(auditPage);
    }

    @Override
    @Transactional
    public void auditTopic(AuditRequestDTO auditRequest, Long auditorId) {
        log.info("审核题目, auditId: {}, auditorId: {}, result: {}", 
                auditRequest.getAuditId(), auditorId, auditRequest.getAuditResult());
        
        // 获取审核记录
        TopicAudit audit = this.getById(auditRequest.getAuditId());
        if (audit == null) {
            throw new IllegalArgumentException("审核记录不存在");
        }
        
        if (audit.getAuditStatus() != TopicAudit.AuditStatus.PENDING) {
            throw new IllegalArgumentException("该题目已经被审核过了");
        }
        
        // 更新审核状态
        audit.setAuditStatus(auditRequest.getAuditResult());
        audit.setAuditorId(auditorId);
        audit.setAuditTime(LocalDateTime.now());
        audit.setAuditComment(auditRequest.getAuditComment());
        this.updateById(audit);
        
        if (auditRequest.getAuditResult() == TopicAudit.AuditStatus.APPROVED) {
            // 审核通过，添加到正式题库
            addToTopicBank(audit);
            
            // 发送通过消息
            systemMessageService.sendAuditApprovedMessage(
                audit.getUserId(), audit.getId(), audit.getTitle());
            
        } else if (auditRequest.getAuditResult() == TopicAudit.AuditStatus.REJECTED) {
            // 审核拒绝，添加到拒绝记录
            addToRejectedTopics(audit, auditRequest.getAuditComment());
            
            // 发送拒绝消息
            systemMessageService.sendAuditRejectedMessage(
                audit.getUserId(), audit.getId(), audit.getTitle(), 
                auditRequest.getAuditComment());
        }
        
        log.info("题目审核完成");
    }

    @Override
    public Map<String, Object> getAuditStatistics() {
        try {
            // 使用QueryWrapper直接查询统计数据
            Long pendingCount = this.count(new QueryWrapper<TopicAudit>()
                .eq("audit_status", TopicAudit.AuditStatus.PENDING));
            
            Long approvedCount = this.count(new QueryWrapper<TopicAudit>()
                .eq("audit_status", TopicAudit.AuditStatus.APPROVED));
            
            Long rejectedCount = this.count(new QueryWrapper<TopicAudit>()
                .eq("audit_status", TopicAudit.AuditStatus.REJECTED));
            
            Long totalCount = pendingCount + approvedCount + rejectedCount;
            
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("pending", pendingCount);          // 前端期望的字段名
            statistics.put("approved", approvedCount);        // 前端期望的字段名
            statistics.put("rejected", rejectedCount);        // 前端期望的字段名
            statistics.put("total", totalCount);              // 前端期望的字段名
            
            // 同时提供两种字段名以兼容
            statistics.put("pendingCount", pendingCount);
            statistics.put("approvedCount", approvedCount);
            statistics.put("rejectedCount", rejectedCount);
            statistics.put("totalCount", totalCount);
            
            log.info("审核统计数据: pending={}, approved={}, rejected={}, total={}", 
                     pendingCount, approvedCount, rejectedCount, totalCount);
            
            return statistics;
        } catch (Exception e) {
            log.error("获取审核统计数据失败", e);
            
            // 返回默认值，避免前端显示错误
            Map<String, Object> defaultStats = new HashMap<>();
            defaultStats.put("pending", 0L);
            defaultStats.put("approved", 0L);
            defaultStats.put("rejected", 0L);
            defaultStats.put("total", 0L);
            defaultStats.put("pendingCount", 0L);
            defaultStats.put("approvedCount", 0L);
            defaultStats.put("rejectedCount", 0L);
            defaultStats.put("totalCount", 0L);
            
            return defaultStats;
        }
    }

    @Override
    public Long getUserPendingCount(Long userId) {
        return baseMapper.countPendingByUserId(userId);
    }

    @Override
    public boolean hasAuditPermission(Long userId) {
        // 检查用户角色，只有管理员有审核权限
        User user = userMapper.selectById(userId);
        return user != null && user.getRole() == 1;
    }

    /**
     * 将审核通过的题目添加到正式题库
     */
    private void addToTopicBank(TopicAudit audit) {
        Topic topic = new Topic();
        BeanUtils.copyProperties(audit, topic);
        topic.setId(null);  // 重置ID，让数据库自动生成
        topic.setCreatedAt(LocalDateTime.now());
        
        // 确保subs字段有默认值，避免数据库插入错误
        if (topic.getSubs() == null) {
            topic.setSubs("[]");  // 设置为空的JSON数组
        }
        
        // 确保其他可能为null的字段有默认值
        if (topic.getTags() == null) {
            topic.setTags("");
        }
        if (topic.getSource() == null) {
            topic.setSource("");
        }
        if (topic.getParse() == null) {
            topic.setParse("");
        }
        
        topicMapper.insert(topic);
        log.info("题目已添加到正式题库, topicId: {}", topic.getId());
    }

    /**
     * 将审核拒绝的题目添加到拒绝记录
     */
    private void addToRejectedTopics(TopicAudit audit, String rejectReason) {
        TopicRejected rejected = new TopicRejected();
        BeanUtils.copyProperties(audit, rejected);
        rejected.setId(null);  // 重置ID
        rejected.setAuditId(audit.getId());
        rejected.setRejectReason(rejectReason);
        rejected.setRejectTime(LocalDateTime.now());
        
        topicRejectedMapper.insert(rejected);
        log.info("题目已添加到拒绝记录, rejectedId: {}", rejected.getId());
    }

    /**
     * 转换为DTO对象
     */
    private IPage<TopicAuditDTO> convertToDTO(IPage<TopicAudit> auditPage) {
        Page<TopicAuditDTO> dtoPage = new Page<>(auditPage.getCurrent(), auditPage.getSize(), auditPage.getTotal());
        
        List<TopicAuditDTO> dtoList = auditPage.getRecords().stream().map(audit -> {
            TopicAuditDTO dto = new TopicAuditDTO();
            // 不能直接使用BeanUtils.copyProperties，因为ID字段类型不匹配
            // BeanUtils.copyProperties(audit, dto);
            
            // 手动复制属性，处理ID字段的类型转换
            dto.setId(audit.getId() != null ? String.valueOf(audit.getId()) : null);
            dto.setUserId(audit.getUserId() != null ? String.valueOf(audit.getUserId()) : null);
            dto.setKnowId(audit.getKnowId());
            dto.setType(audit.getType());
            dto.setTitle(audit.getTitle());
            dto.setOptions(audit.getOptions());
            dto.setSubs(audit.getSubs());
            dto.setAnswer(audit.getAnswer());
            dto.setParse(audit.getParse());
            dto.setScore(audit.getScore());
            dto.setSource(audit.getSource());
            dto.setDifficulty(audit.getDifficulty());
            dto.setTags(audit.getTags());
            dto.setAuditStatus(audit.getAuditStatus());
            dto.setAuditorId(audit.getAuditorId() != null ? String.valueOf(audit.getAuditorId()) : null);
            dto.setAuditTime(audit.getAuditTime());
            dto.setAuditComment(audit.getAuditComment());
            dto.setAutoApproved(audit.getAutoApproved());
            dto.setSubmitTime(audit.getSubmitTime());
            dto.setCreatedAt(audit.getCreatedAt());
            dto.setUpdatedAt(audit.getUpdatedAt());
            
            // 设置审核状态文本
            switch (audit.getAuditStatus()) {
                case TopicAudit.AuditStatus.PENDING:
                    dto.setAuditStatusText("待审核");
                    break;
                case TopicAudit.AuditStatus.APPROVED:
                    dto.setAuditStatusText("审核通过");
                    break;
                case TopicAudit.AuditStatus.REJECTED:
                    dto.setAuditStatusText("审核拒绝");
                    break;
                default:
                    dto.setAuditStatusText("未知状态");
            }
            
            // 获取用户名
            User user = userMapper.selectById(audit.getUserId());
            if (user != null) {
                dto.setUsername(user.getUsername());
            }
            
            // 获取审核员名称
            if (audit.getAuditorId() != null) {
                User auditor = userMapper.selectById(audit.getAuditorId());
                if (auditor != null) {
                    dto.setAuditorName(auditor.getUsername());
                }
            }
            
            // 获取知识点名称
            if (audit.getKnowId() != null) {
                try {
                    QueryWrapper<Knowledge> knowledgeWrapper = new QueryWrapper<>();
                    knowledgeWrapper.eq("knowledge_id", audit.getKnowId())
                                   .eq("is_deleted", 0);
                    Knowledge knowledge = knowledgeMapper.selectOne(knowledgeWrapper);
                    if (knowledge != null) {
                        dto.setKnowledgeName(knowledge.getKnowledgeName());
                    } else {
                        dto.setKnowledgeName("知识点ID: " + audit.getKnowId());
                    }
                } catch (Exception e) {
                    log.warn("获取知识点名称失败, knowId: {}, error: {}", audit.getKnowId(), e.getMessage());
                    dto.setKnowledgeName("知识点ID: " + audit.getKnowId());
                }
            }
            
            return dto;
        }).collect(Collectors.toList());
        
        dtoPage.setRecords(dtoList);
        return dtoPage;
    }

    @Override
    public IPage<TopicAuditDTO> getApprovedAudits(int pageNum, int pageSize) {
        Page<TopicAudit> page = new Page<>(pageNum, pageSize);
        QueryWrapper<TopicAudit> wrapper = new QueryWrapper<>();
        wrapper.eq("audit_status", TopicAudit.AuditStatus.APPROVED)
               .orderByDesc("audit_time");
        
        IPage<TopicAudit> auditPage = this.page(page, wrapper);
        return convertToDTO(auditPage);
    }

    @Override
    public IPage<TopicAuditDTO> getRejectedAudits(int pageNum, int pageSize) {
        Page<TopicAudit> page = new Page<>(pageNum, pageSize);
        QueryWrapper<TopicAudit> wrapper = new QueryWrapper<>();
        wrapper.eq("audit_status", TopicAudit.AuditStatus.REJECTED)
               .orderByDesc("audit_time");
        
        IPage<TopicAudit> auditPage = this.page(page, wrapper);
        return convertToDTO(auditPage);
    }

    @Override
    public TopicAuditDTO getAuditDetail(Long auditId) {
        TopicAudit audit = this.getById(auditId);
        if (audit == null) {
            return null;
        }
        
        TopicAuditDTO dto = new TopicAuditDTO();
        // 手动复制属性，处理ID字段的类型转换
        dto.setId(audit.getId() != null ? String.valueOf(audit.getId()) : null);
        dto.setUserId(audit.getUserId() != null ? String.valueOf(audit.getUserId()) : null);
        dto.setKnowId(audit.getKnowId());
        dto.setType(audit.getType());
        dto.setTitle(audit.getTitle());
        dto.setOptions(audit.getOptions());
        dto.setSubs(audit.getSubs());
        dto.setAnswer(audit.getAnswer());
        dto.setParse(audit.getParse());
        dto.setScore(audit.getScore());
        dto.setSource(audit.getSource());
        dto.setDifficulty(audit.getDifficulty());
        dto.setTags(audit.getTags());
        dto.setAuditStatus(audit.getAuditStatus());
        dto.setAuditorId(audit.getAuditorId() != null ? String.valueOf(audit.getAuditorId()) : null);
        dto.setAuditTime(audit.getAuditTime());
        dto.setAuditComment(audit.getAuditComment());
        dto.setAutoApproved(audit.getAutoApproved());
        dto.setSubmitTime(audit.getSubmitTime());
        dto.setCreatedAt(audit.getCreatedAt());
        dto.setUpdatedAt(audit.getUpdatedAt());
        
        // 设置审核状态文本
        switch (audit.getAuditStatus()) {
            case TopicAudit.AuditStatus.PENDING:
                dto.setAuditStatusText("待审核");
                break;
            case TopicAudit.AuditStatus.APPROVED:
                dto.setAuditStatusText("审核通过");
                break;
            case TopicAudit.AuditStatus.REJECTED:
                dto.setAuditStatusText("审核拒绝");
                break;
            default:
                dto.setAuditStatusText("未知状态");
        }
        
        // 获取用户名
        User user = userMapper.selectById(audit.getUserId());
        if (user != null) {
            dto.setUsername(user.getUsername());
        }
        
        // 获取审核员名称
        if (audit.getAuditorId() != null) {
            User auditor = userMapper.selectById(audit.getAuditorId());
            if (auditor != null) {
                dto.setAuditorName(auditor.getUsername());
            }
        }
        
        // 获取知识点名称
        if (audit.getKnowId() != null) {
            try {
                QueryWrapper<Knowledge> knowledgeWrapper = new QueryWrapper<>();
                knowledgeWrapper.eq("knowledge_id", audit.getKnowId())
                               .eq("is_deleted", 0);
                Knowledge knowledge = knowledgeMapper.selectOne(knowledgeWrapper);
                if (knowledge != null) {
                    dto.setKnowledgeName(knowledge.getKnowledgeName());
                } else {
                    dto.setKnowledgeName("知识点ID: " + audit.getKnowId());
                }
            } catch (Exception e) {
                log.warn("获取知识点名称失败, knowId: {}, error: {}", audit.getKnowId(), e.getMessage());
                dto.setKnowledgeName("知识点ID: " + audit.getKnowId());
            }
        }
        
        return dto;
    }

    @Override
    public List<Long> getAllPendingAuditIds() {
        QueryWrapper<TopicAudit> wrapper = new QueryWrapper<>();
        wrapper.eq("audit_status", TopicAudit.AuditStatus.PENDING)
               .select("id");
        
        List<TopicAudit> audits = this.list(wrapper);
        return audits.stream()
                    .map(TopicAudit::getId)
                    .collect(Collectors.toList());
    }

    @Override
    public IPage<UserDailyAuditGroupDTO> getUserDailyAuditGroups(int pageNum, int pageSize, String keyword) {
        log.info("获取用户每日审核分组数据 - 页码: {}, 大小: {}, 关键词: {}", pageNum, pageSize, keyword);
        
        try {
            // 先检查是否有审核数据
            Long totalCount = this.count();
            log.info("审核表总记录数: {}", totalCount);
            
            if (totalCount == 0) {
                log.warn("审核表中没有任何数据");
                return new Page<>(pageNum, pageSize, 0);
            }
            
            // 执行分组查询
            IPage<UserDailyAuditGroupDTO> result = baseMapper.selectUserDailyAuditGroups(new Page<>(pageNum, pageSize), keyword);
            log.info("分组查询结果 - 总数: {}, 当前页记录数: {}", result.getTotal(), result.getRecords().size());
            
            return result;
        } catch (Exception e) {
            log.error("获取用户每日审核分组数据失败", e);
            throw new RuntimeException("获取分组数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<TopicAuditDTO> getAuditsByUserAndDate(String userId, String date) {
        // 将字符串userId转换为Long类型用于数据库查询
        Long userIdLong;
        try {
            userIdLong = Long.valueOf(userId);
        } catch (NumberFormatException e) {
            log.error("无效的用户ID格式: {}", userId);
            throw new IllegalArgumentException("无效的用户ID格式: " + userId);
        }
        
        QueryWrapper<TopicAudit> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userIdLong)
               .apply("DATE(submit_time) = {0}", date)
               .orderByAsc("submit_time");
        
        List<TopicAudit> audits = this.list(wrapper);
        return audits.stream().map(audit -> {
            TopicAuditDTO dto = new TopicAuditDTO();
            // 手动复制属性，处理ID字段的类型转换
            dto.setId(audit.getId() != null ? String.valueOf(audit.getId()) : null);
            dto.setUserId(audit.getUserId() != null ? String.valueOf(audit.getUserId()) : null);
            dto.setKnowId(audit.getKnowId());
            dto.setType(audit.getType());
            dto.setTitle(audit.getTitle());
            dto.setOptions(audit.getOptions());
            dto.setSubs(audit.getSubs());
            dto.setAnswer(audit.getAnswer());
            dto.setParse(audit.getParse());
            dto.setScore(audit.getScore());
            dto.setSource(audit.getSource());
            dto.setDifficulty(audit.getDifficulty());
            dto.setTags(audit.getTags());
            dto.setAuditStatus(audit.getAuditStatus());
            dto.setAuditorId(audit.getAuditorId() != null ? String.valueOf(audit.getAuditorId()) : null);
            dto.setAuditTime(audit.getAuditTime());
            dto.setAuditComment(audit.getAuditComment());
            dto.setAutoApproved(audit.getAutoApproved());
            dto.setSubmitTime(audit.getSubmitTime());
            dto.setCreatedAt(audit.getCreatedAt());
            dto.setUpdatedAt(audit.getUpdatedAt());
            
            // 设置审核状态文本
            switch (audit.getAuditStatus()) {
                case TopicAudit.AuditStatus.PENDING:
                    dto.setAuditStatusText("待审核");
                    break;
                case TopicAudit.AuditStatus.APPROVED:
                    dto.setAuditStatusText("审核通过");
                    break;
                case TopicAudit.AuditStatus.REJECTED:
                    dto.setAuditStatusText("审核拒绝");
                    break;
                default:
                    dto.setAuditStatusText("未知状态");
            }
            
            // 获取用户名
            User user = userMapper.selectById(audit.getUserId());
            if (user != null) {
                dto.setUsername(user.getUsername());
            }
            
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void batchAuditUserDaily(String userId, String date, int auditResult, String comment, Long auditorId) {
        log.info("批量审核用户每日题目, userId: {}, date: {}, result: {}, auditorId: {}", 
                userId, date, auditResult, auditorId);
        
        // 将字符串userId转换为Long类型用于数据库查询
        Long userIdLong;
        try {
            userIdLong = Long.valueOf(userId);
        } catch (NumberFormatException e) {
            log.error("无效的用户ID格式: {}", userId);
            throw new IllegalArgumentException("无效的用户ID格式: " + userId);
        }
        
        QueryWrapper<TopicAudit> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userIdLong)
               .apply("DATE(submit_time) = {0}", date)
               .eq("audit_status", TopicAudit.AuditStatus.PENDING);
        
        List<TopicAudit> audits = this.list(wrapper);
        log.info("查询到的待审核记录数: {}", audits.size());
        
        if (audits.isEmpty()) {
            log.warn("未找到符合条件的待审核题目 - userId: {}, date: {}", userId, date);
            
            // 额外的调试信息：查看所有该用户的记录
            QueryWrapper<TopicAudit> debugWrapper = new QueryWrapper<>();
            debugWrapper.eq("user_id", userIdLong);
            List<TopicAudit> allUserAudits = this.list(debugWrapper);
            log.info("该用户的所有审核记录数: {}", allUserAudits.size());
            
            for (TopicAudit audit : allUserAudits) {
                log.info("用户记录: ID={}, 提交时间={}, 日期={}, 状态={}", 
                    audit.getId(), audit.getSubmitTime(), 
                    audit.getSubmitTime() != null ? audit.getSubmitTime().toLocalDate() : null,
                    audit.getAuditStatus());
            }
            return;
        }
        
        // 🚀 优化：使用批量审核方法，提高效率
        List<Long> auditIds = audits.stream().map(TopicAudit::getId).collect(Collectors.toList());
        Map<String, Integer> result = batchAuditByIds(auditIds, auditResult, comment, auditorId);
        
        int successCount = result.get("success");
        int failureCount = result.get("failure");
        
        log.info("用户每日批量审核完成, 用户: {}, 日期: {}, 总数: {}, 成功: {}, 失败: {}", 
                userId, date, audits.size(), successCount, failureCount);
    }

    @Override
    @Transactional
    public void simpleBatchAuditUserDaily(String userId, String date, int auditResult, String comment, Long auditorId) {
        log.info("简化批量审核用户每日题目, userId: {}, date: {}, result: {}, auditorId: {}", 
                userId, date, auditResult, auditorId);
        
        // 将字符串userId转换为Long类型用于数据库查询
        Long userIdLong;
        try {
            userIdLong = Long.valueOf(userId);
        } catch (NumberFormatException e) {
            log.error("无效的用户ID格式: {}", userId);
            throw new IllegalArgumentException("无效的用户ID格式: " + userId);
        }
        
        QueryWrapper<TopicAudit> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userIdLong)
               .apply("DATE(submit_time) = {0}", date)
               .eq("audit_status", TopicAudit.AuditStatus.PENDING);
        
        List<TopicAudit> audits = this.list(wrapper);
        log.info("简化方法查询到的待审核记录数: {}", audits.size());
        
        if (audits.isEmpty()) {
            log.warn("简化方法未找到符合条件的待审核题目");
            return;
        }
        
        // 直接更新记录，不调用复杂的auditTopic方法
        for (TopicAudit audit : audits) {
            try {
                audit.setAuditStatus(auditResult);
                audit.setAuditorId(auditorId);
                audit.setAuditTime(LocalDateTime.now());
                audit.setAuditComment(comment);
                
                boolean updated = this.updateById(audit);
                log.info("直接更新记录: ID={}, 成功={}", audit.getId(), updated);
                
                // 如果是通过，添加到题库
                if (auditResult == TopicAudit.AuditStatus.APPROVED) {
                    try {
                        addToTopicBank(audit);
                        log.info("添加到题库成功: ID={}", audit.getId());
                    } catch (Exception e) {
                        log.warn("添加到题库失败: ID={}, error={}", audit.getId(), e.getMessage());
                    }
                }
                
            } catch (Exception e) {
                log.error("简化批量审核失败, auditId: {}, error: {}", audit.getId(), e.getMessage(), e);
            }
        }
        
        log.info("简化批量审核完成");
    }
    
    @Override
    public List<TopicAuditDTO> getAllPendingAudits() {
        log.info("获取所有待审核题目（不分页）");
        
        QueryWrapper<TopicAudit> wrapper = new QueryWrapper<>();
        wrapper.eq("audit_status", TopicAudit.AuditStatus.PENDING)
               .orderByAsc("submit_time");
        
        List<TopicAudit> audits = this.list(wrapper);
        log.info("查询到 {} 条待审核记录", audits.size());
        
        return audits.stream().map(audit -> {
            TopicAuditDTO dto = new TopicAuditDTO();
            // 手动复制属性，处理ID字段的类型转换
            dto.setId(audit.getId() != null ? String.valueOf(audit.getId()) : null);
            dto.setUserId(audit.getUserId() != null ? String.valueOf(audit.getUserId()) : null);
            dto.setKnowId(audit.getKnowId());
            dto.setType(audit.getType());
            dto.setTitle(audit.getTitle());
            dto.setOptions(audit.getOptions());
            dto.setSubs(audit.getSubs());
            dto.setAnswer(audit.getAnswer());
            dto.setParse(audit.getParse());
            dto.setScore(audit.getScore());
            dto.setSource(audit.getSource());
            dto.setDifficulty(audit.getDifficulty());
            dto.setTags(audit.getTags());
            dto.setAuditStatus(audit.getAuditStatus());
            dto.setAuditorId(audit.getAuditorId() != null ? String.valueOf(audit.getAuditorId()) : null);
            dto.setAuditTime(audit.getAuditTime());
            dto.setAuditComment(audit.getAuditComment());
            dto.setAutoApproved(audit.getAutoApproved());
            dto.setSubmitTime(audit.getSubmitTime());
            dto.setCreatedAt(audit.getCreatedAt());
            dto.setUpdatedAt(audit.getUpdatedAt());
            
            // 设置审核状态文本
            dto.setAuditStatusText("待审核");
            
            // 获取用户名
            User user = userMapper.selectById(audit.getUserId());
            if (user != null) {
                dto.setUsername(user.getUsername());
            }
            
            return dto;
        }).collect(Collectors.toList());
    }
    
    @Override
    public List<TopicAuditDTO> getPendingAuditsByUserId(Long userId) {
        log.info("根据用户ID获取待审核题目, userId: {}", userId);
        
        QueryWrapper<TopicAudit> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId)
               .eq("audit_status", TopicAudit.AuditStatus.PENDING)
               .orderByAsc("submit_time");
        
        List<TopicAudit> audits = this.list(wrapper);
        log.info("用户 {} 的待审核记录数: {}", userId, audits.size());
        
        return audits.stream().map(audit -> {
            TopicAuditDTO dto = new TopicAuditDTO();
            // 手动复制属性，处理ID字段的类型转换
            dto.setId(audit.getId() != null ? String.valueOf(audit.getId()) : null);
            dto.setUserId(audit.getUserId() != null ? String.valueOf(audit.getUserId()) : null);
            dto.setKnowId(audit.getKnowId());
            dto.setType(audit.getType());
            dto.setTitle(audit.getTitle());
            dto.setOptions(audit.getOptions());
            dto.setSubs(audit.getSubs());
            dto.setAnswer(audit.getAnswer());
            dto.setParse(audit.getParse());
            dto.setScore(audit.getScore());
            dto.setSource(audit.getSource());
            dto.setDifficulty(audit.getDifficulty());
            dto.setTags(audit.getTags());
            dto.setAuditStatus(audit.getAuditStatus());
            dto.setAuditorId(audit.getAuditorId() != null ? String.valueOf(audit.getAuditorId()) : null);
            dto.setAuditTime(audit.getAuditTime());
            dto.setAuditComment(audit.getAuditComment());
            dto.setAutoApproved(audit.getAutoApproved());
            dto.setSubmitTime(audit.getSubmitTime());
            dto.setCreatedAt(audit.getCreatedAt());
            dto.setUpdatedAt(audit.getUpdatedAt());
            
            // 设置审核状态文本
            dto.setAuditStatusText("待审核");
            
            // 获取用户名
            User user = userMapper.selectById(audit.getUserId());
            if (user != null) {
                dto.setUsername(user.getUsername());
            }
            
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Map<String, Integer> batchAuditByIds(List<Long> auditIds, int auditResult, String comment, Long auditorId) {
        log.info("开始批量审核，审核ID数量: {}, 审核结果: {}, 审核员: {}", auditIds.size(), auditResult, auditorId);
        
        Map<String, Integer> result = new HashMap<>();
        int successCount = 0;
        int failureCount = 0;
        
        try {
            // 验证审核ID是否存在且为待审核状态
            QueryWrapper<TopicAudit> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("id", auditIds)
                       .eq("audit_status", TopicAudit.AuditStatus.PENDING);
            
            List<TopicAudit> validAudits = this.list(queryWrapper);
            log.info("有效的待审核记录数: {}", validAudits.size());
            
            if (validAudits.isEmpty()) {
                log.warn("没有找到有效的待审核记录");
                result.put("success", 0);
                result.put("failure", auditIds.size());
                return result;
            }
            
            // 🚀 分批处理大批量操作，提高稳定性
            int batchSize = 200; // 每批处理200个，避免内存和事务过大
            List<List<TopicAudit>> batches = new ArrayList<>();
            
            for (int i = 0; i < validAudits.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, validAudits.size());
                batches.add(validAudits.subList(i, endIndex));
            }
            
            log.info("分批处理 - 总数: {}, 分为 {} 批，每批最多 {} 个", validAudits.size(), batches.size(), batchSize);
            
            LocalDateTime now = LocalDateTime.now();
            
            // 逐批处理
            for (int batchIndex = 0; batchIndex < batches.size(); batchIndex++) {
                List<TopicAudit> batch = batches.get(batchIndex);
                log.info("处理第 {} 批，数量: {}", batchIndex + 1, batch.size());
                
                try {
                    // 执行批量更新
                    List<Long> batchIds = batch.stream().map(TopicAudit::getId).collect(Collectors.toList());
                    int updatedCount = getBaseMapper().batchUpdateAuditStatus(batchIds, auditResult, auditorId, now, comment);
                    
                    log.info("第 {} 批更新完成，影响行数: {}", batchIndex + 1, updatedCount);
                    
                    // 如果是通过审核，需要添加到题库
                    if (auditResult == TopicAudit.AuditStatus.APPROVED) {
                        for (TopicAudit audit : batch) {
                            try {
                                // 重新设置审核状态和时间，为添加题库做准备
                                audit.setAuditStatus(auditResult);
                                audit.setAuditorId(auditorId);
                                audit.setAuditTime(now);
                                audit.setAuditComment(comment);
                                
                                addToTopicBank(audit);
                                successCount++;
                                log.debug("添加到题库成功: ID={}", audit.getId());
                            } catch (Exception e) {
                                failureCount++;
                                log.warn("添加到题库失败: ID={}, error={}", audit.getId(), e.getMessage());
                            }
                        }
                    } else {
                        // 如果是拒绝，直接计算成功数量
                        successCount += updatedCount;
                    }
                    
                    // 分批之间短暂休息，避免系统负载过高
                    if (batchIndex < batches.size() - 1 && batches.size() > 2) {
                        try {
                            Thread.sleep(100); // 100ms间隔
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                    }
                    
                } catch (Exception e) {
                    log.error("第 {} 批处理失败", batchIndex + 1, e);
                    failureCount += batch.size();
                }
            }
            
            // 计算总的失败数量
            failureCount = auditIds.size() - successCount;
            
            log.info("批量审核完成 - 总数: {}, 成功: {}, 失败: {}", auditIds.size(), successCount, failureCount);
            
        } catch (Exception e) {
            log.error("批量审核过程中发生错误", e);
            failureCount = auditIds.size();
        }
        
        result.put("success", successCount);
        result.put("failure", failureCount);
        return result;
    }

    @Override
    public List<Map<String, Object>> getSubmitterList() {
        log.info("=== 开始获取提交者列表 ===");
        
        try {
            List<Map<String, Object>> submitters = new ArrayList<>();
            
            // 先检查表中是否有数据
            long totalCount = this.count();
            log.info("topic_audit表总记录数: {}", totalCount);
            
            if (totalCount == 0) {
                log.warn("topic_audit表中没有任何数据，返回空列表");
                return submitters;
            }
            
            // 检查有user_id的记录数量
            long hasUserIdCount = this.count(new QueryWrapper<TopicAudit>().isNotNull("user_id"));
            log.info("有user_id的记录数: {}", hasUserIdCount);
            
            if (hasUserIdCount == 0) {
                log.warn("topic_audit表中没有任何有user_id的记录");
                return submitters;
            }
            
            // 检查待审核数据
            long pendingCount = this.count(new QueryWrapper<TopicAudit>().eq("audit_status", 0));
            log.info("待审核记录数: {}", pendingCount);
            
            // 检查待审核且有user_id的记录数量  
            long pendingWithUserIdCount = this.count(new QueryWrapper<TopicAudit>().eq("audit_status", 0).isNotNull("user_id"));
            log.info("待审核且有user_id的记录数: {}", pendingWithUserIdCount);
            
            if (pendingCount == 0) {
                log.warn("没有待审核的记录，尝试获取所有记录的提交者");
                
                // 使用安全的分步查询方式获取所有记录的提交者
                QueryWrapper<TopicAudit> allCountWrapper = new QueryWrapper<>();
                allCountWrapper.select("user_id", "COUNT(*) as count")
                              .isNotNull("user_id")
                              .groupBy("user_id")
                              .orderByDesc("COUNT(*)");
                
                List<Map<String, Object>> allUserStats = baseMapper.selectMaps(allCountWrapper);
                log.info("从所有记录获取到 {} 个提交者的统计信息", allUserStats.size());
                
                if (allUserStats.isEmpty()) {
                    log.warn("没有任何提交者统计信息");
                    return submitters;
                }
                
                // 获取用户详细信息 - 使用userMapper直接查询
                List<Long> allUserIds = allUserStats.stream()
                        .map(stat -> Long.valueOf(stat.get("user_id").toString()))
                        .collect(java.util.stream.Collectors.toList());
                
                log.info("需要查询的用户ID列表: {}", allUserIds);
                
                // 使用userMapper分批查询用户信息，避免IN查询限制
                List<Map<String, Object>> allUserInfos = new ArrayList<>();
                if (!allUserIds.isEmpty()) {
                    for (Long userId : allUserIds) {
                        try {
                            User user = userMapper.selectById(userId);
                            if (user != null && user.getDeleted() == 0) {
                                Map<String, Object> userInfo = new HashMap<>();
                                userInfo.put("id", user.getId());
                                userInfo.put("username", user.getUsername());
                                allUserInfos.add(userInfo);
                            }
                        } catch (Exception e) {
                            log.warn("查询用户ID {} 信息失败: {}", userId, e.getMessage());
                        }
                    }
                }
                log.info("从所有记录获取到 {} 个用户的详细信息", allUserInfos.size());
                
                // 创建映射
                Map<Long, Map<String, Object>> allUserInfoMap = allUserInfos.stream()
                        .collect(java.util.stream.Collectors.toMap(
                            info -> Long.valueOf(info.get("id").toString()),
                            info -> info,
                            (existing, replacement) -> existing
                        ));
                
                // 合并数据
                for (Map<String, Object> stat : allUserStats) {
                    Long userId = Long.valueOf(stat.get("user_id").toString());
                    Map<String, Object> userInfo = allUserInfoMap.get(userId);
                    
                    Map<String, Object> submitter = new HashMap<>();
                    // 确保ID以字符串形式返回，避免JavaScript精度丢失
                    String userIdStr = String.valueOf(userId);
                    submitter.put("id", userIdStr);
                    submitter.put("userId", userIdStr);
                    submitter.put("username", userInfo != null ? userInfo.get("username") : "未知用户");
                    submitter.put("count", stat.get("count"));
                    submitters.add(submitter);
                }
                
                log.info("从所有记录中获取到 {} 个提交者", submitters.size());
                return submitters;
            }
            
            // 使用更安全的SQL构建方式查询待审核的提交者
            log.info("开始查询待审核提交者信息");
            
            // 分两步查询，避免复杂的JOIN操作
            // 第一步：获取提交者ID和数量统计
            QueryWrapper<TopicAudit> countWrapper = new QueryWrapper<>();
            countWrapper.select("user_id", "COUNT(*) as count")
                       .eq("audit_status", 0)
                       .isNotNull("user_id")
                       .groupBy("user_id")
                       .orderByDesc("COUNT(*)");
            
            log.info("执行统计查询SQL: {}", countWrapper.getSqlSegment());
            
            List<Map<String, Object>> userStats = baseMapper.selectMaps(countWrapper);
            log.info("获取到 {} 个待审核提交者的统计信息", userStats.size());
            
            if (!userStats.isEmpty()) {
                log.info("统计信息示例: {}", userStats.subList(0, Math.min(3, userStats.size())));
            }
            
            if (userStats.isEmpty()) {
                log.warn("没有待审核的提交者统计信息，直接返回空结果");
                return submitters;
            }
            
            // 第二步：获取用户详细信息 - 使用userMapper直接查询
            List<Long> userIds = userStats.stream()
                    .map(stat -> Long.valueOf(stat.get("user_id").toString()))
                    .collect(java.util.stream.Collectors.toList());
            
            log.info("需要查询的用户ID列表: {}", userIds);
            
            // 使用userMapper分批查询用户信息，避免SQL错误
            List<Map<String, Object>> userInfos = new ArrayList<>();
            if (!userIds.isEmpty()) {
                for (Long userId : userIds) {
                    try {
                        User user = userMapper.selectById(userId);
                        if (user != null && user.getDeleted() == 0) {
                            Map<String, Object> userInfo = new HashMap<>();
                            userInfo.put("id", user.getId());
                            userInfo.put("username", user.getUsername());
                            userInfos.add(userInfo);
                        }
                    } catch (Exception e) {
                        log.warn("查询用户ID {} 信息失败: {}", userId, e.getMessage());
                    }
                }
            }
            log.info("获取到 {} 个用户的详细信息", userInfos.size());
            
            if (!userInfos.isEmpty()) {
                log.info("用户信息示例: {}", userInfos.subList(0, Math.min(3, userInfos.size())));
            }
            
            // 创建用户ID到用户信息的映射
            Map<Long, Map<String, Object>> userInfoMap = userInfos.stream()
                    .collect(java.util.stream.Collectors.toMap(
                        info -> Long.valueOf(info.get("id").toString()),
                        info -> info,
                        (existing, replacement) -> existing
                    ));
            
            // 合并统计信息和用户信息
            List<Map<String, Object>> results = new ArrayList<>();
            for (Map<String, Object> stat : userStats) {
                Long userId = Long.valueOf(stat.get("user_id").toString());
                Map<String, Object> userInfo = userInfoMap.get(userId);
                
                Map<String, Object> result = new HashMap<>();
                result.put("user_id", userId);
                result.put("username", userInfo != null ? userInfo.get("username") : "未知用户");
                result.put("count", stat.get("count"));
                
                results.add(result);
            }
            
            log.info("合并后获取到 {} 个有效的提交者信息", results.size());
            
            for (Map<String, Object> result : results) {
                Map<String, Object> submitter = new HashMap<>();
                // 确保ID以字符串形式返回，避免JavaScript精度丢失
                Object userIdObj = result.get("user_id");
                String userIdStr = userIdObj != null ? String.valueOf(userIdObj) : null;
                
                submitter.put("id", userIdStr);
                submitter.put("userId", userIdStr);
                submitter.put("username", result.get("username"));
                submitter.put("count", result.get("count"));
                submitters.add(submitter);
                
                log.debug("提交者: ID={}, 用户名={}, 数量={}", 
                    userIdStr, result.get("username"), result.get("count"));
            }
            
            log.info("最终获取到 {} 个待审核提交者", submitters.size());
            return submitters;
            
        } catch (Exception e) {
            log.error("获取提交者列表失败", e);
            // 返回空列表，避免前端错误
            return new ArrayList<>();
        }
    }
    
    /**
     * 调试方法：分析提交者数据为空的原因
     */
    public Map<String, Object> debugSubmitterData() {
        log.info("=== 开始调试提交者数据 ===");
        
        Map<String, Object> debugInfo = new HashMap<>();
        
        try {
            // 1. 检查topic_audit表基本信息
            long totalCount = this.count();
            debugInfo.put("totalAuditRecords", totalCount);
            log.info("topic_audit表总记录数: {}", totalCount);
            
            // 2. 检查有user_id的记录
            long hasUserIdCount = this.count(new QueryWrapper<TopicAudit>().isNotNull("user_id"));
            debugInfo.put("recordsWithUserId", hasUserIdCount);
            log.info("有user_id的记录数: {}", hasUserIdCount);
            
            // 3. 检查待审核记录
            long pendingCount = this.count(new QueryWrapper<TopicAudit>().eq("audit_status", 0));
            debugInfo.put("pendingRecords", pendingCount);
            log.info("待审核记录数: {}", pendingCount);
            
            // 4. 检查待审核且有user_id的记录
            long pendingWithUserIdCount = this.count(new QueryWrapper<TopicAudit>().eq("audit_status", 0).isNotNull("user_id"));
            debugInfo.put("pendingRecordsWithUserId", pendingWithUserIdCount);
            log.info("待审核且有user_id的记录数: {}", pendingWithUserIdCount);
            
            // 5. 获取前5条记录的详细信息
            List<TopicAudit> sampleRecords = this.list(new QueryWrapper<TopicAudit>().last("LIMIT 5"));
            debugInfo.put("sampleRecordsCount", sampleRecords.size());
            
            List<Map<String, Object>> sampleData = new ArrayList<>();
            for (TopicAudit record : sampleRecords) {
                Map<String, Object> sample = new HashMap<>();
                sample.put("id", record.getId());
                sample.put("userId", record.getUserId());
                sample.put("auditStatus", record.getAuditStatus());
                sample.put("title", record.getTitle() != null ? record.getTitle().substring(0, Math.min(30, record.getTitle().length())) : "null");
                sampleData.add(sample);
            }
            debugInfo.put("sampleData", sampleData);
            log.info("样本数据: {}", sampleData);
            
            // 6. 检查user表的记录数 - 使用userMapper
            long userCount = 0;
            try {
                QueryWrapper<User> userCountWrapper = new QueryWrapper<>();
                userCountWrapper.eq("deleted", 0);
                userCount = userMapper.selectCount(userCountWrapper);
            } catch (Exception e) {
                log.warn("查询用户表记录数失败: {}", e.getMessage());
            }
            debugInfo.put("activeUserCount", userCount);
            log.info("活跃用户数: {}", userCount);
            
            // 7. 获取不同的user_id值 - 使用安全的MyBatis-Plus方法
            QueryWrapper<TopicAudit> distinctWrapper = new QueryWrapper<>();
            distinctWrapper.select("DISTINCT user_id")
                          .isNotNull("user_id")
                          .last("LIMIT 10");
            List<TopicAudit> distinctAudits = this.list(distinctWrapper);
            List<Map<String, Object>> distinctUserIds = distinctAudits.stream()
                    .map(audit -> {
                        Map<String, Object> idMap = new HashMap<>();
                        idMap.put("user_id", audit.getUserId());
                        return idMap;
                    })
                    .collect(java.util.stream.Collectors.toList());
            debugInfo.put("distinctUserIds", distinctUserIds);
            log.info("不同的用户ID: {}", distinctUserIds);
            
            // 8. 检查audit_status的分布 - 使用安全的MyBatis-Plus方法
            QueryWrapper<TopicAudit> statusWrapper = new QueryWrapper<>();
            statusWrapper.select("audit_status", "COUNT(*) as count")
                        .groupBy("audit_status");
            List<Map<String, Object>> statusDistribution = baseMapper.selectMaps(statusWrapper);
            debugInfo.put("auditStatusDistribution", statusDistribution);
            log.info("审核状态分布: {}", statusDistribution);
            
            log.info("=== 调试信息收集完成 ===");
            
        } catch (Exception e) {
            log.error("调试过程中发生错误", e);
            debugInfo.put("error", e.getMessage());
        }
        
        return debugInfo;
    }

    @Override
    public List<Map<String, Object>> getKnowledgePointList() {
        log.info("获取知识点列表");
        
        try {
            List<Map<String, Object>> knowledgePoints = new ArrayList<>();
            
            // 使用MyBatis-Plus原生方法获取数据，避免复杂的SQL查询
            log.info("使用MyBatis-Plus原生方法获取知识点统计");
            
            // 获取所有有知识点ID的审核记录
            QueryWrapper<TopicAudit> auditWrapper = new QueryWrapper<>();
            auditWrapper.select("know_id")
                        .isNotNull("know_id")
                        .groupBy("know_id");
            
            List<TopicAudit> auditRecords = this.list(auditWrapper);
            log.info("找到 {} 个不同的知识点", auditRecords.size());
            
            if (auditRecords.isEmpty()) {
                log.warn("没有找到任何知识点数据");
                return knowledgePoints;
            }
            
            // 统计每个知识点的使用次数
            Map<String, Integer> knowledgeCountMap = new HashMap<>();
            for (TopicAudit audit : auditRecords) {
                if (audit.getKnowId() != null) {
                    String knowId = audit.getKnowId().toString();
                    
                    // 查询该知识点的总使用次数
                    QueryWrapper<TopicAudit> countWrapper = new QueryWrapper<>();
                    countWrapper.eq("know_id", knowId);
                    long count = this.count(countWrapper);
                    
                    knowledgeCountMap.put(knowId, (int) count);
                }
            }
            
            log.info("统计完成，共 {} 个知识点有使用记录", knowledgeCountMap.size());
            
            // 从wm_knowledge表获取知识点详细信息（如果有的话）
            for (Map.Entry<String, Integer> entry : knowledgeCountMap.entrySet()) {
                String knowId = entry.getKey();
                Integer count = entry.getValue();
                
                Map<String, Object> kp = new HashMap<>();
                kp.put("id", knowId);
                kp.put("count", count);
                
                // 从wm_knowledge表获取知识点详细信息
                String knowledgeName = "未知知识点";
                String groupName = "默认分组";
                
                try {
                    // 使用KnowledgeMapper查询wm_knowledge表（根据knowledge_id字段查询）
                    Long knowledgeId = Long.valueOf(knowId);
                    QueryWrapper<Knowledge> knowledgeWrapper = new QueryWrapper<>();
                    knowledgeWrapper.eq("knowledge_id", knowledgeId)
                                   .eq("is_deleted", 0);
                    Knowledge knowledge = knowledgeMapper.selectOne(knowledgeWrapper);
                    
                    if (knowledge != null) {
                                 knowledgeName = knowledge.getKnowledgeName();
                                 groupName = knowledge.getGroupName() != null ? knowledge.getGroupName() : "默认分组";
                    } else {
                        // 如果wm_knowledge表中没有找到，尝试从audit记录的tags字段获取
                        QueryWrapper<TopicAudit> tagWrapper = new QueryWrapper<>();
                        tagWrapper.select("tags")
                                 .eq("know_id", knowId)
                                 .isNotNull("tags")
                                 .last("LIMIT 1");
                        
                        List<TopicAudit> tagAudits = this.list(tagWrapper);
                        if (!tagAudits.isEmpty() && tagAudits.get(0).getTags() != null) {
                            knowledgeName = tagAudits.get(0).getTags();
                            
                            // 尝试解析分组信息（如果tags包含分组信息）
                            if (knowledgeName.contains("-")) {
                                String[] parts = knowledgeName.split("-", 2);
                                if (parts.length == 2) {
                                    groupName = parts[0].trim();
                                    knowledgeName = parts[1].trim();
                                }
                            }
                        } else {
                            knowledgeName = "知识点ID: " + knowId;
                        }
                    }
                } catch (Exception e) {
                    log.warn("获取知识点 {} 的详细信息失败: {}", knowId, e.getMessage());
                    knowledgeName = "知识点ID: " + knowId;
                }
                
                kp.put("name", knowledgeName);
                kp.put("groupName", groupName);
                
                // 构建显示名称
                String displayName = String.format("%s - %s (%d条)", 
                    groupName, knowledgeName, count);
                kp.put("displayName", displayName);
                
                knowledgePoints.add(kp);
            }
            
            // 按使用次数排序
            knowledgePoints.sort((a, b) -> {
                Integer countA = (Integer) a.get("count");
                Integer countB = (Integer) b.get("count");
                return countB.compareTo(countA);
            });
            
            log.info("最终获取到 {} 个知识点", knowledgePoints.size());
            return knowledgePoints;
            
        } catch (Exception e) {
            log.error("获取知识点列表失败", e);
            
            // 降级到原有逻辑（使用tags字段）
            log.info("降级到使用tags字段的备用方案");
            return getKnowledgePointListFallback();
        }
    }
    
    /**
     * 备用方案：使用tags字段获取知识点列表
     */
    private List<Map<String, Object>> getKnowledgePointListFallback() {
        try {
            List<Map<String, Object>> knowledgePoints = new ArrayList<>();
            
            QueryWrapper<TopicAudit> wrapper = new QueryWrapper<>();
            wrapper.select("DISTINCT know_id", "tags")
                   .isNotNull("know_id")
                   .eq("audit_status", 0);
            
            List<TopicAudit> audits = this.list(wrapper);
            
            if (audits.isEmpty()) {
                wrapper = new QueryWrapper<>();
                wrapper.select("DISTINCT know_id", "tags")
                       .isNotNull("know_id");
                audits = this.list(wrapper);
            }
            
            Map<String, Map<String, Object>> knowledgeMap = new HashMap<>();
            
            for (TopicAudit audit : audits) {
                String knowId = audit.getKnowId() != null ? audit.getKnowId().toString() : null;
                
                if (knowId != null) {
                    if (!knowledgeMap.containsKey(knowId)) {
                        String knowledgeName = "未知知识点";
                        String groupName = "默认分组";
                        
                        try {
                            // 首先尝试从wm_knowledge表获取（根据knowledge_id字段查询）
                            Long knowledgeId = Long.valueOf(knowId);
                            QueryWrapper<Knowledge> knowledgeWrapper = new QueryWrapper<>();
                            knowledgeWrapper.eq("knowledge_id", knowledgeId)
                                           .eq("is_deleted", 0);
                            Knowledge knowledge = knowledgeMapper.selectOne(knowledgeWrapper);
                            
                            if (knowledge != null) {
                                knowledgeName = knowledge.getKnowledgeName();
                                groupName = knowledge.getGroupName() != null ? knowledge.getGroupName() : "默认分组";
                            } else {
                                // 如果wm_knowledge表中没有，使用tags字段
                                String tags = audit.getTags();
                                if (tags != null && !tags.trim().isEmpty()) {
                                    knowledgeName = tags;
                                    
                                    // 尝试解析分组信息
                                    if (knowledgeName.contains("-")) {
                                        String[] parts = knowledgeName.split("-", 2);
                                        if (parts.length == 2) {
                                            groupName = parts[0].trim();
                                            knowledgeName = parts[1].trim();
                                        }
                                    }
                                } else {
                                    knowledgeName = "知识点ID: " + knowId;
                                }
                            }
                        } catch (Exception e) {
                            log.warn("获取知识点 {} 详细信息失败: {}", knowId, e.getMessage());
                            knowledgeName = "知识点ID: " + knowId;
                        }
                        
                        Map<String, Object> kp = new HashMap<>();
                        kp.put("id", knowId);
                        kp.put("name", knowledgeName);
                        kp.put("groupName", groupName);
                        kp.put("count", 0);
                        knowledgeMap.put(knowId, kp);
                    }
                    
                    Map<String, Object> kp = knowledgeMap.get(knowId);
                    kp.put("count", ((Integer) kp.get("count")) + 1);
                }
            }
            
            for (Map<String, Object> kp : knowledgeMap.values()) {
                String displayName = String.format("%s - %s (%d条)", 
                    kp.get("groupName"), kp.get("name"), 
                    ((Integer) kp.get("count")));
                kp.put("displayName", displayName);
                knowledgePoints.add(kp);
            }
            
            knowledgePoints.sort((a, b) -> {
                Integer countA = (Integer) a.get("count");
                Integer countB = (Integer) b.get("count");
                return countB.compareTo(countA);
            });
            
            return knowledgePoints;
            
        } catch (Exception e) {
            log.error("备用方案也失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public void exportAuditData(java.io.OutputStream outputStream, String keyword, String submitter, String knowledge, String status) {
        log.info("开始导出审核数据 - keyword: {}, submitter: {}, knowledge: {}, status: {}", 
                keyword, submitter, knowledge, status);
        
        try {
            // 构建查询条件
            QueryWrapper<TopicAudit> wrapper = new QueryWrapper<>();
            
            // 添加搜索条件
            if (keyword != null && !keyword.trim().isEmpty()) {
                wrapper.and(w -> w.like("title", keyword).or().like("content", keyword));
            }
            
            if (submitter != null && !submitter.trim().isEmpty()) {
                try {
                    Long submitterId = Long.valueOf(submitter);
                    wrapper.eq("user_id", submitterId);
                } catch (NumberFormatException e) {
                    log.warn("无效的提交者ID: {}", submitter);
                }
            }
            
            if (knowledge != null && !knowledge.trim().isEmpty()) {
                wrapper.and(w -> w.eq("know_id", knowledge).or().like("knowledge_name", knowledge));
            }
            
            if (status != null && !status.trim().isEmpty()) {
                try {
                    Integer statusValue = Integer.valueOf(status);
                    wrapper.eq("audit_status", statusValue);
                } catch (NumberFormatException e) {
                    log.warn("无效的状态值: {}", status);
                }
            }
            
            wrapper.orderByDesc("submit_time");
            
            // 查询数据
            List<TopicAudit> audits = this.list(wrapper);
            log.info("查询到 {} 条记录准备导出", audits.size());
            
            // 创建CSV内容
            StringBuilder csvContent = new StringBuilder();
            csvContent.append("题目标题,题目类型,知识点,提交者,提交时间,审核状态,审核意见,审核时间\n");
            
            for (TopicAudit audit : audits) {
                // 获取用户名
                String username = "";
                try {
                    User user = userMapper.selectById(audit.getUserId());
                    if (user != null) {
                        username = user.getUsername();
                    }
                } catch (Exception e) {
                    log.warn("获取用户名失败: userId={}", audit.getUserId());
                }
                
                // 获取知识点名称 - 直接使用tags字段作为主要方案
                String knowledgeName = "";
                try {
                    // 直接使用tags字段，避免复杂的跨表查询
                    if (audit.getTags() != null && !audit.getTags().trim().isEmpty()) {
                        knowledgeName = audit.getTags();
                    } else if (audit.getKnowId() != null) {
                        // 备用方案：使用知识点ID作为显示名称
                        knowledgeName = "知识点-" + audit.getKnowId();
                    } else {
                        knowledgeName = "未知知识点";
                    }
                } catch (Exception e) {
                    log.warn("获取知识点名称失败: knowId={}", audit.getKnowId());
                    knowledgeName = "未知知识点";
                }
                
                // 获取状态文本
                String statusText = "";
                switch (audit.getAuditStatus()) {
                    case TopicAudit.AuditStatus.PENDING:
                        statusText = "待审核";
                        break;
                    case TopicAudit.AuditStatus.APPROVED:
                        statusText = "审核通过";
                        break;
                    case TopicAudit.AuditStatus.REJECTED:
                        statusText = "审核拒绝";
                        break;
                    default:
                        statusText = "未知状态";
                }
                
                // 构建CSV行
                csvContent.append(String.format("\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\"\n",
                    escapeCSV(audit.getTitle()),
                    escapeCSV(audit.getType()),
                    escapeCSV(knowledgeName), // 使用正确获取的知识点名称
                    escapeCSV(username),
                    audit.getSubmitTime() != null ? audit.getSubmitTime().toString() : "",
                    statusText,
                    escapeCSV(audit.getAuditComment()),
                    audit.getAuditTime() != null ? audit.getAuditTime().toString() : ""
                ));
            }
            
            // 写入输出流
            outputStream.write(csvContent.toString().getBytes("UTF-8"));
            outputStream.flush();
            
            log.info("审核数据导出完成，共导出 {} 条记录", audits.size());
            
        } catch (Exception e) {
            log.error("导出审核数据失败", e);
            throw new RuntimeException("导出失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * CSV字段转义
     */
    private String escapeCSV(String value) {
        if (value == null) {
            return "";
        }
        // 替换双引号为两个双引号，这是CSV的标准转义方式
        return value.replace("\"", "\"\"");
    }

    /**
     * 调试特定用户的数据 - 查看为什么查询返回空结果
     */
    public Map<String, Object> debugSpecificUserData(String userId) {
        log.info("调试特定用户数据 - 用户ID: {}", userId);
        
        Map<String, Object> debugInfo = new HashMap<>();
        
        try {
            // 1. 检查用户ID是否存在于topic_audit表中
            QueryWrapper<TopicAudit> userExistsWrapper = new QueryWrapper<>();
            userExistsWrapper.eq("user_id", userId);
            Long userRecordCount = this.count(userExistsWrapper);
            debugInfo.put("userTotalRecords", userRecordCount);
            log.info("用户 {} 在topic_audit表中的总记录数: {}", userId, userRecordCount);
            
            if (userRecordCount == 0) {
                debugInfo.put("problem", "用户ID在topic_audit表中不存在");
                log.warn("用户ID {} 在topic_audit表中不存在", userId);
                return debugInfo;
            }
            
            // 2. 检查用户的审核状态分布
            String statusSql = "SELECT audit_status, COUNT(*) as count FROM topic_audit WHERE user_id = " + userId + " GROUP BY audit_status";
            QueryWrapper<TopicAudit> statusWrapper = new QueryWrapper<>();
            statusWrapper.groupBy("audit_status");
            statusWrapper.eq("user_id", userId);
            statusWrapper.select("audit_status", "COUNT(*) as count");
            
            List<Map<String, Object>> statusDistribution = baseMapper.selectMaps(statusWrapper);
            debugInfo.put("auditStatusDistribution", statusDistribution);
            log.info("用户 {} 的审核状态分布: {}", userId, statusDistribution);
            
            // 3. 检查用户的待审核记录数
            QueryWrapper<TopicAudit> pendingWrapper = new QueryWrapper<>();
            pendingWrapper.eq("user_id", userId).eq("audit_status", 0);
            Long pendingCount = this.count(pendingWrapper);
            debugInfo.put("pendingCount", pendingCount);
            log.info("用户 {} 的待审核记录数: {}", userId, pendingCount);
            
            // 4. 检查user表中是否存在该用户 - 使用安全的方式
            try {
                // 简化的用户检查 - 避免复杂SQL
                debugInfo.put("userExists", "跳过用户表检查，专注于topic_audit表分析");
                
                // 重点分析：为什么分组查询没有返回这个用户
                log.info("专注于分析topic_audit表中用户 {} 的数据", userId);
                
            } catch (Exception e) {
                log.error("检查用户信息时出错: {}", e.getMessage());
                debugInfo.put("userCheckError", e.getMessage());
            }
            
            // 5. 检查为什么分组查询返回空（模拟分组查询的WHERE条件）
            if (pendingCount > 0) {
                // 如果有待审核记录，检查GROUP BY的结果
                QueryWrapper<TopicAudit> groupWrapper = new QueryWrapper<>();
                groupWrapper.eq("user_id", userId);
                groupWrapper.select("user_id", "DATE(submit_time) as submit_date", "COUNT(*) as count", 
                                  "SUM(CASE WHEN audit_status = 0 THEN 1 ELSE 0 END) as pending_count");
                groupWrapper.groupBy("user_id", "DATE(submit_time)");
                
                List<Map<String, Object>> groupResults = baseMapper.selectMaps(groupWrapper);
                debugInfo.put("groupByResults", groupResults);
                log.info("用户 {} 的分组结果: {}", userId, groupResults);
                
                // 检查是否有pendingCount > 0的分组
                boolean hasPendingGroups = groupResults.stream()
                    .anyMatch(group -> {
                        Object pendingCountObj = group.get("pending_count");
                        return pendingCountObj != null && ((Number) pendingCountObj).intValue() > 0;
                    });
                
                debugInfo.put("hasPendingGroups", hasPendingGroups);
                log.info("用户 {} 是否有待审核分组: {}", userId, hasPendingGroups);
                
                if (!hasPendingGroups) {
                    debugInfo.put("problem", "用户有审核记录，但所有记录都已审核完毕，不符合HAVING pendingCount > 0的条件");
                }
            } else {
                debugInfo.put("problem", "用户没有待审核记录，不符合HAVING pendingCount > 0的条件");
            }
            
            // 6. 获取用户的示例记录
            QueryWrapper<TopicAudit> sampleWrapper = new QueryWrapper<>();
            sampleWrapper.eq("user_id", userId);
            sampleWrapper.orderByDesc("submit_time");
            sampleWrapper.last("LIMIT 5");
            
            List<TopicAudit> sampleRecords = this.list(sampleWrapper);
            List<Map<String, Object>> sampleData = sampleRecords.stream().map(record -> {
                Map<String, Object> sample = new HashMap<>();
                sample.put("id", record.getId());
                sample.put("title", record.getTitle());
                sample.put("submitTime", record.getSubmitTime());
                sample.put("auditStatus", record.getAuditStatus());
                sample.put("submitDate", record.getSubmitTime() != null ? record.getSubmitTime().toLocalDate() : null);
                return sample;
            }).collect(Collectors.toList());
            
            debugInfo.put("sampleRecords", sampleData);
            log.info("用户 {} 的示例记录: {}", userId, sampleData);
            
        } catch (Exception e) {
            log.error("调试用户数据失败: {}", e.getMessage(), e);
            debugInfo.put("error", e.getMessage());
        }
        
        return debugInfo;
    }

    /**
     * 调试所有有审核记录的用户数据
     */
    public Map<String, Object> debugAllUsersWithAudits() {
        log.info("调试所有有审核记录的用户数据");
        
        Map<String, Object> debugInfo = new HashMap<>();
        
        try {
            // 1. 获取所有不同的用户ID
            QueryWrapper<TopicAudit> distinctUserWrapper = new QueryWrapper<>();
            distinctUserWrapper.select("DISTINCT user_id")
                              .isNotNull("user_id");
            
            List<TopicAudit> distinctUsers = this.list(distinctUserWrapper);
            List<String> allUserIds = distinctUsers.stream()
                .map(audit -> String.valueOf(audit.getUserId()))
                .distinct()
                .collect(Collectors.toList());
            
            debugInfo.put("totalDistinctUsers", allUserIds.size());
            debugInfo.put("allUserIds", allUserIds);
            log.info("发现 {} 个不同的用户ID", allUserIds.size());
            
            // 2. 统计每个用户的审核状态分布
            Map<String, Map<String, Object>> userStats = new HashMap<>();
            
            for (String userId : allUserIds) {
                Map<String, Object> userStat = new HashMap<>();
                
                // 总记录数
                QueryWrapper<TopicAudit> totalWrapper = new QueryWrapper<>();
                totalWrapper.eq("user_id", userId);
                long totalCount = this.count(totalWrapper);
                userStat.put("totalCount", totalCount);
                
                // 待审核记录数
                QueryWrapper<TopicAudit> pendingWrapper = new QueryWrapper<>();
                pendingWrapper.eq("user_id", userId).eq("audit_status", 0);
                long pendingCount = this.count(pendingWrapper);
                userStat.put("pendingCount", pendingCount);
                
                // 已通过记录数
                QueryWrapper<TopicAudit> approvedWrapper = new QueryWrapper<>();
                approvedWrapper.eq("user_id", userId).eq("audit_status", 1);
                long approvedCount = this.count(approvedWrapper);
                userStat.put("approvedCount", approvedCount);
                
                // 已拒绝记录数
                QueryWrapper<TopicAudit> rejectedWrapper = new QueryWrapper<>();
                rejectedWrapper.eq("user_id", userId).eq("audit_status", 2);
                long rejectedCount = this.count(rejectedWrapper);
                userStat.put("rejectedCount", rejectedCount);
                
                // 最新提交时间
                QueryWrapper<TopicAudit> latestWrapper = new QueryWrapper<>();
                latestWrapper.eq("user_id", userId)
                           .orderByDesc("submit_time")
                           .last("LIMIT 1");
                
                List<TopicAudit> latestRecords = this.list(latestWrapper);
                if (!latestRecords.isEmpty()) {
                    userStat.put("latestSubmitTime", latestRecords.get(0).getSubmitTime());
                }
                
                userStats.put(userId, userStat);
                
                log.info("用户 {} 统计: 总数={}, 待审核={}, 已通过={}, 已拒绝={}", 
                        userId, totalCount, pendingCount, approvedCount, rejectedCount);
            }
            
            debugInfo.put("userStats", userStats);
            
            // 3. 分析哪些用户会出现在分组查询中（pendingCount > 0）
            List<String> usersWithPending = userStats.entrySet().stream()
                .filter(entry -> {
                    Map<String, Object> stats = entry.getValue();
                    Object pendingCountObj = stats.get("pendingCount");
                    return pendingCountObj != null && ((Number) pendingCountObj).longValue() > 0;
                })
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
            
            debugInfo.put("usersWithPendingAudits", usersWithPending);
            debugInfo.put("usersWithPendingCount", usersWithPending.size());
            log.info("有待审核记录的用户: {} (共{}个)", usersWithPending, usersWithPending.size());
            
            // 4. 解释为什么某些用户不出现在分组查询中
            List<String> usersWithoutPending = userStats.entrySet().stream()
                .filter(entry -> {
                    Map<String, Object> stats = entry.getValue();
                    Object pendingCountObj = stats.get("pendingCount");
                    return pendingCountObj == null || ((Number) pendingCountObj).longValue() == 0;
                })
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
            
            debugInfo.put("usersWithoutPendingAudits", usersWithoutPending);
            debugInfo.put("usersWithoutPendingCount", usersWithoutPending.size());
            log.info("没有待审核记录的用户: {} (共{}个)", usersWithoutPending, usersWithoutPending.size());
            
            // 5. 总结
            Map<String, Object> summary = new HashMap<>();
            summary.put("totalUsers", allUserIds.size());
            summary.put("usersInGroupQuery", usersWithPending.size());
            summary.put("usersNotInGroupQuery", usersWithoutPending.size());
            summary.put("reason", "分组查询只显示有待审核记录的用户（HAVING pendingCount > 0）");
            
            debugInfo.put("summary", summary);
            
        } catch (Exception e) {
            log.error("调试所有用户数据失败: {}", e.getMessage(), e);
            debugInfo.put("error", e.getMessage());
        }
        
        return debugInfo;
    }

    @Override
    @Transactional
    public void revertAudit(Long auditId, Long operatorId) {
        log.info("开始撤销审核, auditId: {}, operatorId: {}", auditId, operatorId);
        
        // 获取审核记录
        TopicAudit audit = this.getById(auditId);
        if (audit == null) {
            throw new IllegalArgumentException("审核记录不存在");
        }
        
        // 检查当前审核状态
        if (audit.getAuditStatus() == TopicAudit.AuditStatus.PENDING) {
            throw new IllegalArgumentException("该题目尚未审核，无法撤销");
        }
        
        int originalStatus = audit.getAuditStatus();
        String originalComment = audit.getAuditComment();
        
        try {
            // 如果是审核通过的题目，需要从正式题库中删除
            if (originalStatus == TopicAudit.AuditStatus.APPROVED) {
                deleteFromTopicBank(audit);
            }
            
            // 如果是审核拒绝的题目，需要从拒绝记录表中删除
            if (originalStatus == TopicAudit.AuditStatus.REJECTED) {
                deleteFromRejectedTopics(audit);
            }
            
            // 重置审核状态
            audit.setAuditStatus(TopicAudit.AuditStatus.PENDING);
            audit.setAuditorId(null);
            audit.setAuditTime(null);
            audit.setAuditComment(null);
            audit.setUpdatedAt(LocalDateTime.now());
            
            this.updateById(audit);
            
            // 发送撤销通知消息
            sendRevertNotification(audit, originalStatus, originalComment, operatorId);
            
            log.info("审核撤销成功, auditId: {}, 原状态: {}", auditId, originalStatus);
            
        } catch (Exception e) {
            log.error("撤销审核失败, auditId: {}, error: {}", auditId, e.getMessage(), e);
            throw new RuntimeException("撤销审核失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从正式题库中删除题目
     */
    private void deleteFromTopicBank(TopicAudit audit) {
        try {
            // 根据审核记录的信息查找并删除正式题库中的题目
            // 由于我们在添加到题库时没有保存映射关系，需要通过题目内容匹配
            QueryWrapper<Topic> wrapper = new QueryWrapper<>();
            wrapper.eq("know_id", audit.getKnowId())
                   .eq("type", audit.getType())
                   .eq("title", audit.getTitle())
                   .eq("answer", audit.getAnswer());
            
            // 添加更多匹配条件以确保唯一性
            if (audit.getScore() != null) {
                wrapper.eq("score", audit.getScore());
            }
            if (audit.getDifficulty() != null) {
                wrapper.eq("difficulty", audit.getDifficulty());
            }
            
            List<Topic> topics = topicMapper.selectList(wrapper);
            
            if (topics.isEmpty()) {
                log.warn("未找到对应的正式题库记录进行删除, auditId: {}", audit.getId());
                return;
            }
            
            if (topics.size() > 1) {
                log.warn("找到多个匹配的题库记录, 将删除最近创建的一个, auditId: {}, 匹配数量: {}", 
                        audit.getId(), topics.size());
                // 按创建时间降序排列，删除最新的一个
                topics.sort((t1, t2) -> t2.getCreatedAt().compareTo(t1.getCreatedAt()));
            }
            
            Topic topicToDelete = topics.get(0);
            topicMapper.deleteById(topicToDelete.getId());
            
            log.info("已从正式题库删除题目, topicId: {}, auditId: {}", topicToDelete.getId(), audit.getId());
            
        } catch (Exception e) {
            log.error("从正式题库删除题目失败, auditId: {}", audit.getId(), e);
            throw new RuntimeException("从正式题库删除题目失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从拒绝记录表中删除记录
     */
    private void deleteFromRejectedTopics(TopicAudit audit) {
        try {
            QueryWrapper<TopicRejected> wrapper = new QueryWrapper<>();
            wrapper.eq("audit_id", audit.getId());
            
            int deletedCount = topicRejectedMapper.delete(wrapper);
            
            if (deletedCount > 0) {
                log.info("已从拒绝记录表删除记录, auditId: {}, 删除数量: {}", audit.getId(), deletedCount);
            } else {
                log.warn("未找到对应的拒绝记录进行删除, auditId: {}", audit.getId());
            }
            
        } catch (Exception e) {
            log.error("从拒绝记录表删除记录失败, auditId: {}", audit.getId(), e);
            throw new RuntimeException("从拒绝记录表删除记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 发送撤销通知消息
     */
    private void sendRevertNotification(TopicAudit audit, int originalStatus, String originalComment, Long operatorId) {
        try {
            String statusText = originalStatus == TopicAudit.AuditStatus.APPROVED ? "通过" : "拒绝";
            String title = "审核撤销通知";
            String content = String.format(
                "您的题目「%s」的审核结果已被撤销。原审核结果：%s。题目将重新进入待审核状态。",
                audit.getTitle(), statusText
            );
            
            // 使用与现有代码相同的调用模式，避免方法签名问题
            // 如果SystemMessageService没有撤销消息的专用方法，我们跳过发送消息
            // 或者可以使用日志记录代替
            log.info("审核撤销通知: userId={}, auditId={}, 原状态={}, 通知内容: {}", 
                    audit.getUserId(), audit.getId(), statusText, content);
            
            // TODO: 需要在SystemMessageService中添加sendAuditRevertedMessage方法
            // systemMessageService.sendAuditRevertedMessage(audit.getUserId(), audit.getId(), audit.getTitle());
            
        } catch (Exception e) {
            log.warn("发送撤销通知消息失败, auditId: {}, error: {}", audit.getId(), e.getMessage());
            // 不抛出异常，避免影响主要撤销流程
        }
    }
}