package com.edu.maizi_edu_sys.service.impl;

import com.edu.maizi_edu_sys.config.MineRuProperties;
import com.edu.maizi_edu_sys.entity.MineRuApiUsage;
import com.edu.maizi_edu_sys.repository.MineRuApiUsageRepository;
import com.edu.maizi_edu_sys.service.MineRuTokenManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * MineRU API密钥管理服务实现
 * 
 * <AUTHOR> Integration
 * @since 2025-01-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MineRuTokenManagerImpl implements MineRuTokenManager {
    
    private final MineRuProperties mineRuProperties;
    private final MineRuApiUsageRepository usageRepository;
    
    // 轮询计数器
    private final AtomicInteger roundRobinCounter = new AtomicInteger(0);
    
    // 密钥健康状态缓存
    private final Map<String, Boolean> tokenHealthStatus = new ConcurrentHashMap<>();
    
    // 密钥故障时间记录
    private final Map<String, LocalDateTime> tokenFailureTime = new ConcurrentHashMap<>();
    
    @Override
    public MineRuProperties.Api.TokenConfig getNextAvailableToken(int estimatedPages) {
        List<MineRuProperties.Api.TokenConfig> availableTokens = getAvailableTokens();
        
        if (availableTokens.isEmpty()) {
            log.warn("没有可用的MineRU API密钥");
            return null;
        }
        
        String strategy = mineRuProperties.getApi().getLoadBalance().getStrategy();
        
        switch (strategy.toLowerCase()) {
            case "priority":
                return selectByPriority(availableTokens, estimatedPages);
            case "least-used":
                return selectByLeastUsed(availableTokens);
            case "round-robin":
            default:
                return selectByRoundRobin(availableTokens);
        }
    }
    
    @Override
    public void recordApiUsage(String tokenName, int parsedPages, boolean success) {
        try {
            LocalDate today = LocalDate.now();
            
            // 确保今日记录存在
            ensureTodayRecord(tokenName, today);
            
            // 更新使用统计
            int isSuccess = success ? 1 : 0;
            int isFailure = success ? 0 : 1;
            
            int updated = usageRepository.updateUsageStats(tokenName, today, parsedPages, isSuccess, isFailure);
            
            if (updated == 0) {
                log.warn("更新API使用统计失败: tokenName={}, parsedPages={}", tokenName, parsedPages);
            }
            
            // 检查是否超出高优先级限制
            checkAndMarkPriorityLimit(tokenName, today);
            
            log.debug("记录API使用情况: tokenName={}, parsedPages={}, success={}", tokenName, parsedPages, success);
            
        } catch (Exception e) {
            log.error("记录API使用情况异常: tokenName={}, parsedPages={}", tokenName, parsedPages, e);
        }
    }
    
    @Override
    public boolean isTokenAvailable(String tokenName) {
        // 检查配置中是否启用
        Optional<MineRuProperties.Api.TokenConfig> tokenConfig = mineRuProperties.getApi().getTokens()
                .stream()
                .filter(token -> tokenName.equals(token.getName()) && token.isEnabled())
                .findFirst();
        
        if (!tokenConfig.isPresent() || !StringUtils.hasText(tokenConfig.get().getKey())) {
            return false;
        }
        
        // 检查健康状态
        Boolean healthStatus = tokenHealthStatus.get(tokenName);
        if (healthStatus != null && !healthStatus) {
            // 检查是否需要恢复
            LocalDateTime failureTime = tokenFailureTime.get(tokenName);
            if (failureTime != null) {
                long minutesSinceFailure = java.time.Duration.between(failureTime, LocalDateTime.now()).toMinutes();
                if (minutesSinceFailure >= 10) { // 10分钟后自动恢复
                    markTokenAvailable(tokenName);
                    log.info("密钥自动恢复可用状态: {}", tokenName);
                } else {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    @Override
    public int getRemainingPriorityPages(String tokenName) {
        try {
            LocalDate today = LocalDate.now();
            MineRuApiUsage usage = usageRepository.findByTokenNameAndUsageDate(tokenName, today);
            
            Optional<MineRuProperties.Api.TokenConfig> tokenConfig = mineRuProperties.getApi().getTokens()
                    .stream()
                    .filter(token -> tokenName.equals(token.getName()))
                    .findFirst();
            
            if (!tokenConfig.isPresent()) {
                return 0;
            }
            
            int dailyLimit = tokenConfig.get().getDailyLimit();
            int usedPages = usage != null ? usage.getParsedPages() : 0;
            
            return Math.max(0, dailyLimit - usedPages);
            
        } catch (Exception e) {
            log.error("获取剩余高优先级页数异常: tokenName={}", tokenName, e);
            return 0;
        }
    }
    
    @Override
    public void markTokenUnavailable(String tokenName, String reason) {
        tokenHealthStatus.put(tokenName, false);
        tokenFailureTime.put(tokenName, LocalDateTime.now());
        log.warn("标记密钥不可用: tokenName={}, reason={}", tokenName, reason);
    }
    
    @Override
    public void markTokenAvailable(String tokenName) {
        tokenHealthStatus.put(tokenName, true);
        tokenFailureTime.remove(tokenName);
        log.info("标记密钥可用: tokenName={}", tokenName);
    }
    
    @Override
    public List<MineRuApiUsageRepository.TokenUsageSummary> getUsageStatistics() {
        try {
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusDays(30); // 最近30天
            
            return usageRepository.getUsageSummary(startDate, endDate);
            
        } catch (Exception e) {
            log.error("获取使用统计异常", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public void cleanupExpiredRecords(int retentionDays) {
        try {
            LocalDate expireDate = LocalDate.now().minusDays(retentionDays);
            int deleted = usageRepository.deleteExpiredRecords(expireDate);
            
            if (deleted > 0) {
                log.info("清理过期API使用记录: {} 条", deleted);
            }
            
        } catch (Exception e) {
            log.error("清理过期记录异常", e);
        }
    }
    
    // 私有辅助方法
    
    private List<MineRuProperties.Api.TokenConfig> getAvailableTokens() {
        return mineRuProperties.getApi().getTokens()
                .stream()
                .filter(token -> token.isEnabled() && 
                               StringUtils.hasText(token.getKey()) && 
                               isTokenAvailable(token.getName()))
                .collect(Collectors.toList());
    }
    
    private MineRuProperties.Api.TokenConfig selectByPriority(List<MineRuProperties.Api.TokenConfig> tokens, int estimatedPages) {
        // 按优先级排序，优先选择高优先级且有足够额度的密钥
        return tokens.stream()
                .sorted(Comparator.comparingInt(MineRuProperties.Api.TokenConfig::getPriority))
                .filter(token -> getRemainingPriorityPages(token.getName()) >= estimatedPages)
                .findFirst()
                .orElse(tokens.get(0)); // 如果没有足够额度的，选择第一个可用的
    }
    
    private MineRuProperties.Api.TokenConfig selectByLeastUsed(List<MineRuProperties.Api.TokenConfig> tokens) {
        // 选择当日使用量最少的密钥
        LocalDate today = LocalDate.now();
        
        return tokens.stream()
                .min((t1, t2) -> {
                    MineRuApiUsage usage1 = usageRepository.findByTokenNameAndUsageDate(t1.getName(), today);
                    MineRuApiUsage usage2 = usageRepository.findByTokenNameAndUsageDate(t2.getName(), today);
                    
                    int pages1 = usage1 != null ? usage1.getParsedPages() : 0;
                    int pages2 = usage2 != null ? usage2.getParsedPages() : 0;
                    
                    return Integer.compare(pages1, pages2);
                })
                .orElse(tokens.get(0));
    }
    
    private MineRuProperties.Api.TokenConfig selectByRoundRobin(List<MineRuProperties.Api.TokenConfig> tokens) {
        // 轮询选择
        int index = roundRobinCounter.getAndIncrement() % tokens.size();
        return tokens.get(index);
    }
    
    private void ensureTodayRecord(String tokenName, LocalDate today) {
        MineRuApiUsage existing = usageRepository.findByTokenNameAndUsageDate(tokenName, today);
        
        if (existing == null) {
            MineRuApiUsage newRecord = new MineRuApiUsage();
            newRecord.setTokenName(tokenName);
            newRecord.setUsageDate(today);
            newRecord.setParsedPages(0);
            newRecord.setRequestCount(0);
            newRecord.setSuccessCount(0);
            newRecord.setFailureCount(0);
            newRecord.setExceededPriorityLimit(false);
            newRecord.setCreatedAt(LocalDateTime.now());
            newRecord.setUpdatedAt(LocalDateTime.now());
            
            usageRepository.insert(newRecord);
            log.debug("创建今日API使用记录: tokenName={}, date={}", tokenName, today);
        }
    }
    
    private void checkAndMarkPriorityLimit(String tokenName, LocalDate today) {
        MineRuApiUsage usage = usageRepository.findByTokenNameAndUsageDate(tokenName, today);
        
        if (usage != null && !usage.getExceededPriorityLimit()) {
            Optional<MineRuProperties.Api.TokenConfig> tokenConfig = mineRuProperties.getApi().getTokens()
                    .stream()
                    .filter(token -> tokenName.equals(token.getName()))
                    .findFirst();
            
            if (tokenConfig.isPresent() && usage.getParsedPages() >= tokenConfig.get().getDailyLimit()) {
                usageRepository.markExceededPriorityLimit(tokenName, today);
                log.info("密钥已超出高优先级限制: tokenName={}, usedPages={}, limit={}", 
                        tokenName, usage.getParsedPages(), tokenConfig.get().getDailyLimit());
            }
        }
    }
}
