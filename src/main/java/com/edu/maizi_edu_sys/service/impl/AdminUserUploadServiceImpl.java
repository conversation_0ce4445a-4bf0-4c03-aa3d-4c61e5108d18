package com.edu.maizi_edu_sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.maizi_edu_sys.entity.TopicAudit;
import com.edu.maizi_edu_sys.entity.User;
import com.edu.maizi_edu_sys.repository.TopicAuditMapper;
import com.edu.maizi_edu_sys.mapper.UserMapper;
import com.edu.maizi_edu_sys.service.AdminUserUploadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 管理员用户上传统计服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AdminUserUploadServiceImpl implements AdminUserUploadService {
    
    private final TopicAuditMapper topicAuditMapper;
    private final UserMapper userMapper;
    
    @Override
    public Map<String, Object> getUploadOverview() {
        Map<String, Object> overview = new HashMap<>();
        
        LocalDate today = LocalDate.now();
        LocalDateTime todayStart = today.atStartOfDay();
        LocalDateTime todayEnd = today.plusDays(1).atStartOfDay();
        
        // 今日总上传量
        Long todayTotal = topicAuditMapper.selectCount(
            new QueryWrapper<TopicAudit>()
                .ge("submit_time", todayStart)
                .lt("submit_time", todayEnd)
        );
        
        // 今日活跃用户数
        List<Long> activeUsers = topicAuditMapper.selectList(
            new QueryWrapper<TopicAudit>()
                .select("DISTINCT user_id")
                .ge("submit_time", todayStart)
                .lt("submit_time", todayEnd)
        ).stream().map(TopicAudit::getUserId).collect(Collectors.toList());
        
        // 平均每用户上传量
        Double avgUploadPerUser = activeUsers.isEmpty() ? 0.0 : 
            todayTotal.doubleValue() / activeUsers.size();
        
        // 异常上传用户数（单日上传超过1000的用户）
        Map<Long, Long> userUploadCounts = getUserUploadCountsForDate(today);
        long anomalousUsers = userUploadCounts.values().stream()
            .mapToLong(Long::longValue)
            .filter(count -> count > 1000)
            .count();
        
        // 最高上传用户及数量
        Optional<Map.Entry<Long, Long>> topUser = userUploadCounts.entrySet().stream()
            .max(Map.Entry.comparingByValue());
        
        overview.put("todayTotal", todayTotal);
        overview.put("activeUsers", activeUsers.size());
        overview.put("avgUploadPerUser", Math.round(avgUploadPerUser * 100.0) / 100.0);
        overview.put("anomalousUsers", anomalousUsers);
        overview.put("topUserId", topUser.map(Map.Entry::getKey).orElse(null));
        overview.put("topUserCount", topUser.map(Map.Entry::getValue).orElse(0L));
        
        return overview;
    }
    
    @Override
    public IPage<?> getDailyUploadStats(int page, int size, LocalDate startDate, LocalDate endDate, 
                                       String username, String orderBy, String orderType) {
        
        // 设置默认日期范围
        if (startDate == null) {
            startDate = LocalDate.now().minusDays(7);
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }
        
        List<Map<String, Object>> dailyStats = new ArrayList<>();
        
        // 按日期遍历，统计每日数据
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            Map<Long, Long> userCounts = getUserUploadCountsForDate(currentDate);
            
            // 如果有用户名筛选
            if (username != null && !username.trim().isEmpty()) {
                List<User> matchingUsers = userMapper.selectList(
                    new QueryWrapper<User>().like("username", username)
                );
                Set<Long> matchingUserIds = matchingUsers.stream()
                    .map(User::getId).collect(Collectors.toSet());
                userCounts = userCounts.entrySet().stream()
                    .filter(entry -> matchingUserIds.contains(entry.getKey()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            }
            
            for (Map.Entry<Long, Long> entry : userCounts.entrySet()) {
                Long userId = entry.getKey();
                Long uploadCount = entry.getValue();
                
                User user = userMapper.selectById(userId);
                if (user != null) {
                    Map<String, Object> stat = new HashMap<>();
                    stat.put("date", currentDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    stat.put("userId", userId.toString()); // 转换为字符串，避免JavaScript精度问题
                    stat.put("username", user.getUsername());
                    stat.put("uploadCount", uploadCount);
                    stat.put("isAnomalous", uploadCount > 1000); // 超过1000为异常
                    
                    // 计算该用户当日上传时间分布
                    List<LocalDateTime> uploadTimes = getUploadTimesForUserAndDate(userId, currentDate);
                    stat.put("uploadTimes", uploadTimes);
                    stat.put("timeSpan", calculateTimeSpan(uploadTimes));
                    
                    dailyStats.add(stat);
                }
            }
            currentDate = currentDate.plusDays(1);
        }
        
        // 排序
        if (orderBy != null) {
            Comparator<Map<String, Object>> comparator = getComparator(orderBy);
            if ("desc".equalsIgnoreCase(orderType)) {
                comparator = comparator.reversed();
            }
            dailyStats.sort(comparator);
        }
        
        // 分页
        int start = (page - 1) * size;
        int end = Math.min(start + size, dailyStats.size());
        List<Map<String, Object>> pagedResults = dailyStats.subList(start, end);
        
        IPage<Map<String, Object>> result = new Page<>(page, size, dailyStats.size());
        result.setRecords(pagedResults);
        
        return result;
    }
    
    @Override
    public List<?> detectAnomalousUploads(LocalDate date) {
        if (date == null) {
            date = LocalDate.now();
        }
        
        Map<Long, Long> userCounts = getUserUploadCountsForDate(date);
        List<Map<String, Object>> anomalies = new ArrayList<>();
        
        for (Map.Entry<Long, Long> entry : userCounts.entrySet()) {
            Long userId = entry.getKey();
            Long uploadCount = entry.getValue();
            
            // 检测异常行为
            List<String> anomalyTypes = new ArrayList<>();
            
            // 1. 单日上传过多
            if (uploadCount > 2000) {
                anomalyTypes.add("单日上传过多（>2000）");
            } else if (uploadCount > 1000) {
                anomalyTypes.add("单日上传较多（>1000）");
            }
            
            // 2. 短时间内集中上传
            List<LocalDateTime> uploadTimes = getUploadTimesForUserAndDate(userId, date);
            
            // 检测1分钟内集中上传
            if (check1MinuteConcentratedUpload(uploadTimes)) {
                anomalyTypes.add("1分钟内集中上传（>100条）");
            }
            
            // 检测3分钟内集中上传
            if (check3MinuteConcentratedUpload(uploadTimes)) {
                anomalyTypes.add("3分钟内集中上传（>300条）");
            }
            
            // 3. 连续高频上传
            boolean isHighFrequency = checkHighFrequencyPattern(uploadTimes);
            if (isHighFrequency) {
                anomalyTypes.add("高频连续上传");
            }
            
            if (!anomalyTypes.isEmpty()) {
                User user = userMapper.selectById(userId);
                if (user != null) {
                    Map<String, Object> anomaly = new HashMap<>();
                    anomaly.put("userId", userId.toString()); // 转换为字符串，避免JavaScript精度问题
                    anomaly.put("username", user.getUsername());
                    anomaly.put("uploadCount", uploadCount);
                    anomaly.put("date", date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    anomaly.put("anomalyTypes", anomalyTypes);
                    anomaly.put("timeSpan", calculateTimeSpan(uploadTimes));
                    anomaly.put("riskLevel", determineRiskLevel(uploadCount, anomalyTypes.size()));
                    
                    anomalies.add(anomaly);
                }
            }
        }
        
        // 按风险等级和上传量排序
        anomalies.sort((a, b) -> {
            String riskA = (String) a.get("riskLevel");
            String riskB = (String) b.get("riskLevel");
            int riskCompare = getRiskLevelValue(riskB) - getRiskLevelValue(riskA);
            if (riskCompare != 0) return riskCompare;
            
            Long countA = (Long) a.get("uploadCount");
            Long countB = (Long) b.get("uploadCount");
            return countB.compareTo(countA);
        });
        
        return anomalies;
    }
    
    @Override
    public Map<String, Object> getUserUploadTrend(Long userId, int days) {
        Map<String, Object> trend = new HashMap<>();
        List<String> dates = new ArrayList<>();
        List<Long> uploadCounts = new ArrayList<>();
        
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(days - 1);
        
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            LocalDateTime dayStart = date.atStartOfDay();
            LocalDateTime dayEnd = date.plusDays(1).atStartOfDay();
            
            QueryWrapper<TopicAudit> query = new QueryWrapper<TopicAudit>()
                .ge("submit_time", dayStart)
                .lt("submit_time", dayEnd);
            
            if (userId != null) {
                query.eq("user_id", userId);
            }
            
            Long count = topicAuditMapper.selectCount(query);
            
            dates.add(date.format(DateTimeFormatter.ofPattern("MM-dd")));
            uploadCounts.add(count);
        }
        
        trend.put("dates", dates);
        trend.put("uploadCounts", uploadCounts);
        
        // 计算平均值、最大值、趋势
        double avgCount = uploadCounts.stream().mapToLong(Long::longValue).average().orElse(0.0);
        long maxCount = uploadCounts.stream().mapToLong(Long::longValue).max().orElse(0L);
        
        trend.put("avgCount", Math.round(avgCount * 100.0) / 100.0);
        trend.put("maxCount", maxCount);
        trend.put("trendDirection", calculateTrendDirection(uploadCounts));
        
        return trend;
    }
    
    @Override
    public Map<String, Object> getUserUploadDetail(Long userId, LocalDate date) {
        if (date == null) {
            date = LocalDate.now();
        }
        
        Map<String, Object> detail = new HashMap<>();
        
        // 确保所有必需字段都有默认值
        detail.put("userId", userId != null ? userId.toString() : "0"); // 转为字符串避免精度问题
        detail.put("username", "加载中...");
        detail.put("userStatus", "加载中...");
        detail.put("date", date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        detail.put("totalUploads", 0);
        detail.put("hourlyStats", new HashMap<>());
        detail.put("timeline", new ArrayList<>());
        
        // 用户基本信息
        User user = null;
        if (userId != null) {
            user = userMapper.selectById(userId);
            log.debug("查询用户信息: userId={}, found={}", userId, user != null);
        }
        
        if (user != null) {
            detail.put("userId", userId.toString()); // 转为字符串避免精度问题
            detail.put("username", user.getUsername() != null ? user.getUsername() : "未知用户");
            detail.put("userStatus", getUserStatusText(user.getStatus()));
        } else {
            // 如果用户不存在，提供默认值
            detail.put("userId", userId != null ? userId.toString() : "0");
            detail.put("username", "用户不存在 (ID: " + userId + ")");
            detail.put("userStatus", "未知状态");
            log.warn("用户不存在或查询失败: userId={}", userId);
            
            // 增加调试信息：检查用户表中是否有接近的ID
            if (userId != null) {
                try {
                    List<User> nearbyUsers = userMapper.selectList(
                        new QueryWrapper<User>()
                            .gt("id", userId - 1000)
                            .lt("id", userId + 1000)
                            .last("LIMIT 5")
                    );
                    log.debug("附近的用户ID: {}", nearbyUsers.stream()
                        .map(u -> u.getId().toString())
                        .collect(java.util.stream.Collectors.joining(", ")));
                } catch (Exception e) {
                    log.error("查询附近用户失败", e);
                }
            }
        }
        
        // 指定日期上传详情
        LocalDateTime dayStart = date.atStartOfDay();
        LocalDateTime dayEnd = date.plusDays(1).atStartOfDay();
        
        try {
            List<TopicAudit> uploads = topicAuditMapper.selectList(
                new QueryWrapper<TopicAudit>()
                    .eq("user_id", userId)
                    .ge("submit_time", dayStart)
                    .lt("submit_time", dayEnd)
                    .orderByAsc("submit_time")
            );
            
            // 更新上传总数
            detail.put("totalUploads", uploads != null ? uploads.size() : 0);
            
            if (uploads != null && !uploads.isEmpty()) {
                // 按小时统计
                Map<Integer, Long> hourlyStats = uploads.stream()
                    .collect(Collectors.groupingBy(
                        upload -> upload.getSubmitTime() != null ? upload.getSubmitTime().getHour() : 0,
                        Collectors.counting()
                    ));
                detail.put("hourlyStats", hourlyStats);
                
                // 上传时间线
                List<Map<String, Object>> timeline = uploads.stream()
                    .map(upload -> {
                        Map<String, Object> timePoint = new HashMap<>();
                        timePoint.put("time", upload.getSubmitTime() != null ? 
                            upload.getSubmitTime().format(DateTimeFormatter.ofPattern("HH:mm:ss")) : "未知时间");
                        timePoint.put("title", upload.getTitle() != null ? upload.getTitle() : "未知题目");
                        timePoint.put("auditStatus", upload.getAuditStatus() != null ? upload.getAuditStatus() : 0);
                        return timePoint;
                    })
                    .collect(Collectors.toList());
                detail.put("timeline", timeline);
            } else {
                // 如果没有上传记录，保持默认的空值
                log.debug("用户 {} 在 {} 没有上传记录", userId, date);
            }
        } catch (Exception e) {
            log.error("查询用户上传记录时出错: userId={}, date={}", userId, date, e);
            // 保持默认值
        }
        
        return detail;
    }
    
    @Override
    public void setUserUploadLimit(Long userId, Integer dailyLimit, String reason, Long adminId) {
        // TODO: 实现用户上传限制功能
        // 这里可以创建一个用户限制表来记录限制信息
        log.info("管理员 {} 为用户 {} 设置每日上传限制: {} 条，原因: {}", adminId, userId, dailyLimit, reason);
    }
    
    @Override
    public String exportUploadStats(LocalDate startDate, LocalDate endDate) {
        // TODO: 实现导出功能
        // 这里可以生成Excel或CSV文件
        log.info("导出用户上传统计: {} 到 {}", startDate, endDate);
        return "/exports/user-upload-stats-" + System.currentTimeMillis() + ".xlsx";
    }
    
    // 辅助方法
    private Map<Long, Long> getUserUploadCountsForDate(LocalDate date) {
        LocalDateTime dayStart = date.atStartOfDay();
        LocalDateTime dayEnd = date.plusDays(1).atStartOfDay();
        
        List<TopicAudit> uploads = topicAuditMapper.selectList(
            new QueryWrapper<TopicAudit>()
                .select("user_id")
                .ge("submit_time", dayStart)
                .lt("submit_time", dayEnd)
        );
        
        return uploads.stream()
            .collect(Collectors.groupingBy(
                TopicAudit::getUserId,
                Collectors.counting()
            ));
    }
    
    private List<LocalDateTime> getUploadTimesForUserAndDate(Long userId, LocalDate date) {
        LocalDateTime dayStart = date.atStartOfDay();
        LocalDateTime dayEnd = date.plusDays(1).atStartOfDay();
        
        return topicAuditMapper.selectList(
            new QueryWrapper<TopicAudit>()
                .select("submit_time")
                .eq("user_id", userId)
                .ge("submit_time", dayStart)
                .lt("submit_time", dayEnd)
                .orderByAsc("submit_time")
        ).stream()
        .map(TopicAudit::getSubmitTime)
        .collect(Collectors.toList());
    }
    
    private String calculateTimeSpan(List<LocalDateTime> uploadTimes) {
        if (uploadTimes.isEmpty()) return "无";
        if (uploadTimes.size() == 1) return "单次上传";
        
        LocalDateTime first = uploadTimes.get(0);
        LocalDateTime last = uploadTimes.get(uploadTimes.size() - 1);
        
        long minutes = java.time.Duration.between(first, last).toMinutes();
        if (minutes < 60) {
            return minutes + "分钟";
        } else {
            return (minutes / 60) + "小时" + (minutes % 60) + "分钟";
        }
    }
    
    private long calculateTimeSpanMinutes(List<LocalDateTime> uploadTimes) {
        if (uploadTimes.size() < 2) return 0;
        
        LocalDateTime first = uploadTimes.get(0);
        LocalDateTime last = uploadTimes.get(uploadTimes.size() - 1);
        
        return java.time.Duration.between(first, last).toMinutes();
    }
    
    private boolean checkHighFrequencyPattern(List<LocalDateTime> uploadTimes) {
        if (uploadTimes.size() < 10) return false;
        
        // 检查是否有连续10次上传间隔都小于5分钟
        for (int i = 0; i < uploadTimes.size() - 9; i++) {
            boolean isHighFreq = true;
            for (int j = i; j < i + 9; j++) {
                long interval = java.time.Duration.between(uploadTimes.get(j), uploadTimes.get(j + 1)).toMinutes();
                if (interval > 5) {
                    isHighFreq = false;
                    break;
                }
            }
            if (isHighFreq) return true;
        }
        
        return false;
    }
    
    /**
     * 检测1分钟内集中上传
     */
    private boolean check1MinuteConcentratedUpload(List<LocalDateTime> uploadTimes) {
        if (uploadTimes.size() < 100) return false;
        
        for (int i = 0; i <= uploadTimes.size() - 100; i++) {
            LocalDateTime startTime = uploadTimes.get(i);
            LocalDateTime endTime = uploadTimes.get(i + 99);
            
            long minutes = java.time.Duration.between(startTime, endTime).toMinutes();
            if (minutes <= 1) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检测3分钟内集中上传
     */
    private boolean check3MinuteConcentratedUpload(List<LocalDateTime> uploadTimes) {
        if (uploadTimes.size() < 300) return false;
        
        for (int i = 0; i <= uploadTimes.size() - 300; i++) {
            LocalDateTime startTime = uploadTimes.get(i);
            LocalDateTime endTime = uploadTimes.get(i + 299);
            
            long minutes = java.time.Duration.between(startTime, endTime).toMinutes();
            if (minutes <= 3) {
                return true;
            }
        }
        
        return false;
    }
    
    private String determineRiskLevel(Long uploadCount, int anomalyTypeCount) {
        if (uploadCount > 3000 || anomalyTypeCount >= 3) {
            return "高风险";
        } else if (uploadCount > 1000 || anomalyTypeCount >= 2) {
            return "中风险";
        } else {
            return "低风险";
        }
    }
    
    private int getRiskLevelValue(String riskLevel) {
        if ("高风险".equals(riskLevel)) {
            return 3;
        } else if ("中风险".equals(riskLevel)) {
            return 2;
        } else if ("低风险".equals(riskLevel)) {
            return 1;
        } else {
            return 0;
        }
    }
    
    private Comparator<Map<String, Object>> getComparator(String orderBy) {
        if ("uploadCount".equals(orderBy)) {
            return Comparator.comparing(map -> (Long) map.get("uploadCount"));
        } else if ("date".equals(orderBy)) {
            return Comparator.comparing(map -> (String) map.get("date"));
        } else if ("username".equals(orderBy)) {
            return Comparator.comparing(map -> (String) map.get("username"));
        } else {
            return Comparator.comparing(map -> (String) map.get("date"));
        }
    }
    
    private String calculateTrendDirection(List<Long> uploadCounts) {
        if (uploadCounts.size() < 2) return "无趋势";
        
        int increasingCount = 0;
        int decreasingCount = 0;
        
        for (int i = 1; i < uploadCounts.size(); i++) {
            if (uploadCounts.get(i) > uploadCounts.get(i - 1)) {
                increasingCount++;
            } else if (uploadCounts.get(i) < uploadCounts.get(i - 1)) {
                decreasingCount++;
            }
        }
        
        if (increasingCount > decreasingCount) {
            return "上升";
        } else if (decreasingCount > increasingCount) {
            return "下降";
        } else {
            return "稳定";
        }
    }
    
    /**
     * 获取用户状态文本
     */
    private String getUserStatusText(Integer status) {
        if (status == null) {
            return "未知状态";
        }
        switch (status) {
            case 0:
                return "禁用";
            case 1:
                return "正常";
            case 2:
                return "待验证";
            default:
                return "未知状态";
        }
    }
    
    @Override
    public Map<String, Object> getUserUploadTimeline(Long userId, LocalDate date, int page, int size) {
        if (date == null) {
            date = LocalDate.now();
        }
        
        Map<String, Object> result = new HashMap<>();
        
        LocalDateTime dayStart = date.atStartOfDay();
        LocalDateTime dayEnd = date.plusDays(1).atStartOfDay();
        
        try {
            // 分页查询
            Page<TopicAudit> pageInfo = new Page<>(page, size);
            QueryWrapper<TopicAudit> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                       .ge("submit_time", dayStart)
                       .lt("submit_time", dayEnd)
                       .orderByDesc("submit_time"); // 最新的在前面
            
            IPage<TopicAudit> pageResult = topicAuditMapper.selectPage(pageInfo, queryWrapper);
            
            // 转换为时间线数据
            List<Map<String, Object>> timeline = pageResult.getRecords().stream()
                .map(upload -> {
                    Map<String, Object> timePoint = new HashMap<>();
                    timePoint.put("id", upload.getId());
                    timePoint.put("time", upload.getSubmitTime() != null ? 
                        upload.getSubmitTime().format(DateTimeFormatter.ofPattern("HH:mm:ss")) : "未知时间");
                    timePoint.put("title", upload.getTitle() != null ? upload.getTitle() : "未知题目");
                    timePoint.put("auditStatus", upload.getAuditStatus() != null ? upload.getAuditStatus() : 0);
                    timePoint.put("submitTime", upload.getSubmitTime());
                    return timePoint;
                })
                .collect(Collectors.toList());
                
            result.put("timeline", timeline);
            result.put("current", pageResult.getCurrent());
            result.put("size", pageResult.getSize());
            result.put("total", pageResult.getTotal());
            result.put("pages", pageResult.getPages());
            result.put("hasNext", pageResult.getCurrent() < pageResult.getPages());
            
            // 如果是第一页，同时返回基本统计信息
            if (page == 1) {
                // 总数统计
                QueryWrapper<TopicAudit> countQuery = new QueryWrapper<>();
                countQuery.eq("user_id", userId)
                          .ge("submit_time", dayStart)
                          .lt("submit_time", dayEnd);
                long totalCount = topicAuditMapper.selectCount(countQuery);
                
                // 按小时统计（仅第一页时计算，避免重复计算）
                List<TopicAudit> allUploads = topicAuditMapper.selectList(
                    new QueryWrapper<TopicAudit>()
                        .select("submit_time")
                        .eq("user_id", userId)
                        .ge("submit_time", dayStart)
                        .lt("submit_time", dayEnd)
                );
                
                Map<Integer, Long> hourlyStats = allUploads.stream()
                    .collect(Collectors.groupingBy(
                        upload -> upload.getSubmitTime() != null ? upload.getSubmitTime().getHour() : 0,
                        Collectors.counting()
                    ));
                
                result.put("totalUploads", totalCount);
                result.put("hourlyStats", hourlyStats);
            }
            
        } catch (Exception e) {
            log.error("分页查询用户上传时间线失败: userId={}, date={}, page={}", userId, date, page, e);
            result.put("timeline", new ArrayList<>());
            result.put("total", 0);
            result.put("hasNext", false);
        }
        
        return result;
    }
} 