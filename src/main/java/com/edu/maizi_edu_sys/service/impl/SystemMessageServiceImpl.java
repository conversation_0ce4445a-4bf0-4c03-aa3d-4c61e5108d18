package com.edu.maizi_edu_sys.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.edu.maizi_edu_sys.entity.SystemMessage;
import com.edu.maizi_edu_sys.repository.SystemMessageMapper;
import com.edu.maizi_edu_sys.service.SystemMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 系统消息服务实现类
 */
@Service
@Slf4j
public class SystemMessageServiceImpl extends ServiceImpl<SystemMessageMapper, SystemMessage> implements SystemMessageService {

    @Override
    public void sendAuditApprovedMessage(Long userId, Long auditId, String topicTitle) {
        log.info("发送审核通过消息, userId: {}, auditId: {}, topicTitle: {}", userId, auditId, topicTitle);
        
        SystemMessage message = new SystemMessage();
        message.setUserId(userId);
        message.setMessageType(SystemMessage.MessageType.AUDIT_APPROVED);
        message.setTitle("题目审核通过");
        message.setContent(String.format("您提交的题目「%s」已通过审核，现已加入题库。", topicTitle));
        message.setRelatedId(auditId);
        message.setRelatedType(SystemMessage.RelatedType.TOPIC_AUDIT);
        message.setPriority(SystemMessage.Priority.NORMAL);
        
        this.save(message);
        log.info("审核通过消息发送成功");
    }

    @Override
    public void sendAuditRejectedMessage(Long userId, Long auditId, String topicTitle, String rejectReason) {
        log.info("发送审核拒绝消息, userId: {}, auditId: {}, topicTitle: {}", userId, auditId, topicTitle);
        
        SystemMessage message = new SystemMessage();
        message.setUserId(userId);
        message.setMessageType(SystemMessage.MessageType.AUDIT_REJECTED);
        message.setTitle("题目审核未通过");
        message.setContent(String.format("您提交的题目「%s」审核未通过。\n拒绝原因：%s", topicTitle, rejectReason));
        message.setRelatedId(auditId);
        message.setRelatedType(SystemMessage.RelatedType.TOPIC_AUDIT);
        message.setPriority(SystemMessage.Priority.IMPORTANT);
        
        this.save(message);
        log.info("审核拒绝消息发送成功");
    }

    @Override
    public void sendSystemNotice(Long userId, String title, String content) {
        log.info("发送系统通知, userId: {}, title: {}", userId, title);
        
        SystemMessage message = new SystemMessage();
        message.setUserId(userId);
        message.setMessageType(SystemMessage.MessageType.SYSTEM_NOTICE);
        message.setTitle(title);
        message.setContent(content);
        message.setPriority(SystemMessage.Priority.NORMAL);
        
        this.save(message);
        log.info("系统通知发送成功");
    }

    @Override
    public IPage<SystemMessage> getUserMessages(Long userId, int pageNum, int pageSize) {
        Page<SystemMessage> page = new Page<>(pageNum, pageSize);
        return baseMapper.selectByUserId(page, userId);
    }

    @Override
    public Long getUnreadCount(Long userId) {
        return baseMapper.countUnreadByUserId(userId);
    }

    @Override
    public boolean markAsRead(Long messageId, Long userId) {
        int result = baseMapper.markAsRead(messageId, userId);
        return result > 0;
    }

    @Override
    public boolean markAllAsRead(Long userId) {
        int result = baseMapper.markAllAsRead(userId);
        return result > 0;
    }
} 