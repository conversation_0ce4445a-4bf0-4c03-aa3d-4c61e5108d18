package com.edu.maizi_edu_sys.service.impl;

import com.edu.maizi_edu_sys.dto.UserStatsDTO;
import com.edu.maizi_edu_sys.service.UserStatsCacheService;
import com.edu.maizi_edu_sys.service.UserStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 简化版用户统计缓存服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SimpleUserStatsCacheServiceImpl implements UserStatsCacheService {
    
    private final UserStatsService userStatsService;
    
    // 缓存统计
    private final AtomicLong hitCount = new AtomicLong(0);
    private final AtomicLong missCount = new AtomicLong(0);
    
    @Override
    @Cacheable(value = "userStats", key = "#userId", unless = "#result == null")
    public UserStatsDTO getUserStats(Long userId) {
        log.debug("缓存未命中，从数据库获取用户统计数据, userId: {}", userId);
        missCount.incrementAndGet();
        
        try {
            return userStatsService.getUserStats(userId);
        } catch (Exception e) {
            log.error("获取用户统计数据失败, userId: {}", userId, e);
            // 返回空统计数据，避免缓存null值
            return createEmptyStats();
        }
    }
    
    @Override
    @CacheEvict(value = "userStats", key = "#userId")
    public void refreshUserStatsCache(Long userId) {
        log.debug("刷新用户统计缓存, userId: {}", userId);
    }
    
    @Override
    @CacheEvict(value = "userStats", key = "#userId")
    public void clearUserStatsCache(Long userId) {
        log.debug("清除用户统计缓存, userId: {}", userId);
    }
    
    @Override
    public void refreshActiveUsersCache() {
        log.info("简化版缓存服务暂不支持批量刷新");
    }
    
    @Override
    public CacheStats getCacheStats() {
        long hits = hitCount.get();
        long misses = missCount.get();
        
        return new CacheStats(hits, misses, 0);
    }
    
    /**
     * 创建空的统计数据
     */
    private UserStatsDTO createEmptyStats() {
        UserStatsDTO stats = new UserStatsDTO();
        stats.setTotalUploaded(0L);
        stats.setApprovedCount(0L);
        stats.setPendingCount(0L);
        stats.setRejectedCount(0L);
        stats.setWeeklyUploaded(0L);
        stats.setConsecutiveDays(0);
        stats.setMaxDailyUpload(0L);
        stats.setAverageAuditTime(0.0);
        stats.setRanking(0);
        stats.setQualityScore(0.0);
        stats.setPassRate(0.0);
        return stats;
    }
    
    /**
     * 记录缓存命中
     */
    public void recordCacheHit() {
        hitCount.incrementAndGet();
    }
}
