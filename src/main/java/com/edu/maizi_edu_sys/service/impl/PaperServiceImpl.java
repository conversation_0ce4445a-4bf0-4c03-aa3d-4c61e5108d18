package com.edu.maizi_edu_sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.maizi_edu_sys.dto.ComprehensivePaperOverviewDTO;
import com.edu.maizi_edu_sys.dto.KpConfigInOverviewDTO;
import com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest;
import com.edu.maizi_edu_sys.dto.PaperWithTopicsDTO;
import com.edu.maizi_edu_sys.dto.SimpleKnowledgePointDto;
import com.edu.maizi_edu_sys.dto.TopicInOverviewDTO;
import com.edu.maizi_edu_sys.dto.PaperDetailDTO;
import com.edu.maizi_edu_sys.dto.PaperStatisticsDTO;
import com.edu.maizi_edu_sys.dto.BatchEditDTO;
import com.edu.maizi_edu_sys.entity.Paper;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.KnowledgePoint;
import com.edu.maizi_edu_sys.entity.PaperDownload;
import com.edu.maizi_edu_sys.repository.KnowledgePointRepository;
import com.edu.maizi_edu_sys.repository.PaperRepository;
import com.edu.maizi_edu_sys.repository.PaperDownloadRepository;
import com.edu.maizi_edu_sys.service.PaperService;
import com.edu.maizi_edu_sys.service.PaperGenerationService;
import org.springframework.core.io.Resource;
import java.io.InputStream;
import com.edu.maizi_edu_sys.util.PaginationResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Collections;
import java.util.HashMap;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Paper Service Implementation
 * 处理与试卷相关的逻辑
 */
@Service
@Slf4j
public class PaperServiceImpl implements PaperService {

    private final ObjectMapper objectMapper;
    private final PaperRepository paperRepository;
    private final KnowledgePointRepository knowledgePointRepository;
    private final ModelMapper modelMapper;
    private final PaperGenerationService paperGenerationService;
    private final PaperDownloadRepository paperDownloadRepository;

    public PaperServiceImpl(ObjectMapper objectMapper,
                           PaperRepository paperRepository,
                           KnowledgePointRepository knowledgePointRepository,
                           ModelMapper modelMapper,
                           PaperGenerationService paperGenerationService,
                           PaperDownloadRepository paperDownloadRepository) {
        this.objectMapper = objectMapper;
        this.paperRepository = paperRepository;
        this.knowledgePointRepository = knowledgePointRepository;
        this.modelMapper = modelMapper;
        this.paperGenerationService = paperGenerationService;
        this.paperDownloadRepository = paperDownloadRepository;
    }

    /**
     * 解析题目选项
     * @param optionsJson 选项JSON字符串
     * @return 选项列表
     */
    @Override
    public List<Map<String, String>> parseOptions(String optionsJson) {
        if (optionsJson == null || optionsJson.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            return objectMapper.readValue(optionsJson, new TypeReference<List<Map<String, String>>>() {});
        } catch (JsonProcessingException e) {
            log.error("Failed to parse options JSON: {}", e.getMessage());
            log.debug("Invalid options JSON: {}", optionsJson);
            return new ArrayList<>();
        }
    }

    /**
     * 解析组合题的子题目
     * @param subsJson 子题目JSON字符串
     * @return 子题目列表
     */
    @Override
    public List<Topic> parseSubTopics(String subsJson) {
        if (subsJson == null || subsJson.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            return objectMapper.readValue(subsJson, new TypeReference<List<Topic>>() {});
        } catch (JsonProcessingException e) {
            log.error("Failed to parse sub-topics JSON: {}", e.getMessage());
            log.debug("Invalid sub-topics JSON: {}", subsJson);
            return new ArrayList<>();
        }
    }

    private List<Topic> getTopicsFromPaperContent(String paperContentJson) {
        if (StringUtils.isBlank(paperContentJson)) {
            return Collections.emptyList();
        }
        try {
            return objectMapper.readValue(paperContentJson, new TypeReference<List<Topic>>() {});
        } catch (JsonProcessingException e) {
            log.error("Failed to parse paper content JSON to List<Topic>: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public ComprehensivePaperOverviewDTO getComprehensivePaperOverview(Long paperId) {
        Paper paper = paperRepository.selectById(paperId);
        if (paper == null) {
            throw new RuntimeException("Paper not found with id: " + paperId);
        }

        ComprehensivePaperOverviewDTO overviewDTO = modelMapper.map(paper, ComprehensivePaperOverviewDTO.class);

        List<KpConfigInOverviewDTO> kpConfigsInOverview = new ArrayList<>();
        if (StringUtils.isNotBlank(paper.getConfig())) {
            try {
                List<KnowledgePointConfigRequest> rawKpConfigs = objectMapper.readValue(paper.getConfig(),
                        new TypeReference<List<KnowledgePointConfigRequest>>() {});

                List<Integer> kpIdsForNameFetch = rawKpConfigs.stream()
                        .map(KnowledgePointConfigRequest::getKnowledgeId)
                        .filter(Objects::nonNull)
                        .map(Long::intValue)
                        .distinct()
                        .collect(Collectors.toList());

                Map<Long, String> kpIdToNameMap = Collections.emptyMap();
                if (!kpIdsForNameFetch.isEmpty()) {
                    List<KnowledgePoint> kps = knowledgePointRepository.selectBatchIds(kpIdsForNameFetch);
                    kpIdToNameMap = kps.stream()
                            .collect(Collectors.toMap(KnowledgePoint::getId, KnowledgePoint::getName));
                }

                for (KnowledgePointConfigRequest rawConfig : rawKpConfigs) {
                    KpConfigInOverviewDTO configDTO = modelMapper.map(rawConfig, KpConfigInOverviewDTO.class);
                    if (rawConfig.getKnowledgeId() != null) {
                        configDTO.setKnowledgeName(kpIdToNameMap.getOrDefault(rawConfig.getKnowledgeId(), "Unknown KP ID: " + rawConfig.getKnowledgeId()));
                    } else {
                        configDTO.setKnowledgeName("KP ID Not Specified");
                    }
                    kpConfigsInOverview.add(configDTO);
                }
            } catch (Exception e) {
                log.error("Error parsing Paper.config for paper " + paperId + ": " + e.getMessage(), e);
            }
        }
        overviewDTO.setOriginalKpConfigs(kpConfigsInOverview);

        List<Topic> topics = getTopicsFromPaperContent(paper.getContent());
        List<TopicInOverviewDTO> topicsInOverview = new ArrayList<>();

        if (!topics.isEmpty()) {
            List<Integer> topicKpIds = topics.stream()
                                           .map(Topic::getKnowId)
                                           .filter(Objects::nonNull)
                                           .distinct()
                                           .collect(Collectors.toList());

            Map<Long, KnowledgePoint> relatedKpsMap = Collections.emptyMap();
            if (!topicKpIds.isEmpty()) {
                List<KnowledgePoint> kps = knowledgePointRepository.selectBatchIds(topicKpIds);
                relatedKpsMap = kps.stream().collect(Collectors.toMap(KnowledgePoint::getId, Function.identity()));
            }

            for (Topic topic : topics) {
                TopicInOverviewDTO topicDTO = modelMapper.map(topic, TopicInOverviewDTO.class);
                if (topic.getKnowId() != null) {
                    KnowledgePoint relatedKp = relatedKpsMap.get(topic.getKnowId().longValue());
                    if (relatedKp != null) {
                        SimpleKnowledgePointDto simpleKpDto = modelMapper.map(relatedKp, SimpleKnowledgePointDto.class);
                        topicDTO.setRelatedKnowledgePoints(Collections.singletonList(simpleKpDto));
                    } else {
                        topicDTO.setRelatedKnowledgePoints(Collections.singletonList(new SimpleKnowledgePointDto(topic.getKnowId().longValue(), "Unknown KP ID: " + topic.getKnowId())));
                    }
                } else {
                    topicDTO.setRelatedKnowledgePoints(Collections.emptyList());
                }
                topicsInOverview.add(topicDTO);
            }
        }
        overviewDTO.setTopicsInPaper(topicsInOverview);
        overviewDTO.setQuestionCount(topicsInOverview.size());

        Map<String, Integer> actualTopicTypeCounts = topicsInOverview.stream()
                .collect(Collectors.groupingBy(TopicInOverviewDTO::getType, Collectors.summingInt(t -> 1)));
        overviewDTO.setActualTopicTypeCounts(actualTopicTypeCounts);

        Map<String, Long> difficultyCounts = topicsInOverview.stream()
                .filter(t -> t.getDifficultyLevel() != null)
                .collect(Collectors.groupingBy(TopicInOverviewDTO::getDifficultyLevel, Collectors.counting()));
        long totalTopicsWithDifficulty = topicsInOverview.stream().filter(t -> t.getDifficultyLevel() != null).count();
        Map<String, Double> actualDifficultyDistribution = new HashMap<>();
        if (totalTopicsWithDifficulty > 0) {
            difficultyCounts.forEach((level, count) ->
                actualDifficultyDistribution.put(level, (double) count / totalTopicsWithDifficulty)
            );
        }
        overviewDTO.setActualDifficultyDistribution(actualDifficultyDistribution);

        return overviewDTO;
    }

    @Override
    @Transactional(readOnly = true)
    public PaperWithTopicsDTO getPaperWithTopicsDTOById(Long paperId) {
        Paper paper = paperRepository.selectById(paperId);
        if (paper == null) {
            log.warn("Paper not found with id: {}", paperId);
            return null;
        }

        PaperWithTopicsDTO paperWithTopicsDTO = modelMapper.map(paper, PaperWithTopicsDTO.class);

        List<Topic> topics = getTopicsFromPaperContent(paper.getContent());
        paperWithTopicsDTO.setTopics(topics);

        return paperWithTopicsDTO;
    }

    @Override
    @Transactional(readOnly = true)
    public PaginationResponse<Paper> getPapersWithPagination(Integer userId, int pageNum, int pageSize, String sort, String search, String type) {
        Page<Paper> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<Paper> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.isNotBlank(search)) {
            queryWrapper.like(Paper::getTitle, search);
        }

        if (StringUtils.isNotBlank(type)) {
            try {
                Integer typeInt = Integer.parseInt(type);
                 queryWrapper.eq(Paper::getType, typeInt);
            } catch (NumberFormatException e) {
                log.warn("Invalid type format for pagination: {}. Expected an integer.", type);
            }
        }

        if (StringUtils.isNotBlank(sort)) {
            String[] sortParams = sort.split("_");
            if (sortParams.length == 2) {
                String sortField = sortParams[0];
                boolean isAsc = "asc".equalsIgnoreCase(sortParams[1]);

                if ("title".equalsIgnoreCase(sortField)) {
                    queryWrapper.orderBy(true, isAsc, Paper::getTitle);
                } else if ("createTime".equalsIgnoreCase(sortField)) {
                    queryWrapper.orderBy(true, isAsc, Paper::getCreateTime);
                } else if ("id".equalsIgnoreCase(sortField)) {
                     queryWrapper.orderBy(true, isAsc, Paper::getId);
                } else {
                    queryWrapper.orderBy(true, false, Paper::getCreateTime); // Default sort desc
                }
            } else {
                 queryWrapper.orderBy(true, false, Paper::getCreateTime); // Default sort desc
            }
        } else {
            queryWrapper.orderBy(true, false, Paper::getCreateTime); // Default sort desc
        }

        Page<Paper> resultPage = paperRepository.selectPage(page, queryWrapper);

        return new PaginationResponse<>(
                resultPage.getRecords(),
                resultPage.getTotal(),
                resultPage.getCurrent(),
                resultPage.getPages(),
                resultPage.getSize()
        );
    }

    private Map<String, Double> calculateDifficultyDistribution(List<Topic> topics) {
        Map<String, Long> counts = new HashMap<>();
        counts.put("1", 0L); // Easy
        counts.put("2", 0L); // Medium
        counts.put("3", 0L); // Hard
        long totalTopicsWithDifficulty = 0;

        if (topics == null || topics.isEmpty()) {
            // Return a default or empty distribution if there are no topics
            Map<String, Double> emptyDistribution = new HashMap<>();
            emptyDistribution.put("1", 0.0);
            emptyDistribution.put("2", 0.0);
            emptyDistribution.put("3", 0.0);
            return emptyDistribution;
        }

        for (Topic topic : topics) {
            Double difficulty = topic.getDifficulty();
            if (difficulty != null) {
                totalTopicsWithDifficulty++;
                if (difficulty <= 0.4) {
                    counts.put("1", counts.get("1") + 1);
                } else if (difficulty <= 0.7) {
                    counts.put("2", counts.get("2") + 1);
                } else {
                    counts.put("3", counts.get("3") + 1);
                }
            }
        }

        Map<String, Double> distribution = new HashMap<>();
        if (totalTopicsWithDifficulty > 0) {
            for (Map.Entry<String, Long> entry : counts.entrySet()) {
                distribution.put(entry.getKey(), (double) entry.getValue() / totalTopicsWithDifficulty);
            }
        } else {
            // If no topics have difficulty, return 0 distribution for each category
            distribution.put("1", 0.0);
            distribution.put("2", 0.0);
            distribution.put("3", 0.0);
        }
        return distribution;
    }

    @Override
    @Transactional(readOnly = true)
    public PaperDetailDTO getPaperDetail(Long paperId) {
        Paper paper = paperRepository.selectById(paperId);
        if (paper == null) {
            log.warn("Paper not found with id: {}", paperId);
            // Consider throwing a specific exception or returning null based on API contract
            return null;
        }

        List<Topic> topics = getTopicsFromPaperContent(paper.getContent());
        PaperDetailDTO dto = PaperDetailDTO.fromEntity(paper, topics);

        // Calculate and set difficulty distribution
        dto.setDifficultyDistribution(calculateDifficultyDistribution(topics));

        // Note: topicsByType, typeCountMap, typeScoreMap are not populated here
        // as they were not part of the immediate requirement for the modal.
        // They can be added if needed for the "View Full Paper" functionality.

        return dto;
    }

    // ========== 历史试卷管理相关方法实现 ==========

    @Override
    @Transactional(readOnly = true)
    public org.springframework.data.domain.Page<Paper> findPapersWithFilters(Map<String, Object> filters, Pageable pageable) {
        LambdaQueryWrapper<Paper> queryWrapper = new LambdaQueryWrapper<>();
        
        // 用户ID过滤
        // if (filters.containsKey("userId")) {
        //     queryWrapper.eq(Paper::getUserId, filters.get("userId"));
        // }
        
        // 搜索关键词
        // if (filters.containsKey("search")) {
        //     String search = (String) filters.get("search");
        //     if (StringUtils.isNotBlank(search)) {
        //         queryWrapper.like(Paper::getTitle, search);
        //     }
        // }
        
        // 日期过滤
        // if (filters.containsKey("dateFilter")) {
        //     String dateFilter = (String) filters.get("dateFilter");
        //     LocalDateTime now = LocalDateTime.now();
        //     switch (dateFilter) {
        //         case "today":
        //             queryWrapper.ge(Paper::getCreateTime, now.toLocalDate().atStartOfDay());
        //             break;
        //         case "week":
        //             queryWrapper.ge(Paper::getCreateTime, now.minusWeeks(1));
        //             break;
        //         case "month":
        //             queryWrapper.ge(Paper::getCreateTime, now.minusMonths(1));
        //             break;
        //         case "quarter":
        //             queryWrapper.ge(Paper::getCreateTime, now.minusMonths(3));
        //             break;
        //     }
        // }
        
        // 分数过滤
        // if (filters.containsKey("scoreFilter")) {
        //     String scoreFilter = (String) filters.get("scoreFilter");
        //     switch (scoreFilter) {
        //         case "0-50":
        //             queryWrapper.between(Paper::getTotalScore, 0, 50);
        //             break;
        //         case "51-80":
        //             queryWrapper.between(Paper::getTotalScore, 51, 80);
        //             break;
        //         case "81-100":
        //             queryWrapper.between(Paper::getTotalScore, 81, 100);
        //             break;
        //         case "100+":
        //             queryWrapper.gt(Paper::getTotalScore, 100);
        //             break;
        //     }
        // }
        
        // 执行分页查询
        Page<Paper> page = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        
        // 添加排序
        if (pageable.getSort().isSorted()) {
            pageable.getSort().forEach(order -> {
                boolean isAsc = order.isAscending();
                String property = order.getProperty();
                switch (property) {
                    case "createTime":
                        queryWrapper.orderBy(true, isAsc, Paper::getCreateTime);
                        break;
                    case "updateTime":
                        queryWrapper.orderBy(true, isAsc, Paper::getUpdateTime);
                        break;
                    case "title":
                        queryWrapper.orderBy(true, isAsc, Paper::getTitle);
                        break;
                    case "totalScore":
                        queryWrapper.orderBy(true, isAsc, Paper::getTotalScore);
                        break;
                    case "downloadCount":
                        queryWrapper.orderBy(true, isAsc, Paper::getDownloadCount);
                        break;
                    default:
                        queryWrapper.orderBy(true, false, Paper::getCreateTime);
                }
            });
        } else {
            queryWrapper.orderBy(true, false, Paper::getCreateTime);
        }
        
        Page<Paper> result = paperRepository.selectPage(page, queryWrapper);
        
        return new PageImpl<>(result.getRecords(), pageable, result.getTotal());
    }

    @Override
    @Transactional(readOnly = true)
    public PaperStatisticsDTO getPaperStatistics() {
        return getPaperStatisticsByUser(null);
    }

    @Override
    @Transactional(readOnly = true)
    public PaperStatisticsDTO getPaperStatisticsByUser(Long userId) {
        PaperStatisticsDTO statistics = new PaperStatisticsDTO();
        
        LambdaQueryWrapper<Paper> baseQuery = new LambdaQueryWrapper<>();
        if (userId != null) {
            baseQuery.eq(Paper::getUserId, userId);
        }
        
        // 总试卷数
        Long totalPapers = paperRepository.selectCount(baseQuery);
        statistics.setTotalPapers(totalPapers);
        
        // 今日创建的试卷数
        LambdaQueryWrapper<Paper> todayQuery = baseQuery.clone();
        todayQuery.ge(Paper::getCreateTime, LocalDate.now().atStartOfDay());
        Long todayPapers = paperRepository.selectCount(todayQuery);
        statistics.setTodayPapers(todayPapers);
        
        // 本周创建的试卷数
        LambdaQueryWrapper<Paper> weekQuery = baseQuery.clone();
        weekQuery.ge(Paper::getCreateTime, LocalDateTime.now().minusWeeks(1));
        Long weekPapers = paperRepository.selectCount(weekQuery);
        statistics.setWeekPapers(weekPapers);
        
        // 本月创建的试卷数
        LambdaQueryWrapper<Paper> monthQuery = baseQuery.clone();
        monthQuery.ge(Paper::getCreateTime, LocalDateTime.now().minusMonths(1));
        Long monthPapers = paperRepository.selectCount(monthQuery);
        statistics.setMonthPapers(monthPapers);
        
        // 总下载次数（需要根据实际字段调整）
        List<Paper> allPapers = paperRepository.selectList(baseQuery);
        Long totalDownloads = allPapers.stream()
                .mapToLong(paper -> paper.getDownloadCount() != null ? paper.getDownloadCount() : 0L)
                .sum();
        statistics.setTotalDownloads(totalDownloads);
        
        // 今日下载次数（这里简化处理，实际应该有下载记录表）
        statistics.setTodayDownloads(0L);
        
        // 分数统计
        if (!allPapers.isEmpty()) {
            Double avgScore = allPapers.stream()
                    .filter(paper -> paper.getTotalScore() != null)
                    .mapToDouble(Paper::getTotalScore)
                    .average()
                    .orElse(0.0);
            statistics.setAverageScore(avgScore);
            
            Double maxScore = allPapers.stream()
                    .filter(paper -> paper.getTotalScore() != null)
                    .mapToDouble(Paper::getTotalScore)
                    .max()
                    .orElse(0.0);
            statistics.setMaxScore(maxScore);
            
            Double minScore = allPapers.stream()
                    .filter(paper -> paper.getTotalScore() != null)
                    .mapToDouble(Paper::getTotalScore)
                    .min()
                    .orElse(0.0);
            statistics.setMinScore(minScore);
        }
        
        return statistics;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean validatePaperOwnership(List<Long> paperIds, Long userId) {
        if (CollectionUtils.isEmpty(paperIds) || userId == null) {
            return false;
        }
        
        LambdaQueryWrapper<Paper> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Paper::getId, paperIds)
                   .eq(Paper::getUserId, userId);
        
        Long count = paperRepository.selectCount(queryWrapper);
        return count.equals((long) paperIds.size());
    }

    @Override
    @Transactional
    public int batchEditPapers(BatchEditDTO batchEditDTO) {
        if (CollectionUtils.isEmpty(batchEditDTO.getPaperIds())) {
            return 0;
        }
        
        List<Paper> papers = paperRepository.selectBatchIds(batchEditDTO.getPaperIds());
        int updatedCount = 0;
        
        for (Paper paper : papers) {
            boolean updated = false;
            
            // 更新标题前缀
            if (StringUtils.isNotBlank(batchEditDTO.getTitlePrefix())) {
                paper.setTitle(batchEditDTO.getTitlePrefix() + paper.getTitle());
                updated = true;
            }
            
            // 更新标题后缀
            if (StringUtils.isNotBlank(batchEditDTO.getTitleSuffix())) {
                paper.setTitle(paper.getTitle() + batchEditDTO.getTitleSuffix());
                updated = true;
            }
            
            // 更新难度等级
            if (batchEditDTO.getDifficultyLevel() != null) {
                paper.setDifficulty(batchEditDTO.getDifficultyLevel().doubleValue());
                updated = true;
            }
            
            // 更新试卷类型
            if (StringUtils.isNotBlank(batchEditDTO.getCategoryTag())) {
                paper.setPaperType(batchEditDTO.getCategoryTag());
                updated = true;
            }
            
            // 更新知识点名称（替代描述）
            if (StringUtils.isNotBlank(batchEditDTO.getDescription())) {
                paper.setKnowledgeName(batchEditDTO.getDescription());
                updated = true;
            }
            
            if (updated) {
                paper.setUpdateTime(LocalDateTime.now());
                paperRepository.updateById(paper);
                updatedCount++;
            }
        }
        
        return updatedCount;
    }

    @Override
    @Transactional(readOnly = true)
    public byte[] exportPapersToExcel(List<Long> paperIds) {
        List<Paper> papers = paperRepository.selectBatchIds(paperIds);
        
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            
            Sheet sheet = workbook.createSheet("试卷数据");
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"试卷ID", "试卷标题", "总分", "题目数量", "难度等级", "创建时间", "更新时间", "下载次数", "试卷类型"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            
            // 填充数据
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            for (int i = 0; i < papers.size(); i++) {
                Paper paper = papers.get(i);
                Row row = sheet.createRow(i + 1);
                
                row.createCell(0).setCellValue(paper.getId());
                row.createCell(1).setCellValue(paper.getTitle());
                row.createCell(2).setCellValue(paper.getTotalScore() != null ? paper.getTotalScore() : 0);
                
                // 计算题目数量
                List<Topic> topics = getTopicsFromPaperContent(paper.getContent());
                row.createCell(3).setCellValue(topics.size());
                
                row.createCell(4).setCellValue(paper.getDifficulty() != null ? paper.getDifficulty() : 0);
                row.createCell(5).setCellValue(paper.getCreateTime() != null ? paper.getCreateTime().format(formatter) : "");
                row.createCell(6).setCellValue(paper.getUpdateTime() != null ? paper.getUpdateTime().format(formatter) : "");
                row.createCell(7).setCellValue(paper.getDownloadCount() != null ? paper.getDownloadCount() : 0);
                row.createCell(8).setCellValue(paper.getType() != null ? paper.getType().toString() : "");
            }
            
            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            workbook.write(outputStream);
            return outputStream.toByteArray();
            
        } catch (IOException e) {
            log.error("导出Excel失败", e);
            throw new RuntimeException("导出Excel失败", e);
        }
    }

    @Override
    @Transactional
    public Paper clonePaper(Long paperId, Long userId) {
        Paper originalPaper = paperRepository.selectById(paperId);
        if (originalPaper == null) {
            throw new RuntimeException("试卷不存在");
        }
        
        Paper clonedPaper = new Paper();
        clonedPaper.setTitle("[副本] " + originalPaper.getTitle());
        clonedPaper.setContent(originalPaper.getContent());
        clonedPaper.setConfig(originalPaper.getConfig());
         clonedPaper.setTotalScore(originalPaper.getTotalScore());
         clonedPaper.setPaperType(originalPaper.getPaperType());
         clonedPaper.setDifficulty(originalPaper.getDifficulty());
         clonedPaper.setKnowledgeId(originalPaper.getKnowledgeId());
         clonedPaper.setKnowledgeName(originalPaper.getKnowledgeName());
         clonedPaper.setActualTotalScore(originalPaper.getActualTotalScore());
         clonedPaper.setDifficultyDistribution(originalPaper.getDifficultyDistribution());
         clonedPaper.setFileFormat(originalPaper.getFileFormat());
         clonedPaper.setUserId(userId);
         clonedPaper.setDownloadCount(0);
         clonedPaper.setCreateTime(LocalDateTime.now());
         clonedPaper.setUpdateTime(LocalDateTime.now());
         clonedPaper.setIsDeleted(false);
        
        paperRepository.insert(clonedPaper);
        return clonedPaper;
    }

    @Override
    @Transactional(readOnly = true)
    public Paper getPaperById(Long paperId) {
        return paperRepository.selectById(paperId);
    }

    @Override
    @Transactional
    public boolean deletePaper(Long paperId) {
        int result = paperRepository.deleteById(paperId);
        return result > 0;
    }

    @Override
    @Transactional(readOnly = true)
    public byte[] generateBatchDownloadResource(List<Long> paperIds, String format, String version) {
        List<Paper> papers = paperRepository.selectBatchIds(paperIds);
        
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ZipOutputStream zos = new ZipOutputStream(baos)) {
            
            for (Paper paper : papers) {
                // 调用具体的PDF或Word生成服务进行批量下载
                Resource resource;
                String fileExtension;
                
                if ("pdf".equalsIgnoreCase(format)) {
                    // 调用PDF生成服务
                    resource = paperGenerationService.generatePaperResource(paper.getId(), version);
                    fileExtension = "pdf";
                } else if ("word".equalsIgnoreCase(format)) {
                    // 调用Word生成服务
                    resource = paperGenerationService.generateWordResource(paper.getId(), version);
                    fileExtension = "docx";
                } else {
                    // 默认生成文本格式
                    String content = generatePaperContent(paper, version);
                    String fileName = sanitizeFileName(paper.getTitle()) + ".txt";
                    
                    ZipEntry entry = new ZipEntry(fileName);
                    zos.putNextEntry(entry);
                    zos.write(content.getBytes("UTF-8"));
                    zos.closeEntry();
                    
                    // 更新下载次数
                    paper.setDownloadCount((paper.getDownloadCount() != null ? paper.getDownloadCount() : 0) + 1);
                    paperRepository.updateById(paper);
                    continue;
                }
                
                // 处理PDF/Word资源
                String fileName = sanitizeFileName(paper.getTitle()) + "." + fileExtension;
                
                ZipEntry entry = new ZipEntry(fileName);
                zos.putNextEntry(entry);
                
                // 将Resource内容写入ZIP
                try (InputStream inputStream = resource.getInputStream()) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        zos.write(buffer, 0, bytesRead);
                    }
                }
                
                zos.closeEntry();
                
                // 更新下载次数
                paper.setDownloadCount((paper.getDownloadCount() != null ? paper.getDownloadCount() : 0) + 1);
                paperRepository.updateById(paper);
            }
            
            zos.finish();
            return baos.toByteArray();
            
        } catch (IOException e) {
            log.error("生成批量下载资源失败", e);
            throw new RuntimeException("生成批量下载资源失败", e);
        }
    }
    
    /**
     * 清理文件名中的非法字符
     */
    private String sanitizeFileName(String fileName) {
        if (fileName == null) {
            return "untitled";
        }
        return fileName.replaceAll("[\\\\/:*?\"<>|]", "_");
    }
    
    /**
     * 生成试卷内容
     */
    private String generatePaperContent(Paper paper, String version) {
        StringBuilder content = new StringBuilder();
        content.append("试卷标题: ").append(paper.getTitle()).append("\n");
        content.append("总分: ").append(paper.getTotalScore()).append("\n");
        content.append("创建时间: ").append(paper.getCreateTime()).append("\n\n");
        
        List<Topic> topics = getTopicsFromPaperContent(paper.getContent());
        
        for (int i = 0; i < topics.size(); i++) {
            Topic topic = topics.get(i);
            content.append("第").append(i + 1).append("题: ").append(topic.getTitle()).append("\n");
            
            // 根据版本显示不同内容
            switch (version) {
                case "standard": // 标准版 - 题目+答案+解析
                    if (StringUtils.isNotBlank(topic.getAnswer())) {
                        content.append("答案: ").append(topic.getAnswer()).append("\n");
                    }
                    if (StringUtils.isNotBlank(topic.getAnalysis())) {
                        content.append("解析: ").append(topic.getAnalysis()).append("\n");
                    }
                    break;
                case "regular": // 学生版 - 只有题目
                    // 不显示答案和解析
                    break;
                case "teacher": // 教师版 - 只有答案和解析
                    if (StringUtils.isNotBlank(topic.getAnswer())) {
                        content.append("答案: ").append(topic.getAnswer()).append("\n");
                    }
                    if (StringUtils.isNotBlank(topic.getAnalysis())) {
                        content.append("解析: ").append(topic.getAnalysis()).append("\n");
                    }
                    break;
            }
            content.append("\n");
        }
        
        return content.toString();
    }

    @Override
    @Transactional
    public int batchDeletePapers(List<Long> paperIds, Long userId) {
        log.info("批量删除试卷，试卷数量: {}, 用户ID: {}", paperIds.size(), userId);
        
        if (CollectionUtils.isEmpty(paperIds)) {
            return 0;
        }
        
        // 验证试卷所有权
        if (!validatePaperOwnership(paperIds, userId)) {
            throw new RuntimeException("无权限删除部分试卷");
        }
        
        int deletedCount = 0;
        for (Long paperId : paperIds) {
            try {
                if (deletePaper(paperId)) {
                    deletedCount++;
                }
            } catch (Exception e) {
                log.error("删除试卷失败，试卷ID: {}", paperId, e);
            }
        }
        
        log.info("批量删除完成，成功删除 {} 个试卷", deletedCount);
        return deletedCount;
    }

    @Override
    @Transactional
    public byte[] batchDownloadPapers(List<Long> paperIds, String format, String version, Long userId) {
        log.info("批量下载试卷，试卷数量: {}, 格式: {}, 版本: {}, 用户ID: {}", paperIds.size(), format, version, userId);
        
        if (CollectionUtils.isEmpty(paperIds)) {
            throw new RuntimeException("试卷ID列表不能为空");
        }
        
        // 验证试卷所有权
        if (!validatePaperOwnership(paperIds, userId)) {
            throw new RuntimeException("无权限下载部分试卷");
        }
        
        // 记录每个试卷的下载历史
        for (Long paperId : paperIds) {
            try {
                PaperDownload download = new PaperDownload();
                download.setPaperId(paperId);
                download.setUserId(userId);
                download.setFileFormat(format.toLowerCase());
                download.setDownloadTime(LocalDateTime.now());
                download.setIpAddress("batch_download"); // 批量下载标识
                
                // 保存下载记录
                paperDownloadRepository.save(download);
                
                log.debug("记录批量下载历史: paperId={}, userId={}, format={}", paperId, userId, format);
            } catch (Exception e) {
                log.error("记录批量下载历史失败: paperId={}, error={}", paperId, e.getMessage());
                // 不影响下载流程，继续处理
            }
        }
        
        return generateBatchDownloadResource(paperIds, format, version);
    }
}