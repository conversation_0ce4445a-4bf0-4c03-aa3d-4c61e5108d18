package com.edu.maizi_edu_sys.service.impl;

import com.edu.maizi_edu_sys.config.MineRuProperties;
import com.edu.maizi_edu_sys.dto.DocumentParseRequest;
import com.edu.maizi_edu_sys.dto.DocumentParseResponse;
import com.edu.maizi_edu_sys.entity.DocumentParseTask;
import com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper;
import com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper;
import com.edu.maizi_edu_sys.service.DocumentParseService;
import com.edu.maizi_edu_sys.util.MineRuApiClient;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 文档解析服务实现
 * 
 * <AUTHOR> Integration
 * @since 2025-01-20
 */
@Slf4j
@Service
public class DocumentParseServiceImpl implements DocumentParseService {

    @Autowired
    private MineRuApiClient mineRuApiClient;

    @Autowired
    private DocumentParseTaskMapper taskMapper;

    @Autowired
    private DocumentParseResultMapper resultMapper;

    @Autowired
    private MineRuProperties mineRuProperties;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    @Transactional
    public DocumentParseResponse createSingleTask(DocumentParseRequest request, Long userId) {
        try {
            // 构建请求体
            Map<String, Object> requestBody = buildSingleTaskRequest(request);
            String requestJson = objectMapper.writeValueAsString(requestBody);

            // 估算页数（单文件默认10页）
            int estimatedPages = estimatePages(request);
            
            // 调用MineRU API
            MineRuApiClient.MineRuResponse response = mineRuApiClient.createTask(requestJson, estimatedPages);
            
            if (!response.isApiSuccess()) {
                log.error("创建解析任务失败: {}", response.getMessage());
                return buildErrorResponse("创建解析任务失败: " + response.getMessage());
            }

            // 解析响应数据
            JsonNode data = response.getData();
            String taskId = data.path("task_id").asText();

            // 保存任务记录
            DocumentParseTask task = new DocumentParseTask()
                    .setTaskId(taskId)
                    .setDataId(request.getDataId())
                    .setFileUrl(request.getUrl())
                    .setFileName(extractFileNameFromUrl(request.getUrl()))
                    .setStatus("pending")
                    .setParseConfig(requestJson)
                    .setUserId(userId)
                    .setCreatedAt(LocalDateTime.now())
                    .setUpdatedAt(LocalDateTime.now());

            taskMapper.insert(task);

            // 构建响应
            return new DocumentParseResponse()
                    .setTaskId(taskId)
                    .setDataId(request.getDataId())
                    .setState("pending")
                    .setFileName(task.getFileName());

        } catch (Exception e) {
            log.error("创建单文件解析任务异常", e);
            return buildErrorResponse("创建解析任务异常: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public DocumentParseResponse createBatchUrlTask(DocumentParseRequest request, Long userId) {
        try {
            // 构建请求体
            Map<String, Object> requestBody = buildBatchUrlTaskRequest(request);
            String requestJson = objectMapper.writeValueAsString(requestBody);

            // 估算页数
            int estimatedPages = estimateBatchPages(request);
        
            // 调用MineRU API
            MineRuApiClient.MineRuResponse response = mineRuApiClient.createBatchTask(requestJson, estimatedPages);
            
            if (!response.isApiSuccess()) {
                log.error("创建批量解析任务失败: {}", response.getMessage());
                return buildErrorResponse("创建批量解析任务失败: " + response.getMessage());
            }

            // 解析响应数据
            JsonNode data = response.getData();
            String batchId = data.path("batch_id").asText();

            // 保存批量任务记录
            for (DocumentParseRequest.FileInfo fileInfo : request.getFiles()) {
                DocumentParseTask task = new DocumentParseTask()
                        .setBatchId(batchId)
                        .setDataId(fileInfo.getDataId())
                        .setFileUrl(fileInfo.getUrl())
                        .setFileName(fileInfo.getFileName())
                        .setStatus("pending")
                        .setParseConfig(requestJson)
                        .setUserId(userId)
                        .setCreatedAt(LocalDateTime.now())
                        .setUpdatedAt(LocalDateTime.now());

                taskMapper.insert(task);
            }

            // 构建响应
            return new DocumentParseResponse()
                    .setBatchId(batchId)
                    .setState("pending");

        } catch (Exception e) {
            log.error("创建批量URL解析任务异常", e);
            return buildErrorResponse("创建批量解析任务异常: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public DocumentParseResponse createBatchUploadUrls(DocumentParseRequest request, Long userId) {
        try {
            // 构建请求体
            Map<String, Object> requestBody = buildBatchUploadRequest(request);
            String requestJson = objectMapper.writeValueAsString(requestBody);

            // 估算页数
            int estimatedPages = estimateBatchPages(request);
        
            // 调用MineRU API
            MineRuApiClient.MineRuResponse response = mineRuApiClient.createBatchUploadUrls(requestJson, estimatedPages);
            
            if (!response.isApiSuccess()) {
                log.error("申请批量上传链接失败: {}", response.getMessage());
                return buildErrorResponse("申请批量上传链接失败: " + response.getMessage());
            }

            // 解析响应数据并返回上传链接
            JsonNode data = response.getData();
            String batchId = data.path("batch_id").asText();

            // 构建响应 (包含上传链接信息)
            DocumentParseResponse result = new DocumentParseResponse()
                    .setBatchId(batchId)
                    .setState("upload_ready");

            // 这里可以添加上传链接信息到响应中
            // result.setUploadUrls(parseUploadUrls(data));

            return result;

        } catch (Exception e) {
            log.error("申请批量上传链接异常", e);
            return buildErrorResponse("申请批量上传链接异常: " + e.getMessage());
        }
    }

    @Override
    public DocumentParseResponse getTaskResult(String taskId) {
        try {
            // 调用MineRU API查询结果
            MineRuApiClient.MineRuResponse response = mineRuApiClient.getTaskResult(taskId);
            
            if (!response.isApiSuccess()) {
                log.error("查询任务结果失败: {}", response.getMessage());
                return buildErrorResponse("查询任务结果失败: " + response.getMessage());
            }

            // 解析响应数据
            JsonNode data = response.getData();
            DocumentParseResponse result = parseTaskResult(data);

            // 更新本地任务状态
            updateLocalTaskStatus(taskId, result);

            return result;

        } catch (Exception e) {
            log.error("查询任务结果异常", e);
            return buildErrorResponse("查询任务结果异常: " + e.getMessage());
        }
    }

    @Override
    public DocumentParseResponse getBatchResults(String batchId) {
        try {
            // 调用MineRU API查询批量结果
            MineRuApiClient.MineRuResponse response = mineRuApiClient.getBatchResults(batchId);
            
            if (!response.isApiSuccess()) {
                log.error("查询批量任务结果失败: {}", response.getMessage());
                return buildErrorResponse("查询批量任务结果失败: " + response.getMessage());
            }

            // 解析响应数据
            JsonNode data = response.getData();
            return parseBatchResults(data);

        } catch (Exception e) {
            log.error("查询批量任务结果异常", e);
            return buildErrorResponse("查询批量任务结果异常: " + e.getMessage());
        }
    }

    @Override
    public List<DocumentParseTask> getUserTasks(Long userId, String status) {
        if (status != null && !status.isEmpty()) {
            return taskMapper.selectByUserIdAndStatus(userId, status);
        } else {
            // 查询用户所有任务
            return taskMapper.selectList(
                com.baomidou.mybatisplus.core.toolkit.Wrappers.<DocumentParseTask>lambdaQuery()
                    .eq(DocumentParseTask::getUserId, userId)
                    .eq(DocumentParseTask::getIsDeleted, 0)
                    .orderByDesc(DocumentParseTask::getCreatedAt)
            );
        }
    }

    @Override
    @Async
    public void syncTaskStatus() {
        log.info("开始同步文档解析任务状态");
        
        List<DocumentParseTask> pendingTasks = taskMapper.selectPendingTasks();
        
        for (DocumentParseTask task : pendingTasks) {
            try {
                DocumentParseResponse result = getTaskResult(task.getTaskId());
                if (result != null && !"pending".equals(result.getState()) && !"running".equals(result.getState())) {
                    log.info("任务 {} 状态已更新: {}", task.getTaskId(), result.getState());
                }
            } catch (Exception e) {
                log.error("同步任务 {} 状态失败", task.getTaskId(), e);
            }
        }
        
        log.info("文档解析任务状态同步完成，处理任务数: {}", pendingTasks.size());
    }

    @Override
    @Transactional
    public void handleCallback(String taskId, String status, String resultData) {
        try {
            DocumentParseTask task = taskMapper.selectByTaskId(taskId);
            if (task == null) {
                log.warn("收到回调但未找到任务: {}", taskId);
                return;
            }

            // 更新任务状态
            task.setStatus(status);
            task.setUpdatedAt(LocalDateTime.now());

            if ("done".equals(status) && resultData != null) {
                // 解析结果数据
                JsonNode resultJson = objectMapper.readTree(resultData);
                String zipUrl = resultJson.path("full_zip_url").asText();
                task.setZipUrl(zipUrl);
                task.setCompletedAt(LocalDateTime.now());
            }

            taskMapper.updateById(task);
            log.info("处理回调完成，任务: {}, 状态: {}", taskId, status);

        } catch (Exception e) {
            log.error("处理MineRU回调异常", e);
        }
    }

    // 私有辅助方法

    private Map<String, Object> buildSingleTaskRequest(DocumentParseRequest request) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("url", request.getUrl());
        
        // 使用配置默认值或请求参数
        MineRuProperties.Parse.Default parseDefaults = mineRuProperties.getParse().getDefaultConfig();
        requestBody.put("is_ocr", request.getOcr() != null ? request.getOcr() : parseDefaults.isOcr());
        requestBody.put("enable_formula", request.getFormula() != null ? request.getFormula() : parseDefaults.isEnableFormula());
        requestBody.put("enable_table", request.getTable() != null ? request.getTable() : parseDefaults.isEnableTable());
        requestBody.put("language", request.getLanguage() != null ? request.getLanguage() : parseDefaults.getLanguage());
        requestBody.put("output_format", request.getOutputFormat() != null ? request.getOutputFormat() : parseDefaults.getOutputFormat());
        requestBody.put("model_version", request.getModelVersion() != null ? request.getModelVersion() : parseDefaults.getModelVersion());
        
        if (request.getDataId() != null) {
            requestBody.put("data_id", request.getDataId());
        }
        if (request.getCallbackUrl() != null) {
            requestBody.put("callback", request.getCallbackUrl());
        }
        // 使用请求参数或配置默认值
        requestBody.put("extra_formats", request.getExtraFormats() != null ? request.getExtraFormats() : parseDefaults.getExtraFormats());
        if (request.getPageRanges() != null) {
            requestBody.put("page_ranges", request.getPageRanges());
        }

        return requestBody;
    }

    private Map<String, Object> buildBatchUrlTaskRequest(DocumentParseRequest request) {
        Map<String, Object> requestBody = new HashMap<>();
        
        // 使用配置默认值
        MineRuProperties.Parse.Default parseDefaults = mineRuProperties.getParse().getDefaultConfig();
        
        List<Map<String, Object>> files = new ArrayList<>();
        for (DocumentParseRequest.FileInfo fileInfo : request.getFiles()) {
            Map<String, Object> file = new HashMap<>();
            file.put("name", fileInfo.getFileName());
            file.put("url", fileInfo.getUrl());
            file.put("is_ocr", fileInfo.getIsOcr() != null ? fileInfo.getIsOcr() : parseDefaults.isOcr());
            if (fileInfo.getDataId() != null) {
                file.put("data_id", fileInfo.getDataId());
            }
            if (fileInfo.getPageRanges() != null) {
                file.put("page_ranges", fileInfo.getPageRanges());
            }
            files.add(file);
        }
        
        requestBody.put("files", files);
        requestBody.put("enable_formula", request.getFormula() != null ? request.getFormula() : parseDefaults.isEnableFormula());
        requestBody.put("enable_table", request.getTable() != null ? request.getTable() : parseDefaults.isEnableTable());
        requestBody.put("language", request.getLanguage() != null ? request.getLanguage() : parseDefaults.getLanguage());
        requestBody.put("output_format", request.getOutputFormat() != null ? request.getOutputFormat() : parseDefaults.getOutputFormat());
        requestBody.put("model_version", request.getModelVersion() != null ? request.getModelVersion() : parseDefaults.getModelVersion());
        
        if (request.getCallbackUrl() != null) {
            requestBody.put("callback", request.getCallbackUrl());
        }
        // 使用请求参数或配置默认值
        requestBody.put("extra_formats", request.getExtraFormats() != null ? request.getExtraFormats() : parseDefaults.getExtraFormats());

        return requestBody;
    }

    private Map<String, Object> buildBatchUploadRequest(DocumentParseRequest request) {
        Map<String, Object> requestBody = new HashMap<>();
        
        // 使用配置默认值
        MineRuProperties.Parse.Default parseDefaults = mineRuProperties.getParse().getDefaultConfig();
        
        List<Map<String, Object>> files = new ArrayList<>();
        for (DocumentParseRequest.FileInfo fileInfo : request.getFiles()) {
            Map<String, Object> file = new HashMap<>();
            file.put("name", fileInfo.getFileName());
            // Use default OCR setting if not specified
            Boolean isOcr = fileInfo.getIsOcr() != null ? fileInfo.getIsOcr() : parseDefaults.isOcr();
            file.put("is_ocr", isOcr);
            if (fileInfo.getDataId() != null) {
                file.put("data_id", fileInfo.getDataId());
            }
            if (fileInfo.getPageRanges() != null) {
                file.put("page_ranges", fileInfo.getPageRanges());
            }
            files.add(file);
        }
        
        requestBody.put("files", files);
        
        // Use default values from configuration if request parameters are null
        requestBody.put("enable_formula", request.getFormula() != null ? request.getFormula() : parseDefaults.isEnableFormula());
        requestBody.put("enable_table", request.getTable() != null ? request.getTable() : parseDefaults.isEnableTable());
        requestBody.put("language", request.getLanguage() != null ? request.getLanguage() : parseDefaults.getLanguage());
        requestBody.put("output_format", request.getOutputFormat() != null ? request.getOutputFormat() : parseDefaults.getOutputFormat());
        requestBody.put("model_version", request.getModelVersion() != null ? request.getModelVersion() : parseDefaults.getModelVersion());
        
        if (request.getCallbackUrl() != null) {
            requestBody.put("callback", request.getCallbackUrl());
        }
        // 使用请求参数或配置默认值
        requestBody.put("extra_formats", request.getExtraFormats() != null ? request.getExtraFormats() : parseDefaults.getExtraFormats());

        return requestBody;
    }

    private DocumentParseResponse parseTaskResult(JsonNode data) {
        DocumentParseResponse result = new DocumentParseResponse();
        
        result.setTaskId(data.path("task_id").asText());
        result.setDataId(data.path("data_id").asText());
        result.setState(data.path("state").asText());
        result.setFileName(data.path("file_name").asText());
        result.setFullZipUrl(data.path("full_zip_url").asText());
        result.setErrMsg(data.path("err_msg").asText());

        // 解析进度信息
        JsonNode progressNode = data.path("extract_progress");
        if (!progressNode.isMissingNode()) {
            DocumentParseResponse.ExtractProgress progress = new DocumentParseResponse.ExtractProgress();
            progress.setExtractedPages(progressNode.path("extracted_pages").asInt());
            progress.setTotalPages(progressNode.path("total_pages").asInt());
            progress.setStartTime(progressNode.path("start_time").asText());
            
            if (progress.getTotalPages() > 0) {
                double percent = (double) progress.getExtractedPages() / progress.getTotalPages() * 100;
                progress.setProgressPercent(percent);
            }
            
            result.setExtractProgress(progress);
        }

        return result;
    }

    private DocumentParseResponse parseBatchResults(JsonNode data) {
        // 解析批量结果的逻辑
        DocumentParseResponse result = new DocumentParseResponse();
        result.setBatchId(data.path("batch_id").asText());
        result.setState(data.path("state").asText());
        
        // 这里可以添加更多批量结果解析逻辑
        
        return result;
    }

    private void updateLocalTaskStatus(String taskId, DocumentParseResponse result) {
        try {
            DocumentParseTask task = taskMapper.selectByTaskId(taskId);
            if (task != null) {
                task.setStatus(result.getState());
                task.setUpdatedAt(LocalDateTime.now());

                // 更新进度信息
                if (result.getExtractProgress() != null) {
                    task.setActualPages(result.getExtractProgress().getTotalPages());
                }

                // 更新结果信息
                if ("done".equals(result.getState())) {
                    task.setZipUrl(result.getFullZipUrl());
                    task.setCompletedAt(LocalDateTime.now());
                } else if ("failed".equals(result.getState())) {
                    task.setErrorMessage(result.getErrMsg());
                }

                taskMapper.updateById(task);
            }
        } catch (Exception e) {
            log.error("更新本地任务状态失败", e);
        }
    }

    private String extractFileNameFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return "unknown";
        }
        
        try {
            String[] parts = url.split("/");
            String fileName = parts[parts.length - 1];
            
            // 移除查询参数
            int queryIndex = fileName.indexOf('?');
            if (queryIndex > 0) {
                fileName = fileName.substring(0, queryIndex);
            }
            
            return fileName.isEmpty() ? "unknown" : fileName;
        } catch (Exception e) {
            return "unknown";
        }
    }

    private DocumentParseResponse buildErrorResponse(String message) {
        DocumentParseResponse response = new DocumentParseResponse();
        response.setState("failed");
        response.setErrMsg(message);
        return response;
    }
    
    /**
     * 估算单文件页数
     */
    private int estimatePages(DocumentParseRequest request) {
        // 如果指定了页面范围，使用范围计算
        if (request.getPageRanges() != null && !request.getPageRanges().isEmpty()) {
            return calculatePagesFromRanges(request.getPageRanges());
        }
        
        // 尝试根据文件大小估算页数
        String url = request.getUrl();
        if (url != null) {
            try {
                long fileSize = getFileSizeFromUrl(url);
                if (fileSize > 0) {
                    return estimatePagesByFileSize(fileSize, extractFileNameFromUrl(url));
                }
            } catch (Exception e) {
                log.debug("无法获取文件大小，使用默认估算: {}", e.getMessage());
            }
        }
        
        return 10; // 默认10页
    }
    
    /**
     * 估算批量文件页数
     */
    private int estimateBatchPages(DocumentParseRequest request) {
        if (request.getFiles() == null || request.getFiles().isEmpty()) {
            return 10;
        }
        
        int totalPages = 0;
        for (DocumentParseRequest.FileInfo fileInfo : request.getFiles()) {
            try {
                // 优先使用文件大小估算
                if (fileInfo.getUrl() != null) {
                    long fileSize = getFileSizeFromUrl(fileInfo.getUrl());
                    if (fileSize > 0) {
                        totalPages += estimatePagesByFileSize(fileSize, fileInfo.getFileName());
                        continue;
                    }
                }
                
                // 如果无法获取文件大小，使用文件名估算
                String fileName = fileInfo.getFileName();
                if (fileName != null) {
                    fileName = fileName.toLowerCase();
                    if (fileName.endsWith(".pdf")) {
                        totalPages += 15;
                    } else if (fileName.endsWith(".docx") || fileName.endsWith(".doc")) {
                        totalPages += 10;
                    } else if (fileName.endsWith(".pptx") || fileName.endsWith(".ppt")) {
                        totalPages += 20;
                    } else {
                        totalPages += 10; // 其他类型默认10页
                    }
                } else {
                    totalPages += 10;
                }
            } catch (Exception e) {
                log.debug("估算文件页数失败，使用默认值: {}", e.getMessage());
                totalPages += 10;
            }
        }
        
        return Math.max(totalPages, 10); // 最小10页
    }
    
    /**
     * 从页面范围计算页数
     */
    private int calculatePagesFromRanges(List<String> pageRanges) {
        int totalPages = 0;
        for (String range : pageRanges) {
            try {
                if (range.contains("-")) {
                    String[] parts = range.split("-");
                    if (parts.length == 2) {
                        int start = Integer.parseInt(parts[0].trim());
                        int end = Integer.parseInt(parts[1].trim());
                        totalPages += Math.max(end - start + 1, 1);
                    }
                } else {
                    totalPages += 1; // 单页
                }
            } catch (NumberFormatException e) {
                totalPages += 1; // 解析失败时默认1页
            }
        }
        return Math.max(totalPages, 1);
    }
    
    /**
     * 获取文件大小（字节）
     */
    private long getFileSizeFromUrl(String url) throws IOException {
        try {
            // 如果是本地文件路径，直接读取文件大小
            if (url.startsWith("file://") || (!url.startsWith("http://") && !url.startsWith("https://"))) {
                String filePath = url.replace("file://", "");
                File file = new File(filePath);
                return file.exists() ? file.length() : 0;
            }
            
            // 对于网络文件，发送HEAD请求获取文件大小
            Request request = new Request.Builder()
                    .url(url)
                    .head() // 使用HEAD请求获取头信息
                    .build();
            
            try (Response response = new OkHttpClient().newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String contentLength = response.header("Content-Length");
                    if (contentLength != null) {
                        return Long.parseLong(contentLength);
                    }
                }
            }
        } catch (Exception e) {
            log.debug("获取文件大小失败: {}", e.getMessage());
        }
        
        return 0; // 无法获取文件大小
    }
    
    /**
     * 根据文件大小估算页数
     */
    private int estimatePagesByFileSize(long fileSizeBytes, String fileName) {
        if (fileSizeBytes <= 0) {
            return 10; // 默认值
        }
        
        // 转换为MB
        double fileSizeMB = fileSizeBytes / (1024.0 * 1024.0);
        
        // 根据文件类型和大小估算页数
        String fileExt = getFileExtension(fileName).toLowerCase();
        
        switch (fileExt) {
            case "pdf":
                // PDF: 平均每页100-200KB，根据内容复杂度调整
                return Math.max(1, (int) Math.ceil(fileSizeMB / 0.15)); // 150KB每页
                
            case "docx":
            case "doc":
                // Word文档: 平均每页20-50KB
                return Math.max(1, (int) Math.ceil(fileSizeMB / 0.035)); // 35KB每页
                
            case "pptx":
            case "ppt":
                // PowerPoint: 平均每页100-500KB（包含图片）
                return Math.max(1, (int) Math.ceil(fileSizeMB / 0.3)); // 300KB每页
                
            case "xlsx":
            case "xls":
                // Excel: 每个Sheet算作1页，根据数据量估算
                return Math.max(1, (int) Math.ceil(fileSizeMB / 0.1)); // 100KB每页
                
            case "txt":
                // 纯文本: 每页约5000字符，约2-3KB
                return Math.max(1, (int) Math.ceil(fileSizeMB / 0.003)); // 3KB每页
                
            case "rtf":
                // RTF文档: 类似Word但稍大
                return Math.max(1, (int) Math.ceil(fileSizeMB / 0.05)); // 50KB每页
                
            case "jpg":
            case "jpeg":
            case "png":
            case "gif":
            case "bmp":
            case "tiff":
                // 图片文件: 每个图片算作1页
                return 1;
                
            default:
                // 其他文件类型: 使用通用估算（50KB每页）
                return Math.max(1, (int) Math.ceil(fileSizeMB / 0.05));
        }
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1);
        }
        
        return "";
    }
}
