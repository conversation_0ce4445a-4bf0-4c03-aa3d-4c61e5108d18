package com.edu.maizi_edu_sys.service.impl;

import com.edu.maizi_edu_sys.entity.ChatMessageEntity;
import com.edu.maizi_edu_sys.entity.ChatSession;
import com.edu.maizi_edu_sys.entity.User;
import com.edu.maizi_edu_sys.entity.dto.ApiResponse;
import com.edu.maizi_edu_sys.entity.dto.ChatBookUpdateRequest;
import com.edu.maizi_edu_sys.entity.dto.ChatCreateRequest;
import com.edu.maizi_edu_sys.entity.dto.ChatMessageRequest;
import com.edu.maizi_edu_sys.repository.ChatMessageRepository;
import com.edu.maizi_edu_sys.repository.ChatSessionRepository;
import com.edu.maizi_edu_sys.service.ChatService;
import com.edu.maizi_edu_sys.service.UserService;
import com.edu.maizi_edu_sys.util.JwtUtil;
import com.volcengine.ark.runtime.model.bot.completion.chat.BotChatCompletionRequest;
import com.volcengine.ark.runtime.model.bot.completion.chat.BotChatCompletionResult;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

@Service
@RequiredArgsConstructor
@Slf4j
public class ChatServiceImpl implements ChatService {
    private final ChatSessionRepository chatSessionRepository;
    private final ChatMessageRepository chatMessageRepository;
    private final JwtUtil jwtUtil;
    private final UserService userService;
    private final ArkService arkService;

    @Value("${bot.id}")
    private String botId;
    
    @Value("${bot.api-key}")
    private String apiKey;
    
    // Add post-construct to verify configuration
    @PostConstruct
    public void init() {
        log.info("Initializing ChatServiceImpl with botId: {}, apiKey-length: {}", 
                botId, apiKey != null ? apiKey.length() : 0);
        if (botId == null || botId.isEmpty()) {
            log.error("Bot ID is not configured properly in application.properties");
        }
        if (apiKey == null || apiKey.isEmpty()) {
            log.error("API key is not configured properly in application.properties");
        }
    }

    @Override
    @Transactional
    public ApiResponse<?> createChat(ChatCreateRequest request, String token) {
        try {
            String username = jwtUtil.getUsernameFromToken(token);
            User user = userService.getByUsername(username);

            ChatSession session = new ChatSession();
            session.setUserId(user.getId());
            session.setKnowId(request.getKnowId());
            session.setBookUrl(request.getBookUrl());
            session.setTitle(request.getTitle() != null ? request.getTitle() : "新对话");
            session.setCreatedAt(LocalDateTime.now());
            session.setUpdatedAt(LocalDateTime.now());
            
            int result = chatSessionRepository.insert(session);
            if (result > 0) {
                return ApiResponse.success(session);
            } else {
                return ApiResponse.error("创建对话失败");
            }
        } catch (Exception e) {
            log.error("Create chat failed", e);
            return ApiResponse.error("创建对话失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<?> getChatHistory(String token) {
        try {
            String username = jwtUtil.getUsernameFromToken(token);
            User user = userService.getByUsername(username);
            List<ChatSession> sessions = chatSessionRepository
                .findByUserIdAndDeletedOrderByUpdatedAtDesc(user.getId(), false);
            return ApiResponse.success(sessions);
        } catch (Exception e) {
            log.error("Get chat history failed", e);
            return ApiResponse.error("获取对话历史失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<?> getChatMessages(Long chatId, String token) {
        try {
            List<ChatMessageEntity> messages = chatMessageRepository.findBySessionIdOrderByCreatedAt(chatId);
            return ApiResponse.success(messages);
        } catch (Exception e) {
            log.error("Get chat messages failed", e);
            return ApiResponse.error("获取对话消息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ApiResponse<?> sendMessage(ChatMessageRequest request, String token) {
        try {
            log.debug("Starting sendMessage process. ChatId: {}, Message length: {}", 
                request.getChatId(), request.getMessage() != null ? request.getMessage().length() : 0);
            
            // 验证用户权限
            String username = jwtUtil.getUsernameFromToken(token);
            User user = userService.getByUsername(username);
            log.debug("User found: {}", user != null ? user.getUsername() : "null");
            
            // 获取并验证会话
            ChatSession session = chatSessionRepository.selectById(request.getChatId());
            if (session == null) {
                throw new RuntimeException("对话不存在");
            }
            
            log.debug("Found chat session: id={}, title={}, userId={}", 
                session.getId(), session.getTitle(), session.getUserId());
                
            // 验证会话所属权
            if (user == null || !session.getUserId().equals(user.getId())) {
                log.error("User {} (id: {}) not authorized to access session {} (userId: {})", 
                    user != null ? user.getUsername() : "null", user != null ? user.getId() : "null", 
                    session.getId(), session.getUserId());
                return ApiResponse.error("无权访问此会话");
            }

            // 更新对话配置信息
            if (request.getKnowId() != null && !request.getKnowId().isEmpty()) {
                session.setKnowId(request.getKnowId());
            }
            
            if (request.getBookUrl() != null && !request.getBookUrl().isEmpty()) {
                session.setBookUrl(request.getBookUrl());
            }
            
            // 保存更新后的会话
            session.setUpdatedAt(LocalDateTime.now());
            chatSessionRepository.updateById(session);

            // 保存用户消息
            ChatMessageEntity userMessage = new ChatMessageEntity();
            userMessage.setSessionId(request.getChatId());
            userMessage.setContent(request.getMessage());
            userMessage.setIsUser(true);
            userMessage.setCreatedAt(LocalDateTime.now());
            
            int userResult = chatMessageRepository.insert(userMessage);
            log.debug("User message saved with result: {}, ID: {}", userResult, userMessage.getId());

            // 构建 AI 提示词
            String prompt = String.format(
                "知识点ID：%s\n书籍链接：%s\n用户问题：%s",
                session.getKnowId(),
                session.getBookUrl(),
                request.getMessage()
            );
            log.debug("Created AI prompt with knowId: {}, book link length: {}", 
                session.getKnowId(), session.getBookUrl() != null ? session.getBookUrl().length() : 0);

            // 调用 AI 接口
            log.debug("Calling AI service with prompt...");
            String aiResponse;
            try {
                aiResponse = callAIService(prompt);
                log.debug("Received AI response, length: {}", aiResponse != null ? aiResponse.length() : 0);
            } catch (Exception e) {
                log.error("Error calling AI service", e);
                return ApiResponse.error("AI 服务调用失败: " + e.getMessage());
            }

            // 保存 AI 响应
            try {
                ChatMessageEntity aiMessage = new ChatMessageEntity();
                aiMessage.setSessionId(request.getChatId());
                aiMessage.setContent(aiResponse);
                aiMessage.setIsUser(false);
                aiMessage.setCreatedAt(LocalDateTime.now());
                
                int aiResult = chatMessageRepository.insert(aiMessage);
                log.debug("AI message saved with result: {}, ID: {}", aiResult, aiMessage.getId());
            } catch (Exception e) {
                log.error("Error saving AI response", e);
                return ApiResponse.error("保存 AI 响应失败: " + e.getMessage());
            }

            // 更新会话时间
            try {
                session.setUpdatedAt(LocalDateTime.now());
                chatSessionRepository.updateById(session);
                log.debug("Updated chat session timestamp");
            } catch (Exception e) {
                log.error("Error updating session timestamp", e);
                // We'll still return the response even if updating timestamp fails
            }

            log.debug("Message process complete, returning AI response");
            return ApiResponse.success(aiResponse);
        } catch (Exception e) {
            log.error("Send message failed", e);
            return ApiResponse.error("发送消息失败: " + e.getMessage());
        }
    }

    private String callAIService(String message) {
        ChatMessage systemMessage = ChatMessage.builder()
            .role(ChatMessageRole.SYSTEM)
            .content("# 高质量题目生成要求\n" +
                    "\n" +
                    "## 基本要求\n" +
                    "- 根据上传的书籍链接生成题目\n" +
                    "\n" +
                    "\n" +
                    "## 知识点覆盖\n" +
                    "- **全面性**：确保覆盖考纲中的所有关键知识点\n" +
                    "- **比例合理**：各章节内容比例与考纲权重保持一致\n" +
                    "- **难度分布**：简单题30%，中等题50%，难题20%\n" +
                    "\n" +
                    "## 题目质量标准\n" +
                    "- **题干**：清晰明确，无歧义表述\n" +
                    "- **选项**：设置合理，干扰项具有科学性和迷惑性\n" +
                    "- **多选题**：答案数量分布合理，避免模式化\n" +
                    "- **范围控制**：严格遵循课程范围，避免偏题怪题\n" +
                    "\n" +
                    "题目字段说明\n" +
                    "\n" +
                    "| 字段       | 必须 | 类型   | 名称       | 说明                                                         |\n" +
                    "| ---------- | ---- | ------ | ---------- | ------------------------------------------------------------ |\n" +
                    "| know_id    | 是   | int    | 知识点id   | 知识点id<br />请通过后台【题库管理】【知识点列表】【#】获取  |\n" +
                    "| type       | 是   | enum   | 题型       | 请参考如下 **题型枚举值列表**                                |\n" +
                    "| title      | 是   | string | 标题       | 题目标题（支持markdown语法）                                 |\n" +
                    "| options    | 否   | array  | 选项       | 请参考如下 **题型枚举值列表** options列                      |\n" +
                    "| subs       | 否   | array  | 组合题数据 | 请参考如下 **组合题**                                        |\n" +
                    "| answer     | 否   | string | 答案       | **单选、多选、判断题**必填<br />其它题型必须留空<br />**单选、多选**答案必须是大写，必须按字母顺序排列<br />如【ACD】，不能是CDA(顺序没按排列)，cda(不能小写)<br />**判断题** 要么填【是】要么填【否】 |\n" +
                    "| source     | 是   | string | 出题依据   | 存储每道题是根据什么来出的题目依据，给出原文的段落。         |\n" +
                    "| parse      | 否   | string | 解析       | 题目解析内容（支持markdown语法）                             |\n" +
                    "| difficulty | 是   | double | 题目难度   | 题目的难度，浮点类型。                                       |\n" +
                    "## 输出格式 Json格式输出\n" +
                    "{\n" +
                    "    \"know_id\": , // 知识点id 需要根据用户提示设置，优先设置这个\n" +
                    "    \"type\": \"choice\", // 题型单选\n" +
                    "    \"title\": \"标题（    ）\", // 标题 默认后面加（    ）\n" +
                    "    \"options\": [{\"key\":\"A\",\"name\":\"选项1\"},{\"key\":\"B\",\"name\":\"选项2\"},{\"key\":\"C\",\"name\":\"选项3\"},{\"key\":\"D\",\"name\":\"选项4\"}],  // 选项\n" +
                    "    \"answer\": \"A\", // 答案\n" +
                    "    \"parse\": \"解析\", // 解析\n" +
                    "    \"source\": \"题目知识点来源\", // 原文数据不能带有这样的[6811c021545931e47b9a919c](CITE)引用符号\n" +
                    "    \"difficulty\": \"题目难度\" // 难度系数必须精确\n" +
                    "}\n" +
                    "{\n" +
                    "    \"know_id\": , // 知识点id 需要根据用户提示设置，优先设置这个\n" +
                    "    \"type\": \"multiple\", // 题型多选\n" +
                    "    \"title\": \"标题（    ）\", // 标题 默认后面加（    ）\n" +
                    "    \"options\": [{\"key\":\"A\",\"name\":\"选项1\"},{\"key\":\"B\",\"name\":\"选项2\"},{\"key\":\"C\",\"name\":\"选项3\"},{\"key\":\"D\",\"name\":\"选项4\"}],  // 选项\n" +
                    "    \"answer\": \"A\", // 答案\n" +
                    "    \"parse\": \"解析\", // 解析\n" +
                    "    \"source\": \"题目知识点来源\", // 原文数据不能带有这样的[6811c021545931e47b9a919c](CITE)引用符号\n" +
                    "    \"difficulty\": \"题目难度\" // 难度系数必须精确\n" +
                    "}\n" +
                    "\n" +
                    "判断题json格式是\n" +
                    "{\n" +
                    "    \"know_id\":  , // 知识点id 需要根据用户提示设置，优先设置这个\n" +
                    "    \"type\": \"judge\", // 题型\n" +
                    "    \"title\": \"标题（    ）\", // 标题 后面默认加（    ）\n" +
                    "    \"answer\": \"是\", // 答案【是|否】\n" +
                    "    \"parse\": \"解析\", // 解析\n" +
                    "    \"source\": \"题目知识点来源\", // 原文数据不能带有这样的[6811c021545931e47b9a919c](CITE)引用符号\n" +
                    "    \"difficulty\": \"题目难度\" // 难度系数必须精确\n" +
                    "}\n" +
                    "## 特别注意\n" +
                    "- 确保题目难度梯度合理，由易到难\n" +
                    "- 题目情境设计贴近实际应用\n" +
                    "- 避免题目之间存在提示关系\n" +
                    "\n" +
                    "重要： 首先要询问用户设置的知识点id是否准确，保证之后生成的题目知识点id都是相同，因为这属于同一本书的内容，假设切换了书籍，应该询问用户要改变知识点id 的内容。")
            .build();

        ChatMessage userMessage = ChatMessage.builder()
            .role(ChatMessageRole.USER)
            .content(message)
            .build();

        List<ChatMessage> messages = new ArrayList<>();
        messages.add(systemMessage);
        messages.add(userMessage);

        BotChatCompletionRequest chatCompletionRequest = BotChatCompletionRequest.builder()
            .botId(botId)
            .messages(messages)
            .build();

        BotChatCompletionResult result = arkService.createBotChatCompletion(chatCompletionRequest);
        Object content = result.getChoices().get(0).getMessage().getContent();
        return content != null ? content.toString() : "";
    }

    @Override
    public ApiResponse<?> deleteChat(Long chatId, String token) {
        try {
            String username = jwtUtil.getUsernameFromToken(token);
            User user = userService.getByUsername(username);
            
            ChatSession session = chatSessionRepository.selectById(chatId);
            if (session == null) {
                return ApiResponse.error("对话不存在");
            }
                
            if (!session.getUserId().equals(user.getId())) {
                return ApiResponse.error("无权删除此对话");
            }
            
            session.setDeleted(true);
            session.setUpdatedAt(LocalDateTime.now());
            chatSessionRepository.updateById(session);
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("Delete chat failed", e);
            return ApiResponse.error("删除对话失败");
        }
    }

    @Override
    public ApiResponse<?> updateChatTitle(Long chatId, String title, String token) {
        try {
            String username = jwtUtil.getUsernameFromToken(token);
            User user = userService.getByUsername(username);
            
            ChatSession session = chatSessionRepository.selectById(chatId);
            if (session == null) {
                return ApiResponse.error("对话不存在");
            }
                
            if (!session.getUserId().equals(user.getId())) {
                return ApiResponse.error("无权修改此对话");
            }
            
            session.setTitle(title);
            session.setUpdatedAt(LocalDateTime.now());
            chatSessionRepository.updateById(session);
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("Update chat title failed", e);
            return ApiResponse.error("更新对话标题失败");
        }
    }

    @Override
    public void streamMessage(
            ChatMessageRequest request, 
            String token,
            Consumer<String> chunkConsumer,
            Consumer<String> errorConsumer,
            Runnable completionCallback
    ) {
        try {
            // Validate user
            String username = jwtUtil.getUsernameFromToken(token);
            User user = userService.getByUsername(username);
            
            if (user == null) {
                errorConsumer.accept("User not found");
                return;
            }
            
            // Get and validate chat session
            ChatSession session = chatSessionRepository.selectById(request.getChatId());
                
            if (session == null) {
                errorConsumer.accept("Chat session not found");
                return;
            }
                
            // Validate ownership
            if (!session.getUserId().equals(user.getId())) {
                errorConsumer.accept("You don't have permission to access this chat");
                return;
            }

            // Save user message
            ChatMessageEntity userMessage = new ChatMessageEntity();
            userMessage.setSessionId(request.getChatId());
            userMessage.setContent(request.getMessage());
            userMessage.setIsUser(true);
            userMessage.setCreatedAt(LocalDateTime.now());
            chatMessageRepository.insert(userMessage);

            // Build prompt
            String prompt = String.format(
                "知识点ID：%s\n书籍链接：%s\n用户问题：%s",
                session.getKnowId(),
                session.getBookUrl(),
                request.getMessage()
            );

            // Create a thread to process the AI response
            new Thread(() -> {
                try {
                    // Call AI API
                    StringBuilder fullResponse = new StringBuilder();
                    callStreamingAIService(prompt, chunk -> {
                        // For each chunk received
                        chunkConsumer.accept(chunk);
                        fullResponse.append(chunk);
                    });
                    
                    // Save the complete response
                    ChatMessageEntity aiMessage = new ChatMessageEntity();
                    aiMessage.setSessionId(request.getChatId());
                    aiMessage.setContent(fullResponse.toString());
                    aiMessage.setIsUser(false);
                    aiMessage.setCreatedAt(LocalDateTime.now());
                    chatMessageRepository.insert(aiMessage);

                    // Update session timestamp
                    session.setUpdatedAt(LocalDateTime.now());
                    chatSessionRepository.updateById(session);
                    
                    // Notify completion
                    completionCallback.run();
                } catch (Exception e) {
                    log.error("Error in streaming AI response", e);
                    errorConsumer.accept("Error streaming response: " + e.getMessage());
                }
            }).start();
        } catch (Exception e) {
            log.error("Error in streamMessage", e);
            errorConsumer.accept("Error: " + e.getMessage());
        }
    }

    /**
     * Call AI service with streaming response
     * 
     * @param message The prompt message
     * @param chunkConsumer Consumer for chunks of text
     */
    private void callStreamingAIService(String message, Consumer<String> chunkConsumer) {
        ChatMessage systemMessage = ChatMessage.builder()
            .role(ChatMessageRole.SYSTEM)
            .content("# 高质量题目生成要求\n" +
                    "\n" +
                    "## 基本要求\n" +
                    "- 根据上传的书籍链接生成题目\n" +
                    "\n" +
                    "\n" +
                    "## 知识点覆盖\n" +
                    "- **全面性**：确保覆盖考纲中的所有关键知识点\n" +
                    "- **比例合理**：各章节内容比例与考纲权重保持一致\n" +
                    "- **难度分布**：简单题30%，中等题50%，难题20%\n" +
                    "\n" +
                    "## 题目质量标准\n" +
                    "- **题干**：清晰明确，无歧义表述\n" +
                    "- **选项**：设置合理，干扰项具有科学性和迷惑性\n" +
                    "- **多选题**：答案数量分布合理，避免模式化\n" +
                    "- **范围控制**：严格遵循课程范围，避免偏题怪题\n" +
                    "\n" +
                    "题目字段说明\n" +
                    "\n" +
                    "| 字段       | 必须 | 类型   | 名称       | 说明                                                         |\n" +
                    "| ---------- | ---- | ------ | ---------- | ------------------------------------------------------------ |\n" +
                    "| know_id    | 是   | int    | 知识点id   | 知识点id<br />请通过后台【题库管理】【知识点列表】【#】获取  |\n" +
                    "| type       | 是   | enum   | 题型       | 请参考如下 **题型枚举值列表**                                |\n" +
                    "| title      | 是   | string | 标题       | 题目标题（支持markdown语法）                                 |\n" +
                    "| options    | 否   | array  | 选项       | 请参考如下 **题型枚举值列表** options列                      |\n" +
                    "| subs       | 否   | array  | 组合题数据 | 请参考如下 **组合题**                                        |\n" +
                    "| answer     | 否   | string | 答案       | **单选、多选、判断题**必填<br />其它题型必须留空<br />**单选、多选**答案必须是大写，必须按字母顺序排列<br />如【ACD】，不能是CDA(顺序没按排列)，cda(不能小写)<br />**判断题** 要么填【是】要么填【否】 |\n" +
                    "| source     | 是   | string | 出题依据   | 存储每道题是根据什么来出的题目依据，给出原文的段落。         |\n" +
                    "| parse      | 否   | string | 解析       | 题目解析内容（支持markdown语法）                             |\n" +
                    "| difficulty | 是   | double | 题目难度   | 题目的难度，浮点类型。                                       |\n" +
                    "## 输出格式 Json格式输出\n" +
                    "{\n" +
                    "    \"know_id\": , // 知识点id 需要根据用户提示设置，优先设置这个\n" +
                    "    \"type\": \"choice\", // 题型单选\n" +
                    "    \"title\": \"标题（    ）\", // 标题 默认后面加（    ）\n" +
                    "    \"options\": [{\"key\":\"A\",\"name\":\"选项1\"},{\"key\":\"B\",\"name\":\"选项2\"},{\"key\":\"C\",\"name\":\"选项3\"},{\"key\":\"D\",\"name\":\"选项4\"}],  // 选项\n" +
                    "    \"answer\": \"A\", // 答案\n" +
                    "    \"parse\": \"解析\", // 解析\n" +
                    "    \"source\": \"题目知识点来源\", // 原文数据不能带有这样的[6811c021545931e47b9a919c](CITE)引用符号\n" +
                    "    \"difficulty\": \"题目难度\" // 难度系数必须精确\n" +
                    "}\n" +
                    "{\n" +
                    "    \"know_id\": , // 知识点id 需要根据用户提示设置，优先设置这个\n" +
                    "    \"type\": \"multiple\", // 题型多选\n" +
                    "    \"title\": \"标题（    ）\", // 标题 默认后面加（    ）\n" +
                    "    \"options\": [{\"key\":\"A\",\"name\":\"选项1\"},{\"key\":\"B\",\"name\":\"选项2\"},{\"key\":\"C\",\"name\":\"选项3\"},{\"key\":\"D\",\"name\":\"选项4\"}],  // 选项\n" +
                    "    \"answer\": \"A\", // 答案\n" +
                    "    \"parse\": \"解析\", // 解析\n" +
                    "    \"source\": \"题目知识点来源\", // 原文数据不能带有这样的[6811c021545931e47b9a919c](CITE)引用符号\n" +
                    "    \"difficulty\": \"题目难度\" // 难度系数必须精确\n" +
                    "}\n" +
                    "\n" +
                    "判断题json格式是\n" +
                    "{\n" +
                    "    \"know_id\":  , // 知识点id 需要根据用户提示设置，优先设置这个\n" +
                    "    \"type\": \"judge\", // 题型\n" +
                    "    \"title\": \"标题（    ）\", // 标题 后面默认加（    ）\n" +
                    "    \"answer\": \"是\", // 答案【是|否】\n" +
                    "    \"parse\": \"解析\", // 解析\n" +
                    "    \"source\": \"题目知识点来源\", // 原文数据不能带有这样的[6811c021545931e47b9a919c](CITE)引用符号\n" +
                    "    \"difficulty\": \"题目难度\" // 难度系数必须精确\n" +
                    "}\n" +
                    "## 特别注意\n" +
                    "- 确保题目难度梯度合理，由易到难\n" +
                    "- 题目情境设计贴近实际应用\n" +
                    "- 避免题目之间存在提示关系\n" +
                    "\n" +
                    "重要： 首先要询问用户设置的知识点id是否准确，保证之后生成的题目知识点id都是相同，因为这属于同一本书的内容，假设切换了书籍，应该询问用户要改变知识点id 的内容。")
            .build();

        ChatMessage userMessage = ChatMessage.builder()
            .role(ChatMessageRole.USER)
            .content(message)
            .build();

        List<ChatMessage> messages = new ArrayList<>();
        messages.add(systemMessage);
        messages.add(userMessage);

        try {
            // Build streaming request
            BotChatCompletionRequest chatCompletionRequest = BotChatCompletionRequest.builder()
                .botId(botId)
                .messages(messages)
                .stream(true)
                .build();
            
            // Use streaming API with reactive approach
            StringBuilder fullResponse = new StringBuilder();
            
            // Use proper streaming implementation
            arkService.streamBotChatCompletion(chatCompletionRequest)
                .doOnError(e -> {
                    log.error("Error in streaming response", e);
                    throw new RuntimeException("Error in streaming response: " + e.getMessage());
                })
                .blockingForEach(choice -> {
                    if (!choice.getChoices().isEmpty() && choice.getChoices().get(0).getMessage() != null) {
                        Object contentObj = choice.getChoices().get(0).getMessage().getContent();
                        if (contentObj != null) {
                            String chunk = contentObj.toString();
                            
                            // Send chunk to consumer without logging
                            chunkConsumer.accept(chunk);
                            fullResponse.append(chunk);
                        } else {
                            log.warn("Received empty content from API");
                        }
                    } else {
                        log.warn("Received empty choices from API");
                    }
                });
            
        } catch (Exception e) {
            log.error("Error in streaming AI call", e);
            throw e;
        }
    }
    
    /**
     * 更新聊天关联的教材配置
     * 
     * @param chatId 聊天ID
     * @param bookData 教材数据
     * @param token 认证令牌
     * @return 操作结果
     */
    @Override
    @Transactional
    public ApiResponse<?> updateChatBook(Long chatId, ChatBookUpdateRequest bookData, String token) {
        try {
            // 解析用户信息
            String username = jwtUtil.getUsernameFromToken(token);
            User user = userService.getByUsername(username);
            if (user == null) {
                return ApiResponse.error("用户不存在");
            }
            
            // 查找对话信息
            ChatSession chatSession = chatSessionRepository.selectById(chatId);
            
            if (chatSession == null || !chatSession.getUserId().equals(user.getId()) || chatSession.getDeleted()) {
                return ApiResponse.error("对话不存在或无权访问");
            }
            
            // 更新教材的信息
            log.info("Updating chat book for chatId={}: bookId={}, bookUrl={}, knowId={}", 
                    chatId, bookData.getBookId(), bookData.getBookUrl(), bookData.getKnowId());
            
            // 更新相关字段
            if (bookData.getBookUrl() != null) {
                chatSession.setBookUrl(bookData.getBookUrl());
            }
            
            if (bookData.getKnowId() != null) {
                chatSession.setKnowId(bookData.getKnowId());
            }
            
            chatSession.setUpdatedAt(LocalDateTime.now());
            
            // 保存更新
            chatSessionRepository.updateById(chatSession);
            
            return ApiResponse.success("聊天教材配置已更新");
            
        } catch (Exception e) {
            log.error("更新聊天教材配置失败: {}", e.getMessage(), e);
            return ApiResponse.error("更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取聊天详情
     * 
     * @param chatId 聊天ID
     * @param token 认证令牌
     * @return 聊天详情
     */
    @Override
    public ApiResponse<?> getChatDetail(Long chatId, String token) {
        try {
            // 解析用户信息
            String username = jwtUtil.getUsernameFromToken(token);
            User user = userService.getByUsername(username);
            if (user == null) {
                return ApiResponse.error("用户不存在");
            }
            
            // 查找对话信息
            ChatSession chatSession = chatSessionRepository.selectById(chatId);
            
            if (chatSession == null || !chatSession.getUserId().equals(user.getId()) || chatSession.getDeleted()) {
                return ApiResponse.error("对话不存在或无权访问");
            }
            
            return ApiResponse.success(chatSession);
            
        } catch (Exception e) {
            log.error("获取聊天详情失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取详情失败: " + e.getMessage());
        }
    }

    @Override
    public boolean updateChatTitle(Long chatId, String newTitle) {
        ChatSession chat = chatSessionRepository.selectById(chatId);
        if (chat == null) {
            return false;
        }
        
        chat.setTitle(newTitle);
        chat.setUpdatedAt(LocalDateTime.now());
        
        try {
            chatSessionRepository.updateById(chat);
            return true;
        } catch (Exception e) {
            log.error("更新聊天标题失败", e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean deleteChat(Long chatId) {
        try {
            // 使用正确的仓库和方法
            chatMessageRepository.deleteAllBySessionId(chatId);
            
            // 然后删除聊天本身
            chatSessionRepository.deleteById(chatId);
            
            return true;
        } catch (Exception e) {
            log.error("删除聊天失败", e);
            return false;
        }
    }

    @Override
    public ChatSession getChatById(Long chatId) {
        return chatSessionRepository.selectById(chatId);
    }
} 