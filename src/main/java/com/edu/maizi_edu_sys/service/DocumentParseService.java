package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.dto.DocumentParseRequest;
import com.edu.maizi_edu_sys.dto.DocumentParseResponse;
import com.edu.maizi_edu_sys.entity.DocumentParseTask;

import java.util.List;

/**
 * 文档解析服务接口
 * 
 * <AUTHOR> Integration
 * @since 2025-01-20
 */
public interface DocumentParseService {

    /**
     * 创建单文件解析任务
     */
    DocumentParseResponse createSingleTask(DocumentParseRequest request, Long userId);

    /**
     * 创建批量URL解析任务
     */
    DocumentParseResponse createBatchUrlTask(DocumentParseRequest request, Long userId);

    /**
     * 申请批量文件上传链接
     */
    DocumentParseResponse createBatchUploadUrls(DocumentParseRequest request, Long userId);

    /**
     * 查询任务结果
     */
    DocumentParseResponse getTaskResult(String taskId);

    /**
     * 查询批量任务结果
     */
    DocumentParseResponse getBatchResults(String batchId);

    /**
     * 根据用户ID查询任务列表
     */
    List<DocumentParseTask> getUserTasks(Long userId, String status);

    /**
     * 同步任务状态 (定时任务调用)
     */
    void syncTaskStatus();

    /**
     * 处理MineRU回调
     */
    void handleCallback(String taskId, String status, String resultData);
}
