package com.edu.maizi_edu_sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.edu.maizi_edu_sys.entity.SystemConfig;

/**
 * 系统配置服务接口
 */
public interface SystemConfigService extends IService<SystemConfig> {
    
    /**
     * 获取配置值
     * @param configKey 配置键
     * @return 配置值，如果不存在返回null
     */
    String getConfigValue(String configKey);
    
    /**
     * 获取配置值，如果不存在返回默认值
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值或默认值
     */
    String getConfigValue(String configKey, String defaultValue);
    
    /**
     * 设置配置值
     * @param configKey 配置键
     * @param configValue 配置值
     * @param description 配置描述
     */
    void setConfigValue(String configKey, String configValue, String description);
    
    /**
     * 更新配置值
     * @param configKey 配置键
     * @param configValue 配置值
     */
    void updateConfigValue(String configKey, String configValue);
    
    /**
     * 获取题目修正提示词
     * @return 提示词内容
     */
    String getCorrectionPrompt();
    
    /**
     * 设置题目修正提示词
     * @param prompt 提示词内容
     */
    void setCorrectionPrompt(String prompt);
    
    /**
     * 获取上次处理的题目ID
     * @return 上次处理的题目ID
     */
    Long getLastProcessedTopicId();
    
    /**
     * 获取上次处理的题目ID（带悲观锁）
     * 用于防止并发修改，确保数据一致性
     * @return 上次处理的题目ID
     */
    Long getLastProcessedTopicIdWithLock();
    
    /**
     * 设置上次处理的题目ID
     * @param lastProcessedId 上次处理的题目ID
     */
    void setLastProcessedTopicId(Long lastProcessedId);
    
    /**
     * 初始化默认配置
     */
    void initDefaultConfigs();
    
    /**
     * 获取失败批次的重试计数
     * @param lastFailedId 失败的最后一个题目ID
     * @return 重试次数
     */
    int getFailedBatchRetryCount(int lastFailedId);
    
    /**
     * 增加失败批次的重试计数
     * @param lastFailedId 失败的最后一个题目ID
     * @return 新的重试次数
     */
    int incrementFailedBatchRetryCount(int lastFailedId);
    
    /**
     * 清除失败批次的重试计数
     * @param lastFailedId 失败的最后一个题目ID
     */
    void clearFailedBatchRetryCount(int lastFailedId);
}