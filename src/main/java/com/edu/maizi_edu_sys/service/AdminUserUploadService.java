package com.edu.maizi_edu_sys.service;

import com.baomidou.mybatisplus.core.metadata.IPage;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 管理员用户上传统计服务接口
 */
public interface AdminUserUploadService {
    
    /**
     * 获取用户上传统计概览
     */
    Map<String, Object> getUploadOverview();
    
    /**
     * 获取用户每日上传统计列表
     */
    IPage<?> getDailyUploadStats(int page, int size, LocalDate startDate, LocalDate endDate, 
                                String username, String orderBy, String orderType);
    
    /**
     * 检测异常上传行为
     */
    List<?> detectAnomalousUploads(LocalDate date);
    
    /**
     * 获取用户上传趋势分析
     */
    Map<String, Object> getUserUploadTrend(Long userId, int days);
    
    /**
     * 获取用户上传详情
     */
    Map<String, Object> getUserUploadDetail(Long userId, LocalDate date);
    
    /**
     * 设置用户上传限制
     */
    void setUserUploadLimit(Long userId, Integer dailyLimit, String reason, Long adminId);
    
    /**
     * 导出用户上传统计
     */
    String exportUploadStats(LocalDate startDate, LocalDate endDate);
    
    /**
     * 分页获取用户上传时间线
     */
    Map<String, Object> getUserUploadTimeline(Long userId, LocalDate date, int page, int size);
} 