package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicCorrectionSuggestion;
import com.edu.maizi_edu_sys.mapper.CorrectionTopicMapper;
import com.edu.maizi_edu_sys.mapper.TopicCorrectionSuggestionMapper;

import com.edu.maizi_edu_sys.util.DoubaoSyncUtil;
import com.edu.maizi_edu_sys.dto.TopicCorrectionDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonProcessingException;

import com.edu.maizi_edu_sys.config.CorrectionProperties;
import com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.context.annotation.Lazy;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 题目修正服务
 * 
 * 进度记录机制说明：
 * 1. 查询方式：使用 getNextBatchById() 方法，按 ORDER BY id ASC 排序
 * 2. 进度记录：记录已处理的最大题目ID，而不是题目的创建时间
 * 3. 时间字段：记录的是处理时间（LocalDateTime.now()），用于监控和审计
 * 4. 兼容性：保留时间字段格式以兼容旧版本的进度文件
 * 
 * 修复说明：
 * - 之前存在查询按ID排序但进度按时间记录的不一致问题
 * - 现在统一使用基于ID的进度追踪，确保顺序一致性
 * - 时间字段明确表示处理时间而非题目创建时间
 */
@Service
public class TopicCorrectionService {

    private static final Logger logger = LoggerFactory.getLogger(TopicCorrectionService.class);
    // 批处理大小由配置注入
    private final int batchSize;
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private CorrectionTopicMapper topicMapper;

    @Autowired
    private DoubaoSyncUtil doubaoSyncUtil;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private TopicCorrectionSuggestionMapper correctionSuggestionMapper;

    private final CorrectionProperties correctionProperties;
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    // 延迟注入避免循环依赖
    private CorrectionApprovalService approvalService;

    // CorrectionProgress类已移除，改为使用数据库存储进度

    @Autowired
    public TopicCorrectionService(CorrectionProperties correctionProperties) {
        this.correctionProperties = correctionProperties;
        this.batchSize = correctionProperties.getBatch().getSize();
        logger.info("题目修正服务初始化完成，批处理大小: {}", batchSize);
    }

    // 设置审核服务（延迟注入）
    @Autowired(required = false)
    @Lazy
    public void setApprovalService(CorrectionApprovalService approvalService) {
        this.approvalService = approvalService;
    }

    @org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
    public Map<String, Object> checkAndProcessOneBatch() {
        Map<String, Object> result = new HashMap<>();
        List<Integer> topicIds = new ArrayList<>();
        
        try {
            // 检查时间窗口限制
            if (correctionProperties.getSchedule().isEnabled()) {
                if (!correctionProperties.getSchedule().isInAllowedTimeWindow()) {
                    logger.info("当前时间不在允许的更新窗口内 ({}~{})，跳过处理", 
                               correctionProperties.getSchedule().getStartTime(),
                               correctionProperties.getSchedule().getEndTime());
                    result.put("success", true);
                    result.put("message", "不在允许的时间窗口内");
                    return result;
                }
                logger.info("当前时间在允许的更新窗口内，继续处理");
            }

            // 处理过期的审核记录
            if (correctionProperties.getApproval().isEnabled() && approvalService != null) {
                approvalService.processExpiredApprovals();
            }

            // 系统启动时检查并恢复处理中的题目
            recoverProcessingTopicsIfNeeded();

            // 从数据库读取上次处理的状态（使用悲观锁防止并发问题）
            Long lastProcessedIdLong = systemConfigService.getLastProcessedTopicIdWithLock();
            int lastProcessedId = lastProcessedIdLong != null ? lastProcessedIdLong.intValue() : 0;
            
            logger.info("开始处理一批题目，上次处理的ID: {}", lastProcessedId);
        
            // 获取下一批待处理的题目
            List<Topic> topics = topicMapper.getNextBatchById(lastProcessedId, batchSize);
            
            if (topics.isEmpty()) {
                logger.info("没有更多题目需要处理");
                result.put("success", true);
                result.put("message", "没有更多题目需要处理");
                result.put("processedCount", 0);
                return result;
            }
            
            logger.info("获取到 {} 个题目进行处理", topics.size());
        
            // 标记题目为处理中状态
            topicIds = topics.stream().map(Topic::getId).collect(Collectors.toList());
            topicMapper.claimTopicsBatch(topicIds);

            // 转换为JSON格式
            String topicsJson = convertTopicsToJson(topics);
            
            // 从数据库获取修正提示词
            String correctionPrompt = systemConfigService.getCorrectionPrompt();

            // 调用大模型进行修正（使用配置的超时时间）
            String aiResponse = doubaoSyncUtil.syncRequest(
                correctionPrompt + "\n\n" + topicsJson, 
                correctionProperties.getApi().getTimeoutSeconds()
            );

            // 记录AI返回的结果
            logger.info("AI返回结果长度: {}", aiResponse != null ? aiResponse.length() : 0);
            
            // 验证AI返回结果
            if (!isValidCorrectionResult(aiResponse)) {
                logger.warn("AI返回结果无效，检查重试计数");
                return handleBatchFailure(topicIds, topics, "AI返回结果无效");
            }
            
            // 解析修正建议
            List<Map<String, Object>> corrections = parseCorrections(aiResponse);
            
            // 检查解析结果
            if (corrections == null) {
                logger.warn("AI返回结果解析失败，检查重试计数");
                return handleBatchFailure(topicIds, topics, "AI返回结果解析失败");
            }
            
            // 如果AI返回空数组，表示没有发现错误，这是正常情况
            if (corrections.isEmpty()) {
                logger.info("AI未发现任何错误，这批题目无需修正");
                // 更新处理进度（空结果也要推进进度）
                Integer newLastProcessedId = topics.get(topics.size() - 1).getId();
                systemConfigService.setLastProcessedTopicId(newLastProcessedId.longValue());
                
                // 成功处理后清除重试计数
                systemConfigService.clearFailedBatchRetryCount(newLastProcessedId);
                
                // 重置题目状态为正常
            for (Integer topicId : topicIds) {
                 topicMapper.update(null, new com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper<Topic>()
                         .eq("id", topicId)
                         .set("corrected", 0));
             }
                
                result.put("success", true);
                result.put("message", "这批题目无需修正");
                result.put("processedCount", topics.size());
                result.put("correctionsCount", 0);
                return result;
            }
            
            // 检查修正数量与输入题目数量的一致性
            if (corrections.size() > topics.size()) {
                logger.warn("AI返回的修正数量({})超过输入题目数量({})，截取前{}条", 
                           corrections.size(), topics.size(), topics.size());
                corrections = corrections.subList(0, topics.size());
            }
            
            // 生成批次ID
            String batchId = UUID.randomUUID().toString();
            
            // 保存修正建议到数据库
            List<TopicCorrectionSuggestion> suggestions = new ArrayList<>();
            for (Map<String, Object> correction : corrections) {
                TopicCorrectionSuggestion suggestion = new TopicCorrectionSuggestion();
                suggestion.setTopicId(((Number) correction.get("id")).longValue());
                suggestion.setCorrectionReason((String) correction.get("reason"));
                suggestion.setSuggestedUpdates(objectMapper.writeValueAsString(correction.get("updates")));
                suggestion.setAiResponse(aiResponse);
                suggestion.setBatchId(batchId);
                suggestion.setStatus(correctionProperties.getApproval().isEnabled() ? "PENDING" : "APPLIED");
                suggestion.setCreatedAt(LocalDateTime.now());
                suggestion.setUpdatedAt(LocalDateTime.now());
                suggestions.add(suggestion);
            }
            
            // 批量插入修正建议
            correctionSuggestionMapper.batchInsert(suggestions);
                
            // 根据配置决定处理方式
            if (correctionProperties.getApproval().isEnabled()) {
                // 审核模式：保存待审核记录
                String correctionJson = objectMapper.writeValueAsString(corrections);
                String statistics = generateCorrectionStatistics(corrections, topics.size());
                Long approvalId = approvalService.savePendingCorrection(correctionJson, statistics);
                logger.info("已保存 {} 条修正建议待审核，审核ID: {}", corrections.size(), approvalId);
                result.put("message", "已保存修正建议待审核");
                result.put("approvalId", approvalId);
            } else {
                // 直接应用模式
                int appliedCount = applyCorrectionUpdates(corrections);
                logger.info("直接应用了 {} 条修正", appliedCount);
                result.put("message", "直接应用了修正");
                result.put("appliedCount", appliedCount);
            }
            
            // 更新处理进度到数据库（只有成功处理后才推进进度）
            Integer newLastProcessedId = topics.get(topics.size() - 1).getId();
            systemConfigService.setLastProcessedTopicId(newLastProcessedId.longValue());
            
            // 成功处理后清除重试计数
            systemConfigService.clearFailedBatchRetryCount(newLastProcessedId);
            
            // 重置题目状态为正常
            for (Integer topicId : topicIds) {
                 topicMapper.update(null, new com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper<Topic>()
                         .eq("id", topicId)
                         .set("corrected", 0));
             }
            
            result.put("success", true);
            result.put("processedCount", topics.size());
            result.put("correctionsCount", corrections.size());
            result.put("batchId", batchId);
            
            // 处理过期的审核记录
            if (correctionProperties.getApproval().isEnabled()) {
                approvalService.processExpiredApprovals();
            }
            
        } catch (Exception e) {
            logger.error("处理批次时发生错误", e);
            // 异常时重置题目状态
            if (!topicIds.isEmpty()) {
                try {
                    // 重置题目状态为待处理
                     for (Integer topicId : topicIds) {
                         topicMapper.update(null, new com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper<Topic>()
                                 .eq("id", topicId)
                                 .set("corrected", 0));
                     }
                    logger.info("异常时已重置 {} 条题目状态", topicIds.size());
                } catch (Exception resetEx) {
                    logger.error("重置题目状态失败", resetEx);
                }
            }
            result.put("success", false);
            result.put("message", "处理失败: " + e.getMessage());
            result.put("retryable", true);
            // 抛出异常以触发事务回滚
            throw new RuntimeException("批次处理失败", e);
        }
        
        return result;
    }

    /**
     * 公开的应用修正更新方法，供其他服务调用
     */
    @org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
    public int applyCorrectionUpdatesPublic(List<TopicCorrectionDTO> corrections) {
        // 转换TopicCorrectionDTO为Map格式
        List<Map<String, Object>> correctionMaps = new ArrayList<>();
        for (TopicCorrectionDTO dto : corrections) {
            Map<String, Object> correctionMap = new HashMap<>();
            correctionMap.put("id", dto.getId().longValue());
            correctionMap.put("reason", dto.getReason());
            correctionMap.put("updates", dto.getUpdates());
            correctionMaps.add(correctionMap);
        }
        return applyCorrectionUpdates(correctionMaps);
    }

    /**
     * 审核通过后更新处理进度，供审核服务调用
     */
    @org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
    public void updateProgressAfterApproval(int maxProcessedId) {
        try {
            Long currentProgress = systemConfigService.getLastProcessedTopicId();
            if (currentProgress == null) {
                currentProgress = 0L;
            }
            
            // 只有当新的ID大于当前进度时才更新
            if (maxProcessedId > currentProgress) {
                systemConfigService.setLastProcessedTopicId((long) maxProcessedId);
                logger.info("审核后更新处理进度：{} -> {}（最大题目ID）", 
                           currentProgress, maxProcessedId);
            } else {
                logger.debug("审核ID {} 小于等于当前进度 {}，无需更新", maxProcessedId, currentProgress);
            }
        } catch (Exception e) {
            logger.error("审核后更新进度失败，maxId: {}", maxProcessedId, e);
        }
    }

    /**
     * 生成修正统计信息字符串
     */
    private String generateCorrectionStatistics(List<Map<String, Object>> corrections, int totalTopics) {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalTopics", totalTopics);
        stats.put("correctionsCount", corrections.size());
        stats.put("correctionRate", corrections.isEmpty() ? 0.0 : (corrections.size() * 100.0 / totalTopics));
        
        // 统计修正字段
        Map<String, Integer> fieldStats = new HashMap<>();
        for (Map<String, Object> correction : corrections) {
            @SuppressWarnings("unchecked")
            Map<String, Object> updates = (Map<String, Object>) correction.get("updates");
            if (updates != null) {
                for (String field : updates.keySet()) {
                    fieldStats.merge(field, 1, Integer::sum);
                }
            }
        }
        stats.put("fieldStats", fieldStats);
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(stats);
        } catch (Exception e) {
            logger.error("生成统计信息JSON失败", e);
            return "{}";
        }
    }
    
    /**
     * 处理批次失败的逻辑（结合重试计数和corrected字段）
     */
    private Map<String, Object> handleBatchFailure(List<Integer> topicIds, List<Topic> topics, String errorMessage) {
        Map<String, Object> result = new HashMap<>();
        
        if (topics.isEmpty()) {
            result.put("success", false);
            result.put("message", errorMessage);
            result.put("retryable", true);
            return result;
        }
        
        // 获取批次的最后一个题目ID作为失败标识
        int lastTopicId = topics.get(topics.size() - 1).getId();
        
        // 增加重试计数
        int retryCount = systemConfigService.incrementFailedBatchRetryCount(lastTopicId);
        int maxRetryCount = ((SystemConfigServiceImpl) systemConfigService).getMaxRetryCount();
        
        logger.warn("批次失败，题目ID范围: {} - {}, 重试次数: {}/{}", 
                   topics.get(0).getId(), lastTopicId, retryCount, maxRetryCount);
        
        if (retryCount >= maxRetryCount) {
            // 超过重试阈值，标记为失败状态并推进游标
            logger.error("批次重试次数超过阈值({})，标记为失败状态: 题目ID {} - {}", 
                        maxRetryCount, topics.get(0).getId(), lastTopicId);
            
            // 标记题目为失败状态（corrected = -1）
            topicMapper.markTopicsAsFailed(topicIds);
            
            // 推进游标，跳过这批有问题的数据
            systemConfigService.setLastProcessedTopicId((long) lastTopicId);
            
            // 清除重试计数
            systemConfigService.clearFailedBatchRetryCount(lastTopicId);
            
            result.put("success", true); // 标记为成功，因为已经处理了问题
            result.put("message", String.format("批次失败超过阈值，已标记%d个题目为失败状态: %s", 
                                               topicIds.size(), errorMessage));
            result.put("processedCount", topics.size());
            result.put("failedCount", topicIds.size());
            result.put("skipped", true);
            
            logger.info("已跳过失败批次，游标推进到: {}", lastTopicId);
        } else {
            // 未超过阈值，重置题目状态等待重试
            for (Integer topicId : topicIds) {
                topicMapper.update(null, new com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper<Topic>()
                        .eq("id", topicId.longValue())
                        .set("corrected", 0));
            }
            
            result.put("success", false);
            result.put("message", String.format("%s (重试 %d/%d)", errorMessage, retryCount, maxRetryCount));
            result.put("retryable", true);
            result.put("retryCount", retryCount);
        }
        
        return result;
    }
    
    /**
     * 验证AI返回结果是否有效
     */
    private boolean isValidCorrectionResult(String correctionResult) {
        if (correctionResult == null || correctionResult.trim().isEmpty()) {
            return false;
        }
        
        String trimmed = correctionResult.trim();
        
        // 空数组[]是有效结果，表示题目没有错误
        if (trimmed.equals("[]")) {
            logger.info("AI返回空数组，表示这批题目没有发现错误");
            return true;
        }
        
        // 空对象{}不是预期的格式
        if (trimmed.equals("{}")) {
            logger.warn("AI返回空对象{}，这不符合预期格式（应该返回数组）");
            return false;
        }
        
        // 验证是否为有效的JSON格式
        try {
            ObjectMapper mapper = new ObjectMapper();
            mapper.readTree(trimmed);
            logger.info("AI返回有效JSON，长度: {}", trimmed.length());
            return true;
        } catch (Exception e) {
            logger.warn("AI返回结果不是有效的JSON格式: {}", trimmed.length() > 100 ? trimmed.substring(0, 100) + "..." : trimmed);
            return false;
        }
    }
    


    /**
     * 系统启动时恢复处理中的题目
     */
    private void recoverProcessingTopicsIfNeeded() {
        try {
            int processingCount = topicMapper.countProcessingTopics();
            if (processingCount > 0) {
                logger.warn("检测到 {} 条题目处于处理中状态，可能由于系统异常退出导致", processingCount);
                
                // 将处理中的题目重置为待处理状态
                int resetCount = topicMapper.resetProcessingTopics();
                logger.info("已重置 {} 条处理中的题目为待处理状态", resetCount);
                
                // 记录恢复操作
                logRecoveryAction(resetCount);
            }
        } catch (Exception e) {
            logger.error("恢复处理中题目失败", e);
        }
    }

    // 进度读取和保存方法已移除，直接使用SystemConfigService进行数据库操作

    /**
     * 记录恢复操作日志
     */
    private void logRecoveryAction(int resetCount) {
        // 使用日志记录恢复操作，不再写入文件
        logger.warn("[{}] 系统恢复：重置 {} 条处理中的题目", 
                   LocalDateTime.now().format(DATE_TIME_FORMATTER), resetCount);
    }

    /**
     * 获取处理统计信息（从数据库）
     */
    public Map<String, Object> getProcessingStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            Long lastProcessedId = systemConfigService.getLastProcessedTopicId();
            if (lastProcessedId == null) {
                lastProcessedId = 0L;
            }
            
            int pendingCount = topicMapper.countPendingTopics(lastProcessedId.intValue());
            int processingCount = topicMapper.countProcessingTopics();
            
            stats.put("lastProcessedId", lastProcessedId);
            stats.put("lastUpdateTime", LocalDateTime.now().format(DATE_TIME_FORMATTER));
            stats.put("pendingTopics", pendingCount);
            stats.put("processingTopics", processingCount);
            stats.put("batchSize", batchSize);
            
            logger.info("处理统计：上次最大ID={}, 待处理={}, 处理中={}（基于题目ID顺序）", 
                       lastProcessedId, pendingCount, processingCount);
            
        } catch (Exception e) {
            logger.error("获取处理统计信息失败", e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }

    private String convertTopicsToJson(List<Topic> topics) {
        ObjectMapper mapper = new ObjectMapper();
        // TypeReference用于帮助Jackson反序列化泛型集合，例如从JSON字符串解析成List<Map<String, String>>
        com.fasterxml.jackson.core.type.TypeReference<List<java.util.Map<String, String>>> typeRef = new com.fasterxml.jackson.core.type.TypeReference<List<java.util.Map<String, String>>>() {};

        List<java.util.Map<String, Object>> topicMaps = new java.util.ArrayList<>();
        for (Topic topic : topics) {
            // 使用LinkedHashMap确保字段顺序与添加顺序一致，更符合预期
            java.util.Map<String, Object> topicMap = new java.util.LinkedHashMap<>();
            topicMap.put("id", topic.getId());
            topicMap.put("know_id", topic.getKnowId());
            topicMap.put("type", topic.getType());
            topicMap.put("title", topic.getTitle());
            topicMap.put("tags", topic.getTags());

            // 关键步骤：将options字段从JSON字符串解析为真正的JSON数组结构
            try {
                if (topic.getOptions() != null && !topic.getOptions().trim().isEmpty()) {
                    topicMap.put("options", mapper.readValue(topic.getOptions(), typeRef));
                } else {
                    topicMap.put("options", new java.util.ArrayList<>());
                }
            } catch (IOException e) {
                logger.warn("无法解析 topic ID {} 的 options 字段, 将其设置为空数组. 内容: '{}'", topic.getId(), topic.getOptions());
                topicMap.put("options", new java.util.ArrayList<>());
            }

            // 关键步骤：同样处理subs字段
            try {
                if (topic.getSubs() != null && !topic.getSubs().trim().isEmpty()) {
                    topicMap.put("subs", mapper.readValue(topic.getSubs(), typeRef));
                } else {
                    topicMap.put("subs", new java.util.ArrayList<>());
                }
            } catch (IOException e) {
                logger.warn("无法解析 topic ID {} 的 subs 字段, 将其设置为空数组. 内容: '{}'", topic.getId(), topic.getSubs());
                topicMap.put("subs", new java.util.ArrayList<>());
            }

            topicMap.put("answer", topic.getAnswer());
            topicMap.put("source", topic.getSource());
            topicMap.put("parse", topic.getParse());
            topicMap.put("difficulty", topic.getDifficulty());

            topicMaps.add(topicMap);
        }

        try {
            // 将构建好的Map列表序列化为格式化的JSON字符串
            return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(topicMaps);
        } catch (Exception e) {
            logger.error("序列化自定义的题目Map为JSON时失败", e);
            return "[]";
        }
    }



    /**
     * 解析AI返回的修正建议JSON
     * @param correctionResult AI返回的JSON字符串
     * @return 修正建议列表，解析失败时返回null
     */
    private List<Map<String, Object>> parseCorrections(String correctionResult) {
        if (correctionResult == null || correctionResult.trim().isEmpty()) {
            logger.warn("AI返回结果为空");
            return null;
        }
        
        String trimmed = correctionResult.trim();
        
        // 空数组是正常情况，表示没有发现错误
        if ("[]".equals(trimmed)) {
            logger.info("AI返回空数组，表示没有发现错误");
            return new ArrayList<>();
        }
        
        // 空对象不是预期格式
        if ("{}".equals(trimmed)) {
            logger.warn("AI返回空对象{}，这不符合预期格式（应该返回数组）");
            return null;
        }
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            
            // 首先检查是否为数组格式
            if (!trimmed.startsWith("[") || !trimmed.endsWith("]")) {
                logger.warn("AI返回结果不是数组格式: {}", trimmed.length() > 100 ? trimmed.substring(0, 100) + "..." : trimmed);
                return null;
            }
            
            TypeReference<List<Map<String, Object>>> typeRef = new TypeReference<List<Map<String, Object>>>() {};
            List<Map<String, Object>> corrections = mapper.readValue(trimmed, typeRef);
            
            if (corrections == null) {
                logger.warn("解析结果为null");
                return null;
            }
            
            // 过滤和验证修正建议
            List<Map<String, Object>> validCorrections = new ArrayList<>();
            for (Map<String, Object> correction : corrections) {
                if (correction == null) {
                    logger.warn("发现null修正建议，跳过");
                    continue;
                }
                
                if (!correction.containsKey("id")) {
                    logger.warn("修正建议缺少题目ID，跳过: {}", correction);
                    continue;
                }
                
                // 验证ID格式
                Object idObj = correction.get("id");
                if (!(idObj instanceof Number) && !(idObj instanceof String)) {
                    logger.warn("题目ID格式无效，跳过: {}", idObj);
                    continue;
                }
                
                // 如果ID是字符串，尝试转换为数字
                if (idObj instanceof String) {
                    try {
                        correction.put("id", Long.parseLong((String) idObj));
                    } catch (NumberFormatException e) {
                        logger.warn("无法解析题目ID: {}", idObj);
                        continue;
                    }
                }
                
                // 检查是否有updates字段
                if (!correction.containsKey("updates")) {
                    logger.warn("修正建议缺少updates字段，跳过: {}", correction);
                    continue;
                }
                
                validCorrections.add(correction);
            }
            
            logger.info("解析到 {} 项修正建议，其中 {} 项有效", corrections.size(), validCorrections.size());
            return validCorrections;
            
        } catch (JsonProcessingException e) {
            logger.error("JSON解析错误: {}", e.getMessage());
            logger.debug("无效的JSON内容: {}", trimmed.length() > 200 ? trimmed.substring(0, 200) + "..." : trimmed);
            return null;
        } catch (Exception e) {
            logger.error("解析AI修正建议时发生未知错误: {}", e.getMessage(), e);
            logger.debug("出错的内容: {}", trimmed.length() > 200 ? trimmed.substring(0, 200) + "..." : trimmed);
            return null;
        }
    }

    /**
     * 应用修正建议更新数据库
     * @param corrections 修正建议列表
     * @return 实际更新的题目数量
     */
    private int applyCorrectionUpdates(List<Map<String, Object>> corrections) {
        if (corrections == null || corrections.isEmpty()) {
            return 0;
        }
        
        int totalUpdated = 0;
        
        for (Map<String, Object> correction : corrections) {
            try {
                Object idObj = correction.get("id");
                if (idObj == null) {
                    logger.warn("修正建议缺少题目ID，跳过: {}", correction);
                    continue;
                }
                
                Long topicId = ((Number) idObj).longValue();
                
                @SuppressWarnings("unchecked")
                Map<String, Object> updates = (Map<String, Object>) correction.get("updates");
                if (updates == null || updates.isEmpty()) {
                    logger.warn("修正建议 ID {} 没有有效的更新字段", topicId);
                    continue;
                }
                
                // 过滤掉null值，避免生成无效的SQL
                Map<String, Object> filteredUpdates = new HashMap<>();
                for (Map.Entry<String, Object> entry : updates.entrySet()) {
                    if (entry.getValue() != null) {
                        filteredUpdates.put(entry.getKey(), entry.getValue());
                    }
                }
                
                // 如果过滤后没有有效的更新字段，跳过
                if (filteredUpdates.isEmpty()) {
                    logger.warn("修正建议 ID {} 过滤后没有有效的更新字段（所有值都为null）", topicId);
                    continue;
                }
                
                // 执行数据库更新
                int updated = topicMapper.updateTopicFields(topicId.intValue(), filteredUpdates);
                
                if (updated > 0) {
                    totalUpdated++;
                    logger.info("成功更新题目 ID {}: {} (原因: {})", 
                               topicId, 
                               filteredUpdates.keySet(), 
                               correction.get("reason"));
                } else {
                    logger.warn("题目 ID {} 更新失败，可能该题目不存在", topicId);
                }
                
            } catch (Exception e) {
                logger.error("应用修正建议失败，题目 ID: {}, 原因: {}", correction.get("id"), correction.get("reason"), e);
            }
        }
        
        return totalUpdated;
    }


}
