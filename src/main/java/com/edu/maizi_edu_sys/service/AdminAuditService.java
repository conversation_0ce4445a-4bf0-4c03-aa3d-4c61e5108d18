package com.edu.maizi_edu_sys.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.edu.maizi_edu_sys.dto.*;

import java.util.List;

/**
 * 管理员审核服务接口
 */
public interface AdminAuditService {
    
    /**
     * 获取审核统计数据
     */
    AdminAuditStatsDTO getAuditStats();
    
    /**
     * 获取待审核题目列表
     */
    IPage<AdminAuditItemDTO> getPendingAudits(int pageNum, int pageSize, String search);
    
    /**
     * 获取已通过题目列表
     */
    IPage<AdminAuditItemDTO> getApprovedAudits(int pageNum, int pageSize, String search);
    
    /**
     * 获取已拒绝题目列表
     */
    IPage<AdminAuditItemDTO> getRejectedAudits(int pageNum, int pageSize, String search);
    
    /**
     * 获取审核记录列表
     */
    IPage<AdminAuditItemDTO> getAuditRecords(int pageNum, int pageSize, String search);
    
    /**
     * 获取审核详情
     */
    AdminAuditItemDTO getAuditDetail(Long auditId);
    
    /**
     * 提交审核结果
     */
    void submitAuditResult(AdminAuditSubmitDTO auditSubmitDTO, Long auditorId);
    
    /**
     * 快速通过
     */
    void quickApprove(Long auditId, Long auditorId);
    
    /**
     * 快速拒绝
     */
    void quickReject(Long auditId, String reason, Long auditorId);
    
    /**
     * 批量审核
     */
    void batchAudit(List<Long> auditIds, AdminBatchAuditDTO batchAuditDTO, Long auditorId);
} 