package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.config.CorrectionProperties;
import com.edu.maizi_edu_sys.entity.CorrectionApproval;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.mapper.CorrectionApprovalMapper;
import com.edu.maizi_edu_sys.mapper.CorrectionTopicMapper;
import com.edu.maizi_edu_sys.mapper.TopicCorrectionSuggestionMapper;
import com.edu.maizi_edu_sys.dto.TopicCorrectionDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

@Service
public class CorrectionApprovalService {

    private static final Logger logger = LoggerFactory.getLogger(CorrectionApprovalService.class);

    @Autowired
    private CorrectionApprovalMapper approvalMapper;

    @Autowired
    private CorrectionTopicMapper topicMapper;

    @Autowired
    private CorrectionProperties correctionProperties;

    @Autowired
    @Lazy
    private TopicCorrectionService topicCorrectionService;
    
    @Autowired
    private TopicCorrectionSuggestionMapper correctionSuggestionMapper;

    /**
     * 保存待审核的修正建议
     */
    @Transactional(rollbackFor = Exception.class)
    public Long savePendingCorrection(String correctionJson, String statistics) {
        String date = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        
        CorrectionApproval approval = new CorrectionApproval();
        approval.setCorrectionDate(date);
        approval.setCorrectionJson(correctionJson);
        approval.setStatistics(statistics);
        approval.setStatus(0); // 待审核
        approval.setCreatedAt(LocalDateTime.now());
        
        // 设置过期时间
        LocalDateTime expiresAt = LocalDateTime.now().plusHours(correctionProperties.getApproval().getAutoRejectHours());
        approval.setExpiresAt(expiresAt);
        
        approvalMapper.insert(approval);
        
        logger.info("保存待审核修正记录，ID: {}, 日期: {}, 过期时间: {}", 
                   approval.getId(), date, expiresAt.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        return approval.getId();
    }

    /**
     * 获取待审核的修正记录
     */
    public List<CorrectionApproval> getPendingApprovals() {
        return approvalMapper.getPendingApprovals();
    }

    /**
     * 获取待审核的修正记录（分页）
     */
    public Map<String, Object> getPendingApprovalsWithPagination(int page, int size, String search, String status) {
        try {
            // 计算偏移量
            int offset = (page - 1) * size;
            
            // 获取总数
            int total = approvalMapper.countPendingApprovals(search, status);
            
            // 获取分页数据
            List<CorrectionApproval> data = approvalMapper.getPendingApprovalsWithPagination(offset, size, search, status);
            
            // 计算分页信息
            int totalPages = (int) Math.ceil((double) total / size);
            
            Map<String, Object> result = new HashMap<>();
            result.put("data", data);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", totalPages);
            result.put("hasNext", page < totalPages);
            result.put("hasPrev", page > 1);
            
            return result;
        } catch (Exception e) {
            logger.error("获取分页待审核列表失败", e);
            throw e;
        }
    }

    /**
     * 审核通过修正
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean approveCorrection(Long approvalId, String approver, String comment) {
        try {
            // 获取审核记录
            CorrectionApproval approval = approvalMapper.selectById(approvalId);
            if (approval == null || approval.getStatus() != 0) {
                logger.warn("审核记录不存在或状态无效，ID: {}", approvalId);
                return false;
            }

            // 解析修正建议并应用到数据库
            List<TopicCorrectionDTO> corrections = parseCorrections(approval.getCorrectionJson());
            List<Integer> topicIds = new java.util.ArrayList<>();
            
            if (!corrections.isEmpty()) {
                int updatedCount = topicCorrectionService.applyCorrectionUpdatesPublic(corrections);
                logger.info("审核通过，应用 {} 项修正建议，更新了 {} 条题目", corrections.size(), updatedCount);
                
                // 收集涉及的题目ID
                topicIds = corrections.stream()
                        .map(TopicCorrectionDTO::getId)
                        .collect(Collectors.toList());
            }

            // 将相关题目标记为已校对（从corrected=2改为corrected=1）
            // 注意：即使没有修正建议，也需要将处理中的题目标记为已完成
            if (!topicIds.isEmpty()) {
                int markedCount = topicMapper.markTopicsCorrected(topicIds);
                logger.info("审核通过，已将 {} 条题目标记为已校对", markedCount);
                
                // 更新TopicCorrectionSuggestion状态为APPLIED
                List<Long> topicLongIds = topicIds.stream()
                        .map(Integer::longValue)
                        .collect(Collectors.toList());
                int updatedSuggestions = correctionSuggestionMapper.updateStatusByTopicIds(topicLongIds, "APPLIED");
                logger.info("审核通过，已将 {} 条修正建议状态更新为APPLIED", updatedSuggestions);
                
                // 更新处理进度 - 记录最大的题目ID
                int maxId = topicIds.stream().mapToInt(Integer::intValue).max().orElse(0);
                if (maxId > 0) {
                    // 调用TopicCorrectionService的公开方法更新进度
                    topicCorrectionService.updateProgressAfterApproval(maxId);
                    logger.info("审核通过后更新处理进度到最大ID: {}（基于题目ID顺序）", maxId);
                }
            } else {
                // 如果没有修正建议，需要查找所有处理中状态的题目并标记为已完成
                // 这种情况下，AI返回空数组表示这批题目无需修正，应该标记为已完成
                logger.info("审核通过，AI返回空修正建议，需要手动查找并标记相关题目为已完成");
                // TODO: 这里需要根据审核记录的批次信息来确定哪些题目需要标记为已完成
                // 当前设计缺少批次关联信息，建议在CorrectionApproval表中添加batch_id字段
            }

            // 更新审核状态
            LocalDateTime now = LocalDateTime.now();
            int updated = approvalMapper.approveCorrection(approvalId, approver, comment, now);
            
            if (updated > 0) {
                logger.info("修正审核通过，ID: {}, 审核人: {}", approvalId, approver);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            logger.error("审核通过修正失败，ID: {}", approvalId, e);
            return false;
        }
    }

    /**
     * 拒绝修正
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean rejectCorrection(Long approvalId, String approver, String comment) {
        try {
            CorrectionApproval approval = approvalMapper.selectById(approvalId);
            if (approval == null || approval.getStatus() != 0) {
                logger.warn("审核记录不存在或状态无效，ID: {}", approvalId);
                return false;
            }

            // 解析修正建议，获取涉及的题目ID
            List<TopicCorrectionDTO> corrections = parseCorrections(approval.getCorrectionJson());
            if (!corrections.isEmpty()) {
                List<Integer> topicIds = corrections.stream()
                        .map(TopicCorrectionDTO::getId)
                        .collect(Collectors.toList());
                
                // 重置题目状态为待处理（从corrected=2改为corrected=0）
                resetTopicsStatus(topicIds);
                logger.info("审核拒绝，已重置 {} 条题目状态供重新处理", topicIds.size());
                
                // 更新TopicCorrectionSuggestion状态为REJECTED
                List<Long> topicLongIds = topicIds.stream()
                        .map(Integer::longValue)
                        .collect(Collectors.toList());
                int updatedSuggestions = correctionSuggestionMapper.updateStatusByTopicIds(topicLongIds, "REJECTED");
                logger.info("审核拒绝，已将 {} 条修正建议状态更新为REJECTED", updatedSuggestions);
            }

            LocalDateTime now = LocalDateTime.now();
            int updated = approvalMapper.rejectCorrection(approvalId, approver, comment, now);
            
            if (updated > 0) {
                logger.info("修正审核拒绝，ID: {}, 审核人: {}, 原因: {}", approvalId, approver, comment);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            logger.error("拒绝修正失败，ID: {}", approvalId, e);
            return false;
        }
    }

    /**
     * 处理过期的审核记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void processExpiredApprovals() {
        try {
            // 先获取过期的记录，重置对应题目状态
            List<CorrectionApproval> expiredApprovals = approvalMapper.getExpiredApprovals();
            
            for (CorrectionApproval approval : expiredApprovals) {
                List<TopicCorrectionDTO> corrections = parseCorrections(approval.getCorrectionJson());
                if (!corrections.isEmpty()) {
                    List<Integer> topicIds = corrections.stream()
                            .map(TopicCorrectionDTO::getId)
                            .collect(Collectors.toList());
                    
                    // 重置题目状态为待处理（从corrected=2改为corrected=0）
                    resetTopicsStatus(topicIds);
                    logger.info("过期审核记录 {} 涉及的 {} 条题目状态已重置", approval.getId(), topicIds.size());
                    
                    // 更新TopicCorrectionSuggestion状态为REJECTED（过期视为拒绝）
                    List<Long> topicLongIds = topicIds.stream()
                            .map(Integer::longValue)
                            .collect(Collectors.toList());
                    int updatedSuggestions = correctionSuggestionMapper.updateStatusByTopicIds(topicLongIds, "REJECTED");
                    logger.info("过期审核记录 {} 涉及的 {} 条修正建议状态已更新为REJECTED", approval.getId(), updatedSuggestions);
                }
            }
            
            // 然后标记审核记录为过期
            int expiredCount = approvalMapper.markExpiredApprovals();
            if (expiredCount > 0) {
                logger.info("标记了 {} 条过期的审核记录", expiredCount);
            }
        } catch (Exception e) {
            logger.error("处理过期审核记录失败", e);
        }
    }

    /**
     * 重置题目状态供下次重试
     */
    private void resetTopicsStatus(List<Integer> ids) {
        try {
            topicMapper.update(null, new com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper<Topic>()
                    .in("id", ids)
                    .set("corrected", 0));
            logger.info("已重置 {} 条题目状态供下次重试", ids.size());
        } catch (Exception e) {
            logger.error("重置题目状态失败", e);
        }
    }

    /**
     * 根据日期获取审核记录
     */
    public List<CorrectionApproval> getApprovalsByDate(String date) {
        return approvalMapper.getApprovalsByDate(date);
    }

    /**
     * 获取审核统计信息
     */
    public Map<String, Object> getApprovalStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 统计各种状态的记录数量
            // 0-待审核, 1-已通过, 2-已拒绝, 3-已过期
            long pendingCount = approvalMapper.selectCount(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<CorrectionApproval>()
                    .eq("status", 0)
            );
            
            long approvedCount = approvalMapper.selectCount(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<CorrectionApproval>()
                    .eq("status", 1)
            );
            
            long rejectedCount = approvalMapper.selectCount(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<CorrectionApproval>()
                    .eq("status", 2)
            );
            
            long expiredCount = approvalMapper.selectCount(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<CorrectionApproval>()
                    .eq("status", 3)
            );
            
            // 添加统计数据
            stats.put("pendingCount", pendingCount);
            stats.put("approvedCount", approvedCount);
            stats.put("rejectedCount", rejectedCount);
            stats.put("expiredCount", expiredCount);
            stats.put("withdrawnCount", 0L); // 当前系统中没有回撤状态，设为0
            
            // 配置信息
            stats.put("isSystemEnabled", correctionProperties.isEnabled());
            stats.put("isApprovalEnabled", correctionProperties.getApproval().isEnabled());
            stats.put("isScheduleEnabled", correctionProperties.getSchedule().isEnabled());
            
            if (correctionProperties.getSchedule().isEnabled()) {
                stats.put("allowedTimeWindow", 
                         correctionProperties.getSchedule().getStartTime() + " - " + 
                         correctionProperties.getSchedule().getEndTime());
                stats.put("inAllowedWindow", correctionProperties.getSchedule().isInAllowedTimeWindow());
            } else {
                // 当时间调度未启用时，设置默认值
                stats.put("allowedTimeWindow", "时间调度未启用");
                stats.put("inAllowedWindow", true); // 未启用时间限制时，默认为可运行
            }
            
        } catch (Exception e) {
            logger.error("获取审核统计信息失败", e);
            // 返回默认值
            stats.put("pendingCount", 0L);
            stats.put("approvedCount", 0L);
            stats.put("rejectedCount", 0L);
            stats.put("expiredCount", 0L);
            stats.put("withdrawnCount", 0L);
            stats.put("isSystemEnabled", false);
            stats.put("isApprovalEnabled", false);
            stats.put("isScheduleEnabled", false);
            stats.put("allowedTimeWindow", "获取失败");
            stats.put("inAllowedWindow", false);
        }
        
        return stats;
    }

    /**
     * 回撤审核
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean withdrawCorrection(Long approvalId, String reason) {
        try {
            CorrectionApproval approval = approvalMapper.selectById(approvalId);
            if (approval == null) {
                logger.warn("审核记录不存在，ID: {}", approvalId);
                return false;
            }
            
            // 只有已通过或已拒绝的记录才能回撤
            if (approval.getStatus() != 1 && approval.getStatus() != 2) {
                logger.warn("审核记录状态无效，无法回撤，ID: {}, 状态: {}", approvalId, approval.getStatus());
                return false;
            }
            
            // 如果是已通过的记录，需要撤销对题目的修改
            if (approval.getStatus() == 1) {
                List<TopicCorrectionDTO> corrections = parseCorrections(approval.getCorrectionJson());
                if (!corrections.isEmpty()) {
                    List<Integer> topicIds = corrections.stream()
                            .map(TopicCorrectionDTO::getId)
                            .collect(Collectors.toList());
                    
                    // 重置题目状态为待处理
                    resetTopicsStatus(topicIds);
                    logger.info("回撤审核，已重置 {} 条题目状态供重新处理", topicIds.size());
                    
                    // 更新TopicCorrectionSuggestion状态为PENDING
                    List<Long> topicLongIds = topicIds.stream()
                            .map(Integer::longValue)
                            .collect(Collectors.toList());
                    int updatedSuggestions = correctionSuggestionMapper.updateStatusByTopicIds(topicLongIds, "PENDING");
                    logger.info("回撤审核，已将 {} 条修正建议状态更新为PENDING", updatedSuggestions);
                }
            }
            
            // 将审核记录状态重置为待审核
            approval.setStatus(0);
            approval.setApprover(null);
            approval.setApprovalComment(reason != null && !reason.trim().isEmpty() ? "回撤原因: " + reason : "已回撤");
            approval.setApprovedAt(null);
            
            int updated = approvalMapper.updateById(approval);
            
            if (updated > 0) {
                logger.info("审核回撤成功，ID: {}, 原因: {}", approvalId, reason);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            logger.error("回撤审核失败，ID: {}", approvalId, e);
            return false;
        }
    }

    /**
     * 获取原始题目数据
     */
    public Topic getOriginalTopicById(Long topicId) {
        try {
            return topicMapper.selectById(topicId);
        } catch (Exception e) {
            logger.error("获取原始题目数据失败，ID: {}", topicId, e);
            return null;
        }
    }

    /**
     * 解析AI返回的修正建议JSON
     */
    private List<TopicCorrectionDTO> parseCorrections(String correctionResult) {
        if (correctionResult == null || correctionResult.trim().isEmpty()) {
            return new java.util.ArrayList<>();
        }
        
        String trimmed = correctionResult.trim();
        
        // 如果是空数组，返回空列表
        if ("[]".equals(trimmed)) {
            return new java.util.ArrayList<>();
        }
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            TypeReference<List<TopicCorrectionDTO>> typeRef = new TypeReference<List<TopicCorrectionDTO>>() {};
            return mapper.readValue(trimmed, typeRef);
        } catch (Exception e) {
            logger.error("解析AI修正建议失败: {}", trimmed.length() > 200 ? trimmed.substring(0, 200) + "..." : trimmed, e);
            return new java.util.ArrayList<>();
        }
    }
}