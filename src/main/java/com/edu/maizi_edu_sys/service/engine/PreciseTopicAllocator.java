package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.util.DifficultyClassifier;
import com.edu.maizi_edu_sys.util.TopicTypeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 精确题型分配器
 * 负责根据用户精确配置分配题目，确保100%满足用户需求
 */
@Component
@Slf4j
public class PreciseTopicAllocator {

    @Autowired
    private DifficultyClassifier difficultyClassifier;

    /**
     * 根据知识点配置精确分配题目
     *
     * @param availableTopics 可用题目列表
     * @param knowledgePointConfigs 知识点配置列表
     * @return 分配结果
     */
    public AllocationResult allocateTopics(List<Topic> availableTopics,
                                         List<KnowledgePointConfigRequest> knowledgePointConfigs) {

        if (availableTopics == null || availableTopics.isEmpty()) {
            return new AllocationResult(false, "没有可用题目", Collections.emptyList(), Collections.emptyList());
        }

        if (knowledgePointConfigs == null || knowledgePointConfigs.isEmpty()) {
            return new AllocationResult(false, "没有知识点配置", Collections.emptyList(), Collections.emptyList());
        }

        // 按知识点分组题目
        Map<Integer, List<Topic>> topicsByKnowledgePoint = availableTopics.stream()
            .collect(Collectors.groupingBy(Topic::getKnowId));

        List<Topic> allocatedTopics = new ArrayList<>();
        List<String> allocationMessages = new ArrayList<>();
        boolean allocationSuccess = true;

        // 为每个知识点分配题目
        for (KnowledgePointConfigRequest config : knowledgePointConfigs) {
            Integer knowledgeId = config.getKnowledgeId().intValue();
            List<Topic> kpTopics = topicsByKnowledgePoint.getOrDefault(knowledgeId, Collections.emptyList());

            AllocationResult kpResult = allocateTopicsForKnowledgePoint(config, kpTopics);

            if (kpResult.isSuccess()) {
                allocatedTopics.addAll(kpResult.getAllocatedTopics());
                allocationMessages.addAll(kpResult.getMessages());
            } else {
                allocationSuccess = false;
                allocationMessages.add(String.format("知识点%d分配失败: %s", knowledgeId,
                                                   String.join("; ", kpResult.getMessages())));
            }
        }

        return new AllocationResult(allocationSuccess,
                                  allocationSuccess ? "所有知识点分配成功" : "部分知识点分配失败",
                                  allocatedTopics, allocationMessages);
    }

    /**
     * 为单个知识点分配题目
     */
    private AllocationResult allocateTopicsForKnowledgePoint(KnowledgePointConfigRequest config,
                                                           List<Topic> availableTopics) {

        Integer knowledgeId = config.getKnowledgeId().intValue();
        List<Topic> allocatedTopics = new ArrayList<>();
        List<String> messages = new ArrayList<>();

        // 按题型分组可用题目
        Map<String, List<Topic>> topicsByType = availableTopics.stream()
            .collect(Collectors.groupingBy(topic -> TopicTypeMapper.toDbFormat(topic.getType())));

        // 检查是否有简答题配置
        if (!config.hasShortAnswerConfiguration()) {
            messages.add(String.format("知识点%d没有简答题配置", knowledgeId));
            return new AllocationResult(true, "无需分配简答题", allocatedTopics, messages);
        }

        // 只分配简答题（独立计算）
        if (config.getShortAnswerCount() > 0) {
            AllocationResult shortResult = allocateShortAnswerQuestions(knowledgeId,
                                                                       config.getShortAnswerCount(),
                                                                       topicsByType.getOrDefault("short", Collections.emptyList()),
                                                                       allocatedTopics);

            if (!shortResult.isSuccess()) {
                messages.add(String.format("知识点%d简答题分配失败: %s", knowledgeId,
                                         String.join("; ", shortResult.getMessages())));
                return new AllocationResult(false, "简答题分配失败", allocatedTopics, messages);
            }

            allocatedTopics.addAll(shortResult.getAllocatedTopics());
            messages.addAll(shortResult.getMessages());
        }

        return new AllocationResult(true, String.format("知识点%d分配成功", knowledgeId),
                                  allocatedTopics, messages);
    }



    /**
     * 分配简答题（独立计算，不与其他题目冲突）
     * 严格限制只能从指定知识点选择简答题
     */
    private AllocationResult allocateShortAnswerQuestions(Integer knowledgeId,
                                                        int requiredCount,
                                                        List<Topic> availableShortAnswers,
                                                        List<Topic> alreadyAllocated) {

        List<Topic> allocatedTopics = new ArrayList<>();
        List<String> messages = new ArrayList<>();

        if (requiredCount <= 0) {
            return new AllocationResult(true, "无需分配简答题", allocatedTopics, messages);
        }

        // 严格验证：确保所有可用简答题都属于指定知识点
        List<Topic> validShortAnswers = availableShortAnswers.stream()
            .filter(topic -> {
                boolean isCorrectKnowledge = knowledgeId.equals(topic.getKnowId());
                boolean isShortAnswer = TopicTypeMapper.DB_SHORT_ANSWER.equals(TopicTypeMapper.toDbFormat(topic.getType()));
                if (!isCorrectKnowledge) {
                    log.warn("⚠️ 发现跨知识点简答题：题目ID={}, 题目知识点={}, 期望知识点={}",
                        topic.getId(), topic.getKnowId(), knowledgeId);
                }
                return isCorrectKnowledge && isShortAnswer;
            })
            .collect(Collectors.toList());

        // 过滤掉已经分配的题目
        Set<Integer> allocatedIds = alreadyAllocated.stream()
            .map(Topic::getId)
            .collect(Collectors.toSet());

        List<Topic> availableTopics = validShortAnswers.stream()
            .filter(topic -> !allocatedIds.contains(topic.getId()))
            .collect(Collectors.toList());

        log.info("知识点{}简答题分配：需要{}道，验证后可用{}道，过滤重复后可用{}道",
            knowledgeId, requiredCount, validShortAnswers.size(), availableTopics.size());

        if (availableTopics.size() < requiredCount) {
            messages.add(String.format("❌ 知识点%d的简答题不足：需要%d道，实际可用%d道（严格限制知识点范围）",
                                     knowledgeId, requiredCount, availableTopics.size()));
            log.warn("❌ 知识点{}简答题不足，无法满足要求。建议：1)减少简答题数量 2)选择有简答题的知识点", knowledgeId);
            return new AllocationResult(false, "简答题数量不足", allocatedTopics, messages);
        }

        // 精确选择所需数量的简答题
        List<Topic> selectedTopics = selectTopicsWithDiversity(availableTopics, requiredCount);
        allocatedTopics.addAll(selectedTopics);

        // 验证选择的题目都属于正确的知识点
        boolean allFromCorrectKnowledge = selectedTopics.stream()
            .allMatch(topic -> knowledgeId.equals(topic.getKnowId()));

        if (!allFromCorrectKnowledge) {
            log.error("严重错误：选择的简答题中包含其他知识点的题目！");
            return new AllocationResult(false, "选择的简答题包含其他知识点题目", Collections.emptyList(), messages);
        }

        messages.add(String.format("知识点%d成功分配简答题%d道（严格限制知识点范围）", knowledgeId, selectedTopics.size()));
        log.info("知识点{}简答题分配成功：选择了{}道题目，题目ID：{}",
            knowledgeId, selectedTopics.size(),
            selectedTopics.stream().map(Topic::getId).collect(Collectors.toList()));

        return new AllocationResult(true, "简答题分配成功", allocatedTopics, messages);
    }

    /**
     * 带多样性的题目选择
     */
    private List<Topic> selectTopicsWithDiversity(List<Topic> availableTopics, int count) {
        if (availableTopics.size() <= count) {
            return new ArrayList<>(availableTopics);
        }

        // 按难度分组，确保多样性
        Map<String, List<Topic>> topicsByDifficulty = availableTopics.stream()
            .collect(Collectors.groupingBy(topic -> difficultyClassifier.getDifficultyName(topic.getDifficulty())));

        List<Topic> selectedTopics = new ArrayList<>();
        List<String> difficulties = Arrays.asList("easy", "medium", "hard");

        // 轮流从不同难度中选择题目
        int selectedCount = 0;
        int difficultyIndex = 0;

        while (selectedCount < count) {
            String difficulty = difficulties.get(difficultyIndex % difficulties.size());
            List<Topic> difficultyTopics = topicsByDifficulty.getOrDefault(difficulty, Collections.emptyList());

            // 从该难度中选择一个未选择的题目
            Optional<Topic> unselectedTopic = difficultyTopics.stream()
                .filter(topic -> !selectedTopics.contains(topic))
                .findFirst();

            if (unselectedTopic.isPresent()) {
                selectedTopics.add(unselectedTopic.get());
                selectedCount++;
            }

            difficultyIndex++;

            // 如果所有难度都遍历过但还没选够，随机选择剩余的
            if (difficultyIndex >= difficulties.size() * 3 && selectedCount < count) {
                List<Topic> remainingTopics = availableTopics.stream()
                    .filter(topic -> !selectedTopics.contains(topic))
                    .collect(Collectors.toList());

                Collections.shuffle(remainingTopics);
                int needed = count - selectedCount;
                selectedTopics.addAll(remainingTopics.subList(0, Math.min(needed, remainingTopics.size())));
                break;
            }
        }

        return selectedTopics;
    }

    // 删除重复的normalizeTopicType方法，使用统一的TopicTypeMapper.toDbFormat
    // 难度分类现在使用统一的DifficultyClassifier，确保与GeneticSolver保持一致

    /**
     * 分配结果类
     */
    public static class AllocationResult {
        private final boolean success;
        private final String summary;
        private final List<Topic> allocatedTopics;
        private final List<String> messages;

        public AllocationResult(boolean success, String summary, List<Topic> allocatedTopics, List<String> messages) {
            this.success = success;
            this.summary = summary;
            this.allocatedTopics = allocatedTopics != null ? allocatedTopics : Collections.emptyList();
            this.messages = messages != null ? messages : Collections.emptyList();
        }

        public boolean isSuccess() {
            return success;
        }

        public String getSummary() {
            return summary;
        }

        public List<Topic> getAllocatedTopics() {
            return allocatedTopics;
        }

        public List<String> getMessages() {
            return messages;
        }

        @Override
        public String toString() {
            return String.format("AllocationResult{success=%s, summary='%s', topics=%d, messages=%s}",
                               success, summary, allocatedTopics.size(), messages);
        }
    }
}
