package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicEnhancementData;
import com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 知识点级别的题目使用统计跟踪器
 * 
 * <p>负责跟踪和管理知识点内部题目的使用情况，包括：</p>
 * <ul>
 *   <li>知识点级别的题目使用频率统计</li>
 *   <li>知识点内部题目的最后使用时间记录</li>
 *   <li>知识点多样性指标计算</li>
 *   <li>知识点级别的重复率分析</li>
 * </ul>
 */
@Service
@Slf4j
public class KnowledgePointUsageTracker {

    @Autowired
    private TopicEnhancementDataMapper enhancementDataMapper;

    /**
     * 更新知识点级别的题目使用统计
     * 
     * @param topics 使用的题目列表
     */
    @Transactional
    public void updateKnowledgePointUsageStats(List<Topic> topics) {
        if (topics == null || topics.isEmpty()) {
            log.debug("KnowledgePointUsageTracker: No topics to update");
            return;
        }

        // 按知识点分组统计
        Map<Integer, List<Topic>> topicsByKnowledgePoint = topics.stream()
            .filter(topic -> topic.getKnowId() != null)
            .collect(Collectors.groupingBy(Topic::getKnowId));

        log.info("KnowledgePointUsageTracker: Updating usage stats for {} knowledge points", 
                topicsByKnowledgePoint.size());

        LocalDateTime now = LocalDateTime.now();

        for (Map.Entry<Integer, List<Topic>> entry : topicsByKnowledgePoint.entrySet()) {
            Integer knowledgePointId = entry.getKey();
            List<Topic> kpTopics = entry.getValue();

            log.debug("KnowledgePointUsageTracker: Updating {} topics for knowledge point {}", 
                     kpTopics.size(), knowledgePointId);

            // 更新该知识点下每个题目的使用统计
            for (Topic topic : kpTopics) {
                updateSingleTopicUsageStats(topic, now);
            }

            // 记录知识点级别的使用模式
            recordKnowledgePointUsagePattern(knowledgePointId, kpTopics, now);
        }
    }

    /**
     * 更新单个题目的使用统计
     * 
     * @param topic 题目
     * @param usageTime 使用时间
     */
    private void updateSingleTopicUsageStats(Topic topic, LocalDateTime usageTime) {
        try {
            TopicEnhancementData data = enhancementDataMapper.selectByTopicId(topic.getId());
            boolean isNew = false;

            if (data == null) {
                isNew = true;
                data = new TopicEnhancementData();
                data.setTopicId(topic.getId());
                data.setUsageCount(1);
                data.setCreateTime(usageTime);
            } else {
                data.setUsageCount(data.getUsageCount() == null ? 1 : data.getUsageCount() + 1);
            }

            data.setLastUsedTime(usageTime);

            if (isNew) {
                enhancementDataMapper.insert(data);
                log.trace("KnowledgePointUsageTracker: Created new enhancement data for topic {}", topic.getId());
            } else {
                enhancementDataMapper.updateById(data);
                log.trace("KnowledgePointUsageTracker: Updated enhancement data for topic {} (usage count: {})", 
                         topic.getId(), data.getUsageCount());
            }
        } catch (Exception e) {
            log.error("KnowledgePointUsageTracker: Failed to update usage stats for topic {}: {}", 
                     topic.getId(), e.getMessage(), e);
        }
    }

    /**
     * 记录知识点级别的使用模式
     * 
     * @param knowledgePointId 知识点ID
     * @param topics 使用的题目列表
     * @param usageTime 使用时间
     */
    private void recordKnowledgePointUsagePattern(Integer knowledgePointId, List<Topic> topics, LocalDateTime usageTime) {
        try {
            // 统计题型分布
            Map<String, Long> typeDistribution = topics.stream()
                .collect(Collectors.groupingBy(Topic::getType, Collectors.counting()));

            // 统计难度分布
            Map<String, Long> difficultyDistribution = topics.stream()
                .filter(topic -> topic.getDifficulty() != null)
                .collect(Collectors.groupingBy(
                    topic -> getDifficultyLevel(topic.getDifficulty()),
                    Collectors.counting()
                ));

            log.debug("KnowledgePointUsageTracker: Knowledge point {} usage pattern - " +
                     "Types: {}, Difficulties: {}, Total topics: {}", 
                     knowledgePointId, typeDistribution, difficultyDistribution, topics.size());

        } catch (Exception e) {
            log.error("KnowledgePointUsageTracker: Failed to record usage pattern for knowledge point {}: {}", 
                     knowledgePointId, e.getMessage(), e);
        }
    }

    /**
     * 获取难度级别描述（与后端难度定义保持一致）
     * 
     * @param difficulty 难度值
     * @return 难度级别
     */
    private String getDifficultyLevel(Double difficulty) {
        if (difficulty == null) {
            return "unknown";
        }
        // 与后端难度定义保持一致：≤0.4为简单，≤0.7为中等，≤1为难题
        if (difficulty <= 0.4) {
            return "easy";
        } else if (difficulty <= 0.7) {
            return "medium";
        } else if (difficulty <= 1.0) {
            return "hard";
        } else {
            return "very_hard";
        }
    }

    /**
     * 分析知识点的题目重复率
     * 
     * @param knowledgePointId 知识点ID
     * @param recentDays 最近天数
     * @return 重复率分析结果
     */
    public KnowledgePointDiversityAnalysis analyzeKnowledgePointDiversity(Integer knowledgePointId, int recentDays) {
        try {
            // 获取该知识点的所有题目增强数据
            // 这里需要一个新的查询方法，暂时使用简化实现
            log.info("KnowledgePointUsageTracker: Analyzing diversity for knowledge point {} over {} days", 
                    knowledgePointId, recentDays);

            // TODO: 实现具体的多样性分析逻辑
            return new KnowledgePointDiversityAnalysis(knowledgePointId, recentDays, 0.8, new HashMap<>());

        } catch (Exception e) {
            log.error("KnowledgePointUsageTracker: Failed to analyze diversity for knowledge point {}: {}", 
                     knowledgePointId, e.getMessage(), e);
            return new KnowledgePointDiversityAnalysis(knowledgePointId, recentDays, 0.0, new HashMap<>());
        }
    }

    /**
     * 知识点多样性分析结果
     */
    public static class KnowledgePointDiversityAnalysis {
        private final Integer knowledgePointId;
        private final int analysisPeriodDays;
        private final double diversityScore;
        private final Map<String, Object> details;

        public KnowledgePointDiversityAnalysis(Integer knowledgePointId, int analysisPeriodDays, 
                                             double diversityScore, Map<String, Object> details) {
            this.knowledgePointId = knowledgePointId;
            this.analysisPeriodDays = analysisPeriodDays;
            this.diversityScore = diversityScore;
            this.details = details;
        }

        public Integer getKnowledgePointId() { return knowledgePointId; }
        public int getAnalysisPeriodDays() { return analysisPeriodDays; }
        public double getDiversityScore() { return diversityScore; }
        public Map<String, Object> getDetails() { return details; }

        @Override
        public String toString() {
            return String.format("KnowledgePointDiversityAnalysis{knowledgePointId=%d, diversityScore=%.2f, period=%d days}", 
                               knowledgePointId, diversityScore, analysisPeriodDays);
        }
    }
}
