package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicEnhancementData;
import com.edu.maizi_edu_sys.util.DifficultyClassifier;
import com.edu.maizi_edu_sys.util.TopicTypeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 启发式种子生成器
 * 负责生成高质量的初始种群种子，提高遗传算法的收敛效率
 */
@Component
@Slf4j
public class GreedySeedGenerator {

    @Autowired
    private DifficultyClassifier difficultyClassifier;

    /**
     * 生成启发式种子
     *
     * @param availableQuestions 可用题目列表
     * @param typeTargetCounts 目标题型数量
     * @param targetScore 目标分数
     * @param difficultyDistribution 目标难度分布
     * @param typeScores 题型分数映射
     * @param enhancementDataMap 题目增强数据
     * @param seedCount 需要生成的种子数量
     * @return 种子染色体列表
     */
    public List<GeneticSolver.Chromosome> generateSeeds(List<Topic> availableQuestions,
                                                       Map<String, Integer> typeTargetCounts,
                                                       int targetScore,
                                                       Map<String, Double> difficultyDistribution,
                                                       Map<String, Integer> typeScores,
                                                       Map<Integer, TopicEnhancementData> enhancementDataMap,
                                                       int seedCount) {

        List<GeneticSolver.Chromosome> seeds = new ArrayList<>();

        if (availableQuestions == null || availableQuestions.isEmpty() ||
            typeTargetCounts == null || typeTargetCounts.isEmpty()) {
            return seeds;
        }

        log.debug("Generating {} greedy seeds from {} available questions", seedCount, availableQuestions.size());

        // 按题型分组题目
        Map<String, List<TopicWithIndex>> topicsByType = groupTopicsByType(availableQuestions);

        // 生成多个不同策略的种子
        for (int i = 0; i < seedCount; i++) {
            GeneticSolver.Chromosome seed = generateSingleSeed(
                availableQuestions, topicsByType, typeTargetCounts,
                targetScore, difficultyDistribution, typeScores,
                enhancementDataMap, i
            );

            if (seed != null) {
                seeds.add(seed);
            }
        }

        log.debug("Successfully generated {} greedy seeds", seeds.size());
        return seeds;
    }

    /**
     * 生成单个种子
     */
    private GeneticSolver.Chromosome generateSingleSeed(List<Topic> availableQuestions,
                                                       Map<String, List<TopicWithIndex>> topicsByType,
                                                       Map<String, Integer> typeTargetCounts,
                                                       int targetScore,
                                                       Map<String, Double> difficultyDistribution,
                                                       Map<String, Integer> typeScores,
                                                       Map<Integer, TopicEnhancementData> enhancementDataMap,
                                                       int seedIndex) {

        BitSet gene = new BitSet(availableQuestions.size());
        int currentScore = 0;
        Map<String, Integer> currentTypeCounts = new HashMap<>();
        Map<String, Integer> currentDifficultyCounts = new HashMap<>();

        // 根据种子索引选择不同的策略
        SeedStrategy strategy = SeedStrategy.values()[seedIndex % SeedStrategy.values().length];

        // 按题型逐一选择题目
        for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
            String type = entry.getKey();
            int requiredCount = entry.getValue();

            if (requiredCount <= 0) {
                continue;
            }

            List<TopicWithIndex> typeTopics = topicsByType.getOrDefault(type, new ArrayList<>());
            if (typeTopics.isEmpty()) {
                log.warn("No topics available for type: {}", type);
                continue;
            }

            // 根据策略排序题目
            List<TopicWithIndex> sortedTopics = sortTopicsByStrategy(typeTopics, strategy,
                                                                   enhancementDataMap, typeScores,
                                                                   difficultyDistribution);

            // 选择题目
            int selected = 0;
            for (TopicWithIndex topicWithIndex : sortedTopics) {
                if (selected >= requiredCount) {
                    break;
                }

                int index = topicWithIndex.getIndex();
                Topic topic = topicWithIndex.getTopic();

                // 检查是否会导致分数过高
                int topicScore = getTopicScore(topic, typeScores);
                if (currentScore + topicScore <= targetScore * 1.2) { // 允许20%超出
                    gene.set(index);
                    currentScore += topicScore;
                    currentTypeCounts.merge(type, 1, Integer::sum);

                    Double difficultyValue = topic.getDifficulty();
                    if (difficultyValue != null) {
                        String difficulty = difficultyClassifier.getDifficultyName(difficultyValue);
                        currentDifficultyCounts.merge(difficulty, 1, Integer::sum);
                    }

                    selected++;
                }
            }
        }

        // 如果分数不足，尝试添加更多题目
        if (currentScore < targetScore * 0.8) {
            adjustScoreUpward(gene, availableQuestions, topicsByType, typeScores,
                            enhancementDataMap, targetScore, currentScore);
        }

        return new GeneticSolver.Chromosome(gene);
    }

    /**
     * 按题型分组题目
     */
    private Map<String, List<TopicWithIndex>> groupTopicsByType(List<Topic> availableQuestions) {
        Map<String, List<TopicWithIndex>> topicsByType = new HashMap<>();

        for (int i = 0; i < availableQuestions.size(); i++) {
            Topic topic = availableQuestions.get(i);
            String normalizedType = TopicTypeMapper.toDbFormat(topic.getType());

            topicsByType.computeIfAbsent(normalizedType, k -> new ArrayList<>())
                       .add(new TopicWithIndex(topic, i));
        }

        return topicsByType;
    }

    /**
     * 根据策略排序题目
     */
    private List<TopicWithIndex> sortTopicsByStrategy(List<TopicWithIndex> topics,
                                                    SeedStrategy strategy,
                                                    Map<Integer, TopicEnhancementData> enhancementDataMap,
                                                    Map<String, Integer> typeScores,
                                                    Map<String, Double> difficultyDistribution) {

        List<TopicWithIndex> sorted = new ArrayList<>(topics);

        switch (strategy) {
            case QUALITY_FIRST:
                sorted.sort((t1, t2) -> Double.compare(
                    calculateTopicQuality(t2.getTopic(), enhancementDataMap),
                    calculateTopicQuality(t1.getTopic(), enhancementDataMap)
                ));
                break;

            case SCORE_BALANCED:
                sorted.sort((t1, t2) -> Integer.compare(
                    getTopicScore(t1.getTopic(), typeScores),
                    getTopicScore(t2.getTopic(), typeScores)
                ));
                break;

            case DIFFICULTY_BALANCED:
                sorted.sort((t1, t2) -> {
                    Double d1Value = t1.getTopic().getDifficulty();
                    Double d2Value = t2.getTopic().getDifficulty();
                    String d1 = d1Value != null ? difficultyClassifier.getDifficultyName(d1Value) : "medium";
                    String d2 = d2Value != null ? difficultyClassifier.getDifficultyName(d2Value) : "medium";
                    return compareDifficulty(d1, d2, difficultyDistribution);
                });
                break;

            case RANDOM:
            default:
                Collections.shuffle(sorted);
                break;
        }

        return sorted;
    }

    /**
     * 向上调整分数
     */
    private void adjustScoreUpward(BitSet gene,
                                 List<Topic> availableQuestions,
                                 Map<String, List<TopicWithIndex>> topicsByType,
                                 Map<String, Integer> typeScores,
                                 Map<Integer, TopicEnhancementData> enhancementDataMap,
                                 int targetScore,
                                 int currentScore) {

        int neededScore = targetScore - currentScore;
        List<TopicWithIndex> candidates = new ArrayList<>();

        // 收集所有未选中的题目
        for (List<TopicWithIndex> typeTopics : topicsByType.values()) {
            for (TopicWithIndex topicWithIndex : typeTopics) {
                if (!gene.get(topicWithIndex.getIndex())) {
                    candidates.add(topicWithIndex);
                }
            }
        }

        // 按分数和质量排序
        candidates.sort((t1, t2) -> {
            int score1 = getTopicScore(t1.getTopic(), typeScores);
            int score2 = getTopicScore(t2.getTopic(), typeScores);

            // 优先选择分数接近需求的题目
            int scoreDiff1 = Math.abs(score1 - neededScore);
            int scoreDiff2 = Math.abs(score2 - neededScore);

            if (scoreDiff1 != scoreDiff2) {
                return Integer.compare(scoreDiff1, scoreDiff2);
            }

            // 分数相同时选择质量更高的
            double quality1 = calculateTopicQuality(t1.getTopic(), enhancementDataMap);
            double quality2 = calculateTopicQuality(t2.getTopic(), enhancementDataMap);
            return Double.compare(quality2, quality1);
        });

        // 选择最佳候选题目
        for (TopicWithIndex candidate : candidates) {
            int topicScore = getTopicScore(candidate.getTopic(), typeScores);
            if (topicScore > 0 && topicScore <= neededScore * 1.5) {
                gene.set(candidate.getIndex());
                break;
            }
        }
    }

    /**
     * 计算题目质量
     */
    private double calculateTopicQuality(Topic topic, Map<Integer, TopicEnhancementData> enhancementDataMap) {
        if (enhancementDataMap == null) {
            return 0.5;
        }

        TopicEnhancementData data = enhancementDataMap.get(topic.getId());
        if (data == null) {
            return 0.8; // 新题目质量较高
        }

        Integer usageCount = data.getUsageCount();
        if (usageCount == null || usageCount == 0) {
            return 1.0;
        }

        return 1.0 / (1.0 + usageCount * 0.1);
    }

    /**
     * 获取题目分数
     */
    private int getTopicScore(Topic topic, Map<String, Integer> typeScores) {
        if (typeScores != null) {
            String normalizedType = TopicTypeMapper.toDbFormat(topic.getType());
            Integer typeScore = typeScores.get(normalizedType);
            if (typeScore != null) {
                return typeScore;
            }
        }
        return topic.getScore() != null ? topic.getScore() : 0;
    }

    /**
     * 比较难度
     */
    private int compareDifficulty(String d1, String d2, Map<String, Double> difficultyDistribution) {
        if (difficultyDistribution == null) {
            return 0;
        }

        double weight1 = difficultyDistribution.getOrDefault(d1, 0.0);
        double weight2 = difficultyDistribution.getOrDefault(d2, 0.0);

        return Double.compare(weight2, weight1); // 降序，优先选择权重高的难度
    }

    // 难度分类现在使用统一的DifficultyClassifier，确保与GeneticSolver和PreciseTopicAllocator保持一致



    /**
     * 种子生成策略
     */
    private enum SeedStrategy {
        QUALITY_FIRST,      // 质量优先
        SCORE_BALANCED,     // 分数平衡
        DIFFICULTY_BALANCED, // 难度平衡
        RANDOM              // 随机
    }

    /**
     * 带索引的题目
     */
    private static class TopicWithIndex {
        private final Topic topic;
        private final int index;

        public TopicWithIndex(Topic topic, int index) {
            this.topic = topic;
            this.index = index;
        }

        public Topic getTopic() {
            return topic;
        }

        public int getIndex() {
            return index;
        }
    }
}
