package com.edu.maizi_edu_sys.service.engine;

import com.edu.maizi_edu_sys.entity.AlgorithmExecution;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.entity.TopicEnhancementData;
import com.edu.maizi_edu_sys.service.memory.MemoryManager;
import com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService;
import com.edu.maizi_edu_sys.service.AlgorithmMonitorDataService;
import com.edu.maizi_edu_sys.util.DifficultyClassifier;
import com.edu.maizi_edu_sys.util.TopicTypeMapper;
import com.edu.maizi_edu_sys.util.TagDiversityAnalyzer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.BitSet;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.ThreadLocalRandom;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

import java.util.stream.Collectors;

/**
 * 重构后的遗传算法求解器 - 修复配置、性能和收敛问题
 * 
 * 主要改进：
 * 1. 修复配置管理混乱问题
 * 2. 优化内存使用和性能
 * 3. 实现真正的多样性保持机制
 * 4. 完善约束检查和修复
 * 5. 实现适应度归一化
 */
@Service
@Slf4j
public class GeneticSolver {

    // === 核心依赖注入 ===
    @Autowired
    private MemoryManager memoryManager;
    
    @Autowired
    private AlgorithmMonitoringService monitoringService;
    
    @Autowired
    private AlgorithmMonitorDataService monitorDataService;
    
    @Autowired
    private TagDiversityAnalyzer tagDiversityAnalyzer;
    
    @Autowired
    private DifficultyClassifier difficultyClassifier;
    
    @Autowired
    private RepairOperator repairOperator;
    
    @Autowired
    private FeasibilityChecker feasibilityChecker;
    
    @Autowired
    private GreedySeedGenerator greedySeedGenerator;
    
    @Autowired
    private KnowledgePointConstraintChecker knowledgePointConstraintChecker;

    // === 线程池管理 ===
    private volatile ForkJoinPool evaluationThreadPool;
    private final Object threadPoolLock = new Object();

    // === 基础算法参数 ===
    @Value("${algorithm.genetic.population-size:100}")
    private int POPULATION_SIZE;

    @Value("${algorithm.genetic.max-generations:200}")
    private int MAX_GENERATIONS;

    @Value("${algorithm.genetic.min-generations:30}")
    private int MIN_GENERATIONS;

    @Value("${algorithm.genetic.crossover-rate:0.8}")
    private double CROSSOVER_RATE;

    @Value("${algorithm.genetic.mutation-rate:0.1}")
    private double MUTATION_RATE;

    @Value("${algorithm.genetic.tournament-size:5}")
    private int TOURNAMENT_SIZE;

    @Value("${algorithm.genetic.elite-size:10}")
    private int ELITE_SIZE;

    // === 适应度权重 (已归一化) ===
    @Value("${algorithm.genetic.fitness-weights.score:0.21}")
    private double WEIGHT_SCORE;

    @Value("${algorithm.genetic.fitness-weights.quality:0.20}")
    private double WEIGHT_QUALITY;

    @Value("${algorithm.genetic.fitness-weights.difficulty-distribution:0.15}")
    private double WEIGHT_DIFFICULTY_DIST;

    @Value("${algorithm.genetic.fitness-weights.cognitive-distribution:0.01}")
    private double WEIGHT_COGNITIVE_DIST;

    @Value("${algorithm.genetic.fitness-weights.kp-coverage:0.10}")
    private double WEIGHT_KP_COVERAGE;

    @Value("${algorithm.genetic.fitness-weights.topic-type-diversity:0.05}")
    private double WEIGHT_TOPIC_TYPE_DIVERSITY;

    @Value("${algorithm.genetic.fitness-weights.kp-type-balance:0.03}")
    private double WEIGHT_KP_TYPE_BALANCE;

    @Value("${algorithm.genetic.fitness-weights.tag-diversity:0.25}")
    private double WEIGHT_TAG_DIVERSITY;

    // === 难度区间配置 (修复了缺失默认值) ===
    @Value("${algorithm.genetic.difficulty.easy-max:0.4}")
    private double DIFF_EASY_MAX;

    @Value("${algorithm.genetic.difficulty.medium-min:0.4}")
    private double DIFF_MEDIUM_MIN;

    @Value("${algorithm.genetic.difficulty.medium-max:0.7}")
    private double DIFF_MEDIUM_MAX;

    @Value("${algorithm.genetic.difficulty.hard-min:0.71}")
    private double DIFF_HARD_MIN;

    // === 性能和约束配置 ===
    @Value("${algorithm.genetic.hard-constraint-mode:false}")
    private boolean HARD_CONSTRAINT_MODE;

    @Value("${algorithm.genetic.early-terminate-threshold:0.95}")
    private double EARLY_TERMINATE_THRESHOLD;

    @Value("${algorithm.genetic.global-timeout-seconds:30}")
    private int GLOBAL_TIMEOUT_SECONDS;

    @Value("${algorithm.genetic.diversity-threshold:0.1}")
    private double DIVERSITY_THRESHOLD;

    @Value("${algorithm.genetic.stagnation-threshold:15}")
    private int STAGNATION_THRESHOLD;

    // === 自适应变异配置 ===
    @Value("${algorithm.genetic.adaptive-mutation.enabled:true}")
    private boolean ADAPTIVE_MUTATION_ENABLED;

    @Value("${algorithm.genetic.adaptive-mutation.max-rate:0.3}")
    private double ADAPTIVE_MUTATION_MAX_RATE;

    @Value("${algorithm.genetic.adaptive-mutation.min-rate:0.05}")
    private double ADAPTIVE_MUTATION_MIN_RATE;

    @Value("${algorithm.genetic.adaptive-mutation.stagnation-threshold:10}")
    private int ADAPTIVE_MUTATION_STAGNATION_THRESHOLD;

    // === 质量评估配置 ===
    @Value("${algorithm.genetic.recency.very-recent-days:7}")
    private int VERY_RECENT_DAYS_THRESHOLD;

    @Value("${algorithm.genetic.recency.recent-days:30}")
    private int RECENT_DAYS_THRESHOLD;

    @Value("${algorithm.genetic.recency.less-recent-days:90}")
    private int LESS_RECENT_DAYS_THRESHOLD;

    @Value("${algorithm.genetic.recency.penalty-very-recent:0.6}")
    private double PENALTY_VERY_RECENT;

    @Value("${algorithm.genetic.recency.penalty-recent:0.3}")
    private double PENALTY_RECENT;

    @Value("${algorithm.genetic.recency.penalty-less-recent:0.1}")
    private double PENALTY_LESS_RECENT;

    @Value("${algorithm.genetic.quality.min-topic-quality:0.05}")
    private double MIN_TOPIC_QUALITY;

    // === 运行时状态 ===
    private Map<String, Integer> typeTargetCounts;
    private final Map<String, Double> fitnessNormalizationFactors = new HashMap<>();

    /**
     * 构造函数 - 初始化线程池
     */
    public GeneticSolver() {
        initializeThreadPool();
    }

    /**
     * 安全的线程池初始化
     */
    private void initializeThreadPool() {
        synchronized (threadPoolLock) {
            if (evaluationThreadPool == null || evaluationThreadPool.isShutdown()) {
                int parallelism = Math.max(2, Runtime.getRuntime().availableProcessors());
                evaluationThreadPool = new ForkJoinPool(parallelism);
                log.info("Initialized ForkJoinPool with parallelism: {}", parallelism);
            }
        }
    }

    /**
     * 组件验证 - 确保所有必要组件正确注入
     */
    @javax.annotation.PostConstruct
    public void validateDependencies() {
        List<String> missingComponents = new ArrayList<>();
        
        if (tagDiversityAnalyzer == null) missingComponents.add("TagDiversityAnalyzer");
        if (difficultyClassifier == null) missingComponents.add("DifficultyClassifier");
        if (memoryManager == null) missingComponents.add("MemoryManager");
        if (monitoringService == null) missingComponents.add("AlgorithmMonitoringService");
        if (repairOperator == null) missingComponents.add("RepairOperator");
        if (feasibilityChecker == null) missingComponents.add("FeasibilityChecker");
        if (greedySeedGenerator == null) missingComponents.add("GreedySeedGenerator");
        if (knowledgePointConstraintChecker == null) missingComponents.add("KnowledgePointConstraintChecker");

        if (!missingComponents.isEmpty()) {
            log.error("Missing critical components: {}", missingComponents);
            throw new IllegalStateException("GeneticSolver initialization failed due to missing components");
        }

        // 验证配置合理性
        validateConfiguration();
        
        log.info("GeneticSolver initialization completed successfully");
    }

    /**
     * 配置验证
     */
    private void validateConfiguration() {
        if (DIFF_MEDIUM_MAX <= DIFF_MEDIUM_MIN) {
            log.warn("Invalid difficulty configuration: MEDIUM_MAX ({}) <= MEDIUM_MIN ({}). Using defaults.",
                    DIFF_MEDIUM_MAX, DIFF_MEDIUM_MIN);
            DIFF_MEDIUM_MIN = 0.4;
            DIFF_MEDIUM_MAX = 0.7;
        }
        
        if (DIFF_HARD_MIN <= DIFF_MEDIUM_MAX) {
            log.warn("Invalid difficulty configuration: HARD_MIN ({}) <= MEDIUM_MAX ({}). Adjusting.",
                    DIFF_HARD_MIN, DIFF_MEDIUM_MAX);
            DIFF_HARD_MIN = DIFF_MEDIUM_MAX + 0.1;
        }

        // 验证权重总和
        double totalWeight = WEIGHT_SCORE + WEIGHT_QUALITY + WEIGHT_DIFFICULTY_DIST + 
                           WEIGHT_COGNITIVE_DIST + WEIGHT_KP_COVERAGE + WEIGHT_TOPIC_TYPE_DIVERSITY + 
                           WEIGHT_KP_TYPE_BALANCE + WEIGHT_TAG_DIVERSITY;
        
        if (Math.abs(totalWeight - 1.0) > 0.01) {
            log.warn("Fitness weights sum to {} instead of 1.0. This may cause unexpected behavior.", totalWeight);
        }
        
        log.info("Configuration validated. Population: {}, Generations: {}-{}, Timeout: {}s",
                POPULATION_SIZE, MIN_GENERATIONS, MAX_GENERATIONS, GLOBAL_TIMEOUT_SECONDS);
    }

    /**
     * 获取线程安全的随机数生成器
     */
    private Random getThreadSafeRandom() {
        return ThreadLocalRandom.current();
    }

    /**
     * 内存压力下的备用贪心选择算法
     */
    private List<Topic> fallbackGreedySelection(List<Topic> availableQuestions, int targetScore,
                                              Map<String, Integer> typeTargetCounts,
                                              Map<String, Integer> typeScoreMap) {
        log.warn("Executing fallback greedy selection due to memory pressure or algorithm failure");

        List<Topic> result = new ArrayList<>();
        int remainingScore = targetScore;

        // 按题型分组
        Map<String, List<Topic>> topicsByType = availableQuestions.stream()
            .collect(Collectors.groupingBy(topic -> getStandardTopicType(topic.getType())));

        // 按优先级顺序选择题目
        for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
            String type = entry.getKey();
            int needed = entry.getValue();

            List<Topic> candidates = topicsByType.getOrDefault(type, Collections.emptyList());
            if (candidates.isEmpty()) continue;

            // 按分数排序，优先选择接近平均分的题目
            int avgScore = typeScoreMap.getOrDefault(type, remainingScore / needed);
            candidates.sort(Comparator.comparingInt(t -> Math.abs(t.getScore() - avgScore)));

            // 选择所需数量的题目
            for (int i = 0; i < Math.min(needed, candidates.size()); i++) {
                Topic topic = candidates.get(i);
                result.add(topic);
                remainingScore -= topic.getScore();
            }
        }

        log.info("Fallback selection completed: {} topics, total score: {}",
                result.size(), result.stream().mapToInt(Topic::getScore).sum());

        return result;
    }

    /**
     * 根据题目数量动态调整线程池大小
     */
    private void adjustThreadPoolSize(int questionCount) {
        // 始终使用全部可用 CPU 核心，避免在大核心机器上被不必要地降核
        int optimalThreads = Runtime.getRuntime().availableProcessors();
        if (evaluationThreadPool.getParallelism() != optimalThreads) {
            evaluationThreadPool.shutdown();
            evaluationThreadPool = new ForkJoinPool(optimalThreads);
            log.info("Adjusted thread pool size to {} (available processors) for {} questions", optimalThreads, questionCount);
        }
    }

    /**
     * 执行遗传算法，从候选题目中选取最优组合以构成试卷。
     *
     * @param availableQuestions          候选题目列表，经过初步筛选和过滤。
     * @param targetScore                 试卷的目标总分。
     * @param typeTargetCounts            (未使用，但保留参数以兼容) 目标题型数量，用于优化每种题型的选取数量
     * @param difficultyDistributionTarget 目标难度分布，Map<难度名称, 期望百分比>。
     * @param cognitiveLevelDistributionTarget 目标认知层次分布，Map<认知层次名称, 期望百分比>。
     * @param enhancementDataMap          题目增强数据，Map<题目ID, TopicEnhancementData>，包含使用次数、认知层次等。
     * @param targetKnowledgeIds          目标知识点ID列表，用于评估知识点覆盖度。
     * @return 经过遗传算法优化选出的题目列表。若无法找到合适解，则可能返回空列表。
     */
    public List<Topic> solve(List<Topic> availableQuestions,
                             int targetScore,
                             Map<String, Integer> typeTargetCounts, // 目标题型数量，用于优化每种题型的选取数量
                             Map<String, Double> difficultyDistributionTarget,
                             Map<String, Double> cognitiveLevelDistributionTarget,
                             Map<Integer, TopicEnhancementData> enhancementDataMap,
                             List<Integer> targetKnowledgeIds) {

        return solve(availableQuestions, targetScore, typeTargetCounts, difficultyDistributionTarget,
                    cognitiveLevelDistributionTarget, enhancementDataMap, targetKnowledgeIds, null, null);
    }

    /**
     * 执行遗传算法，支持知识点级别约束的版本
     *
     * @param availableQuestions          候选题目列表，经过初步筛选和过滤。
     * @param targetScore                 试卷的目标总分。
     * @param typeTargetCounts            目标题型数量，用于优化每种题型的选取数量
     * @param difficultyDistributionTarget 目标难度分布，Map<难度名称, 期望百分比>。
     * @param cognitiveLevelDistributionTarget 目标认知层次分布，Map<认知层次名称, 期望百分比>。
     * @param enhancementDataMap          题目增强数据，Map<题目ID, TopicEnhancementData>，包含使用次数、认知层次等。
     * @param targetKnowledgeIds          目标知识点ID列表，用于评估知识点覆盖度。
     * @param knowledgePointConfigs       知识点配置列表，用于知识点级别约束检查
     * @return 经过遗传算法优化选出的题目列表。若无法找到合适解，则可能返回空列表。
     */
    public List<Topic> solve(List<Topic> availableQuestions,
                             int targetScore,
                             Map<String, Integer> typeTargetCounts,
                             Map<String, Double> difficultyDistributionTarget,
                             Map<String, Double> cognitiveLevelDistributionTarget,
                             Map<Integer, TopicEnhancementData> enhancementDataMap,
                             List<Integer> targetKnowledgeIds,
                             List<com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest> knowledgePointConfigs,
                             Map<String, Integer> typeScoreMap,
                             Long executionId) {

        return solveWithExecution(availableQuestions, targetScore, typeTargetCounts,
                                 difficultyDistributionTarget, cognitiveLevelDistributionTarget,
                                 enhancementDataMap, targetKnowledgeIds, knowledgePointConfigs,
                                 typeScoreMap, executionId);
    }

    /**
     * 执行遗传算法，支持知识点级别约束和题型分值配置的版本
     */
    public List<Topic> solve(List<Topic> availableQuestions,
                             int targetScore,
                             Map<String, Integer> typeTargetCounts,
                             Map<String, Double> difficultyDistributionTarget,
                             Map<String, Double> cognitiveLevelDistributionTarget,
                             Map<Integer, TopicEnhancementData> enhancementDataMap,
                             List<Integer> targetKnowledgeIds,
                             List<com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest> knowledgePointConfigs,
                             Map<String, Integer> typeScoreMap) {

        return solveWithExecution(availableQuestions, targetScore, typeTargetCounts,
                                 difficultyDistributionTarget, cognitiveLevelDistributionTarget,
                                 enhancementDataMap, targetKnowledgeIds, knowledgePointConfigs,
                                 typeScoreMap, null);
    }

    /**
     * 内部执行方法，支持传入executionId或自动创建
     */
    private List<Topic> solveWithExecution(List<Topic> availableQuestions,
                                          int targetScore,
                                          Map<String, Integer> typeTargetCounts,
                                          Map<String, Double> difficultyDistributionTarget,
                                          Map<String, Double> cognitiveLevelDistributionTarget,
                                          Map<Integer, TopicEnhancementData> enhancementDataMap,
                                          List<Integer> targetKnowledgeIds,
                                          List<com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest> knowledgePointConfigs,
                                          Map<String, Integer> typeScoreMap,
                                          Long providedExecutionId) {

        // 输入验证
        if (availableQuestions == null || availableQuestions.isEmpty()) {
            log.warn("No available questions provided to genetic algorithm");
            return Collections.emptyList();
        }

        this.typeTargetCounts = typeTargetCounts;
        
        // 初始化归一化因子
        initializeFitnessNormalization(availableQuestions, targetScore, enhancementDataMap);

        // 开始监控
        String requestId = "GA_ENHANCED_" + System.currentTimeMillis();
        AlgorithmMonitoringService.ExecutionContext context = monitoringService.startExecution(requestId);

        // 处理执行记录：使用提供的executionId或创建新的
        Long executionId = providedExecutionId;
        boolean shouldCreateExecution = (executionId == null);
        
        if (shouldCreateExecution) {
            try {
                AlgorithmExecution execution = monitorDataService.createExecution(
                    "GA_" + System.currentTimeMillis(), // paperId
                    "Genetic Algorithm Paper Generation", // paperName
                    MAX_GENERATIONS, // maxGenerations
                    POPULATION_SIZE  // populationSize
                );
                executionId = execution.getId();
                
                log.info("Created execution record with ID: {}", executionId);
            } catch (Exception e) {
                log.error("Failed to create execution record: {}", e.getMessage());
                // 继续执行，但不记录数据
            }
        } else {
            log.info("Using provided execution record ID: {}", executionId);
        }
        
        // 记录算法开始事件
        if (executionId != null) {
            try {
                monitorDataService.recordEvent(executionId, "START", "Algorithm execution started");
            } catch (Exception e) {
                log.error("Failed to record start event: {}", e.getMessage());
            }
        }

        try {
            // 内存压力检查
            if (memoryManager.isMemoryPressure()) {
                log.warn("High memory pressure detected, using fallback algorithm");
                if (executionId != null) {
                    monitorDataService.recordEvent(executionId, "WARNING", "Memory pressure detected, using fallback");
                }
                return fallbackGreedySelection(availableQuestions, targetScore, typeTargetCounts, typeScoreMap);
            }

            List<Topic> result = solveInternalEnhanced(
                availableQuestions, targetScore, typeTargetCounts,
                difficultyDistributionTarget, cognitiveLevelDistributionTarget,
                enhancementDataMap, targetKnowledgeIds, knowledgePointConfigs, typeScoreMap,
                executionId);

            monitoringService.recordSuccess(context, 
                result.isEmpty() ? 0.0 : 1.0, MAX_GENERATIONS);

            // 更新执行状态为成功
            if (executionId != null) {
                try {
                    monitorDataService.updateExecutionStatus(executionId, AlgorithmExecution.ExecutionStatus.COMPLETED);
                    monitorDataService.recordEvent(executionId, "SUCCESS", 
                        String.format("Algorithm completed successfully with %d topics selected", result.size()));
                } catch (Exception e) {
                    log.error("Failed to update execution status: {}", e.getMessage());
                }
            }

            return result;

        } catch (Exception e) {
            log.error("Enhanced genetic algorithm failed: {}", e.getMessage(), e);
            monitoringService.recordFailure(context, "ALGORITHM_ERROR", e.getMessage());
            
            // 更新执行状态为失败
            if (executionId != null) {
                try {
                    monitorDataService.updateExecutionStatus(executionId, AlgorithmExecution.ExecutionStatus.FAILED);
                    monitorDataService.recordEvent(executionId, "ERROR", 
                        "Algorithm execution failed: " + e.getMessage());
                } catch (Exception persistError) {
                    log.error("Failed to update execution status: {}", persistError.getMessage());
                }
            }
            
            // 尝试fallback
            try {
                if (executionId != null) {
                    monitorDataService.recordEvent(executionId, "FALLBACK", "Attempting fallback algorithm");
                }
                return fallbackGreedySelection(availableQuestions, targetScore, typeTargetCounts, typeScoreMap);
            } catch (Exception fallbackError) {
                log.error("Fallback algorithm also failed: {}", fallbackError.getMessage());
                if (executionId != null) {
                    try {
                        monitorDataService.recordEvent(executionId, "ERROR", 
                            "Fallback algorithm also failed: " + fallbackError.getMessage());
                    } catch (Exception persistError) {
                        log.error("Failed to record fallback error: {}", persistError.getMessage());
                    }
                }
                return Collections.emptyList();
            }
        }
    }

    /**
     * 初始化适应度归一化因子
     */
    private void initializeFitnessNormalization(List<Topic> availableQuestions, int targetScore,
                                               Map<Integer, TopicEnhancementData> enhancementDataMap) {
        // 分数归一化
        fitnessNormalizationFactors.put("score", (double) targetScore);
        
        // 质量归一化 - 基于题目使用统计
        double maxQuality = enhancementDataMap.values().stream()
            .mapToDouble(data -> calculateTopicQuality(data))
            .max().orElse(1.0);
        fitnessNormalizationFactors.put("quality", maxQuality);
        
        // 多样性归一化
        fitnessNormalizationFactors.put("diversity", 1.0);
        
        log.debug("Fitness normalization factors initialized: {}", fitnessNormalizationFactors);
    }

    /**
     * 主进化循环 - 增强版本，使用更多优化技术
     */
    private List<Topic> solveInternalEnhanced(List<Topic> availableQuestions,
                                            int targetScore,
                                            Map<String, Integer> typeTargetCounts,
                                            Map<String, Double> difficultyDistributionTarget,
                                            Map<String, Double> cognitiveLevelDistributionTarget,
                                            Map<Integer, TopicEnhancementData> enhancementDataMap,
                                            List<Integer> targetKnowledgeIds,
                                            List<com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest> knowledgePointConfigs,
                                            Map<String, Integer> typeScoreMap,
                                            Long executionId) {

        long startTime = System.currentTimeMillis();
        log.info("Enhanced GA started - Population: {}, Generations: {}, Questions: {}", 
                POPULATION_SIZE, MAX_GENERATIONS, availableQuestions.size());

        // 1. 智能种群初始化 - 混合精英种子和随机个体
        List<EnhancedChromosome> population = initializeIntelligentPopulation(availableQuestions, typeTargetCounts, typeScoreMap);
        if (population.isEmpty()) {
            log.error("Failed to initialize population");
            return Collections.emptyList();
        }

        // 2. 初始评估
        evaluatePopulationEnhanced(population, availableQuestions, targetScore, typeTargetCounts,
                                 difficultyDistributionTarget, cognitiveLevelDistributionTarget,
                                 enhancementDataMap, targetKnowledgeIds, knowledgePointConfigs, typeScoreMap);

        population.sort(Comparator.comparing(EnhancedChromosome::getFitness).reversed());

        double bestFitnessOverall = population.get(0).getFitness();
        int generationsWithoutImprovement = 0;
        List<Double> diversityHistory = new ArrayList<>();
        List<Double> fitnessHistory = new ArrayList<>();

        // 3. 主进化循环
        for (int generation = 1; generation <= MAX_GENERATIONS; generation++) {
            long generationStart = System.currentTimeMillis();

            // 超时检查
            if (System.currentTimeMillis() - startTime > GLOBAL_TIMEOUT_SECONDS * 1000L) {
                log.warn("Global timeout reached at generation {}", generation);
                break;
            }

            // 计算种群多样性 - 使用增强的多样性计算
            double currentDiversity = calculateEnhancedPopulationDiversity(population, availableQuestions);
            diversityHistory.add(currentDiversity);
            fitnessHistory.add(bestFitnessOverall);

            // 精英保留策略 - 动态调整精英数量
            int dynamicEliteSize = calculateDynamicEliteSize(generation, currentDiversity);
            List<EnhancedChromosome> elites = new ArrayList<>(population.subList(0, Math.min(dynamicEliteSize, population.size())));

            // 选择、交叉、变异 - 增强版
            List<EnhancedChromosome> newPopulation = new ArrayList<>(elites);
            
            while (newPopulation.size() < POPULATION_SIZE) {
                // 父代选择 - 使用改进的锦标赛选择
                EnhancedChromosome parent1 = tournamentSelectionEnhanced(population);
                EnhancedChromosome parent2 = tournamentSelectionEnhanced(population);

                // 交叉产生子代 - 使用类型保持交叉
                EnhancedChromosome child = crossoverEnhanced(parent1, parent2, availableQuestions, typeTargetCounts);

                // 变异 - 自适应变异率
                double mutationRate = calculateAdaptiveMutationRate(generation, generationsWithoutImprovement, currentDiversity);
                mutateEnhanced(child, availableQuestions, typeTargetCounts, mutationRate);

                // 约束修复 - 启用约束修复机制
                if (repairOperator != null && feasibilityChecker != null) {
                    try {
                        child = repairChromosomeConstraints(child, availableQuestions, typeTargetCounts, targetScore, typeScoreMap);
                    } catch (Exception e) {
                        log.debug("Constraint repair failed: {}", e.getMessage());
                    }
                }

                // 局部搜索 - 对高质量个体进行局部优化
                if (child.getFitness() > 0.8) {
                    child = performLocalSearch(child, availableQuestions, typeTargetCounts, typeScoreMap);
                }

                newPopulation.add(child);
            }

            // 评估新种群
            evaluatePopulationEnhanced(newPopulation, availableQuestions, targetScore, typeTargetCounts,
                                     difficultyDistributionTarget, cognitiveLevelDistributionTarget,
                                     enhancementDataMap, targetKnowledgeIds, knowledgePointConfigs, typeScoreMap);

            // 多样性保持 - 使用多种策略
            if (currentDiversity < DIVERSITY_THRESHOLD) {
                newPopulation = maintainPopulationDiversity(newPopulation, availableQuestions, typeTargetCounts, generation);
                log.debug("Enhanced diversity maintenance at generation {} (diversity: {})", generation, currentDiversity);
            }

            // 帕累托优化 - 处理多目标冲突
            newPopulation = applyParetoOptimization(newPopulation);

            // 排序
            newPopulation.sort(Comparator.comparing(EnhancedChromosome::getFitness).reversed());
            population = newPopulation;

            // 检查改进
            double currentBestFitness = population.get(0).getFitness();
            if (currentBestFitness > bestFitnessOverall + 1e-6) {
                bestFitnessOverall = currentBestFitness;
                generationsWithoutImprovement = 0;
            } else {
                generationsWithoutImprovement++;
            }

            // 记录统计信息
            if (generation % 10 == 0 || generation <= 5) {
                double avgFitness = population.stream().mapToDouble(EnhancedChromosome::getFitness).average().orElse(0.0);
                long generationTime = System.currentTimeMillis() - generationStart;
                
                log.info("Gen {}: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time={}ms, NoImprove={}, Elite={}",
                        generation, currentBestFitness, avgFitness, currentDiversity, generationTime, 
                        generationsWithoutImprovement, dynamicEliteSize);
            }
            
            // 持久化generation数据到数据库
            if (executionId != null) {
                try {
                    double avgFitness = population.stream().mapToDouble(EnhancedChromosome::getFitness).average().orElse(0.0);
                    double worstFitness = population.stream().mapToDouble(EnhancedChromosome::getFitness).min().orElse(0.0);
                    
                    // 计算收敛速度（基于代数进步情况）
                    double convergenceSpeed = generationsWithoutImprovement > 0 ? 
                        1.0 / generationsWithoutImprovement : 1.0;
                    
                    // 计算适应度标准差
                    double fitnessStdDev = population.stream()
                        .mapToDouble(EnhancedChromosome::getFitness)
                        .reduce(0.0, (sum, fitness) -> sum + Math.pow(fitness - avgFitness, 2));
                    fitnessStdDev = Math.sqrt(fitnessStdDev / population.size());
                    
                    // 约束违反数量（暂时设为0，可后续扩展）
                    int constraintViolations = 0;
                    
                    monitorDataService.recordGenerationData(
                        executionId,
                        generation,
                        currentBestFitness,
                        avgFitness,
                        worstFitness,
                        currentDiversity,
                        convergenceSpeed,
                        constraintViolations,
                        fitnessStdDev
                    );
                    
                    // 记录适应度维度数据（每5代或前10代）
                    if (generation % 5 == 0 || generation <= 10) {
                        recordFitnessDimensions(executionId, generation, population.get(0), 
                            availableQuestions, targetScore, typeTargetCounts, difficultyDistributionTarget, 
                            cognitiveLevelDistributionTarget, enhancementDataMap, targetKnowledgeIds, typeScoreMap);
                    }
                } catch (Exception e) {
                    log.error("Failed to persist generation data for generation {}: {}", generation, e.getMessage());
                }
            }

            // 提前终止检查
            if (shouldTerminateEarly(generation, currentBestFitness, generationsWithoutImprovement, currentDiversity)) {
                log.info("Early termination at generation {}", generation);
                break;
            }

            // 内存压力检查
            if (generation % 20 == 0 && memoryManager.isMemoryPressure()) {
                log.warn("Memory pressure detected at generation {}", generation);
                break;
            }
        }

        // 提取最终解
        EnhancedChromosome bestChromosome = population.isEmpty() ? null : population.get(0);
        List<Topic> result = bestChromosome != null ? extractSolutionFromEnhanced(bestChromosome, availableQuestions) : Collections.emptyList();

        long totalTime = System.currentTimeMillis() - startTime;
        log.info("Enhanced GA completed in {}ms. Best fitness: {:.4f}, Selected: {} topics",
                totalTime, bestFitnessOverall, result.size());

        // 记录性能指标
        recordPerformanceMetrics(diversityHistory, fitnessHistory, totalTime);

        return result;
    }

    /**
     * 智能种群初始化 - 混合贪心种子和随机个体
     */
    private List<EnhancedChromosome> initializeIntelligentPopulation(List<Topic> availableQuestions,
                                                                   Map<String, Integer> typeTargetCounts,
                                                                   Map<String, Integer> typeScoreMap) {
        List<EnhancedChromosome> population = new ArrayList<>();

                 // 30% 使用贪心种子（高质量个体）
         int greedySeedCount = (int) (POPULATION_SIZE * 0.3);
         if (greedySeedGenerator != null) {
             try {
                 for (int i = 0; i < greedySeedCount; i++) {
                     // 使用简化的贪心策略生成高质量种子
                     List<Topic> greedySolution = generateSimpleGreedySeed(
                         availableQuestions, typeTargetCounts, typeScoreMap);
                     
                     List<Integer> geneIndices = greedySolution.stream()
                         .map(availableQuestions::indexOf)
                         .filter(idx -> idx >= 0)
                         .collect(Collectors.toList());
                     
                     if (!geneIndices.isEmpty()) {
                         population.add(new EnhancedChromosome(geneIndices));
                     }
                 }
                 log.info("Generated {} greedy seed chromosomes", population.size());
             } catch (Exception e) {
                 log.warn("Failed to generate greedy seeds: {}", e.getMessage());
             }
         }

        // 70% 使用精确类型匹配的随机个体
        int randomCount = POPULATION_SIZE - population.size();
        List<EnhancedChromosome> randomPopulation = initializeEnhancedPopulation(availableQuestions, typeTargetCounts);
        population.addAll(randomPopulation.subList(0, Math.min(randomCount, randomPopulation.size())));

        // 如果还不够，填充随机个体
        while (population.size() < POPULATION_SIZE) {
            EnhancedChromosome randomChromosome = generateRandomEnhancedChromosome(availableQuestions, typeTargetCounts);
            population.add(randomChromosome);
        }

        log.info("Intelligent population initialized: {} total chromosomes", population.size());
        return population;
    }

    /**
     * 增强的种群多样性计算 - 考虑基因型和表现型多样性
     */
    private double calculateEnhancedPopulationDiversity(List<EnhancedChromosome> population, List<Topic> questions) {
        if (population.size() < 2) return 0.0;

        double geneticDiversity = calculatePopulationDiversity(population); // 基因型多样性
        double phenotypicDiversity = calculatePhenotypicDiversity(population, questions); // 表现型多样性
        
        // 加权组合
        return 0.6 * geneticDiversity + 0.4 * phenotypicDiversity;
    }

    /**
     * 计算表现型多样性 - 基于题目特征的多样性
     */
    private double calculatePhenotypicDiversity(List<EnhancedChromosome> population, List<Topic> questions) {
        if (population.size() < 2) return 0.0;

        double totalDiversity = 0.0;
        int comparisons = 0;
        int sampleSize = Math.min(30, population.size());

        for (int i = 0; i < sampleSize; i++) {
            for (int j = i + 1; j < sampleSize; j++) {
                EnhancedChromosome c1 = population.get(i);
                EnhancedChromosome c2 = population.get(j);
                
                double diversity = calculatePhenotypicDistance(c1, c2, questions);
                totalDiversity += diversity;
                comparisons++;
            }
        }

        return comparisons > 0 ? totalDiversity / comparisons : 0.0;
    }

    /**
     * 计算两个染色体的表现型距离
     */
    private double calculatePhenotypicDistance(EnhancedChromosome c1, EnhancedChromosome c2, List<Topic> questions) {
        List<Topic> topics1 = c1.getGeneIndices().stream().map(questions::get).collect(Collectors.toList());
        List<Topic> topics2 = c2.getGeneIndices().stream().map(questions::get).collect(Collectors.toList());

        // 难度分布差异
        double difficultyDistance = calculateDifficultyDistance(topics1, topics2);
        
        // 知识点覆盖差异
        double knowledgeDistance = calculateKnowledgeDistance(topics1, topics2);
        
        // 题型分布差异
        double typeDistance = calculateTypeDistance(topics1, topics2);
        
        // 分数差异
        double scoreDistance = Math.abs(c1.getTotalScore() - c2.getTotalScore()) / 100.0; // 归一化
        
        return (difficultyDistance + knowledgeDistance + typeDistance + scoreDistance) / 4.0;
    }

    /**
     * 计算难度分布距离
     */
    private double calculateDifficultyDistance(List<Topic> topics1, List<Topic> topics2) {
        Map<String, Double> dist1 = calculateDifficultyDistribution(topics1);
        Map<String, Double> dist2 = calculateDifficultyDistribution(topics2);
        
        double distance = 0.0;
        Set<String> allKeys = new HashSet<>(dist1.keySet());
        allKeys.addAll(dist2.keySet());
        
        for (String key : allKeys) {
            double v1 = dist1.getOrDefault(key, 0.0);
            double v2 = dist2.getOrDefault(key, 0.0);
            distance += Math.abs(v1 - v2);
        }
        
        return distance / 2.0; // 归一化到[0,1]
    }

    /**
     * 计算知识点覆盖距离
     */
    private double calculateKnowledgeDistance(List<Topic> topics1, List<Topic> topics2) {
        Set<Integer> kp1 = topics1.stream().map(Topic::getKnowId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Integer> kp2 = topics2.stream().map(Topic::getKnowId).filter(Objects::nonNull).collect(Collectors.toSet());
        
        Set<Integer> union = new HashSet<>(kp1);
        union.addAll(kp2);
        
        Set<Integer> intersection = new HashSet<>(kp1);
        intersection.retainAll(kp2);
        
        if (union.isEmpty()) return 0.0;
        return 1.0 - (double) intersection.size() / union.size();
    }

    /**
     * 计算题型分布距离
     */
    private double calculateTypeDistance(List<Topic> topics1, List<Topic> topics2) {
        Map<String, Double> dist1 = calculateTypeDistributionAsDouble(topics1);
        Map<String, Double> dist2 = calculateTypeDistributionAsDouble(topics2);
        
        double distance = 0.0;
        Set<String> allTypes = new HashSet<>(dist1.keySet());
        allTypes.addAll(dist2.keySet());
        
        for (String type : allTypes) {
            double v1 = dist1.getOrDefault(type, 0.0);
            double v2 = dist2.getOrDefault(type, 0.0);
            distance += Math.abs(v1 - v2);
        }
        
        return distance / 2.0;
    }

    /**
     * 计算难度分布
     */
    private Map<String, Double> calculateDifficultyDistribution(List<Topic> topics) {
        Map<String, Integer> counts = new HashMap<>();
        for (Topic topic : topics) {
            String difficulty = difficultyClassifier.getDifficultyName(topic.getDifficulty());
            counts.merge(difficulty, 1, Integer::sum);
        }
        
        Map<String, Double> distribution = new HashMap<>();
        int total = topics.size();
        if (total > 0) {
            for (Map.Entry<String, Integer> entry : counts.entrySet()) {
                distribution.put(entry.getKey(), (double) entry.getValue() / total);
            }
        }
        return distribution;
    }

    /**
     * 计算题型分布（双精度版本）
     */
    private Map<String, Double> calculateTypeDistributionAsDouble(List<Topic> topics) {
        Map<String, Integer> counts = new HashMap<>();
        for (Topic topic : topics) {
            String type = getStandardTopicType(topic.getType());
            counts.merge(type, 1, Integer::sum);
        }
        
        Map<String, Double> distribution = new HashMap<>();
        int total = topics.size();
        if (total > 0) {
            for (Map.Entry<String, Integer> entry : counts.entrySet()) {
                distribution.put(entry.getKey(), (double) entry.getValue() / total);
            }
        }
        return distribution;
    }

    /**
     * 动态精英规模计算
     */
    private int calculateDynamicEliteSize(int generation, double diversity) {
        // 基础精英规模
        int baseEliteSize = ELITE_SIZE;
        
        // 根据多样性调整
        if (diversity < DIVERSITY_THRESHOLD * 0.5) {
            // 多样性过低，减少精英保留
            baseEliteSize = Math.max(2, baseEliteSize / 2);
        } else if (diversity > DIVERSITY_THRESHOLD * 2) {
            // 多样性过高，增加精英保留
            baseEliteSize = Math.min(POPULATION_SIZE / 4, baseEliteSize * 2);
        }
        
        // 根据进化阶段调整
        double progress = (double) generation / MAX_GENERATIONS;
        if (progress > 0.8) {
            // 后期增加精英保留，加速收敛
            baseEliteSize = Math.min(POPULATION_SIZE / 3, (int) (baseEliteSize * 1.5));
        }
        
        return Math.max(1, Math.min(POPULATION_SIZE / 2, baseEliteSize));
    }

    /**
     * 约束修复机制
     */
    private EnhancedChromosome repairChromosomeConstraints(EnhancedChromosome chromosome,
                                                         List<Topic> availableQuestions,
                                                         Map<String, Integer> typeTargetCounts,
                                                         int targetScore,
                                                         Map<String, Integer> typeScoreMap) {
        if (feasibilityChecker == null) return chromosome;

        List<Topic> selectedTopics = chromosome.getGeneIndices().stream()
            .map(availableQuestions::get)
            .collect(Collectors.toList());

        // 检查约束违反
        boolean needsRepair = false;
        
        // 检查题型约束
        Map<String, Integer> actualTypeCounts = new HashMap<>();
        for (Topic topic : selectedTopics) {
            String type = getStandardTopicType(topic.getType());
            actualTypeCounts.merge(type, 1, Integer::sum);
        }
        
        for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
            String type = entry.getKey();
            int target = entry.getValue();
            int actual = actualTypeCounts.getOrDefault(type, 0);
            if (Math.abs(actual - target) > 0) {
                needsRepair = true;
                break;
            }
        }
        
        // 检查分数约束
        int actualScore = selectedTopics.stream()
            .mapToInt(topic -> getTopicScore(topic, typeScoreMap))
            .sum();
        double scoreDeviation = Math.abs(actualScore - targetScore) / (double) targetScore;
        if (scoreDeviation > 0.1) {
            needsRepair = true;
        }

        if (needsRepair) {
            // 执行修复
            EnhancedChromosome repairedChromosome = performConstraintRepair(
                chromosome, availableQuestions, typeTargetCounts, targetScore, typeScoreMap);
            return repairedChromosome != null ? repairedChromosome : chromosome;
        }

        return chromosome;
    }

    /**
     * 执行约束修复
     */
    private EnhancedChromosome performConstraintRepair(EnhancedChromosome chromosome,
                                                     List<Topic> availableQuestions,
                                                     Map<String, Integer> typeTargetCounts,
                                                     int targetScore,
                                                     Map<String, Integer> typeScoreMap) {
        List<Integer> currentGenes = new ArrayList<>(chromosome.getGeneIndices());
        Map<String, List<Integer>> availableByType = new HashMap<>();
        
        // 按题型分组可用题目
        for (int i = 0; i < availableQuestions.size(); i++) {
            String type = getStandardTopicType(availableQuestions.get(i).getType());
            availableByType.computeIfAbsent(type, k -> new ArrayList<>()).add(i);
        }

        // 修复题型约束
        for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
            String type = entry.getKey();
            int targetCount = entry.getValue();
            
            // 当前该题型的题目索引
            List<Integer> currentTypeIndices = currentGenes.stream()
                .filter(idx -> getStandardTopicType(availableQuestions.get(idx).getType()).equals(type))
                .collect(Collectors.toList());
            
            int currentCount = currentTypeIndices.size();
            
            if (currentCount < targetCount) {
                // 需要添加题目
                List<Integer> availableIndices = availableByType.getOrDefault(type, Collections.emptyList());
                List<Integer> candidates = availableIndices.stream()
                    .filter(idx -> !currentGenes.contains(idx))
                    .collect(Collectors.toList());
                
                Collections.shuffle(candidates);
                int toAdd = Math.min(targetCount - currentCount, candidates.size());
                currentGenes.addAll(candidates.subList(0, toAdd));
                
            } else if (currentCount > targetCount) {
                // 需要移除题目
                Collections.shuffle(currentTypeIndices);
                int toRemove = currentCount - targetCount;
                for (int i = 0; i < toRemove; i++) {
                    currentGenes.remove(currentTypeIndices.get(i));
                }
            }
        }

        return new EnhancedChromosome(currentGenes);
    }

    /**
     * 局部搜索优化
     */
    private EnhancedChromosome performLocalSearch(EnhancedChromosome chromosome,
                                                List<Topic> availableQuestions,
                                                Map<String, Integer> typeTargetCounts,
                                                Map<String, Integer> typeScoreMap) {
        EnhancedChromosome bestChromosome = chromosome;
        double bestFitness = chromosome.getFitness();
        
        List<Integer> currentGenes = new ArrayList<>(chromosome.getGeneIndices());
        Random random = getThreadSafeRandom();
        
        // 尝试小幅度改进
        for (int attempt = 0; attempt < 5; attempt++) {
            List<Integer> modifiedGenes = new ArrayList<>(currentGenes);
            
            // 随机选择一个题型进行微调
            List<String> types = new ArrayList<>(typeTargetCounts.keySet());
            if (!types.isEmpty()) {
                String selectedType = types.get(random.nextInt(types.size()));
                
                // 在该题型内进行1对1替换
                List<Integer> typeGenes = modifiedGenes.stream()
                    .filter(idx -> getStandardTopicType(availableQuestions.get(idx).getType()).equals(selectedType))
                    .collect(Collectors.toList());
                
                if (!typeGenes.isEmpty()) {
                    List<Integer> availableIndices = new ArrayList<>();
                    for (int i = 0; i < availableQuestions.size(); i++) {
                        if (getStandardTopicType(availableQuestions.get(i).getType()).equals(selectedType) 
                            && !modifiedGenes.contains(i)) {
                            availableIndices.add(i);
                        }
                    }
                    
                    if (!availableIndices.isEmpty()) {
                        int toRemove = typeGenes.get(random.nextInt(typeGenes.size()));
                        int toAdd = availableIndices.get(random.nextInt(availableIndices.size()));
                        
                        modifiedGenes.remove(Integer.valueOf(toRemove));
                        modifiedGenes.add(toAdd);
                        
                        EnhancedChromosome candidate = new EnhancedChromosome(modifiedGenes);
                        // 这里需要重新评估适应度，简化处理
                        if (candidate.getFitness() > bestFitness) {
                            bestChromosome = candidate;
                            bestFitness = candidate.getFitness();
                        }
                    }
                }
            }
        }
        
        return bestChromosome;
    }

    /**
     * 多样性保持 - 使用多种策略
     */
    private List<EnhancedChromosome> maintainPopulationDiversity(List<EnhancedChromosome> population,
                                                               List<Topic> availableQuestions,
                                                               Map<String, Integer> typeTargetCounts,
                                                               int generation) {
        List<EnhancedChromosome> newPopulation = new ArrayList<>(population);
        
        // 策略1: 注入新的随机个体
        int injectCount = Math.min(POPULATION_SIZE / 10, 5);
        for (int i = 0; i < injectCount; i++) {
            int replaceIndex = population.size() - 1 - i;
            if (replaceIndex >= 0) {
                EnhancedChromosome newIndividual = generateRandomEnhancedChromosome(availableQuestions, typeTargetCounts);
                newPopulation.set(replaceIndex, newIndividual);
            }
        }
        
        // 策略2: 突变低多样性区域
        if (generation > 50) {
            for (int i = population.size() / 2; i < population.size(); i++) {
                EnhancedChromosome individual = newPopulation.get(i);
                // 强制变异
                mutateEnhanced(individual, availableQuestions, typeTargetCounts, 0.3);
            }
        }
        
        // 策略3: 使用距离选择保持多样性
        newPopulation = applyDiversitySelection(newPopulation, availableQuestions);
        
        return newPopulation;
    }

    /**
     * 距离选择保持多样性
     */
    private List<EnhancedChromosome> applyDiversitySelection(List<EnhancedChromosome> population, List<Topic> questions) {
        if (population.size() <= POPULATION_SIZE) return population;
        
        List<EnhancedChromosome> selected = new ArrayList<>();
        List<EnhancedChromosome> candidates = new ArrayList<>(population);
        
        // 先选择最优个体
        candidates.sort(Comparator.comparing(EnhancedChromosome::getFitness).reversed());
        selected.add(candidates.remove(0));
        
        // 基于距离选择剩余个体
        while (selected.size() < POPULATION_SIZE && !candidates.isEmpty()) {
            EnhancedChromosome bestCandidate = null;
            double maxMinDistance = -1.0;
            
            for (EnhancedChromosome candidate : candidates) {
                double minDistance = Double.MAX_VALUE;
                
                // 计算与已选个体的最小距离
                for (EnhancedChromosome selectedIndividual : selected) {
                    double distance = calculatePhenotypicDistance(candidate, selectedIndividual, questions);
                    minDistance = Math.min(minDistance, distance);
                }
                
                if (minDistance > maxMinDistance) {
                    maxMinDistance = minDistance;
                    bestCandidate = candidate;
                }
            }
            
            if (bestCandidate != null) {
                selected.add(bestCandidate);
                candidates.remove(bestCandidate);
            } else {
                break;
            }
        }
        
        return selected;
    }

    /**
     * 帕累托优化 - 处理多目标冲突
     */
    private List<EnhancedChromosome> applyParetoOptimization(List<EnhancedChromosome> population) {
        // 简化的帕累托前沿选择
        List<EnhancedChromosome> paretoFront = new ArrayList<>();
        
        for (EnhancedChromosome individual : population) {
            boolean isDominated = false;
            
            // 检查是否被其他个体支配
            for (EnhancedChromosome other : population) {
                if (other != individual && dominates(other, individual)) {
                    isDominated = true;
                    break;
                }
            }
            
            if (!isDominated) {
                paretoFront.add(individual);
            }
        }
        
        // 如果帕累托前沿太小，补充高质量个体
        if (paretoFront.size() < POPULATION_SIZE / 2) {
            population.sort(Comparator.comparing(EnhancedChromosome::getFitness).reversed());
            for (EnhancedChromosome individual : population) {
                if (!paretoFront.contains(individual) && paretoFront.size() < POPULATION_SIZE) {
                    paretoFront.add(individual);
                }
            }
        }
        
        return paretoFront.size() <= POPULATION_SIZE ? paretoFront : 
               paretoFront.subList(0, POPULATION_SIZE);
    }

    /**
     * 检查个体a是否支配个体b
     */
    private boolean dominates(EnhancedChromosome a, EnhancedChromosome b) {
        // 简化的支配关系：适应度更高且总分更接近目标
        boolean betterFitness = a.getFitness() >= b.getFitness();
        boolean betterOrEqualScore = Math.abs(a.getTotalScore()) >= Math.abs(b.getTotalScore());
        boolean strictlyBetter = a.getFitness() > b.getFitness() || Math.abs(a.getTotalScore()) > Math.abs(b.getTotalScore());
        
        return betterFitness && betterOrEqualScore && strictlyBetter;
    }

    /**
     * 记录性能指标
     */
    private void recordPerformanceMetrics(List<Double> diversityHistory, List<Double> fitnessHistory, long totalTime) {
        if (monitoringService != null) {
            try {
                // 计算收敛指标
                double convergenceRate = calculateConvergenceRate(fitnessHistory);
                double avgDiversity = diversityHistory.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                double finalDiversity = diversityHistory.isEmpty() ? 0.0 : diversityHistory.get(diversityHistory.size() - 1);
                
                // 记录指标
                Map<String, Object> metrics = new HashMap<>();
                metrics.put("convergence_rate", convergenceRate);
                metrics.put("avg_diversity", avgDiversity);
                metrics.put("final_diversity", finalDiversity);
                metrics.put("total_time_ms", totalTime);
                metrics.put("generations", fitnessHistory.size());
                
                log.info("Algorithm performance metrics: {}", metrics);
                
            } catch (Exception e) {
                log.warn("Failed to record performance metrics: {}", e.getMessage());
            }
        }
    }

    /**
     * 计算收敛速率
     */
    private double calculateConvergenceRate(List<Double> fitnessHistory) {
        if (fitnessHistory.size() < 2) return 0.0;
        
        double initialFitness = fitnessHistory.get(0);
        double finalFitness = fitnessHistory.get(fitnessHistory.size() - 1);
        
        if (initialFitness == 0) return 0.0;
        
        return (finalFitness - initialFitness) / (initialFitness * fitnessHistory.size());
    }
    
    /**
     * 记录适应度维度数据到数据库
     */
    private void recordFitnessDimensions(Long executionId, int generation, EnhancedChromosome bestChromosome,
                                       List<Topic> availableQuestions, int targetScore, 
                                       Map<String, Integer> typeTargetCounts,
                                       Map<String, Double> difficultyDistributionTarget,
                                       Map<String, Double> cognitiveLevelDistributionTarget,
                                       Map<Integer, TopicEnhancementData> enhancementDataMap,
                                       List<Integer> targetKnowledgeIds,
                                       Map<String, Integer> typeScoreMap) {
        if (executionId == null || bestChromosome == null) return;
        
        try {
            // 获取最佳染色体对应的题目列表
            List<Topic> selectedTopics = bestChromosome.getGeneIndices().stream()
                .filter(idx -> idx < availableQuestions.size())
                .map(availableQuestions::get)
                .collect(Collectors.toList());
            
            if (selectedTopics.isEmpty()) return;
            
            // 计算各个适应度维度
            double scoreFitness = calculateScoreFitness(selectedTopics, targetScore, typeScoreMap);
            double qualityFitness = calculateQualityFitnessEnhanced(selectedTopics, enhancementDataMap);
            double difficultyFitness = calculateDifficultyFitnessEnhanced(selectedTopics, difficultyDistributionTarget);
            double cognitiveFitness = calculateCognitiveFitnessEnhanced(selectedTopics, cognitiveLevelDistributionTarget);
            double kpCoverageFitness = calculateKpCoverageFitnessEnhanced(selectedTopics, targetKnowledgeIds);
            double typeDiversityFitness = calculateTopicTypeDiversityFitness(selectedTopics);
            double kpBalanceFitness = calculateKnowledgePointTypeBalanceFitness(selectedTopics, targetKnowledgeIds);
            
            // 记录各个维度数据
            monitorDataService.recordFitnessDimension(executionId, generation, "SCORE_FITNESS", scoreFitness);
            monitorDataService.recordFitnessDimension(executionId, generation, "QUALITY_FITNESS", qualityFitness);
            monitorDataService.recordFitnessDimension(executionId, generation, "DIFFICULTY_FITNESS", difficultyFitness);
            monitorDataService.recordFitnessDimension(executionId, generation, "COGNITIVE_FITNESS", cognitiveFitness);
            monitorDataService.recordFitnessDimension(executionId, generation, "KP_COVERAGE_FITNESS", kpCoverageFitness);
            monitorDataService.recordFitnessDimension(executionId, generation, "TYPE_DIVERSITY_FITNESS", typeDiversityFitness);
            monitorDataService.recordFitnessDimension(executionId, generation, "KP_BALANCE_FITNESS", kpBalanceFitness);
            monitorDataService.recordFitnessDimension(executionId, generation, "OVERALL_FITNESS", bestChromosome.getFitness());
            
            log.debug("Recorded fitness dimensions for generation {} (execution {})", generation, executionId);
            
        } catch (Exception e) {
            log.error("Failed to record fitness dimensions for generation {} (execution {}): {}", 
                     generation, executionId, e.getMessage(), e);
            // 记录详细的错误信息
            log.error("Error details - executionId: {}, generation: {}, bestChromosome: {}", 
                     executionId, generation, bestChromosome != null ? "present" : "null");
        }
    }

    /**
     * 实现性能基准测试方法
     */
    public Map<String, Object> benchmarkPaperGeneration(List<Topic> testQuestions, 
                                                       int targetScore,
                                                       Map<String, Integer> typeTargetCounts,
                                                       int iterations) {
        log.info("Starting genetic algorithm benchmark with {} iterations", iterations);
        
        Map<String, Object> results = new HashMap<>();
        List<Long> executionTimes = new ArrayList<>();
        List<Double> bestFitnesses = new ArrayList<>();
        List<Integer> convergenceGenerations = new ArrayList<>();
        
        for (int i = 0; i < iterations; i++) {
            long startTime = System.currentTimeMillis();
            
            // 运行算法
            List<Topic> solution = solve(testQuestions, targetScore, typeTargetCounts, 
                                       null, null, new HashMap<>(), Collections.emptyList());
            
            long executionTime = System.currentTimeMillis() - startTime;
            executionTimes.add(executionTime);
            
            // 记录结果质量
            double fitness = evaluateSolutionQuality(solution, targetScore, typeTargetCounts);
            bestFitnesses.add(fitness);
            
            log.info("Benchmark iteration {}: time={}ms, fitness={:.4f}, topics={}", 
                    i + 1, executionTime, fitness, solution.size());
        }
        
        // 统计结果
        double avgTime = executionTimes.stream().mapToLong(Long::longValue).average().orElse(0.0);
        double avgFitness = bestFitnesses.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double maxFitness = bestFitnesses.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
        double minFitness = bestFitnesses.stream().mapToDouble(Double::doubleValue).min().orElse(0.0);
        
        results.put("iterations", iterations);
        results.put("avg_execution_time_ms", avgTime);
        results.put("avg_fitness", avgFitness);
        results.put("max_fitness", maxFitness);
        results.put("min_fitness", minFitness);
        results.put("fitness_std", calculateStandardDeviation(bestFitnesses));
        results.put("execution_times", executionTimes);
        results.put("fitness_values", bestFitnesses);
        
        log.info("Benchmark completed: {}", results);
        return results;
    }

    /**
     * 评估解的质量
     */
    private double evaluateSolutionQuality(List<Topic> solution, int targetScore, Map<String, Integer> typeTargetCounts) {
        if (solution.isEmpty()) return 0.0;
        
        // 分数匹配度
        int actualScore = solution.stream().mapToInt(Topic::getScore).sum();
        double scoreDeviation = Math.abs(actualScore - targetScore) / (double) targetScore;
        double scoreFitness = 1.0 / (1.0 + scoreDeviation);
        
        // 题型匹配度
        Map<String, Integer> actualTypeCounts = new HashMap<>();
        for (Topic topic : solution) {
            String type = getStandardTopicType(topic.getType());
            actualTypeCounts.merge(type, 1, Integer::sum);
        }
        
        double typeDeviation = 0.0;
        for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
            String type = entry.getKey();
            int target = entry.getValue();
            int actual = actualTypeCounts.getOrDefault(type, 0);
            if (target > 0) {
                typeDeviation += Math.abs(target - actual) / (double) target;
            }
        }
        double typeFitness = 1.0 / (1.0 + typeDeviation / typeTargetCounts.size());
        
        return 0.7 * scoreFitness + 0.3 * typeFitness;
    }

    /**
     * 计算标准差
     */
    private double calculateStandardDeviation(List<Double> values) {
        if (values.size() < 2) return 0.0;
        
        double mean = values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = values.stream()
            .mapToDouble(v -> Math.pow(v - mean, 2))
            .average().orElse(0.0);
        
        return Math.sqrt(variance);
    }

    /**
     * 生成简单的贪心种子解
     */
    private List<Topic> generateSimpleGreedySeed(List<Topic> availableQuestions,
                                               Map<String, Integer> typeTargetCounts,
                                               Map<String, Integer> typeScoreMap) {
        List<Topic> result = new ArrayList<>();
        
        // 按题型分组
        Map<String, List<Topic>> topicsByType = availableQuestions.stream()
            .collect(Collectors.groupingBy(topic -> getStandardTopicType(topic.getType())));
        
        // 为每个题型选择高质量的题目
        for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
            String type = entry.getKey();
            int needed = entry.getValue();
            
            List<Topic> candidates = topicsByType.getOrDefault(type, Collections.emptyList());
            if (candidates.isEmpty()) continue;
            
            // 按质量和分数匹配度排序
            candidates.sort((t1, t2) -> {
                int targetScore = typeScoreMap != null ? typeScoreMap.getOrDefault(
                    convertDbTypeToFrontendType(type), 10) : 10;
                
                // 分数匹配度
                double scoreMatch1 = 1.0 / (1.0 + Math.abs(t1.getScore() - targetScore));
                double scoreMatch2 = 1.0 / (1.0 + Math.abs(t2.getScore() - targetScore));
                
                // 题目新鲜度（简化评估）
                double freshness1 = t1.getCreatedAt() != null ? 
                    1.0 / (1.0 + ChronoUnit.DAYS.between(t1.getCreatedAt(), LocalDateTime.now()) / 30.0) : 0.5;
                double freshness2 = t2.getCreatedAt() != null ? 
                    1.0 / (1.0 + ChronoUnit.DAYS.between(t2.getCreatedAt(), LocalDateTime.now()) / 30.0) : 0.5;
                
                double quality1 = 0.7 * scoreMatch1 + 0.3 * freshness1;
                double quality2 = 0.7 * scoreMatch2 + 0.3 * freshness2;
                
                return Double.compare(quality2, quality1); // 降序
            });
            
            // 选择前N个高质量题目
            int toSelect = Math.min(needed, candidates.size());
            result.addAll(candidates.subList(0, toSelect));
        }
        
        return result;
    }



    /**
     * 增强的种群多样性计算
     */
    private double calculatePopulationDiversity(List<EnhancedChromosome> population) {
        if (population.size() < 2) return 0.0;

        double totalDistance = 0.0;
        int comparisons = 0;
        int sampleSize = Math.min(30, population.size());

        for (int i = 0; i < sampleSize; i++) {
            for (int j = i + 1; j < sampleSize; j++) {
                totalDistance += hammingDistance(population.get(i).getGeneIndices(), population.get(j).getGeneIndices());
                comparisons++;
            }
        }

        return comparisons > 0 ? totalDistance / comparisons : 0.0;
    }

    /**
     * 多样性注入 - 当种群多样性过低时注入新个体
     */
    private void injectDiversityIntoPopulation(List<EnhancedChromosome> population, 
                                             List<Topic> availableQuestions,
                                             Map<String, Integer> typeTargetCounts) {
        int injectCount = Math.min(POPULATION_SIZE / 10, 5); // 注入10%的新个体
        
        for (int i = 0; i < injectCount; i++) {
            // 替换适应度最低的个体
            int worstIndex = population.size() - 1 - i;
            if (worstIndex >= 0) {
                EnhancedChromosome newIndividual = generateRandomEnhancedChromosome(availableQuestions, typeTargetCounts);
                population.set(worstIndex, newIndividual);
            }
        }
    }

    /**
     * 提前终止判断
     */
    private boolean shouldTerminateEarly(int generation, double bestFitness, 
                                       int generationsWithoutImprovement, double diversity) {
        return generation >= MIN_GENERATIONS && (
            bestFitness >= EARLY_TERMINATE_THRESHOLD ||
            generationsWithoutImprovement >= STAGNATION_THRESHOLD ||
            (diversity < DIVERSITY_THRESHOLD / 2 && generationsWithoutImprovement >= 10)
        );
    }

    /**
     * 自适应变异率计算 - 考虑多样性 (修复版本)
     */
    private double calculateAdaptiveMutationRate(int generation, int stagnation, double diversity) {
        if (!ADAPTIVE_MUTATION_ENABLED) {
            return MUTATION_RATE;
        }

        // 基础衰减
        double baseRate = ADAPTIVE_MUTATION_MIN_RATE + 
                         (ADAPTIVE_MUTATION_MAX_RATE - ADAPTIVE_MUTATION_MIN_RATE) * 
                         (1.0 - (double) generation / MAX_GENERATIONS);

        // 停滞惩罚
        if (stagnation > ADAPTIVE_MUTATION_STAGNATION_THRESHOLD) {
            baseRate *= (1.0 + (stagnation - ADAPTIVE_MUTATION_STAGNATION_THRESHOLD) * 0.1);
        }

        // 多样性惩罚
        if (diversity < DIVERSITY_THRESHOLD) {
            baseRate *= (1.0 + (DIVERSITY_THRESHOLD - diversity) * 2.0);
        }

        return Math.max(ADAPTIVE_MUTATION_MIN_RATE, Math.min(ADAPTIVE_MUTATION_MAX_RATE, baseRate));
    }

    /**
     * 题目质量计算 - 修复版本
     */
    private double calculateTopicQuality(TopicEnhancementData data) {
        if (data == null) return 0.5; // 默认中等质量

        double quality = 1.0;
        double usagePenalty = 0.0;
        double recencyPenalty = 0.0;
        double baseQualityForTopic = 1.0;

        // 使用次数惩罚
        if (data.getUsageCount() != null && data.getUsageCount() > 0) {
            usagePenalty = 1.0 / (1.0 + data.getUsageCount());
            quality *= (1.0 - usagePenalty * 0.1);
        }

        // 最近使用时间惩罚
        if (data.getLastUsedTime() != null) {
            long daysSinceLastUse = ChronoUnit.DAYS.between(data.getLastUsedTime(), LocalDateTime.now());
            if (daysSinceLastUse < VERY_RECENT_DAYS_THRESHOLD) {
                recencyPenalty = PENALTY_VERY_RECENT;
            } else if (daysSinceLastUse < RECENT_DAYS_THRESHOLD) {
                recencyPenalty = PENALTY_RECENT;
            } else if (daysSinceLastUse <= LESS_RECENT_DAYS_THRESHOLD) {
                recencyPenalty = PENALTY_LESS_RECENT;
            }
            quality *= (1.0 - recencyPenalty);
        }

        return Math.max(MIN_TOPIC_QUALITY, quality);
    }

    /**
     * 获取题目分数 - 优先使用类型分数映射
     */
    private int getTopicScore(Topic topic, Map<String, Integer> typeScoreMap) {
        if (typeScoreMap != null && !typeScoreMap.isEmpty()) {
            String type = getStandardTopicType(topic.getType());
            String frontendType = convertDbTypeToFrontendType(type);
            return typeScoreMap.getOrDefault(frontendType, 
                   topic.getScore() != null ? topic.getScore() : 0);
        }
        return topic.getScore() != null ? topic.getScore() : 0;
    }

    /**
     * 增强的适应度计算方法们
     */
    private double calculateScoreFitnessEnhanced(List<Topic> selectedTopics, int targetScore, 
                                               Map<String, Integer> typeScoreMap) {
        if (targetScore <= 0) return 1.0;
        
        int actualScore = selectedTopics.stream()
            .mapToInt(topic -> getTopicScore(topic, typeScoreMap))
            .sum();
            
        double deviation = Math.abs(actualScore - targetScore) / (double) targetScore;
        return 1.0 / (1.0 + deviation);
    }

    private double calculateQualityFitnessEnhanced(List<Topic> selectedTopics, 
                                                 Map<Integer, TopicEnhancementData> enhancementDataMap) {
        if (selectedTopics.isEmpty()) return 0.0;
        
        double totalQuality = selectedTopics.stream()
            .mapToDouble(topic -> {
                TopicEnhancementData data = enhancementDataMap.get(topic.getId());
                return calculateTopicQuality(data);
            })
            .sum();
            
        return totalQuality / selectedTopics.size();
    }

    private double calculateDifficultyFitnessEnhanced(List<Topic> selectedTopics, 
                                                    Map<String, Double> targetDistribution) {
        if (targetDistribution == null || targetDistribution.isEmpty() || selectedTopics.isEmpty()) {
            return 1.0;
        }

        Map<String, Integer> actualCounts = new HashMap<>();
        for (Topic topic : selectedTopics) {
            String difficultyName = difficultyClassifier.getDifficultyName(topic.getDifficulty());
            actualCounts.merge(difficultyName, 1, Integer::sum);
        }

        return calculateDistributionFitness(actualCounts, targetDistribution, selectedTopics.size());
    }

    private double calculateCognitiveFitnessEnhanced(List<Topic> selectedTopics, 
                                                   Map<String, Double> targetDistribution) {
        if (targetDistribution == null || targetDistribution.isEmpty() || selectedTopics.isEmpty()) {
            return 1.0;
        }

        Map<String, Integer> actualCounts = new HashMap<>();
        for (Topic topic : selectedTopics) {
            // 假设Topic有getCognitiveLevel方法，如果没有则返回默认值
            String cognitiveLevel = getCognitiveLevel(topic);
            actualCounts.merge(cognitiveLevel, 1, Integer::sum);
        }

        return calculateDistributionFitness(actualCounts, targetDistribution, selectedTopics.size());
    }

    private double calculateKpCoverageFitnessEnhanced(List<Topic> selectedTopics, 
                                                    List<Integer> targetKnowledgeIds) {
        if (targetKnowledgeIds == null || targetKnowledgeIds.isEmpty()) {
            return 1.0;
        }
        if (selectedTopics.isEmpty()) {
            return 0.0;
        }

        Set<Integer> coveredKpIds = selectedTopics.stream()
            .map(Topic::getKnowId)
            .filter(targetKnowledgeIds::contains)
            .collect(Collectors.toSet());

        double coverageRatio = (double) coveredKpIds.size() / targetKnowledgeIds.size();
        return coveredKpIds.size() == targetKnowledgeIds.size() ? coverageRatio : coverageRatio * 0.5;
    }

    private double calculateTypeDiversityFitnessEnhanced(List<Topic> selectedTopics, 
                                                       Map<String, Integer> typeTargetCounts) {
        if (typeTargetCounts == null || typeTargetCounts.isEmpty() || selectedTopics.isEmpty()) {
            return 1.0;
        }

        Map<String, Integer> actualCounts = new HashMap<>();
        for (Topic topic : selectedTopics) {
            String type = getStandardTopicType(topic.getType());
            actualCounts.merge(type, 1, Integer::sum);
        }

        double totalDeviation = 0.0;
        for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
            String type = entry.getKey();
            int target = entry.getValue();
            int actual = actualCounts.getOrDefault(type, 0);
            
            if (target > 0) {
                double deviation = Math.abs(actual - target) / (double) target;
                totalDeviation += deviation;
            }
        }

        double avgDeviation = typeTargetCounts.isEmpty() ? 0 : totalDeviation / typeTargetCounts.size();
        return 1.0 / (1.0 + avgDeviation);
    }

    private double calculateKpBalanceFitnessEnhanced(List<Topic> selectedTopics, 
                                                   List<Integer> targetKnowledgeIds) {
        if (targetKnowledgeIds == null || targetKnowledgeIds.isEmpty() || selectedTopics.isEmpty()) {
            return 1.0;
        }

        return calculateKnowledgePointTypeBalanceFitness(selectedTopics, targetKnowledgeIds);
    }

    /**
     * 计算分数适应度
     * 基于实际总分与目标分数的偏差计算适应度
     */
    private double calculateScoreFitness(List<Topic> selectedTopics, int targetScore, 
                                       Map<String, Integer> typeScoreMap) {
        if (targetScore <= 0) return 1.0;
        
        int actualScore = selectedTopics.stream()
            .mapToInt(topic -> getTopicScore(topic, typeScoreMap))
            .sum();
            
        double deviation = Math.abs(actualScore - targetScore) / (double) targetScore;
        return 1.0 / (1.0 + deviation);
    }

    /**
     * 计算题型多样性适应度
     * 基于选中题目的题型分布与目标分布的匹配度
     */
    private double calculateTopicTypeDiversityFitness(List<Topic> selectedTopics) {
        if (selectedTopics == null || selectedTopics.isEmpty()) {
            return 0.0;
        }

        // 如果有题型目标计数，使用精确匹配计算
        if (typeTargetCounts != null && !typeTargetCounts.isEmpty()) {
            return calculateTypeDiversityFitnessEnhanced(selectedTopics, typeTargetCounts);
        }

        // 否则计算基本的题型多样性（使用香农熵）
        Map<String, Integer> actualCounts = new HashMap<>();
        for (Topic topic : selectedTopics) {
            String type = getStandardTopicType(topic.getType());
            actualCounts.merge(type, 1, Integer::sum);
        }

        if (actualCounts.size() <= 1) {
            return 0.5; // 只有一种题型，多样性较低
        }

        // 计算香农熵作为多样性度量
        double entropy = 0.0;
        int total = selectedTopics.size();
        for (int count : actualCounts.values()) {
            if (count > 0) {
                double probability = (double) count / total;
                entropy -= probability * Math.log(probability) / Math.log(2);
            }
        }

        // 归一化熵值到[0,1]范围
        double maxEntropy = Math.log(actualCounts.size()) / Math.log(2);
        return maxEntropy > 0 ? entropy / maxEntropy : 0.0;
    }

    /**
     * 获取认知层次 - 辅助方法
     */
    private String getCognitiveLevel(Topic topic) {
        // 这里需要根据实际的Topic实体结构来实现
        // 如果Topic没有认知层次字段，返回默认值
        return "understand"; // 默认为理解层次
    }

    /**
     * 获取染色体代表的题目组合的题型分布
     */
    private Map<String, Integer> getTypeDistribution(Chromosome chromosome, List<Topic> questions) {
        Map<String, Integer> typeCounts = new HashMap<>();
        BitSet gene = chromosome.getGene();

        for (int i = 0; i < questions.size(); i++) {
            if (gene.get(i)) {
                Topic topic = questions.get(i);
                String type = getStandardTopicType(topic.getType());
                typeCounts.put(type, typeCounts.getOrDefault(type, 0) + 1);
            }
        }

        return typeCounts;
    }

    /**
     * 强制类型数量匹配的初始化方法
     * 生成的每个染色体都确保精确包含目标数量的每种题型
     */
    private List<Chromosome> enforceExactTypeCountsInitialization(List<Topic> availableQuestions, Map<String, Integer> typeTargetCounts) {
        List<Chromosome> population = new ArrayList<>(POPULATION_SIZE);
        if (typeTargetCounts == null || typeTargetCounts.isEmpty()) {
            // 如果没有题型限制，回退到标准初始化
            return initializePopulation(availableQuestions.size());
        }

        // 按题型分组所有题目
        Map<String, List<Integer>> indexesByType = new HashMap<>();
        for (int i = 0; i < availableQuestions.size(); i++) {
            Topic topic = availableQuestions.get(i);
            String type = getStandardTopicType(topic.getType());
            indexesByType.computeIfAbsent(type, k -> new ArrayList<>()).add(i);
        }

        // 调试：输出各题型的可用题目数量
        log.info("题库中各题型的可用题目数量:");
        for (Map.Entry<String, List<Integer>> entry : indexesByType.entrySet()) {
            log.info("- {}: {} 题", entry.getKey(), entry.getValue().size());
        }

        // 检查是否有足够的每种类型题目
        boolean hasSufficientTopics = true;
        log.info("检查题型约束要求:");
        for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
            String type = entry.getKey();
            int requiredCount = entry.getValue();
            List<Integer> availableIndexes = indexesByType.getOrDefault(type, Collections.emptyList());

            log.info("- 题型 {}: 需要 {} 题，可用 {} 题", type, requiredCount, availableIndexes.size());

            if (requiredCount > 0 && availableIndexes.size() < requiredCount) {
                log.warn("题型 {} 题目不足，需要 {} 题，但只有 {} 题可用",
                       type, requiredCount, availableIndexes.size());
                hasSufficientTopics = false;
            } else if (requiredCount > 0) {
                log.info("题型 {} 题目充足", type);
            }
        }

        if (!hasSufficientTopics) {
            log.warn("无法创建满足题型数量要求的初始种群");
            return Collections.emptyList();
        }

        Random random = getThreadSafeRandom();

        // 生成POPULATION_SIZE个染色体，每个都严格满足题型数量要求
        for (int i = 0; i < POPULATION_SIZE; i++) {
            BitSet gene = new BitSet(availableQuestions.size());

            // 确保每种题型都选择精确的目标数量
            for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
                String type = entry.getKey();
                int targetCount = entry.getValue();
                List<Integer> availableIndices = new ArrayList<>(indexesByType.getOrDefault(type, Collections.emptyList()));

                if (targetCount > 0 && !availableIndices.isEmpty()) {
                    // 随机打乱可用索引
                    Collections.shuffle(availableIndices, random);

                    // 选择所需数量的题目索引
                    for (int j = 0; j < Math.min(targetCount, availableIndices.size()); j++) {
                        gene.set(availableIndices.get(j));
                    }
                }
            }

            // 添加染色体到种群
            population.add(new Chromosome(gene));
        }

        log.info("成功创建了 {} 个染色体的强制类型匹配初始种群", population.size());
        return population;
    }

    /**
     * 计算给定实际计数的分布与目标分布之间的适应度。
     * 适应度越高，表示实际分布与目标分布越吻合。
     * 计算方法是基于各项的百分比偏差之和的倒数。
     *
     * @param actualCounts         实际各项的计数 (例如 Map<难度名称, 数量> 或 Map<认知层次名称, 数量>)
     * @param targetDistribution   目标各项的百分比分布 (例如 Map<难度名称, 期望百分比>)
     * @param totalItems           实际选中的总项目数，用于计算各项的实际百分比。
     * @return 分布适应度值，介于0和1之间，1表示完美匹配。
     */
    private double calculateDistributionFitness(Map<String, Integer> actualCounts,
                                              Map<String, Double> targetDistribution,
                                              int totalItems) {
        if (targetDistribution == null || targetDistribution.isEmpty() || totalItems == 0) {
            return 1.0; // 没有目标分布或没有选中题目，视为完美匹配或不参与评估
        }

        double totalDeviation = 0.0;
        // 遍历所有目标分布项 (如 easy, medium, hard)
        for (Map.Entry<String, Double> targetEntry : targetDistribution.entrySet()) {
            String key = targetEntry.getKey(); // 难度/认知层次的名称
            double targetPercent = targetEntry.getValue(); // 期望的百分比
            // 计算实际百分比：该项的实际数量 / 总数量
            double actualPercent = (double) actualCounts.getOrDefault(key, 0) / totalItems;
            // 累加绝对偏差
            totalDeviation += Math.abs(targetPercent - actualPercent);
        }

        // 适应度公式：1 / (1 + 总偏差)。总偏差为0时，适应度为1。
        return 1.0 / (1.0 + totalDeviation);
    }

    /**
     * 并行评估整个种群中所有染色体的适应度。
     * 使用自定义的 {@link ForkJoinPool} 来控制并行度，避免过多消耗系统资源。
     * 评估完成后，种群将按照适应度从高到低排序。
     *
     * @param population                     待评估的染色体种群。
     * @param questions                      题库中所有可用题目列表。
     * @param targetScore                    试卷目标总分。
     * @param difficultyDistributionTarget   目标难度分布。
     * @param cognitiveLevelDistributionTarget 目标认知层次分布。
     * @param enhancementDataMap             题目增强数据。
     * @param targetKnowledgeIds             目标知识点ID列表。
     */
    private void evaluatePopulation(List<Chromosome> population,
                                List<Topic> questions,
                                int targetScore,
                                Map<String, Integer> typeTargetCounts, // Added
                                Map<String, Double> difficultyDistributionTarget,
                                Map<String, Double> cognitiveLevelDistributionTarget,
                                Map<Integer, TopicEnhancementData> enhancementDataMap,
                                List<Integer> targetKnowledgeIds,
                                List<com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest> knowledgePointConfigs, // Added
                                Map<String, Integer> typeScoreMap) {
        if (population == null || population.isEmpty()) return;

        try {
            // 使用自定义线程池进行并行评估，避免双重并行
            List<java.util.concurrent.Future<Void>> futures = new ArrayList<>();
            final int chunkSize = 20; // 每个任务评估 20 条染色体，降低调度开销
            for (int start = 0; start < population.size(); start += chunkSize) {
                int end = Math.min(population.size(), start + chunkSize);
                List<Chromosome> chunk = population.subList(start, end);
                java.util.concurrent.Future<Void> future = evaluationThreadPool.submit(() -> {
                    for (Chromosome c : chunk) {
                        evaluateChromosome(
                                c,
                                questions,
                                targetScore,
                                typeTargetCounts,
                                difficultyDistributionTarget,
                                cognitiveLevelDistributionTarget,
                                enhancementDataMap,
                                targetKnowledgeIds,
                                knowledgePointConfigs,
                                typeScoreMap);
                    }
                    return null;
                });
                futures.add(future);
            }

            // 等待所有任务完成
            for (java.util.concurrent.Future<Void> future : futures) {
                future.get();
            }

        } catch (Exception e) {
            log.error("Error during parallel population evaluation: {}. Falling back to sequential evaluation.", e.getMessage(), e);
            // 如果并行评估出错，则回退到串行评估模式，确保程序健壮性
            for (Chromosome chromosome : population) {
                evaluateChromosome(
                    chromosome,
                    questions,
                    targetScore,
                    typeTargetCounts, // Added
                    difficultyDistributionTarget,
                    cognitiveLevelDistributionTarget,
                    enhancementDataMap,
                    targetKnowledgeIds,
                    knowledgePointConfigs, // Added
                    typeScoreMap
                );
            }
        }
        // 评估完成后，按适应度从高到低（降序）对种群进行排序
        population.sort(Comparator.comparing(Chromosome::getFitness).reversed());
    }

    /**
     * 评估单个染色体的适应度。
     * 适应度是多个子适应度（总分、质量、难度分布、认知层次分布、知识点覆盖）的加权和。
     * 每个子适应度衡量染色体（即题目组合）在对应维度上与目标的吻合程度。
     *
     * @param chromosome                    待评估的染色体。
     * @param questions                     题库中所有可用题目列表。
     * @param targetScore                   试卷目标总分。
     * @param difficultyDistributionTarget  目标难度分布。
     * @param cognitiveLevelDistributionTarget 目标认知层次分布。
     * @param enhancementDataMap            题目增强数据。
     * @param targetKnowledgeIds            目标知识点ID列表。
     * Evaluate chromosome with optional hard-constraint mode: any violation yields fitness 0.
     */
    @SuppressWarnings("deprecation") // 如果 Topic.getScore() 可能返回 null 且有特定处理逻辑，可保留。否则考虑移除。
    private void evaluateChromosome(Chromosome chromosome,
                                 List<Topic> questions,
                                 int targetScore,
                                 Map<String, Integer> typeTargetCounts, // Added
                                 Map<String, Double> difficultyDistributionTarget,
                                 Map<String, Double> cognitiveLevelDistributionTarget,
                                 Map<Integer, TopicEnhancementData> enhancementDataMap,
                                 List<Integer> targetKnowledgeIds,
                                 @SuppressWarnings("unused") List<com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest> knowledgePointConfigs, // Added but not yet used
                                 Map<String, Integer> typeScoreMap) {
        // --- Hard-constraint quick rejection (types & score) ---
        if (HARD_CONSTRAINT_MODE && typeTargetCounts != null && !typeTargetCounts.isEmpty()) {
            Map<String, Integer> countsTmp = new java.util.HashMap<>();
            int hardCurrentScore = 0;
            BitSet g = chromosome.getGene();
            for (int idx = g.nextSetBit(0); idx >= 0 && idx < questions.size(); idx = g.nextSetBit(idx + 1)) {
                Topic q = questions.get(idx);
                String dbType = getStandardTopicType(q.getType());
                countsTmp.merge(dbType, 1, Integer::sum);

                int scoreOfTopic;
                if (typeScoreMap != null && !typeScoreMap.isEmpty()) {
                    scoreOfTopic = typeScoreMap.getOrDefault(convertDbTypeToFrontendType(dbType),
                            q.getScore() != null ? q.getScore() : 0);
                } else {
                    scoreOfTopic = q.getScore() != null ? q.getScore() : 0;
                }
                hardCurrentScore += scoreOfTopic;
            }
            if (hardCurrentScore != targetScore) {
                chromosome.setFitness(0.0);
                return;
            }
            for (Map.Entry<String, Integer> e : typeTargetCounts.entrySet()) {
                if (!countsTmp.getOrDefault(e.getKey(), 0).equals(e.getValue())) {
                    chromosome.setFitness(0.0);
                    return;
                }
            }
        }

        List<Topic> selectedQuestions = new ArrayList<>();
        BitSet gene = chromosome.getGene(); // 获取染色体的基因序列 (BitSet)
        // 预先统计题型和难度分布，避免后续多次遍历
        Map<String, Integer> actualTypeCounts = new HashMap<>();
        Map<String, Integer> actualDifficultyCounts = new HashMap<>();

        // --- 1. 计算染色体代表的题目组合的总分和题目列表 ---
        int currentTotalScore = 0;
        // 使用 BitSet.nextSetBit 遍历已选中的题目索引，避免对 gene.length() 做 O(N) 全扫描
        for (int idx = gene.nextSetBit(0); idx >= 0 && idx < questions.size(); idx = gene.nextSetBit(idx + 1)) {
            Topic question = questions.get(idx);
            selectedQuestions.add(question);

            // 统计题型
            String standardizedType = getStandardTopicType(question.getType());
            actualTypeCounts.merge(standardizedType, 1, Integer::sum);
            // 统计难度
            String difficultyName = difficultyClassifier.getDifficultyName(question.getDifficulty());
            actualDifficultyCounts.merge(difficultyName, 1, Integer::sum);

            // 使用 typeScoreMap 计算分数，如果没有配置则使用题目本身的分数
            int questionScore;
            if (typeScoreMap != null && !typeScoreMap.isEmpty()) {
                String frontendType = convertDbTypeToFrontendType(standardizedType);
                questionScore = typeScoreMap.getOrDefault(frontendType,
                               question.getScore() != null ? question.getScore() : 0);
            } else {
                questionScore = question.getScore() != null ? question.getScore() : 0;
            }
            currentTotalScore += questionScore;
        }
        chromosome.setTotalScore(currentTotalScore); // 记录该染色体的总分

        int totalSelectedTopics = selectedQuestions.size();
        if (totalSelectedTopics == 0) { // 如果没有选中任何题目，则适应度为0，避免后续除零等问题
            chromosome.setFitness(0.0);
            return;
        }

        // --- 2. 硬约束检查和分数适应度计算 ---
        // 首先检查题型约束是否满足
        if (typeTargetCounts != null && !typeTargetCounts.isEmpty()) {


            // 计算题型偏差惩罚
            double typeDeviationPenalty = 0.0;
            for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
                String type = entry.getKey();
                int target = entry.getValue();
                int actual = actualTypeCounts.getOrDefault(type, 0);
                double deviation = Math.abs(target - actual) / (double) Math.max(target, 1);
                typeDeviationPenalty += deviation;
            }

            // 硬约束模式：题型不匹配直接返回0
            if (HARD_CONSTRAINT_MODE && typeDeviationPenalty > 0.1) {
                chromosome.setFitness(0.0);
                return;
            }
        }

        // 计算分数适应度
        double scoreFitness = 1.0;
        if (targetScore > 0) {
            double scoreDeviation = Math.abs(targetScore - currentTotalScore) / (double) targetScore;

            // 硬约束模式：分数偏差过大直接返回0
            if (HARD_CONSTRAINT_MODE && scoreDeviation > 0.1) {
                chromosome.setFitness(0.0);
                return;
            }

            scoreFitness = 1.0 / (1.0 + scoreDeviation);
        }

        if (targetScore > 0) {
            scoreFitness = 1.0 / (1.0 + (double)Math.abs(targetScore - currentTotalScore) / targetScore);
        } else if (currentTotalScore != 0) { // target is 0, but current is not, penalize
            scoreFitness = 0.0;
        } // If targetScore is 0 and currentTotalScore is 0, scoreFitness remains 1.0 (perfect match)

        // --- 3. 计算题目质量适应度 (Quality Fitness) ---
        // 衡量所选题目集合的平均"质量"。
        // 质量综合考虑使用次数和上次使用时间。
        double averageQuality = 0.5; // Default medium quality
        if (!selectedQuestions.isEmpty()) { // Ensure there are questions to calculate quality for
            double totalTopicSpecificQuality = 0.0;
            for (Topic question : selectedQuestions) {
                TopicEnhancementData data = enhancementDataMap != null ? enhancementDataMap.get(question.getId()) : null;

                double usagePenalty = 0.0;
                double recencyPenalty = 0.0;
                double baseQualityForTopic = 1.0; // Max quality for a topic before penalties

                if (data != null) {
                    // Usage Count Penalty: 1 - (1 / (1 + usageCount))
                    // Lower usage count means lower penalty (closer to 0)
                    if (data.getUsageCount() != null && data.getUsageCount() > 0) {
                        usagePenalty = 1.0 - (1.0 / (1.0 + data.getUsageCount()));
                    }

                    // Recency Penalty
                    if (data.getLastUsedTime() != null) {
                        long daysSinceLastUse = java.time.temporal.ChronoUnit.DAYS.between(data.getLastUsedTime(), java.time.LocalDateTime.now());

                        if (daysSinceLastUse <= VERY_RECENT_DAYS_THRESHOLD) {
                            recencyPenalty = PENALTY_VERY_RECENT;
                        } else if (daysSinceLastUse <= RECENT_DAYS_THRESHOLD) {
                            recencyPenalty = PENALTY_RECENT;
                        } else if (daysSinceLastUse <= LESS_RECENT_DAYS_THRESHOLD) {
                            recencyPenalty = PENALTY_LESS_RECENT;
                        }
                    }
                }
                // If no enhancement data, usagePenalty and recencyPenalty remain 0.

                // Combine: Start with baseQuality and subtract penalties.
                double topicQuality = baseQualityForTopic - usagePenalty - recencyPenalty;
                totalTopicSpecificQuality += Math.max(MIN_TOPIC_QUALITY, topicQuality); // Ensure a small minimum quality
            }
            averageQuality = totalTopicSpecificQuality / totalSelectedTopics; // average of topic specific qualities
        }

        // --- 4. 计算难度分布适应度 (Difficulty Distribution Fitness) ---
        // 衡量所选题目在不同难度级别（easy, medium, hard）上的数量分布与目标分布的吻合程度。

        double difficultyDistFitness = calculateDistributionFitness(
                actualDifficultyCounts,
                difficultyDistributionTarget,
                totalSelectedTopics
        );

        // --- 5. 计算认知层次分布适应度 (Cognitive Level Distribution Fitness) ---
        // 衡量题目在不同认知层次上的数量分布与目标分布的吻合度。
        double cognitiveDistFitness = 1.0; // 默认完美匹配，如果无相关数据或目标
        if (cognitiveLevelDistributionTarget != null && !cognitiveLevelDistributionTarget.isEmpty() &&
            WEIGHT_COGNITIVE_DIST > 0.001 && // Only calculate if weight is meaningful
            enhancementDataMap != null && !enhancementDataMap.isEmpty()) {
            Map<String, Integer> actualCognitiveCounts = new HashMap<>();
            int topicsWithCognitiveData = 0;
            for (Topic question : selectedQuestions) {
                TopicEnhancementData data = enhancementDataMap.get(question.getId());
                if (data != null && data.getCognitiveLevel() != null && !data.getCognitiveLevel().trim().isEmpty()) {
                    actualCognitiveCounts.put(data.getCognitiveLevel(),
                            actualCognitiveCounts.getOrDefault(data.getCognitiveLevel(), 0) + 1);
                    topicsWithCognitiveData++;
                }
            }
            // Only calculate if there are topics with cognitive data and a target distribution for them
            if (topicsWithCognitiveData > 0 && !actualCognitiveCounts.isEmpty()) {
                cognitiveDistFitness = calculateDistributionFitness(
                        actualCognitiveCounts, cognitiveLevelDistributionTarget, totalSelectedTopics);
                        // Note: totalSelectedTopics is used as the denominator, which might need review
                        // if only a subset of topics have cognitive data.
                        // Alternative: use topicsWithCognitiveData if targetDist only covers known levels.
            } else {
                if (log.isDebugEnabled()) {
                    log.debug("Cognitive distribution fitness not calculated: no topics with cognitive data or actual counts map is empty, though target and weight were set.");
                }
                // cognitiveDistFitness remains 1.0 (neutral)
            }
        } else {
            if (WEIGHT_COGNITIVE_DIST > 0.001) { // Log only if it was intended to be used
                if (log.isDebugEnabled()) {
                    log.debug("Cognitive distribution fitness not calculated: Target distribution empty/null, or weight too small, or enhancement data missing.");
                }
            }
            // cognitiveDistFitness remains 1.0 (neutral)
        }

        // --- 6. 计算知识点覆盖适应度 (Knowledge Point Coverage Fitness) ---
        // 衡量对目标知识点的覆盖程度。
        double kpCoverageFitness = calculateKnowledgePointCoverageFitness(
                selectedQuestions, targetKnowledgeIds);

        // --- 7. 计算题型多样性适应度 (Topic Type Diversity Fitness) ---
        // 衡量所选题目对不同题型的覆盖程度
        double typeDistributionFitness = calculateTopicTypeDistributionFitness(selectedQuestions);

        // --- 8. 计算标签多样性适应度 (Tag Diversity Fitness) ---
        double tagDiversityFitness = calculateTagDiversityFitness(selectedQuestions);

        // --- 9.  计算知识点题型均衡适应度 (Knowledge Point Type Balance Fitness) ---
        // 衡量每个知识点内部题型分布的均匀性，避免某个知识点某题型过多而某题型过少
        double kpTypeBalanceFitness = calculateKnowledgePointTypeBalanceFitness(selectedQuestions, targetKnowledgeIds);

        // 动态调整标签多样性权重
        double adjustedTagDiversityWeight = getAdjustedTagDiversityWeight(selectedQuestions.size());

        // --- 10. 计算最终总适应度 (Weighted Sum) ---
        // 各子适应度根据预设权重加权求和。
        double finalFitness = (WEIGHT_SCORE * scoreFitness) +
                              (WEIGHT_QUALITY * averageQuality) + // 使用 averageQuality 作为 qualityFitness
                              (WEIGHT_DIFFICULTY_DIST * difficultyDistFitness) +
                              (WEIGHT_COGNITIVE_DIST * cognitiveDistFitness) +
                              (WEIGHT_KP_COVERAGE * kpCoverageFitness) +
                              (WEIGHT_TOPIC_TYPE_DIVERSITY * typeDistributionFitness) + // 添加题型多样性适应度
                              (adjustedTagDiversityWeight * tagDiversityFitness) + // 使用动态调整的标签多样性权重
                              (WEIGHT_KP_TYPE_BALANCE * kpTypeBalanceFitness); //  添加知识点题型均衡适应度

        chromosome.setFitness(finalFitness);
    }

    /**
     * 计算标签多样性适应度：使用香农熵衡量标签分布的均匀性
     * 避免题目过度集中在某个章节或知识点
     *
     * @param selectedQuestions 选中的题目列表
     * @return 标签多样性适应度分数
     */
    private double calculateTagDiversityFitness(List<Topic> selectedQuestions) {
        if (selectedQuestions == null || selectedQuestions.isEmpty()) {
            return 0.0;
        }

        // 使用TagDiversityAnalyzer计算更精确的多样性分数
        double diversityScore = 0.5; // 默认中等多样性分数
        boolean tooConcentrated = false;

        if (tagDiversityAnalyzer != null) {
            diversityScore = tagDiversityAnalyzer.calculateTagDiversity(selectedQuestions);
            // 检查是否过于集中（超过50%的题目来自同一个标签）
            tooConcentrated = tagDiversityAnalyzer.isTagDistributionTooConcentrated(selectedQuestions, 0.5);

            if (log.isDebugEnabled()) {
                log.debug("Tag diversity analysis: score={} , tooConcentrated={}, topics={}",
                        String.format("%.3f", diversityScore), tooConcentrated, selectedQuestions.size());
            }
        } else {
            log.warn("TagDiversityAnalyzer not injected, using default diversity score");
        }

        if (tooConcentrated) {
            // 如果过于集中，给予惩罚
            diversityScore *= 0.5;
            if (log.isDebugEnabled()) {
                log.debug("Tag distribution too concentrated, applying penalty. Diversity score: {}", diversityScore);
            }
        }

        return diversityScore;
    }

    /**
     * 根据题目数量动态调整标签多样性权重
     * 题目数量越多，标签多样性越重要
     */
    private double getAdjustedTagDiversityWeight(int topicCount) {
        if (topicCount <= 10) {
            // 题目较少时，标签多样性不太重要
            return WEIGHT_TAG_DIVERSITY * 0.5;
        } else if (topicCount <= 30) {
            // 中等数量题目，使用标准权重
            return WEIGHT_TAG_DIVERSITY;
        } else {
            // 题目较多时，标签多样性更重要
            return WEIGHT_TAG_DIVERSITY * 1.5;
        }
    }

    private List<Chromosome> initializePopulation(int geneLength) {
        List<Chromosome> population = new ArrayList<>(POPULATION_SIZE);
        Random random = getThreadSafeRandom(); // 使用线程安全的随机数生成器

        for (int i = 0; i < POPULATION_SIZE; i++) {
            BitSet gene = new BitSet(geneLength);
            for (int j = 0; j < geneLength; j++) {
                // 以约50%的概率决定是否选择该题目，可以根据实际情况调整此初始选择概率
                if (random.nextDouble() < 0.5) {
                    gene.set(j);
                }
            }
            population.add(new Chromosome(gene));
        }
        return population;
    }

    /**
     * 锦标赛选择策略。
     * 从当前种群中随机选取 {@link #TOURNAMENT_SIZE} 个个体进行"锦标赛"，
     * 其中适应度最高的个体被选中作为父代。
     *
     * @param population 当前染色体种群。
     * @return 经过锦标赛选择胜出的染色体。
     */
    private Chromosome tournamentSelection(List<Chromosome> population) {
        if (population == null || population.isEmpty()) {
            throw new IllegalArgumentException("Population cannot be null or empty for tournament selection.");
        }
        List<Chromosome> tournamentCandidates = new ArrayList<>(TOURNAMENT_SIZE);
        Random random = getThreadSafeRandom();

        for (int i = 0; i < TOURNAMENT_SIZE; i++) {
            // 随机从种群中选择一个候选者（允许重复选择，即放回抽样）
            tournamentCandidates.add(population.get(random.nextInt(population.size())));
        }

        // 从锦标赛候选者中选出适应度最高的那个
        return Collections.max(tournamentCandidates, Comparator.comparing(Chromosome::getFitness));
    }

    /**
     * 类型保持交叉操作。
     * 此交叉操作确保子代染色体维持与父代相同的题型分布。
     *
     * @param parent1 父代染色体1
     * @param parent2 父代染色体2
     * @param questions 候选题目列表
     * @param typeTargetCounts 目标题型数量
     * @return 生成的子代染色体
     */
    private Chromosome typePreservingCrossover(Chromosome parent1, Chromosome parent2,
                                               List<Topic> questions,
                                               Map<String, Integer> typeTargetCounts) {
        if (parent1 == null || parent2 == null) {
            return new Chromosome(new BitSet());
        }

        Random random = getThreadSafeRandom();
        if (random.nextDouble() > CROSSOVER_RATE) {
            return new Chromosome((BitSet) (random.nextBoolean() ? parent1.getGene().clone() : parent2.getGene().clone()));
        }

        // 检查父代是否都满足类型约束
        Map<String, Set<Integer>> typeIndicesParent1 = getTypeIndices(parent1.getGene(), questions);
        Map<String, Set<Integer>> typeIndicesParent2 = getTypeIndices(parent2.getGene(), questions);

        // 创建子代基因
        BitSet childGene = new BitSet(questions.size());

        // 对每种题型分别进行交叉
        for (String type : typeTargetCounts.keySet()) {
            int targetCount = typeTargetCounts.getOrDefault(type, 0);
            if (targetCount <= 0) continue;

            Set<Integer> indicesOfTypeParent1 = typeIndicesParent1.getOrDefault(type, new HashSet<>());
            Set<Integer> indicesOfTypeParent2 = typeIndicesParent2.getOrDefault(type, new HashSet<>());

            // 如果任一父代没有足够的此类型题目，则联合两个父代的题目
            Set<Integer> allAvailableIndicesOfType = new HashSet<>(indicesOfTypeParent1);
            allAvailableIndicesOfType.addAll(indicesOfTypeParent2);

            if (allAvailableIndicesOfType.size() < targetCount) {
                // 题目不足，全部选择
                for (int index : allAvailableIndicesOfType) {
                    childGene.set(index);
                }
            } else {
                // 随机选择交叉点 - 对此类型的索引列表进行切分
                List<Integer> parent1IndicesList = new ArrayList<>(indicesOfTypeParent1);
                List<Integer> parent2IndicesList = new ArrayList<>(indicesOfTypeParent2);

                if (!parent1IndicesList.isEmpty() && !parent2IndicesList.isEmpty()) {
                    // 对索引列表排序，以便确定性地进行交叉
                    Collections.sort(parent1IndicesList);
                    Collections.sort(parent2IndicesList);

                    // 随机选择交叉点，范围在0和目标数量之间
                    int crossPoint = random.nextInt(targetCount + 1);

                    // 从第一个父代选择前crossPoint个元素
                    for (int i = 0; i < Math.min(crossPoint, parent1IndicesList.size()); i++) {
                        childGene.set(parent1IndicesList.get(i));
                    }

                    // 从第二个父代选择剩余元素，直到达到目标数量
                    int remaining = targetCount - Math.min(crossPoint, parent1IndicesList.size());
                    for (int i = 0; i < Math.min(remaining, parent2IndicesList.size()); i++) {
                        childGene.set(parent2IndicesList.get(i));
                    }

                    // 如果仍然不足，从第一个父代的剩余元素中补充
                    if (childGene.cardinality() < targetCount) {
                        int stillNeeded = targetCount - childGene.cardinality();
                        for (int i = crossPoint; i < parent1IndicesList.size() && stillNeeded > 0; i++) {
                            int index = parent1IndicesList.get(i);
                            if (!childGene.get(index)) {
                                childGene.set(index);
                                stillNeeded--;
                            }
                        }
                    }

                    // 如果仍然不足，从第二个父代的剩余元素中补充
                    if (childGene.cardinality() < targetCount) {
                        int stillNeeded = targetCount - childGene.cardinality();
                        for (int i = remaining; i < parent2IndicesList.size() && stillNeeded > 0; i++) {
                            int index = parent2IndicesList.get(i);
                            if (!childGene.get(index)) {
                                childGene.set(index);
                                stillNeeded--;
                            }
                        }
                    }
                } else if (!parent1IndicesList.isEmpty()) {
                    // 只有第一个父代有此类型，尽可能选择足够数量
                    for (int i = 0; i < Math.min(targetCount, parent1IndicesList.size()); i++) {
                        childGene.set(parent1IndicesList.get(i));
                    }
                } else if (!parent2IndicesList.isEmpty()) {
                    // 只有第二个父代有此类型，尽可能选择足够数量
                    for (int i = 0; i < Math.min(targetCount, parent2IndicesList.size()); i++) {
                        childGene.set(parent2IndicesList.get(i));
                    }
                }
            }
        }

        return new Chromosome(childGene);
    }

    /**
     * 类型保持变异操作。
     * 此变异操作确保变异后的染色体维持所需的题型数量分布。
     *
     * @param chromosome 待变异的染色体
     * @param questions 候选题目列表
     * @param typeTargetCounts 目标题型数量
     */
    private void typePreservingMutate(Chromosome chromosome, List<Topic> questions, Map<String, Integer> typeTargetCounts) {
        BitSet gene = chromosome.getGene();
        if (gene.isEmpty()) return;

        Random random = getThreadSafeRandom();

        // 获取当前染色体按题型分组的索引
        Map<String, Set<Integer>> typeIndices = getTypeIndices(gene, questions);

        // 对每种题型单独变异，确保变异后保持相同数量
        for (String type : typeTargetCounts.keySet()) {
            int targetCount = typeTargetCounts.getOrDefault(type, 0);
            if (targetCount <= 0) continue;

            Set<Integer> indicesOfType = typeIndices.getOrDefault(type, new HashSet<>());
            int currentCount = indicesOfType.size();

            // 如果当前数量等于目标数量，执行1对1替换变异
            if (currentCount == targetCount && random.nextDouble() < MUTATION_RATE) {
                // 查找此类型的所有候选题目索引
                Set<Integer> allCandidateIndicesOfType = getAllIndicesOfType(questions, type);

                // 移除当前已选索引，得到可以替换的候选集
                Set<Integer> candidatesForReplacement = new HashSet<>(allCandidateIndicesOfType);
                candidatesForReplacement.removeAll(indicesOfType);

                if (!candidatesForReplacement.isEmpty() && !indicesOfType.isEmpty()) {
                    // 随机选择一个当前索引移除
                    List<Integer> currentIndicesList = new ArrayList<>(indicesOfType);
                    int indexToRemove = currentIndicesList.get(random.nextInt(currentIndicesList.size()));

                    // 随机选择一个新索引添加
                    List<Integer> candidatesList = new ArrayList<>(candidatesForReplacement);
                    int indexToAdd = candidatesList.get(random.nextInt(candidatesList.size()));

                    // 执行替换
                    gene.clear(indexToRemove);
                    gene.set(indexToAdd);
                }
            }
            // 如果当前数量小于目标数量，尝试添加新题目
            else if (currentCount < targetCount) {
                // 查找此类型的所有可添加候选题目
                Set<Integer> allCandidateIndicesOfType = getAllIndicesOfType(questions, type);
                Set<Integer> candidatesForAddition = new HashSet<>(allCandidateIndicesOfType);
                candidatesForAddition.removeAll(indicesOfType);

                if (!candidatesForAddition.isEmpty()) {
                    // 随机选择并添加所需数量的题目
                    List<Integer> candidatesList = new ArrayList<>(candidatesForAddition);
                    Collections.shuffle(candidatesList, random);

                    for (int i = 0; i < Math.min(targetCount - currentCount, candidatesList.size()); i++) {
                        gene.set(candidatesList.get(i));
                    }
                }
            }
            // 如果当前数量大于目标数量，移除多余题目
            else if (currentCount > targetCount) {
                // 随机选择并移除多余题目
                List<Integer> currentIndicesList = new ArrayList<>(indicesOfType);
                Collections.shuffle(currentIndicesList, random);

                for (int i = targetCount; i < currentCount && i < currentIndicesList.size(); i++) {
                    gene.clear(currentIndicesList.get(i));
                }
            }
        }
    }

    /**
     * 获取指定题型的所有候选题目索引
     */
    private Set<Integer> getAllIndicesOfType(List<Topic> questions, String type) {
        Set<Integer> indices = new HashSet<>();
        for (int i = 0; i < questions.size(); i++) {
            if (getStandardTopicType(questions.get(i).getType()).equals(type)) {
                indices.add(i);
            }
        }
        return indices;
    }

    /**
     * 获取染色体中按题型分组的索引
     */
    private Map<String, Set<Integer>> getTypeIndices(BitSet gene, List<Topic> questions) {
        Map<String, Set<Integer>> typeIndices = new HashMap<>();

        for (int i = 0; i < questions.size(); i++) {
            if (gene.get(i)) {
                String type = getStandardTopicType(questions.get(i).getType());
                typeIndices.computeIfAbsent(type, k -> new HashSet<>()).add(i);
            }
        }

        return typeIndices;
    }

    /**
     * 从最优染色体中提取实际的题目列表。
     * 根据染色体的基因序列（BitSet），从原始可用题目列表中筛选出被选中的题目。
     *
     * @param bestSolutionChromosome 适应度最高的染色体。
     * @param questions              原始可用题目列表。
     * @return 染色体代表的题目组合（一个 {@link Topic} 列表）。
     */
    private List<Topic> extractSolution(Chromosome bestSolutionChromosome, List<Topic> questions) {
        List<Topic> solutionTopics = new ArrayList<>();
        if (bestSolutionChromosome == null) return solutionTopics;

        BitSet gene = bestSolutionChromosome.getGene();
        for (int i = 0; i < questions.size(); i++) { // 遍历候选题目列表
            if (i < gene.length() && gene.get(i)) { // 如果基因位为1且在基因长度内
                solutionTopics.add(questions.get(i));
            }
        }
        return solutionTopics;
    }

    /**
     * (未使用) 计算种群多样性的一个示例方法。
     * 可以通过计算种群中个体间基因的平均汉明距离等指标来衡量多样性。
     * 高多样性有助于算法探索更广阔的解空间，避免早熟。
     *
     * @param population 染色体种群。
     * @return 种群多样性的一个量化指标。
     */
    @SuppressWarnings("unused") // 标记为未使用，但保留作为未来扩展或参考
    private double calculateDiversity(List<Chromosome> population) {
        if (population == null || population.isEmpty() || population.size() < 2) return 0.0;

        double totalPairwiseDistance = 0.0;
        int comparisons = 0;

        // 为避免 N^2 复杂度，可以对部分样本进行比较，例如随机选取或只比较部分精英个体
        int sampleSize = Math.min(population.size(), 30); // 例如，最多取30个样本进行比较
        List<Chromosome> samplePopulation = new ArrayList<>(population.subList(0, sampleSize));

        for (int i = 0; i < samplePopulation.size(); i++) {
            for (int j = i + 1; j < samplePopulation.size(); j++) {
                totalPairwiseDistance += hammingDistance(
                    samplePopulation.get(i).getGene(),
                    samplePopulation.get(j).getGene()
                );
                comparisons++;
            }
        }
        return comparisons > 0 ? totalPairwiseDistance / comparisons : 0.0;
    }

    /**
     * (未使用) 计算两个基因序列（BitSet）之间的归一化汉明距离。
     * 汉明距离指两个等长字符串对应位置的不同字符的个数。
     * 此处归一化到 [0,1] 区间，表示差异程度。
     *
     * @param gene1 第一个基因序列。
     * @param gene2 第二个基因序列。
     * @return 归一化的汉明距离。
     */
    private double hammingDistance(BitSet gene1, BitSet gene2) {
        BitSet xorResult = (BitSet) gene1.clone();
        xorResult.xor(gene2); // 执行异或操作，结果中为1的位表示对应位置不同

        // 基因长度取两者中较大者，以正确处理不等长BitSet的情况 (尽管GA中通常等长)
        int effectiveLength = Math.max(gene1.length(), gene2.length());
        if (effectiveLength == 0) return 0.0;

        // xorResult.cardinality() 返回BitSet中值为1的位的数量，即不同位的数量
        return (double) xorResult.cardinality() / effectiveLength;
    }

    /**
     * 内部类，代表遗传算法中的染色体（一个个体或一个潜在解）。
     * 包含基因序列 (BitSet) 和该染色体的适应度值及总分。
     * 线程安全版本，使用同步机制保护共享状态。
     */
    public static class Chromosome {
        /** 基因序列，使用 BitSet 表示，每一位对应候选题目列表中的一个题目是否被选中。 */
        private final BitSet gene;

        /** 该染色体的适应度值，综合评价其优劣。 */
        private volatile double fitness;

        /** 该染色体所代表的题目组合的总分。 */
        private volatile int totalScore;

        /** 同步锁，保护基因序列的访问 */
        private final Object geneLock = new Object();

        /**
         * Chromosome 构造函数。
         * @param gene 该染色体的基因序列。
         */
        public Chromosome(BitSet gene) {
            synchronized (geneLock) {
                this.gene = (BitSet) gene.clone(); // 防御性复制
            }
            this.fitness = 0.0; // 初始适应度为0
            this.totalScore = 0;  // 初始总分为0
        }

        /**
         * 线程安全地获取基因序列的副本
         */
        public BitSet getGene() {
            synchronized (geneLock) {
                return (BitSet) gene.clone();
            }
        }

        /**
         * 获取适应度值
         */
        public double getFitness() {
            return fitness;
        }

        /**
         * 设置适应度值
         */
        public void setFitness(double fitness) {
            this.fitness = fitness;
        }

        /**
         * 获取总分
         */
        public int getTotalScore() {
            return totalScore;
        }

        /**
         * 设置总分
         */
        public void setTotalScore(int totalScore) {
            this.totalScore = totalScore;
        }
    }

    /**
     * 计算基于数值的难度分布适应度。
     * 首先将数值型的实际难度计数转换为类别型（easy, medium, hard）的计数，
     * 然后调用通用的 {@link #calculateDistributionFitness} 方法计算适应度。
     *
     * @param actualNumericDifficultyCounts Map<难度值, 数量>，表示实际选出题目中各数值难度的题目数量。
     * @param targetDifficultyDistribution Map<难度名, 期望百分比>，目标难度分布。
     * @param totalSelectedTopics 实际选中的总题目数。
     * @return 难度分布适应度。
     */
    private double calculateNumericDistributionFitness(Map<Double, Integer> actualNumericDifficultyCounts,
                                               Map<String, Double> targetDifficultyDistribution,
                                               int totalSelectedTopics) {
        if (targetDifficultyDistribution == null || targetDifficultyDistribution.isEmpty() || totalSelectedTopics == 0) {
            return 1.0; // 没有目标分布或没有选中题目，视为完美匹配
        }

        // 将实际的数值难度计数，按 getDifficultyName 规则，转换为按 "easy", "medium", "hard" 分类的计数
        Map<String, Integer> categorizedActualCounts = new HashMap<>();
        for (Map.Entry<Double, Integer> entry : actualNumericDifficultyCounts.entrySet()) {
            String difficultyName = difficultyClassifier.getDifficultyName(entry.getKey()); // 将0.1, 0.3, 0.5等映射为"easy", "medium", "hard"
            categorizedActualCounts.put(difficultyName,
                    categorizedActualCounts.getOrDefault(difficultyName, 0) + entry.getValue());
        }

        // 使用转换后的类别计数，调用通用的分布适应度计算方法
        return calculateDistributionFitness(categorizedActualCounts, targetDifficultyDistribution, totalSelectedTopics);
    }

    /**
     * (空方法，仅为演示或未来扩展保留) 性能基准测试方法。
     * 可用于评估不同参数配置下遗传算法的执行效率。
     */
    @SuppressWarnings("unused")
    public void benchmarkPaperGeneration() {
        // 此处可实现性能基准测试的相关逻辑，例如：
        // 1. 构建固定的测试用例 (availableQuestions, targetScore等)
        // 2. 多次调用 solve 方法并记录执行时间
        // 3. 分析不同参数（种群大小、迭代次数等）对性能和结果质量的影响
        log.info("Benchmark method called. Implement benchmark logic here.");
    }

    /**
     * 计算题型精确匹配适应度。
     * 不再是多样性，而是精确匹配目标题型数量分布。
     *
     * @param selectedTopics 当前染色体选中的题目列表
     * @return 题型匹配适应度，值域在 [0, 1]，1表示完全匹配目标分布
     */
    private double calculateTopicTypeDistributionFitness(List<Topic> selectedTopics) {
        if (selectedTopics == null || selectedTopics.isEmpty()) {
            return 0.0; // 如果没有选中题目，适应度为0
        }

        // 如果没有题型目标，返回中性值
        if (typeTargetCounts == null || typeTargetCounts.isEmpty()) {
            return 1.0;
        }

        // 统计当前染色体中各题型的题目数量
        Map<String, Integer> actualTypeCounts = new HashMap<>();
        for (Topic topic : selectedTopics) {
            String standardType = getStandardTopicType(topic.getType());
            actualTypeCounts.put(standardType, actualTypeCounts.getOrDefault(standardType, 0) + 1);
        }

        // 计算精确匹配适应度：实际数量与目标数量的匹配程度
        double totalDeviation = 0.0;

        for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
            String type = entry.getKey();
            int targetCount = entry.getValue();
            int actualCount = actualTypeCounts.getOrDefault(type, 0);

            if (targetCount > 0) {
                // 计算相对偏差：|实际-目标| / 目标
                double deviation = Math.abs(actualCount - targetCount) / (double) targetCount;
                totalDeviation += deviation;
            }
        }

        // 适应度 = 1 / (1 + 平均偏差)
        // 完全匹配时偏差为0，适应度为1
        // 偏差越大，适应度越低
        double avgDeviation = !typeTargetCounts.isEmpty() ? totalDeviation / typeTargetCounts.size() : 0;
        return 1.0 / (1.0 + avgDeviation);
    }

    /**
     * 将各种题型标识映射为标准的数据库格式题型标识。
     * 使用统一的TopicTypeMapper工具类确保一致性。
     *
     * @param typeKey 原始题型标识
     * @return 数据库标准格式的题型标识
     */
    private String getStandardTopicType(String typeKey) {
        if (typeKey == null) {
            return TopicTypeMapper.DB_SINGLE_CHOICE; // 默认返回单选题
        }

        // 使用统一的映射工具，转换为数据库标准格式
        return TopicTypeMapper.toDbFormat(typeKey);
    }

    /**
     * 计算知识点覆盖适应度。
     * 衡量当前选中的题目集合对目标知识点列表的覆盖程度。
     *
     * @param selectedTopics 当前染色体（解方案）选中的题目列表。
     * @param targetKnowledgeIds 试卷要求覆盖的目标知识点ID列表。
     * @return 知识点覆盖适应度。值域通常在 [0, 1]，1表示完美覆盖。
     *         当前实现：如果未指定目标知识点，则为1；如果未选出题目，则为0。
     *         若目标知识点未被全部覆盖，则适应度会根据覆盖比例打折（例如乘以0.5）。
     */
    private double calculateKnowledgePointCoverageFitness(
            List<Topic> selectedTopics,
            List<Integer> targetKnowledgeIds) {

        if (targetKnowledgeIds == null || targetKnowledgeIds.isEmpty()) {
            return 1.0; // 没有指定知识点目标，默认完美覆盖 (或不参与此项评估)
        }
        if (selectedTopics == null || selectedTopics.isEmpty()) {
            return 0.0; // 没有选出任何题目，知识点覆盖度为0
        }

        Set<Integer> coveredKnowledgeIdsBySelection = new HashSet<>();

        for (Topic topic : selectedTopics) {
            Integer knowId = topic.getKnowId(); // 假设 Topic 实体中有 getKnowId() 方法返回其所属知识点ID
            if (knowId != null && targetKnowledgeIds.contains(knowId)) {
                coveredKnowledgeIdsBySelection.add(knowId);
            }
        }

        // 计算覆盖到的目标知识点占所有目标知识点的比例
        double coverageRatio = (double) coveredKnowledgeIdsBySelection.size() / targetKnowledgeIds.size();

        // 策略：如果未能覆盖所有目标知识点，则给予惩罚
        // 例如，如果只覆盖了一半，那么适应度可能是 0.5 * (惩罚因子，如0.5) = 0.25
        // 如果全部覆盖，则适应度为1.0
        if (coveredKnowledgeIdsBySelection.size() < targetKnowledgeIds.size()) {
            return coverageRatio * 0.5; // 示例惩罚：未全覆盖则适应度减半 (基于覆盖比例)
        }

        // (未来可扩展) 如果所有目标知识点都已覆盖，可以进一步评估其均衡性
        // 例如，计算各覆盖知识点下题目数量或分数的标准差，标准差越小，均衡性越好，适应度越高。
        // double balanceFitness = calculateBalanceFitness(topicsPerCoveredKp, scorePerCoveredKp);
        // return balanceFitness; // (如果均衡性很重要)

        return coverageRatio; // 当前：如果全覆盖则为1.0，否则为打折后的覆盖比例
    }

    /**
     *  计算知识点题型均衡适应度
     * 衡量每个知识点内部题型分布的均匀性，避免某个知识点某题型过多而某题型过少
     *
     * @param selectedTopics 选中的题目列表
     * @param targetKnowledgeIds 目标知识点ID列表
     * @return 知识点题型均衡适应度，范围[0,1]，1表示完美均衡
     */
    private double calculateKnowledgePointTypeBalanceFitness(List<Topic> selectedTopics, List<Integer> targetKnowledgeIds) {
        if (selectedTopics == null || selectedTopics.isEmpty() || targetKnowledgeIds == null || targetKnowledgeIds.isEmpty()) {
            return 1.0; // 没有题目或知识点，默认完美均衡
        }

        // 按知识点分组题目
        Map<Integer, List<Topic>> topicsByKnowledgePoint = new HashMap<>();
        for (Topic topic : selectedTopics) {
            Integer knowId = topic.getKnowId();
            if (knowId != null && targetKnowledgeIds.contains(knowId)) {
                topicsByKnowledgePoint.computeIfAbsent(knowId, k -> new ArrayList<>()).add(topic);
            }
        }

        if (topicsByKnowledgePoint.isEmpty()) {
            return 0.0; // 没有匹配的知识点，均衡度为0
        }

        double totalBalanceScore = 0.0;
        int validKnowledgePoints = 0;

        // 为每个知识点计算题型均衡度
        for (Map.Entry<Integer, List<Topic>> entry : topicsByKnowledgePoint.entrySet()) {
            Integer knowledgeId = entry.getKey();
            List<Topic> kpTopics = entry.getValue();

            if (kpTopics.size() < 2) {
                // 如果知识点只有1道题，认为是完美均衡（无法不均衡）
                totalBalanceScore += 1.0;
                validKnowledgePoints++;
                continue;
            }

            // 统计该知识点的题型分布
            Map<String, Integer> typeDistribution = new HashMap<>();
            for (Topic topic : kpTopics) {
                String type = getStandardTopicType(topic.getType());
                typeDistribution.put(type, typeDistribution.getOrDefault(type, 0) + 1);
            }

            // 计算该知识点的题型均衡度
            double kpBalanceScore = calculateTypeBalanceScore(typeDistribution, kpTopics.size());
            totalBalanceScore += kpBalanceScore;
            validKnowledgePoints++;

            log.debug("Knowledge point {} type balance: {} (types: {})",
                     knowledgeId, kpBalanceScore, typeDistribution);
        }

        // 返回所有知识点的平均均衡度
        double averageBalance = validKnowledgePoints > 0 ? totalBalanceScore / validKnowledgePoints : 0.0;

        log.debug("Overall knowledge point type balance fitness: {} (valid KPs: {})",
                 averageBalance, validKnowledgePoints);

        return averageBalance;
    }

    /**
     * 计算单个知识点的题型均衡分数
     *
     * @param typeDistribution 题型分布 Map<题型, 数量>
     * @param totalTopics 总题目数
     * @return 均衡分数，范围[0,1]，1表示完美均衡
     */
    private double calculateTypeBalanceScore(Map<String, Integer> typeDistribution, int totalTopics) {
        if (typeDistribution.isEmpty() || totalTopics == 0) {
            return 1.0; // 没有题型分布，默认完美
        }

        if (typeDistribution.size() == 1) {
            return 1.0; // 只有一种题型，无法不均衡
        }

        // 计算理想的平均每种题型数量
        double idealAverage = (double) totalTopics / typeDistribution.size();

        // 计算各题型与理想平均值的偏差
        double totalDeviation = 0.0;
        for (Integer count : typeDistribution.values()) {
            double deviation = Math.abs(count - idealAverage) / idealAverage;
            totalDeviation += deviation;
        }

        // 平均偏差率
        double averageDeviation = totalDeviation / typeDistribution.size();

        // 转换为均衡分数：偏差越小，分数越高
        // 使用指数衰减函数，确保分数在[0,1]范围内
        double balanceScore = Math.exp(-averageDeviation);

        return Math.max(0.0, Math.min(1.0, balanceScore));
    }

    /**
     * 计算自适应变异率
     * 根据当前代数和停滞代数动态调整变异率
     */
    private double calculateAdaptiveMutationRate(int generation, int generationsWithoutImprovement) {
        if (!ADAPTIVE_MUTATION_ENABLED) {
            // 如果未启用自适应变异，使用传统的线性递减
            return MUTATION_RATE * Math.max(0.1, (1.0 - (double)generation / MAX_GENERATIONS * 0.9));
        }

        // 基础变异率：随进化进程线性递减
        double baseRate = ADAPTIVE_MUTATION_MIN_RATE +
                         (ADAPTIVE_MUTATION_MAX_RATE - ADAPTIVE_MUTATION_MIN_RATE) *
                         (1.0 - (double)generation / MAX_GENERATIONS);

        // 停滞惩罚：如果连续多代无改进，提高变异率
        if (generationsWithoutImprovement >= ADAPTIVE_MUTATION_STAGNATION_THRESHOLD) {
            double stagnationMultiplier = 1.0 +
                (generationsWithoutImprovement - ADAPTIVE_MUTATION_STAGNATION_THRESHOLD) * 0.1;
            baseRate = Math.min(ADAPTIVE_MUTATION_MAX_RATE, baseRate * stagnationMultiplier);
        }

        return Math.max(ADAPTIVE_MUTATION_MIN_RATE, Math.min(ADAPTIVE_MUTATION_MAX_RATE, baseRate));
    }

    /**
     * 将数据库格式的题型转换为前端格式
     * 使用统一的TopicTypeMapper工具类确保一致性
     */
    private String convertDbTypeToFrontendType(String dbType) {
        if (dbType == null) return TopicTypeMapper.FRONTEND_SINGLE_CHOICE;

        // 使用统一的TopicTypeMapper工具类进行转换
        return TopicTypeMapper.toFrontendFormat(dbType);
    }

    /**
     * 从染色体中提取选中的题目列表
     */
    private List<Topic> getSelectedTopics(Chromosome chromosome, List<Topic> availableQuestions) {
        List<Topic> selectedTopics = new ArrayList<>();
        BitSet gene = chromosome.getGene();

        for (int i = 0; i < gene.length() && i < availableQuestions.size(); i++) {
            if (gene.get(i)) {
                selectedTopics.add(availableQuestions.get(i));
            }
        }

        return selectedTopics;
    }

    /**
     * 增强的染色体类 - 使用索引数组而非BitSet以节省内存
     */
    public static class EnhancedChromosome {
        private final List<Integer> geneIndices; // 选中的题目索引列表
        private volatile double fitness;
        private volatile int totalScore;
        private final Map<String, Object> metadata; // 缓存计算结果

        public EnhancedChromosome(List<Integer> geneIndices) {
            this.geneIndices = new ArrayList<>(geneIndices);
            this.fitness = 0.0;
            this.totalScore = 0;
            this.metadata = new HashMap<>();
        }

        public List<Integer> getGeneIndices() {
            return new ArrayList<>(geneIndices);
        }

        public double getFitness() {
            return fitness;
        }

        public void setFitness(double fitness) {
            this.fitness = fitness;
        }

        public int getTotalScore() {
            return totalScore;
        }

        public void setTotalScore(int totalScore) {
            this.totalScore = totalScore;
        }

        public Map<String, Object> getMetadata() {
            return metadata;
        }

        public int size() {
            return geneIndices.size();
        }

        public boolean contains(int index) {
            return geneIndices.contains(index);
        }

        public void addGene(int index) {
            if (!geneIndices.contains(index)) {
                geneIndices.add(index);
            }
        }

        public void removeGene(int index) {
            geneIndices.remove(Integer.valueOf(index));
        }
    }

    /**
     * 初始化增强的种群
     */
    private List<EnhancedChromosome> initializeEnhancedPopulation(List<Topic> availableQuestions,
                                                               Map<String, Integer> typeTargetCounts) {
        List<EnhancedChromosome> population = new ArrayList<>();

        // 按题型分组题目索引
        Map<String, List<Integer>> indexesByType = new HashMap<>();
        for (int i = 0; i < availableQuestions.size(); i++) {
            String type = getStandardTopicType(availableQuestions.get(i).getType());
            indexesByType.computeIfAbsent(type, k -> new ArrayList<>()).add(i);
        }

        // 验证是否有足够的题目
        for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
            String type = entry.getKey();
            int required = entry.getValue();
            int available = indexesByType.getOrDefault(type, Collections.emptyList()).size();
            
            if (required > 0 && available < required) {
                log.warn("Insufficient topics for type {}: required={}, available={}", type, required, available);
            }
        }

        // 生成种群
        Random random = getThreadSafeRandom();
        for (int i = 0; i < POPULATION_SIZE; i++) {
            List<Integer> geneIndices = new ArrayList<>();

            // 为每种题型选择所需数量的题目
            for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
                String type = entry.getKey();
                int required = entry.getValue();
                List<Integer> availableIndices = indexesByType.getOrDefault(type, Collections.emptyList());

                if (required > 0 && !availableIndices.isEmpty()) {
                    List<Integer> shuffled = new ArrayList<>(availableIndices);
                    Collections.shuffle(shuffled, random);
                    
                    int toSelect = Math.min(required, shuffled.size());
                    geneIndices.addAll(shuffled.subList(0, toSelect));
                }
            }

            population.add(new EnhancedChromosome(geneIndices));
        }

        log.info("Initialized enhanced population with {} chromosomes", population.size());
        return population;
    }

    /**
     * 生成随机的增强染色体
     */
    private EnhancedChromosome generateRandomEnhancedChromosome(List<Topic> availableQuestions,
                                                              Map<String, Integer> typeTargetCounts) {
        List<Integer> geneIndices = new ArrayList<>();
        Random random = getThreadSafeRandom();

        // 按题型分组
        Map<String, List<Integer>> indexesByType = new HashMap<>();
        for (int i = 0; i < availableQuestions.size(); i++) {
            String type = getStandardTopicType(availableQuestions.get(i).getType());
            indexesByType.computeIfAbsent(type, k -> new ArrayList<>()).add(i);
        }

        // 随机选择每种题型的题目
        for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
            String type = entry.getKey();
            int required = entry.getValue();
            List<Integer> availableIndices = indexesByType.getOrDefault(type, Collections.emptyList());

            if (required > 0 && !availableIndices.isEmpty()) {
                List<Integer> shuffled = new ArrayList<>(availableIndices);
                Collections.shuffle(shuffled, random);
                
                int toSelect = Math.min(required, shuffled.size());
                geneIndices.addAll(shuffled.subList(0, toSelect));
            }
        }

        return new EnhancedChromosome(geneIndices);
    }

    /**
     * 增强的种群评估
     */
    private void evaluatePopulationEnhanced(List<EnhancedChromosome> population,
                                          List<Topic> questions,
                                          int targetScore,
                                          Map<String, Integer> typeTargetCounts,
                                          Map<String, Double> difficultyDistributionTarget,
                                          Map<String, Double> cognitiveLevelDistributionTarget,
                                          Map<Integer, TopicEnhancementData> enhancementDataMap,
                                          List<Integer> targetKnowledgeIds,
                                          List<com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest> knowledgePointConfigs,
                                          Map<String, Integer> typeScoreMap) {
        
        initializeThreadPool(); // 确保线程池可用
        
        try {
            // 并行评估
            population.parallelStream().forEach(chromosome -> {
                evaluateEnhancedChromosome(chromosome, questions, targetScore, typeTargetCounts,
                                         difficultyDistributionTarget, cognitiveLevelDistributionTarget,
                                         enhancementDataMap, targetKnowledgeIds, knowledgePointConfigs, typeScoreMap);
            });
        } catch (Exception e) {
            log.error("Parallel evaluation failed, falling back to sequential: {}", e.getMessage());
            // 回退到串行评估
            for (EnhancedChromosome chromosome : population) {
                evaluateEnhancedChromosome(chromosome, questions, targetScore, typeTargetCounts,
                                         difficultyDistributionTarget, cognitiveLevelDistributionTarget,
                                         enhancementDataMap, targetKnowledgeIds, knowledgePointConfigs, typeScoreMap);
            }
        }
    }

    /**
     * 增强染色体适应度评估
     */
    private void evaluateEnhancedChromosome(EnhancedChromosome chromosome,
                                          List<Topic> questions,
                                          int targetScore,
                                          Map<String, Integer> typeTargetCounts,
                                          Map<String, Double> difficultyDistributionTarget,
                                          Map<String, Double> cognitiveLevelDistributionTarget,
                                          Map<Integer, TopicEnhancementData> enhancementDataMap,
                                          List<Integer> targetKnowledgeIds,
                                          List<com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest> knowledgePointConfigs,
                                          Map<String, Integer> typeScoreMap) {

        List<Topic> selectedTopics = chromosome.getGeneIndices().stream()
            .map(questions::get)
            .collect(Collectors.toList());

        if (selectedTopics.isEmpty()) {
            chromosome.setFitness(0.0);
            return;
        }

        // 计算各项适应度组件
        double scoreFitness = calculateScoreFitnessEnhanced(selectedTopics, targetScore, typeScoreMap);
        double qualityFitness = calculateQualityFitnessEnhanced(selectedTopics, enhancementDataMap);
        double difficultyFitness = calculateDifficultyFitnessEnhanced(selectedTopics, difficultyDistributionTarget);
        double cognitiveFitness = calculateCognitiveFitnessEnhanced(selectedTopics, cognitiveLevelDistributionTarget);
        double kpCoverageFitness = calculateKpCoverageFitnessEnhanced(selectedTopics, targetKnowledgeIds);
        double typeDiversityFitness = calculateTypeDiversityFitnessEnhanced(selectedTopics, typeTargetCounts);
        double kpBalanceFitness = calculateKpBalanceFitnessEnhanced(selectedTopics, targetKnowledgeIds);

        // 归一化并加权合成
        double totalFitness = 
            WEIGHT_SCORE * normalizeFitness(scoreFitness, "score") +
            WEIGHT_QUALITY * normalizeFitness(qualityFitness, "quality") +
            WEIGHT_DIFFICULTY_DIST * difficultyFitness +
            WEIGHT_COGNITIVE_DIST * cognitiveFitness +
            WEIGHT_KP_COVERAGE * kpCoverageFitness +
            WEIGHT_TOPIC_TYPE_DIVERSITY * typeDiversityFitness +
            WEIGHT_KP_TYPE_BALANCE * kpBalanceFitness;

        chromosome.setFitness(totalFitness);
        
        // 计算总分
        int totalScore = selectedTopics.stream()
            .mapToInt(topic -> getTopicScore(topic, typeScoreMap))
            .sum();
        chromosome.setTotalScore(totalScore);
    }

    /**
     * 归一化适应度值
     */
    private double normalizeFitness(double rawFitness, String component) {
        Double factor = fitnessNormalizationFactors.get(component);
        if (factor == null || factor == 0.0) {
            return rawFitness;
        }
        return Math.min(1.0, rawFitness / factor);
    }

    /**
     * 增强的锦标赛选择
     */
    private EnhancedChromosome tournamentSelectionEnhanced(List<EnhancedChromosome> population) {
        if (population.isEmpty()) {
            throw new IllegalArgumentException("Population cannot be empty");
        }

        Random random = getThreadSafeRandom();
        List<EnhancedChromosome> tournament = new ArrayList<>();

        for (int i = 0; i < TOURNAMENT_SIZE; i++) {
            tournament.add(population.get(random.nextInt(population.size())));
        }

        return Collections.max(tournament, Comparator.comparing(EnhancedChromosome::getFitness));
    }

    /**
     * 增强的交叉操作
     */
    private EnhancedChromosome crossoverEnhanced(EnhancedChromosome parent1, EnhancedChromosome parent2,
                                               List<Topic> questions, Map<String, Integer> typeTargetCounts) {
        Random random = getThreadSafeRandom();
        
        if (random.nextDouble() > CROSSOVER_RATE) {
            return random.nextBoolean() ? 
                new EnhancedChromosome(parent1.getGeneIndices()) : 
                new EnhancedChromosome(parent2.getGeneIndices());
        }

        // 按题型进行交叉
        List<Integer> childGenes = new ArrayList<>();
        
        // 按题型分组父代基因
        Map<String, List<Integer>> parent1ByType = groupGenesByType(parent1.getGeneIndices(), questions);
        Map<String, List<Integer>> parent2ByType = groupGenesByType(parent2.getGeneIndices(), questions);

        for (Map.Entry<String, Integer> entry : typeTargetCounts.entrySet()) {
            String type = entry.getKey();
            int required = entry.getValue();

            List<Integer> p1Genes = parent1ByType.getOrDefault(type, Collections.emptyList());
            List<Integer> p2Genes = parent2ByType.getOrDefault(type, Collections.emptyList());

            // 混合选择
            Set<Integer> combined = new HashSet<>(p1Genes);
            combined.addAll(p2Genes);
            
            List<Integer> available = new ArrayList<>(combined);
            Collections.shuffle(available, random);
            
            int toSelect = Math.min(required, available.size());
            childGenes.addAll(available.subList(0, toSelect));
        }

        return new EnhancedChromosome(childGenes);
    }

    /**
     * 增强的变异操作
     */
    private void mutateEnhanced(EnhancedChromosome chromosome, List<Topic> questions,
                              Map<String, Integer> typeTargetCounts, double mutationRate) {
        Random random = getThreadSafeRandom();
        
        if (random.nextDouble() > mutationRate) {
            return;
        }

        // 按题型分组可用题目
        Map<String, List<Integer>> availableByType = new HashMap<>();
        for (int i = 0; i < questions.size(); i++) {
            String type = getStandardTopicType(questions.get(i).getType());
            availableByType.computeIfAbsent(type, k -> new ArrayList<>()).add(i);
        }

        // 随机选择一个题型进行变异
        List<String> types = new ArrayList<>(typeTargetCounts.keySet());
        if (types.isEmpty()) return;
        
        String selectedType = types.get(random.nextInt(types.size()));
        List<Integer> currentGenes = chromosome.getGeneIndices().stream()
            .filter(idx -> getStandardTopicType(questions.get(idx).getType()).equals(selectedType))
            .collect(Collectors.toList());

        List<Integer> availableGenes = availableByType.getOrDefault(selectedType, Collections.emptyList());
        List<Integer> candidates = availableGenes.stream()
            .filter(idx -> !currentGenes.contains(idx))
            .collect(Collectors.toList());

        // 执行1对1替换变异
        if (!currentGenes.isEmpty() && !candidates.isEmpty()) {
            int toRemove = currentGenes.get(random.nextInt(currentGenes.size()));
            int toAdd = candidates.get(random.nextInt(candidates.size()));
            
            chromosome.removeGene(toRemove);
            chromosome.addGene(toAdd);
        }
    }

    /**
     * 按题型分组基因
     */
    private Map<String, List<Integer>> groupGenesByType(List<Integer> genes, List<Topic> questions) {
        Map<String, List<Integer>> grouped = new HashMap<>();
        
        for (Integer geneIndex : genes) {
            if (geneIndex < questions.size()) {
                String type = getStandardTopicType(questions.get(geneIndex).getType());
                grouped.computeIfAbsent(type, k -> new ArrayList<>()).add(geneIndex);
            }
        }
        
        return grouped;
    }

    /**
     * 从增强染色体提取解
     */
    private List<Topic> extractSolutionFromEnhanced(EnhancedChromosome chromosome, List<Topic> questions) {
        return chromosome.getGeneIndices().stream()
            .filter(idx -> idx < questions.size())
            .map(questions::get)
            .collect(Collectors.toList());
    }

    /**
     * 汉明距离计算 - 适用于索引列表
     */
    private double hammingDistance(List<Integer> indices1, List<Integer> indices2) {
        Set<Integer> set1 = new HashSet<>(indices1);
        Set<Integer> set2 = new HashSet<>(indices2);
        
        Set<Integer> union = new HashSet<>(set1);
        union.addAll(set2);
        
        Set<Integer> intersection = new HashSet<>(set1);
        intersection.retainAll(set2);
        
        if (union.isEmpty()) return 0.0;
        
        return 1.0 - (double) intersection.size() / union.size();
    }
}