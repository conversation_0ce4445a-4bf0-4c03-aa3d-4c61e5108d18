package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.dto.UserStatsDTO;

/**
 * 用户统计缓存服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-27
 */
public interface UserStatsCacheService {
    
    /**
     * 获取用户统计数据（优先从缓存获取）
     * 
     * @param userId 用户ID
     * @return 用户统计数据
     */
    UserStatsDTO getUserStats(Long userId);
    
    /**
     * 刷新用户统计缓存
     * 
     * @param userId 用户ID
     */
    void refreshUserStatsCache(Long userId);
    
    /**
     * 清除用户统计缓存
     * 
     * @param userId 用户ID
     */
    void clearUserStatsCache(Long userId);
    
    /**
     * 批量刷新活跃用户的统计缓存
     */
    void refreshActiveUsersCache();
    
    /**
     * 获取缓存统计信息
     */
    CacheStats getCacheStats();
    
    /**
     * 缓存统计信息
     */
    class CacheStats {
        private long hitCount;
        private long missCount;
        private long cacheSize;
        private double hitRate;
        
        public CacheStats(long hitCount, long missCount, long cacheSize) {
            this.hitCount = hitCount;
            this.missCount = missCount;
            this.cacheSize = cacheSize;
            this.hitRate = hitCount + missCount > 0 ? (double) hitCount / (hitCount + missCount) : 0.0;
        }
        
        // Getters
        public long getHitCount() { return hitCount; }
        public long getMissCount() { return missCount; }
        public long getCacheSize() { return cacheSize; }
        public double getHitRate() { return hitRate; }
    }
}
