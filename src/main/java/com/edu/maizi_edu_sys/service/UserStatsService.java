package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.dto.UserStatsDTO;
import com.edu.maizi_edu_sys.dto.UserTrendDTO;
import com.edu.maizi_edu_sys.dto.UserTopicTypeDistributionDTO;

import java.util.List;

/**
 * 用户统计服务接口
 */
public interface UserStatsService {
    
    /**
     * 获取用户个人统计数据
     */
    UserStatsDTO getUserStats(Long userId);
    
    /**
     * 获取用户上传趋势数据
     */
    UserTrendDTO getUserUploadTrend(Long userId, int days);
    
    /**
     * 获取用户题目类型分布
     */
    List<UserTopicTypeDistributionDTO> getUserTopicTypeDistribution(Long userId);
} 