package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.entity.Paper;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.dto.ComprehensivePaperOverviewDTO;
import com.edu.maizi_edu_sys.dto.PaperWithTopicsDTO;
import com.edu.maizi_edu_sys.dto.PaperDetailDTO;
import com.edu.maizi_edu_sys.dto.PaperStatisticsDTO;
import com.edu.maizi_edu_sys.dto.BatchEditDTO;
import com.edu.maizi_edu_sys.util.PaginationResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * Paper Service Interface
 * 处理与试卷相关的逻辑
 */
public interface PaperService {

    /**
     * 解析题目选项
     * @param optionsJson 选项JSON字符串
     * @return 选项列表
     */
    List<Map<String, String>> parseOptions(String optionsJson);

    /**
     * 解析组合题的子题目
     * @param subsJson 子题目JSON字符串
     * @return 子题目列表
     */
    List<Topic> parseSubTopics(String subsJson);

    PaperWithTopicsDTO getPaperWithTopicsDTOById(Long paperId);

    PaginationResponse<Paper> getPapersWithPagination(Integer userId, int page, int size, String sort, String search, String type);

    ComprehensivePaperOverviewDTO getComprehensivePaperOverview(Long paperId);

    /**
     * 获取试卷详细信息，包括基本信息、题目列表、类型、时间戳和难度分布
     * @param paperId 试卷ID
     * @return 试卷详情DTO
     */
    PaperDetailDTO getPaperDetail(Long paperId);

    // ========== 历史试卷管理相关方法 ==========

    /**
     * 根据筛选条件查询试卷
     * @param filters 筛选条件
     * @param pageable 分页信息
     * @return 试卷分页结果
     */
    Page<Paper> findPapersWithFilters(Map<String, Object> filters, Pageable pageable);

    /**
     * 获取试卷统计信息
     * @return 统计信息DTO
     */
    PaperStatisticsDTO getPaperStatistics();

    /**
     * 根据用户ID获取试卷统计信息
     * @param userId 用户ID
     * @return 统计信息DTO
     */
    PaperStatisticsDTO getPaperStatisticsByUser(Long userId);

    /**
     * 验证试卷所有权
     * @param paperIds 试卷ID列表
     * @param userId 用户ID
     * @return 是否有权限
     */
    boolean validatePaperOwnership(List<Long> paperIds, Long userId);

    /**
     * 批量编辑试卷
     * @param batchEditDTO 批量编辑数据
     * @return 更新的试卷数量
     */
    int batchEditPapers(BatchEditDTO batchEditDTO);

    /**
     * 导出试卷数据到Excel
     * @param paperIds 试卷ID列表
     * @return Excel文件字节数组
     */
    byte[] exportPapersToExcel(List<Long> paperIds);

    /**
     * 克隆试卷
     * @param paperId 原试卷ID
     * @param userId 用户ID
     * @return 克隆的试卷
     */
    Paper clonePaper(Long paperId, Long userId);

    /**
     * 根据ID获取试卷
     * @param paperId 试卷ID
     * @return 试卷实体
     */
    Paper getPaperById(Long paperId);

    /**
     * 删除试卷
     * @param paperId 试卷ID
     * @return 是否删除成功
     */
    boolean deletePaper(Long paperId);

    /**
     * 生成批量下载资源
     * @param paperIds 试卷ID列表
     * @param format 文件格式
     * @param version 试卷版本
     * @return 下载资源字节数组
     */
    byte[] generateBatchDownloadResource(List<Long> paperIds, String format, String version);

    /**
     * 批量删除试卷
     * @param paperIds 试卷ID列表
     * @param userId 用户ID
     * @return 删除的试卷数量
     */
    int batchDeletePapers(List<Long> paperIds, Long userId);

    /**
     * 批量下载试卷
     * @param paperIds 试卷ID列表
     * @param format 文件格式
     * @param version 试卷版本
     * @param userId 用户ID
     * @return 下载资源字节数组
     */
    byte[] batchDownloadPapers(List<Long> paperIds, String format, String version, Long userId);
}