package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.config.MineRuProperties;
import com.edu.maizi_edu_sys.repository.MineRuApiUsageRepository;

/**
 * MineRU API密钥管理服务接口
 * 
 * <AUTHOR> Integration
 * @since 2025-01-20
 */
public interface MineRuTokenManager {
    
    /**
     * 获取下一个可用的API密钥
     * 
     * @param estimatedPages 预估页数
     * @return API密钥配置，如果没有可用密钥返回null
     */
    MineRuProperties.Api.TokenConfig getNextAvailableToken(int estimatedPages);
    
    /**
     * 记录API调用结果
     * 
     * @param tokenName 密钥名称
     * @param parsedPages 解析页数
     * @param success 是否成功
     */
    void recordApiUsage(String tokenName, int parsedPages, boolean success);
    
    /**
     * 检查密钥是否可用
     * 
     * @param tokenName 密钥名称
     * @return 是否可用
     */
    boolean isTokenAvailable(String tokenName);
    
    /**
     * 获取密钥当日剩余高优先级页数
     * 
     * @param tokenName 密钥名称
     * @return 剩余页数
     */
    int getRemainingPriorityPages(String tokenName);
    
    /**
     * 标记密钥为不可用（故障转移）
     * 
     * @param tokenName 密钥名称
     * @param reason 原因
     */
    void markTokenUnavailable(String tokenName, String reason);
    
    /**
     * 恢复密钥可用状态
     * 
     * @param tokenName 密钥名称
     */
    void markTokenAvailable(String tokenName);
    
    /**
     * 获取所有密钥的使用统计
     * 
     * @return 使用统计列表
     */
    java.util.List<MineRuApiUsageRepository.TokenUsageSummary> getUsageStatistics();
    
    /**
     * 清理过期的使用记录
     * 
     * @param retentionDays 保留天数
     */
    void cleanupExpiredRecords(int retentionDays);
}
