package com.edu.maizi_edu_sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.DocumentParseResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 文档解析结果Mapper
 * 
 * <AUTHOR> Integration
 * @since 2025-01-20
 */
@Mapper
public interface DocumentParseResultMapper extends BaseMapper<DocumentParseResult> {

    /**
     * 根据任务ID查询解析结果
     */
    @Select("SELECT * FROM document_parse_result WHERE task_id = #{taskId} AND deleted = 0 ORDER BY created_at")
    List<DocumentParseResult> selectByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据任务ID和结果类型查询解析结果
     */
    @Select("SELECT * FROM document_parse_result WHERE task_id = #{taskId} AND result_type = #{resultType} AND deleted = 0")
    DocumentParseResult selectByTaskIdAndType(@Param("taskId") Long taskId, @Param("resultType") String resultType);
}
