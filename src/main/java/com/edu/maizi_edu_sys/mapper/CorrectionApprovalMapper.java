package com.edu.maizi_edu_sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.CorrectionApproval;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface CorrectionApprovalMapper extends BaseMapper<CorrectionApproval> {

    /**
     * 查询待审核的修正记录
     */
    List<CorrectionApproval> getPendingApprovals();

    /**
     * 查询已过期但未处理的记录
     */
    @Select("SELECT * FROM correction_approval WHERE status = 0 AND expires_at < NOW()")
    List<CorrectionApproval> getExpiredApprovals();

    /**
     * 批量更新过期记录状态
     */
    @Update("UPDATE correction_approval SET status = 3 WHERE status = 0 AND expires_at < NOW()")
    int markExpiredApprovals();

    /**
     * 审核通过修正
     */
    int approveCorrection(@Param("approvalId") Long approvalId, @Param("approver") String approver, @Param("comment") String comment, @Param("approvedAt") LocalDateTime approvedAt);

    /**
     * 拒绝修正
     */
    int rejectCorrection(@Param("approvalId") Long approvalId, @Param("approver") String approver, @Param("comment") String comment, @Param("rejectedAt") LocalDateTime rejectedAt);

    /**
     * 根据日期查询审核记录
     */
    @Select("SELECT * FROM correction_approval WHERE correction_date = #{date} ORDER BY created_at DESC")
    List<CorrectionApproval> getApprovalsByDate(@Param("date") String date);

    /**
     * 分页查询待审核记录
     */
    List<CorrectionApproval> getPendingApprovalsWithPagination(@Param("offset") int offset, @Param("size") int size, @Param("search") String search, @Param("status") String status);

    /**
     * 统计待审核记录数量
     */
    int countPendingApprovals(@Param("search") String search, @Param("status") String status);
}