package com.edu.maizi_edu_sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.DocumentParseTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文档解析任务Mapper
 * 
 * <AUTHOR> Integration
 * @since 2025-01-20
 */
@Mapper
public interface DocumentParseTaskMapper extends BaseMapper<DocumentParseTask> {

    /**
     * 根据任务ID查询任务
     */
    @Select("SELECT * FROM document_parse_task WHERE task_id = #{taskId} AND deleted = 0")
    DocumentParseTask selectByTaskId(@Param("taskId") String taskId);

    /**
     * 根据批次ID查询任务列表
     */
    @Select("SELECT * FROM document_parse_task WHERE batch_id = #{batchId} AND deleted = 0 ORDER BY created_at")
    List<DocumentParseTask> selectByBatchId(@Param("batchId") String batchId);

    /**
     * 根据用户ID和状态查询任务列表
     */
    @Select("SELECT * FROM document_parse_task WHERE user_id = #{userId} AND status = #{status} AND deleted = 0 ORDER BY created_at DESC")
    List<DocumentParseTask> selectByUserIdAndStatus(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 更新任务状态
     */
    @Update("UPDATE document_parse_task SET status = #{status}, updated_at = #{updateTime} WHERE task_id = #{taskId}")
    int updateStatusByTaskId(@Param("taskId") String taskId, @Param("status") String status, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新任务进度
     */
    @Update("UPDATE document_parse_task SET extracted_pages = #{extractedPages}, total_pages = #{totalPages}, updated_at = #{updateTime} WHERE task_id = #{taskId}")
    int updateProgressByTaskId(@Param("taskId") String taskId, @Param("extractedPages") Integer extractedPages, 
                               @Param("totalPages") Integer totalPages, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新任务结果
     */
    @Update("UPDATE document_parse_task SET status = #{status}, result_zip_url = #{resultZipUrl}, complete_time = #{completeTime}, updated_at = #{updateTime} WHERE task_id = #{taskId}")
    int updateResultByTaskId(@Param("taskId") String taskId, @Param("status") String status, 
                             @Param("resultZipUrl") String resultZipUrl, @Param("completeTime") LocalDateTime completeTime, 
                             @Param("updateTime") LocalDateTime updateTime);

    /**
     * 查询待同步的任务 (状态为pending或running)
     */
    @Select("SELECT * FROM document_parse_task WHERE status IN ('pending', 'running', 'converting') AND deleted = 0 ORDER BY created_at")
    List<DocumentParseTask> selectPendingTasks();
}
