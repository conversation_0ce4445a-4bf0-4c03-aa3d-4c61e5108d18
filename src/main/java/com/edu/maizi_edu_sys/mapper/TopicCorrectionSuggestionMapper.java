package com.edu.maizi_edu_sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.TopicCorrectionSuggestion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Insert;

import java.util.List;

/**
 * 题目修正建议Mapper接口
 */
@Mapper
public interface TopicCorrectionSuggestionMapper extends BaseMapper<TopicCorrectionSuggestion> {
    
    /**
     * 批量插入修正建议
     */
    int batchInsert(@Param("suggestions") List<TopicCorrectionSuggestion> suggestions);
    
    /**
     * 根据批次ID查询修正建议
     */
    @Select("SELECT * FROM topic_correction_suggestion WHERE batch_id = #{batchId} ORDER BY created_at DESC")
    List<TopicCorrectionSuggestion> selectByBatchId(@Param("batchId") String batchId);
    
    /**
     * 根据题目ID查询修正建议
     */
    @Select("SELECT * FROM topic_correction_suggestion WHERE topic_id = #{topicId} ORDER BY created_at DESC")
    List<TopicCorrectionSuggestion> selectByTopicId(@Param("topicId") Long topicId);
    
    /**
     * 根据状态查询修正建议
     */
    @Select("SELECT * FROM topic_correction_suggestion WHERE status = #{status} ORDER BY created_at DESC")
    List<TopicCorrectionSuggestion> selectByStatus(@Param("status") String status);
    
    /**
     * 根据日期范围查询修正建议
     */
    @Select("SELECT * FROM topic_correction_suggestion WHERE DATE(created_at) = #{date} ORDER BY created_at DESC")
    List<TopicCorrectionSuggestion> selectByDate(@Param("date") String date);
    
    /**
     * 根据题目ID列表更新状态
     */
    int updateStatusByTopicIds(@Param("topicIds") List<Long> topicIds, @Param("status") String status);
    
    /**
     * 根据批次ID更新状态
     */
    int updateStatusByBatchId(@Param("batchId") String batchId, @Param("status") String status);
}