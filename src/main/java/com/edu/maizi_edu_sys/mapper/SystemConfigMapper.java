package com.edu.maizi_edu_sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.SystemConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 系统配置Mapper接口
 */
@Mapper
public interface SystemConfigMapper extends BaseMapper<SystemConfig> {
    
    /**
     * 根据配置键获取配置值
     */
    @Select("SELECT config_value FROM system_config WHERE config_key = #{configKey}")
    String getConfigValue(@Param("configKey") String configKey);
    
    /**
     * 根据配置键获取配置值（悲观锁）
     * 用于防止并发修改，确保数据一致性
     */
    @Select("SELECT config_value FROM system_config WHERE config_key = #{configKey} FOR UPDATE")
    String getConfigValueWithLock(@Param("configKey") String configKey);
    
    /**
     * 更新配置值
     */
    @Update("UPDATE system_config SET config_value = #{configValue}, updated_at = NOW() WHERE config_key = #{configKey}")
    int updateConfigValue(@Param("configKey") String configKey, @Param("configValue") String configValue);
    
    /**
     * 插入或更新配置
     */
    @Update("INSERT INTO system_config (config_key, config_value, description, created_at, updated_at) " +
            "VALUES (#{configKey}, #{configValue}, #{description}, NOW(), NOW()) " +
            "ON DUPLICATE KEY UPDATE config_value = #{configValue}, updated_at = NOW()")
    int insertOrUpdateConfig(@Param("configKey") String configKey, 
                           @Param("configValue") String configValue, 
                           @Param("description") String description);
    
    /**
     * 删除配置
     */
    @Update("DELETE FROM system_config WHERE config_key = #{configKey}")
    int deleteConfig(@Param("configKey") String configKey);
}