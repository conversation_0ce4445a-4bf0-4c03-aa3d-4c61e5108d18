package com.edu.maizi_edu_sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.edu.maizi_edu_sys.entity.Topic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.mapping.ResultSetType;

import java.util.List;
import java.util.Map;

@Mapper
public interface CorrectionTopicMapper extends BaseMapper<Topic> {

    // 原有的基于时间和ID的查询方法（保留兼容性）
    @Select("SELECT id, know_id, type, title, tags, options, subs, answer, source, parse, difficulty, created_at FROM topic_bak WHERE corrected = 0 AND (created_at, id) > (#{lastTime}, #{lastId}) ORDER BY created_at ASC, id ASC LIMIT #{limit}")
    @Options(fetchSize = 500, resultSetType = ResultSetType.FORWARD_ONLY)
    List<Topic> getNextBatch(@Param("lastTime") String lastTime, @Param("lastId") int lastId, @Param("limit") int limit);

    // 高效的基于ID范围的查询方法
    @Select("SELECT id, know_id, type, title, tags, options, subs, answer, source, parse, difficulty, created_at FROM topic_bak WHERE corrected = 0 AND id > #{lastId} ORDER BY id ASC LIMIT #{limit}")
    @Options(fetchSize = 500, resultSetType = ResultSetType.FORWARD_ONLY)
    List<Topic> getNextBatchById(@Param("lastId") int lastId, @Param("limit") int limit);

    // 获取指定ID范围内待处理的题目数量
    @Select("SELECT COUNT(*) FROM topic_bak WHERE corrected = 0 AND id > #{lastId}")
    int countPendingTopics(@Param("lastId") int lastId);

    // 批量原子性更新：将指定ID列表的题目标记为处理中
    @Update("<script>" +
            "UPDATE topic_bak SET corrected = 2 WHERE corrected = 0 AND id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int claimTopicsBatch(@Param("ids") List<Integer> ids);

    // 恢复处理中状态的题目（系统重启时使用）
    @Update("UPDATE topic_bak SET corrected = 0 WHERE corrected = 2")
    int resetProcessingTopics();

    // 获取处理中状态的题目数量（用于监控）
    @Select("SELECT COUNT(*) FROM topic_bak WHERE corrected = 2")
    int countProcessingTopics();

    // 第一阶段：抢占批次数据
    @Update("UPDATE topic_bak SET corrected = 2 WHERE corrected = 0 ORDER BY created_at ASC, id ASC LIMIT #{batchSize}")
    int claimBatch(@Param("batchSize") int batchSize);

    // 第二阶段：获取抢占的数据
    @Select("SELECT id, know_id, type, title, tags, options, subs, answer, source, parse, difficulty, created_at FROM topic_bak WHERE corrected = 2 ORDER BY id ASC LIMIT #{limit}")
    List<Topic> getClaimedBatch(@Param("limit") int limit);

    // 标记题目为已校对完成
    @Update("<script>" +
            "UPDATE topic_bak SET corrected = 1 WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int markTopicsCorrected(@Param("ids") List<Integer> ids);
    
    // 标记题目为失败状态（需要人工处理）
    @Update("<script>" +
            "UPDATE topic_bak SET corrected = -1 WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int markTopicsAsFailed(@Param("ids") List<Integer> ids);

    /**
     * 动态更新单个题目的指定字段
     * @param id 题目ID
     * @param updates 要更新的字段映射，key为字段名，value为新值
     * @return 影响的行数
     */
    @Update("<script>" +
            "UPDATE topic_bak " +
            "<set>" +
            "<if test='updates.type != null'>" +
            "type = #{updates.type}," +
            "</if>" +
            "<if test='updates.title != null'>" +
            "title = #{updates.title}," +
            "</if>" +
            "<if test='updates.options != null'>" +
            "options = #{updates.options}," +
            "</if>" +
            "<if test='updates.subs != null'>" +
            "subs = #{updates.subs}," +
            "</if>" +
            "<if test='updates.answer != null'>" +
            "answer = #{updates.answer}," +
            "</if>" +
            "<if test='updates.parse != null'>" +
            "parse = #{updates.parse}," +
            "</if>" +
            "<if test='updates.difficulty != null'>" +
            "difficulty = #{updates.difficulty}," +
            "</if>" +
            "</set>" +
            "WHERE id = #{id}" +
            "</script>")
    int updateTopicFields(@Param("id") Integer id, @Param("updates") Map<String, Object> updates);

    /**
     * 批量动态更新题目字段
     * @param corrections 修正列表，每个元素包含id和updates
     * @return 总计影响的行数
     */
    @Update("<script>" +
            "<foreach collection='corrections' item='correction' separator=';'>" +
            "UPDATE topic_bak " +
            "<set>" +
            "<if test='correction.updates.type != null'>" +
            "type = #{correction.updates.type}," +
            "</if>" +
            "<if test='correction.updates.title != null'>" +
            "title = #{correction.updates.title}," +
            "</if>" +
            "<if test='correction.updates.options != null'>" +
            "options = #{correction.updates.options}," +
            "</if>" +
            "<if test='correction.updates.subs != null'>" +
            "subs = #{correction.updates.subs}," +
            "</if>" +
            "<if test='correction.updates.answer != null'>" +
            "answer = #{correction.updates.answer}," +
            "</if>" +
            "<if test='correction.updates.parse != null'>" +
            "parse = #{correction.updates.parse}," +
            "</if>" +
            "<if test='correction.updates.difficulty != null'>" +
            "difficulty = #{correction.updates.difficulty}," +
            "</if>" +
            "</set>" +
            "WHERE id = #{correction.id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateTopicFields(@Param("corrections") List<Map<String, Object>> corrections);
}
