2025-07-27 14:28:30.544 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-27 14:28:30.619 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 9192 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-27 14:28:30.626 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-27 14:28:31.897 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 14:28:31.900 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-27 14:28:31.961 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-27 14:28:31.962 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 45 ms. Found 0 Redis repository interfaces.
2025-07-27 14:28:32.590 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-27 14:28:32.599 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-27 14:28:32.599 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 14:28:32.600 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-27 14:28:32.871 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 14:28:32.872 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2200 ms
2025-07-27 14:28:33.098 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-27 14:28:33.100 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-27 14:28:33.100 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-27 14:28:33.103 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-27 14:28:33.134 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-27 14:28:33.246 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-27 14:28:33.618 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.622 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.627 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:28:33.644 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 14:28:33.647 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 14:28:33.664 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.664 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.665 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.669 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:28:33.797 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.798 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.799 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.799 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.799 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.799 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.800 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:33.800 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:35.672 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-27 14:28:35.676 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-27 14:28:36.799 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-27 14:28:36.943 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-27 14:28:37.080 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:28:37.314 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 14:28:37.315 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-27 14:28:37.443 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 5
2025-07-27 14:28:37.513 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 14:28:37.805 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 14:28:38.174 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - RedisTemplate configured successfully
2025-07-27 14:28:38.461 [main] INFO  org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-27 14:28:38.484 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Configuring resource handler: path=/uploads/**, location=file:D:\IdeaProject\maizi_edu_sys\.\.\uploads\
2025-07-27 14:28:38.977 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-27 14:28:39.027 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-07-27 14:28:39.054 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-27 14:28:39.056 [main] INFO  org.springframework.web.SimpLogging - Starting...
2025-07-27 14:28:39.056 [main] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@270b8c2a]]
2025-07-27 14:28:39.056 [main] INFO  org.springframework.web.SimpLogging - Started.
2025-07-27 14:28:39.070 [main] INFO  com.edu.maizi_edu_sys.Application - Started Application in 9.024 seconds (JVM running for 11.549)
2025-07-27 14:28:39.075 [main] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 题目自动校对独立服务启动，调度间隔 2 分钟，批大小 5
2025-07-27 14:28:39.115 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377401
2025-07-27 14:28:39.131 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 5 个题目进行处理
2025-07-27 14:28:39.231 [RMI TCP Connection(1)-************] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 14:28:39.231 [RMI TCP Connection(1)-************] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 14:28:39.234 [RMI TCP Connection(1)-************] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-27 14:29:11.332 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 259
2025-07-27 14:29:11.336 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回有效JSON，长度: 259
2025-07-27 14:29:11.337 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 解析到 1 项修正建议，其中 1 项有效
2025-07-27 14:29:11.378 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.CorrectionApprovalService - 保存待审核修正记录，ID: 2329, 日期: 2025-07-27, 过期时间: 2025-09-25 14:29:11
2025-07-27 14:29:11.378 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 已保存 1 条修正建议待审核，审核ID: 2329
2025-07-27 14:29:11.381 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377406
2025-07-27 14:29:11.384 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377406的重试计数
2025-07-27 14:29:11.446 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 5 个题目，服务将在 2 分钟后处理下一批。
2025-07-27 14:30:39.077 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 开始处理一批题目，上次处理的ID: 377406
2025-07-27 14:30:39.079 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 获取到 5 个题目进行处理
2025-07-27 14:31:03.561 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回结果长度: 2
2025-07-27 14:31:03.561 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示这批题目没有发现错误
2025-07-27 14:31:03.561 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI返回空数组，表示没有发现错误
2025-07-27 14:31:03.561 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - AI未发现任何错误，这批题目无需修正
2025-07-27 14:31:03.564 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 更新配置成功: topic_correction_last_processed_id = 377411
2025-07-27 14:31:03.565 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.service.impl.SystemConfigServiceImpl - 清除批次377411的重试计数
2025-07-27 14:31:03.579 [correction-exec(core=1,max=1)-1] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - 本批次处理完成，处理了 5 个题目，服务将在 2 分钟后处理下一批。
2025-07-27 14:32:38.000 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopping...
2025-07-27 14:32:38.000 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@270b8c2a]]
2025-07-27 14:32:38.000 [SpringApplicationShutdownHook] INFO  org.springframework.web.SimpLogging - Stopped.
2025-07-27 14:32:38.872 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.runner.TopicCorrectionRunner - TopicCorrectionRunner 即将关闭，等待正在执行的任务完成...
2025-07-27 14:32:38.873 [SpringApplicationShutdownHook] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-27 14:32:38.876 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-27 14:32:38.881 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-27 14:45:59.535 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-27 14:45:59.609 [main] INFO  com.edu.maizi_edu_sys.Application - Starting Application using Java 1.8.0_221 on USER-20230226QO with PID 8868 (D:\IdeaProject\maizi_edu_sys\target\classes started by Administrator in D:\IdeaProject\maizi_edu_sys)
2025-07-27 14:45:59.613 [main] INFO  com.edu.maizi_edu_sys.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-27 14:46:00.865 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 14:46:00.868 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-27 14:46:00.932 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.edu.maizi_edu_sys.repository.PaperDownloadRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-27 14:46:00.933 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 46 ms. Found 0 Redis repository interfaces.
2025-07-27 14:46:01.551 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-27 14:46:01.560 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-07-27 14:46:01.561 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 14:46:01.561 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-27 14:46:01.818 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 14:46:01.818 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2159 ms
2025-07-27 14:46:01.978 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - JwtUtil initialized.
2025-07-27 14:46:01.980 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Secret Key (first 5 chars): 'F9A8C...', Length: 67
2025-07-27 14:46:01.981 [main] INFO  com.edu.maizi_edu_sys.util.JwtUtil - Loaded JWT Expiration: 86400000 ms
2025-07-27 14:46:01.983 [main] INFO  com.edu.maizi_edu_sys.config.FileUploadConfig - Upload directories initialized: base=D:\IdeaProject\maizi_edu_sys\.\.\uploads, avatar=D:\IdeaProject\maizi_edu_sys\.\.\uploads\avatars
2025-07-27 14:46:02.008 [main] WARN  com.edu.maizi_edu_sys.config.FallbackConfig - Using fallback Redis Connection Factory. Redis operations will not work correctly!
2025-07-27 14:46:02.117 [main] INFO  com.edu.maizi_edu_sys.config.RedisConfig - Integer RedisTemplate initialized successfully
2025-07-27 14:46:02.441 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.442 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.selectByTaskIdAndType] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.445 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:46:02.457 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-27 14:46:02.459 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-27 14:46:02.469 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByBatchId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.469 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByTaskId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.469 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.selectByUserIdAndStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.473 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-27 14:46:02.560 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopicsByKnowledgeId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.560 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.561 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.countAllTopics] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.561 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findAnyTopic] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.561 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectByType] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.562 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.selectFromBakByKnowId] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.562 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeAndDifficulty] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:02.562 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.TopicMapper.findIdsByKnowledgeAndTypeWithWiderRange] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:04.354 [main] INFO  com.edu.maizi_edu_sys.service.monitoring.AlgorithmMonitoringService - Algorithm monitoring service initialized with log level: INFO
2025-07-27 14:46:04.357 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager initialized with pool size: 100, bitset size: 10000
2025-07-27 14:46:05.301 [main] INFO  com.edu.maizi_edu_sys.service.impl.ChatServiceImpl - Initializing ChatServiceImpl with botId: bot-20250507182807-dbmrx, apiKey-length: 36
2025-07-27 14:46:05.419 [main] INFO  com.edu.maizi_edu_sys.util.MineRuApiClient - MineRU API客户端初始化完成，baseUrl: https://mineru.net, timeout: 60s
2025-07-27 14:46:05.505 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.edu.maizi_edu_sys.repository.AlgorithmExecutionRepository.countByStatus] is ignored, because it exists, maybe from xml file
2025-07-27 14:46:05.606 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-27 14:46:05.609 [main] WARN  com.baomidou.mybatisplus.core.injector.AbstractMethod - [com.edu.maizi_edu_sys.repository.TopicEnhancementDataMapper.selectBatchIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectBatchByIds]
2025-07-27 14:46:05.683 [main] INFO  com.edu.maizi_edu_sys.service.TopicCorrectionService - 题目修正服务初始化完成，批处理大小: 5
2025-07-27 14:46:05.716 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 14:46:05.880 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 14:46:06.083 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userStatsController' defined in file [D:\IdeaProject\maizi_edu_sys\target\classes\com\edu\maizi_edu_sys\controller\UserStatsController.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'com.edu.maizi_edu_sys.service.UserStatsCacheService' available: expected single matching bean but found 2: simpleUserStatsCacheService,userStatsCacheServiceImpl
2025-07-27 14:46:06.084 [main] INFO  com.edu.maizi_edu_sys.service.memory.MemoryManager - MemoryManager cleaned up
2025-07-27 14:46:06.086 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-27 14:46:06.098 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-27 14:46:06.103 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-27 14:46:06.117 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-27 14:46:06.135 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 1 of constructor in com.edu.maizi_edu_sys.controller.UserStatsController required a single bean, but 2 were found:
	- simpleUserStatsCacheService: defined in file [D:\IdeaProject\maizi_edu_sys\target\classes\com\edu\maizi_edu_sys\service\impl\SimpleUserStatsCacheServiceImpl.class]
	- userStatsCacheServiceImpl: defined in file [D:\IdeaProject\maizi_edu_sys\target\classes\com\edu\maizi_edu_sys\service\impl\UserStatsCacheServiceImpl.class]


Action:

Consider marking one of the beans as @Primary, updating the consumer to accept multiple beans, or using @Qualifier to identify the bean that should be consumed

