/* 管理后台仪表板样式 */

/* 全局样式 */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --sidebar-width: 240px;
    --navbar-height: 60px;
}

body {
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
    background-color: #f4f6f9;
    padding-top: var(--navbar-height);
}

/* 导航栏样式 */
.navbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    height: var(--navbar-height);
    z-index: 1050;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.brand-text {
    display: inline-block;
}

@media (max-width: 576px) {
    .brand-text {
        display: none;
    }
}

/* 通知下拉菜单 */
.notification-dropdown {
    min-width: 300px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item:last-child {
    border-bottom: none;
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: var(--navbar-height);
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);
    background-color: #fff;
    transition: all 0.3s ease;
}

@media (max-width: 767.98px) {
    .sidebar {
        position: fixed;
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
}

.sidebar .nav-link {
    color: #333;
    padding: 12px 20px;
    transition: all 0.2s ease;
    border-radius: 0;
    border-left: 3px solid transparent;
}

.sidebar .nav-link:hover {
    color: var(--primary-color);
    background-color: #f8f9fa;
    border-left-color: var(--primary-color);
}

.sidebar .nav-link.active {
    color: var(--primary-color);
    background-color: #e3f2fd;
    border-left-color: var(--primary-color);
    font-weight: 600;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

.sidebar-heading {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

/* 主要内容区域 */
main {
    margin-top: 20px;
    min-height: calc(100vh - var(--navbar-height) - 40px);
}

/* 统计卡片样式 */
.card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}

.card.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}

.card.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}

.card.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.text-xs {
    font-size: 0.75rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-uppercase {
    text-transform: uppercase !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

/* 图表区域 */
.chart-container {
    position: relative;
    height: 300px;
}

/* 活动列表样式 */
.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    padding: 15px 0;
    border-bottom: 1px solid #e3e6f0;
    display: flex;
    align-items: flex-start;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.activity-icon.bg-primary {
    background-color: var(--primary-color);
    color: white;
}

.activity-icon.bg-success {
    background-color: var(--success-color);
    color: white;
}

.activity-icon.bg-warning {
    background-color: var(--warning-color);
    color: white;
}

.activity-icon.bg-info {
    background-color: var(--info-color);
    color: white;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.activity-time {
    font-size: 0.875rem;
    color: #6c757d;
}

.activity-description {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 5px;
}

/* 加载动画 */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 表格样式 */
.table {
    background-color: white;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
}

.table td {
    border-top: 1px solid #e3e6f0;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

/* 按钮样式 */
.btn {
    border-radius: 0.35rem;
    font-weight: 400;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, 
        color-mix(in srgb, var(--primary-color) 85%, black) 0%, 
        color-mix(in srgb, var(--secondary-color) 85%, black) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    border-radius: 0.35rem;
    padding: 0.375rem 0.75rem;
}

/* 分页样式 */
.pagination {
    margin-bottom: 0;
}

.page-link {
    color: var(--primary-color);
    border-color: #dee2e6;
}

.page-link:hover {
    color: var(--secondary-color);
    background-color: #e9ecef;
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 模态框样式 */
.modal-content {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 0.5rem;
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* 表单样式 */
.form-control {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    transition: all 0.15s ease-in-out;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

/* 搜索框样式 */
.search-box {
    position: relative;
}

.search-box input {
    padding-right: 40px;
}

.search-box i {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        border: none;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin-bottom: 0.5rem;
    }
}

/* 淡入动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translate3d(0, 40px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.online {
    background-color: #28a745;
}

.status-indicator.offline {
    background-color: #dc3545;
}

.status-indicator.busy {
    background-color: #ffc107;
}

/* 工具提示 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: #333;
    color: white;
    border-radius: 0.25rem;
    padding: 0.5rem 0.75rem;
}

/* 进度条 */
.progress {
    height: 0.5rem;
    border-radius: 0.25rem;
}

.progress-bar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

/* 下拉菜单 */
.dropdown-menu {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 0.35rem;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: all 0.15s ease-in-out;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    color: var(--primary-color);
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}

.empty-state h5 {
    margin-bottom: 0.5rem;
    color: #495057;
}

.empty-state p {
    margin-bottom: 0;
} 