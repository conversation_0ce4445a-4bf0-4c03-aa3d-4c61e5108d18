/* 题目管理页面样式 */

/* 搜索框样式 */
.search-box {
    position: relative;
}

.search-box i {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 2;
}

.search-box input {
    padding-left: 35px;
}

/* 统计卡片样式 */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-secondary {
    border-left: 0.25rem solid #858796 !important;
}

.border-left-dark {
    border-left: 0.25rem solid #5a5c69 !important;
}

/* 题目表格样式 */
.topic-table {
    width: 100%;
    margin-bottom: 1rem;
    color: #858796;
}

.topic-table th,
.topic-table td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid #e3e6f0;
}

.topic-table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #e3e6f0;
    background-color: #f8f9fc;
    color: #5a5c69;
    font-weight: 800;
    font-size: 0.85rem;
    text-transform: uppercase;
}

/* 难度标签样式 */
.difficulty-easy {
    background-color: #d4edda;
    color: #155724;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.difficulty-medium {
    background-color: #fff3cd;
    color: #856404;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.difficulty-hard {
    background-color: #f8d7da;
    color: #721c24;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

/* 题目类型标签样式 */
.topic-type-choice {
    background-color: #d1ecf1;
    color: #0c5460;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.topic-type-multiple {
    background-color: #e2e3e5;
    color: #383d41;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.topic-type-judge {
    background-color: #ffeaa7;
    color: #6c5700;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.topic-type-fill {
    background-color: #fab1a0;
    color: #a04000;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.topic-type-short {
    background-color: #fd79a8;
    color: #880e4f;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

/* 操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.action-buttons .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 0.375rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.modal-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid #e3e6f0;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e3e6f0;
    border-bottom-right-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

/* 分页样式 */
.pagination {
    justify-content: center;
    margin-top: 1rem;
}

.pagination .page-link {
    color: #5a5c69;
    border: 1px solid #dddfeb;
    margin: 0 0.125rem;
}

.pagination .page-item.active .page-link {
    background-color: #5a5c69;
    border-color: #5a5c69;
}

.pagination .page-link:hover {
    color: #5a5c69;
    background-color: #eaecf4;
    border-color: #dddfeb;
}

/* 加载指示器 */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    vertical-align: text-bottom;
    border: 0.125em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
    }
    
    .action-buttons .btn {
        width: 100%;
        margin-bottom: 0.25rem;
    }
    
    .table-responsive {
        border: none;
    }
} 