/**
 * 管理员审核管理JavaScript
 * 
 * 修复日志 - 大整数ID处理问题 (2025-7-3日修复)：
 * 1. 问题：JavaScript中的用户ID如1920280447393230854超过了Number.MAX_SAFE_INTEGER限制
 * 2. 影响：ID在JavaScript中会丢失精度，导致数据不一致和API调用失败
 * 3. 解决方案：
 *    - 添加全局safeParseId函数，统一处理所有ID为字符串格式
 *    - 修复所有涉及用户ID的渲染、API调用和数据处理逻辑
 *    - 保持与后端API的兼容性，确保ID以正确格式传递
 * 4. 修复范围：
 *    - 提交者数据加载和选择框填充
 *    - 分组审核视图中的用户ID显示和操作
 *    - 批量审核操作中的用户ID处理
 *    - 用户详情查看和模态框处理
 *    - 所有测试函数中的ID处理
 */

// 全局ID安全处理工具函数
window.safeParseId = function(id) {
    if (id === null || id === undefined || id === '') return null;
    
    // 统一转换为字符串
    const idStr = String(id);
    
    // 如果已经是字符串且不是数字字符串，直接返回
    if (typeof id === 'string' && isNaN(Number(id))) {
        return idStr;
    }
    
    const idNum = Number(id);
    
    // 检查是否是有效数字
    if (isNaN(idNum)) {

        return idStr;
    }
    
    // 检查是否超过JavaScript安全整数范围
    if (idNum > Number.MAX_SAFE_INTEGER || idNum < Number.MIN_SAFE_INTEGER) {

        return idStr;
    }
    
    // 即使在安全范围内，也统一返回字符串，确保一致性
    return idStr;
};

// 为了向后兼容，也创建一个不带window前缀的版本
const safeParseId = window.safeParseId;

class AdminAuditManagement {
    constructor() {
        this.currentTab = 'pending';
        this.currentPage = 1;
        this.pageSize = 10;
        this.searchTerm = '';
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        
        // 延迟执行，确保DOM完全加载
        setTimeout(() => {
            this.initializePageContent();
        }, 100);
    }
    
    initializePageContent() {
        // 检查当前页面是否是审核管理相关页面
        const isAuditPage = window.location.pathname.includes('/audit') || 
                           document.querySelector('#auditTabs') || 
                           document.querySelector('#statsContainer');
        
        if (!isAuditPage) {
            return;
        }
        
        // 检查是否是审核管理页面（是否有统计元素）
        const hasStatsElements = document.getElementById('pendingCount') && 
                                 document.getElementById('approvedCount') && 
                                 document.getElementById('rejectedCount') && 
                                 document.getElementById('totalCount');
        
        if (hasStatsElements) {
            this.loadAuditStats();
        }
        
        // 检查是否有审核选项卡，决定默认加载的数据
        const auditTabs = document.getElementById('auditTabs');
        if (auditTabs) {
            // 默认加载分组审核数据，因为它是第一个激活的标签页
            this.loadTabData('group-audit', 1);
        } else {
            // 如果没有选项卡，可能是单独的审核记录页面
            const auditorRecordsContainer = document.getElementById('auditorRecordsContainer');
            if (auditorRecordsContainer) {
                this.loadTabData('auditor-records', 1);
            }
        }
    }
    
    bindEvents() {
        // Tab切换事件
        document.querySelectorAll('#auditTabs button').forEach(button => {
            button.addEventListener('click', (e) => {
                // 获取按钮元素，确保不是子元素
                let target = e.target;
                while (target && !target.hasAttribute('data-bs-target')) {
                    target = target.parentElement;
                }
                
                if (target && target.hasAttribute('data-bs-target')) {
                    const tab = target.getAttribute('data-bs-target').replace('#', '');
                    this.switchTab(tab);
                }
            });
        });
        
        // 搜索事件
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchTerm = e.target.value;
                this.searchAudits();
            });
        }
        
        // 刷新按钮事件
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshAuditData();
            });
        }
    }
    
    // 切换Tab
    switchTab(tab) {
        this.currentTab = tab;
        this.currentPage = 1;
        this.loadTabData(tab, 1);
    }
    
    // 加载审核统计数据
    async loadAuditStats() {
        try {
            const response = await this.fetchAPI('/api/admin/audit/stats');
            
            if (this.isResponseSuccess(response)) {
                const statsData = this.getResponseData(response);
                this.updateStatsDisplay(statsData);
            } else {
                this.showError('加载统计数据失败: ' + this.getResponseMessage(response));
            }
        } catch (error) {
            console.error('加载审核统计失败:', error);
            this.showError('网络错误，无法加载统计数据: ' + error.message);
        }
    }
    
    // 更新统计显示
    updateStatsDisplay(stats) {
        if (!stats) {
            return;
        }
        
        const statsMapping = {
            'pendingCount': stats.pending || stats.pendingCount || 0,
            'approvedCount': stats.approved || stats.approvedCount || 0,
            'rejectedCount': stats.rejected || stats.rejectedCount || 0,
            'totalCount': stats.total || stats.totalCount || 0
        };
        
        Object.entries(statsMapping).forEach(([elementId, value]) => {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value;
            }
        });
    }
    
    // 加载Tab数据
    async loadTabData(tab, page = 1) {
        this.currentTab = tab;
        this.currentPage = page;
        
        switch (tab) {
            case 'group-audit':
                await this.loadGroupAuditData(page);
                break;
            case 'pending':
                await this.loadPendingAudits(page);
                break;
            case 'approved':
                await this.loadApprovedAudits(page);
                break;
            case 'rejected':
                await this.loadRejectedAudits(page);
                break;
            case 'auditor-records':
                await this.loadAuditorRecords(page);
                break;
        }
    }
    
    // 加载待审核（使用统一的搜索逻辑）
    async loadPendingAudits(page = 1) {
        try {
            this.showLoading('pendingAuditsContainer');
            
            // 使用统一的搜索功能
            const searchParams = getSearchParams();
            await searchByTab('pending', searchParams);
            
        } catch (error) {
            console.error('Failed to load pending audits:', error);
            this.showError('网络错误，无法加载待审核数据: ' + error.message);
        }
    }
    
    // 渲染列表视图（适配TopicAuditDTO）
    renderListView(records, container) {
        let html = `
            <div class="d-flex justify-content-between align-items-center mb-3 p-3 bg-light">
                <div class="d-flex gap-2">
                    <button class="btn btn-success" onclick="adminAudit.batchApprove()" id="batchApproveBtn" disabled>
                        <i class="fas fa-check"></i> 批量通过 (<span id="selectedCount">0</span>)
                    </button>
                    <button class="btn btn-danger" onclick="adminAudit.batchReject()" id="batchRejectBtn" disabled>
                        <i class="fas fa-times"></i> 批量拒绝 (<span id="selectedCount2">0</span>)
                    </button>
                    <button class="btn btn-info" onclick="adminAudit.smartAudit()" id="smartAuditBtn">
                        <i class="fas fa-magic"></i> 智能审核
                    </button>
                </div>
                <div class="text-muted">
                    <small>已选择 <span id="selectedCountDisplay">0</span> 项</small>
                </div>
            </div>
            <div class="table-responsive list-view-container" style="margin: 0;">
                <table class="table table-hover mb-0">
                    <thead class="table-light sticky-top">
                        <tr>
                            <th width="50">
                                <input type="checkbox" class="form-check-input" onchange="adminAudit.toggleSelectAll(this)" id="selectAllCheckbox">
                            </th>
                            <th>题目标题</th>
                            <th>题目类型</th>
                            <th>知识点</th>
                            <th>提交者</th>
                            <th>提交时间</th>
                            <th>状态</th>
                            <th width="150">操作</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        records.forEach(audit => {
            // 适配TopicAuditDTO字段名
            const title = audit.title || '题目标题';
            const type = audit.type || '';
            const knowledgePoint = audit.knowledgeName || audit.knowledge || audit.knowId || '知识点';
            const submitterName = audit.username || '提交者';
            const submitTime = audit.submitTime;
            const auditStatus = audit.auditStatus !== undefined ? audit.auditStatus : audit.status;
            
            // 状态样式和文本
            const statusClass = getStatusClass(auditStatus);
            const statusText = getStatusText(auditStatus);
            
            html += `
                <tr data-audit-id="${audit.id}">
                    <td>
                        <input type="checkbox" class="form-check-input audit-checkbox" 
                               value="${audit.id}" onchange="adminAudit.updateSelectedCount()">
                    </td>
                    <td>
                        <a href="#" onclick="showAuditModal(${audit.id})" class="text-decoration-none">
                            ${this.escapeHtml(title)}
                        </a>
                        <div class="small text-muted">${this.getTopicContentPreview(audit.content || audit.options || '', 50)}</div>
                    </td>
                    <td><span class="badge bg-secondary">${this.escapeHtml(type)}</span></td>
                    <td><span class="badge bg-info">${this.escapeHtml(knowledgePoint)}</span></td>
                    <td>
                        <span class="badge bg-primary">${this.escapeHtml(submitterName)}</span>
                        ${audit.userId ? `<div class="small text-muted">ID: ${safeParseId(audit.userId)}</div>` : ''}
                    </td>
                    <td>
                        <small>${this.formatTime(submitTime)}</small>
                    </td>
                    <td>
                        <span class="badge ${statusClass}">${statusText}</span>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            ${auditStatus === 0 ? `
                                <button type="button" class="btn btn-success" onclick="quickApprove(${audit.id})" title="快速通过">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button type="button" class="btn btn-danger" onclick="quickReject(${audit.id})" title="快速拒绝">
                                    <i class="fas fa-times"></i>
                                </button>
                            ` : ''}
                            <button type="button" class="btn btn-info" onclick="showEnhancedTopicPreview(${audit.id})" title="详细预览">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="showAuditModal(${audit.id})" title="审核表单">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });
        
        html += '</tbody></table></div>';
        container.innerHTML = html;
    }
    
    // 渲染分组视图
    renderGroupedView(records, container) {
        // 按提交者分组
        const groupedData = {};
        records.forEach(audit => {
            const key = audit.username || '未知用户';
            if (!groupedData[key]) {
                groupedData[key] = {
                    username: key,
                    userId: audit.userId,
                    audits: [],
                    totalCount: 0
                };
            }
            groupedData[key].audits.push(audit);
            groupedData[key].totalCount++;
        });
        
        let html = `
            <div class="d-flex justify-content-between align-items-center mb-3 p-3 bg-light sticky-top">
                <div class="d-flex gap-2">
                    <button class="btn btn-success" onclick="adminAudit.batchApproveByUser()" id="batchApproveByUserBtn">
                        <i class="fas fa-users"></i> 按用户批量通过
                    </button>
                    <button class="btn btn-warning" onclick="adminAudit.showBulkAuditModal()" id="bulkAuditBtn">
                        <i class="fas fa-layer-group"></i> 批量智能审核
                    </button>
                </div>
                <div class="text-muted">
                    <small>共 ${Object.keys(groupedData).length} 个提交者</small>
                </div>
            </div>
            <div class="container-fluid p-3">
                <div class="row g-3">
        `;
        
        Object.values(groupedData).forEach(group => {
            // 安全处理用户ID
            const safeUserId = safeParseId(group.userId);
            
            html += `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <span class="fw-bold">${this.escapeHtml(group.username)}</span>
                            <span class="badge bg-primary">${group.totalCount} 条</span>
                        </div>
                        <div class="card-body">
                            <div class="small text-muted mb-2">用户ID: ${safeUserId}</div>
                            <div class="d-grid gap-2">
                                <button class="btn btn-success btn-sm" 
                                        onclick="adminAudit.approveAllByUser('${safeUserId}')">
                                    <i class="fas fa-check"></i> 全部通过
                                </button>
                                <button class="btn btn-info btn-sm" 
                                        onclick="adminAudit.showUserAudits('${safeUserId}', '${this.escapeHtml(group.username)}')">
                                    <i class="fas fa-list"></i> 查看详情
                                </button>
                                <button class="btn btn-warning btn-sm" 
                                        onclick="adminAudit.smartAuditByUser('${safeUserId}')">
                                    <i class="fas fa-magic"></i> 智能审核
                                </button>
                            </div>
                        </div>
                        <div class="card-footer">
                            <small class="text-muted">
                                最新提交: ${this.formatTime(group.audits[0]?.submitTime)}
                            </small>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += '</div></div>';
        container.innerHTML = html;
    }
    
    // 更新待审核统计
    updatePendingStats(total, records) {
        const statsContainer = document.getElementById('pendingStatsContainer');
        if (!statsContainer) return;
        
        // 统计提交者数量
        const submitters = new Set(records.map(r => r.username));
        const knowledgePoints = new Set(records.map(r => r.knowledgeName));
        
        statsContainer.innerHTML = `
            <div class="row g-3 mb-3">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">${total}</h5>
                            <p class="card-text">待审核总数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-info">${submitters.size}</h5>
                            <p class="card-text">提交者数量</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">${knowledgePoints.size}</h5>
                            <p class="card-text">知识点数量</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary">${Math.round(total / submitters.size)}</h5>
                            <p class="card-text">平均每人提交</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 切换全选
    toggleSelectAll(checkbox) {
        const checkboxes = document.querySelectorAll('.audit-checkbox');
        checkboxes.forEach(cb => cb.checked = checkbox.checked);
        this.updateSelectedCount();
    }
    
    // 更新选中数量
    updateSelectedCount() {
        const selected = document.querySelectorAll('.audit-checkbox:checked');
        const count = selected.length;
        
        // 更新各个计数显示
        const elements = ['selectedCount', 'selectedCount2', 'selectedCountDisplay'];
        elements.forEach(id => {
            const el = document.getElementById(id);
            if (el) el.textContent = count;
        });
        
        // 更新按钮状态
        const batchButtons = ['batchApproveBtn', 'batchRejectBtn'];
        batchButtons.forEach(id => {
            const btn = document.getElementById(id);
            if (btn) btn.disabled = count === 0;
        });
        
        // 更新全选复选框状态
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        if (selectAllCheckbox) {
            const totalCheckboxes = document.querySelectorAll('.audit-checkbox').length;
            selectAllCheckbox.checked = count > 0 && count === totalCheckboxes;
            selectAllCheckbox.indeterminate = count > 0 && count < totalCheckboxes;
        }
    }
    
    // 加载已通过
    async loadApprovedAudits(page = 1) {
        try {
            this.showLoading('approvedAuditsContainer');
            
            const response = await this.fetchAPI(`/api/admin/audit/approved?pageNum=${page}&pageSize=${this.pageSize}`);
            
            if (this.isResponseSuccess(response)) {
                const container = document.getElementById('approvedAuditsContainer');
                const data = this.getResponseData(response);
                
                // 兼容两种数据格式
                const records = data.records || data.list || [];
                
                if (records.length === 0) {
                    container.innerHTML = this.getEmptyState('暂无已通过题目');
                    document.getElementById('approvedPagination').style.display = 'none';
                    return;
                }
                
                let html = '<div class="table-responsive"><table class="table table-hover"><thead><tr>';
                html += '<th>题目标题</th><th>知识点</th><th>提交者</th><th>审核人</th><th>审核时间</th><th>操作</th></tr></thead><tbody>';
                
                records.forEach(audit => {
                    // 使用TopicAuditDTO的正确字段名
                    const title = audit.title || '题目标题';
                    const knowledgePoint = audit.knowledgeName || audit.knowId || '知识点';
                    const submitterName = audit.username || '提交者';
                    const auditorName = audit.auditorName || '审核员';
                    const auditTime = audit.auditTime;
                    
                    html += `
                        <tr>
                            <td><a href="#" onclick="showAuditDetail(${audit.id})" class="text-decoration-none">${this.escapeHtml(title)}</a></td>
                            <td><span class="badge bg-info">${this.escapeHtml(knowledgePoint)}</span></td>
                            <td>${this.escapeHtml(submitterName)}</td>
                            <td>${this.escapeHtml(auditorName)}</td>
                            <td>${this.formatTime(auditTime)}</td>
                            <td>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-info btn-sm" 
                                            onclick="adminAudit.viewAuditDetail(${audit.id})">
                                        <i class="fas fa-eye"></i> 详情
                                    </button>
                                    <button type="button" class="btn btn-warning btn-sm" 
                                            onclick="adminAudit.revertAudit(${audit.id})" title="撤销审核">
                                        <i class="fas fa-undo"></i> 撤销
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                });
                
                html += '</tbody></table></div>';
                container.innerHTML = html;
                
                const currentPage = data.current || page;
                const totalPages = data.pages || Math.ceil((data.total || 0) / this.pageSize);
                this.updatePagination('approvedPagination', currentPage, totalPages, 'approved');
            } else {
                this.showError('加载已通过数据失败: ' + this.getResponseMessage(response));
            }
        } catch (error) {
            console.error('Failed to load approved audits:', error);
            this.showError('网络错误，无法加载已通过数据: ' + error.message);
        }
    }
    
    // 加载已拒绝
    async loadRejectedAudits(page = 1) {
        try {
            this.showLoading('rejectedAuditsContainer');
            
            const response = await this.fetchAPI(`/api/admin/audit/rejected?pageNum=${page}&pageSize=${this.pageSize}`);
            
            if (this.isResponseSuccess(response)) {
                const container = document.getElementById('rejectedAuditsContainer');
                const data = this.getResponseData(response);
                
                // 兼容两种数据格式
                const records = data.records || data.list || [];
                
                if (records.length === 0) {
                    container.innerHTML = this.getEmptyState('暂无已拒绝题目');
                    document.getElementById('rejectedPagination').style.display = 'none';
                    return;
                }
                
                let html = '<div class="table-responsive"><table class="table table-hover"><thead><tr>';
                html += '<th>题目标题</th><th>知识点</th><th>提交者</th><th>审核人</th><th>拒绝原因</th><th>审核时间</th><th>操作</th></tr></thead><tbody>';
                
                records.forEach(audit => {
                    // 使用TopicAuditDTO的正确字段名
                    const title = audit.title || '题目标题';
                    const knowledgePoint = audit.knowledgeName || audit.knowId || '知识点';
                    const submitterName = audit.username || '提交者';
                    const auditorName = audit.auditorName || '审核员';
                    const auditTime = audit.auditTime;
                    const auditComment = audit.auditComment || '-';
                    
                    html += `
                        <tr>
                            <td><a href="#" onclick="showAuditDetail(${audit.id})" class="text-decoration-none">${this.escapeHtml(title)}</a></td>
                            <td><span class="badge bg-info">${this.escapeHtml(knowledgePoint)}</span></td>
                            <td>${this.escapeHtml(submitterName)}</td>
                            <td>${this.escapeHtml(auditorName)}</td>
                            <td><span class="text-danger">${this.escapeHtml(auditComment)}</span></td>
                            <td>${this.formatTime(auditTime)}</td>
                            <td>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-info btn-sm" 
                                            onclick="adminAudit.viewAuditDetail(${audit.id})">
                                        <i class="fas fa-eye"></i> 详情
                                    </button>
                                    <button type="button" class="btn btn-warning btn-sm" 
                                            onclick="adminAudit.revertAudit(${audit.id})" title="撤销审核">
                                        <i class="fas fa-undo"></i> 撤销
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                });
                
                html += '</tbody></table></div>';
                container.innerHTML = html;
                
                const currentPage = data.current || page;
                const totalPages = data.pages || Math.ceil((data.total || 0) / this.pageSize);
                this.updatePagination('rejectedPagination', currentPage, totalPages, 'rejected');
            } else {
                this.showError('加载已拒绝数据失败: ' + this.getResponseMessage(response));
            }
        } catch (error) {
            console.error('Failed to load rejected audits:', error);
            this.showError('网络错误，无法加载已拒绝数据: ' + error.message);
        }
    }
    
    // 加载审核记录
    async loadAuditorRecords(page = 1) {
        try {
            this.showLoading('auditorRecordsContainer');
            
            const response = await this.fetchAPI(`/api/admin/audit/auditor-records?pageNum=${page}&pageSize=${this.pageSize}`);
            
            if (this.isResponseSuccess(response)) {
                const container = document.getElementById('auditorRecordsContainer');
                const data = this.getResponseData(response);
                
                // 兼容两种数据格式
                const records = data.records || data.list || [];
                
                if (records.length === 0) {
                    container.innerHTML = this.getEmptyState('暂无审核记录');
                    document.getElementById('auditorRecordsPagination').style.display = 'none';
                    return;
                }
                
                let html = '<div class="table-responsive"><table class="table table-hover"><thead><tr>';
                html += '<th>题目标题</th><th>提交者</th><th>审核结果</th><th>审核意见</th><th>审核时间</th></tr></thead><tbody>';
                
                records.forEach(record => {
                    // 使用TopicAuditDTO的正确字段名
                    const title = record.title || '题目标题';
                    const submitterName = record.username || '提交者';
                    const auditStatus = record.auditStatus;
                    const auditComment = record.auditComment || '-';
                    const auditTime = record.auditTime;
                    
                    const statusClass = auditStatus === 1 ? 'success' : 'danger';
                    const statusText = auditStatus === 1 ? '通过' : '拒绝';
                    
                    html += `
                        <tr>
                            <td>${this.escapeHtml(title)}</td>
                            <td>${this.escapeHtml(submitterName)}</td>
                            <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                            <td>${this.escapeHtml(auditComment)}</td>
                            <td>${this.formatTime(auditTime)}</td>
                        </tr>
                    `;
                });
                
                html += '</tbody></table></div>';
                container.innerHTML = html;
                
                const currentPage = data.current || page;
                const totalPages = data.pages || Math.ceil((data.total || 0) / this.pageSize);
                this.updatePagination('auditorRecordsPagination', currentPage, totalPages, 'records');
            } else {
                this.showError('加载审核记录失败: ' + this.getResponseMessage(response));
            }
        } catch (error) {
            console.error('Failed to load auditor records:', error);
            this.showError('网络错误，无法加载审核记录: ' + error.message);
        }
    }
    
    // 搜索审核
    async searchAudits() {
        // 防抖处理
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.loadTabData(this.currentTab, 1);
        }, 500);
    }
    
    // 刷新审核数据
    refreshAuditData() {
        this.loadAuditStats();
        this.loadTabData(this.currentTab, this.currentPage);
        this.showSuccess('数据已刷新');
    }
    
    // 更新分页
    updatePagination(containerId, current, total, tabType) {
        const container = document.getElementById(containerId);
        if (!container) {
            return;
        }
        
        // 如果总页数为0，隐藏分页
        if (total <= 0) {
            container.style.display = 'none';
            return;
        }
        
        // 显示分页组件
        container.style.display = 'block';
        const pagination = container.querySelector('.pagination') || container;
        
        let html = '';
        
        // 上一页
        if (current > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="event.preventDefault(); window.adminAudit.goToPage('${tabType}', ${current - 1})">
                <i class="fas fa-chevron-left"></i> 上一页
            </a></li>`;
        } else {
            html += `<li class="page-item disabled"><span class="page-link">
                <i class="fas fa-chevron-left"></i> 上一页
            </span></li>`;
        }
        
        // 页码
        const start = Math.max(1, current - 2);
        const end = Math.min(total, current + 2);
        
        // 如果不是从第1页开始，显示第1页
        if (start > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="event.preventDefault(); window.adminAudit.goToPage('${tabType}', 1)">1</a></li>`;
            if (start > 2) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }
        
        for (let i = start; i <= end; i++) {
            const activeClass = i === current ? 'active' : '';
            html += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="event.preventDefault(); window.adminAudit.goToPage('${tabType}', ${i})">${i}</a></li>`;
        }
        
        // 如果不是到最后一页，显示最后一页
        if (end < total) {
            if (end < total - 1) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            html += `<li class="page-item"><a class="page-link" href="#" onclick="event.preventDefault(); window.adminAudit.goToPage('${tabType}', ${total})">${total}</a></li>`;
        }
        
        // 下一页
        if (current < total) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="event.preventDefault(); window.adminAudit.goToPage('${tabType}', ${current + 1})">
                下一页 <i class="fas fa-chevron-right"></i>
            </a></li>`;
        } else {
            html += `<li class="page-item disabled"><span class="page-link">
                下一页 <i class="fas fa-chevron-right"></i>
            </span></li>`;
        }
        
        // 添加分页信息到最后
        html += `<li class="page-item disabled ms-auto">
            <span class="page-link bg-light text-muted border-0">
                第 ${current} 页 / 共 ${total} 页
            </span>
        </li>`;
        
        pagination.innerHTML = html;
    }
    
    // 统一的分页跳转方法
    goToPage(tabType, page) {
        this.currentPage = page;
        switch (tabType) {
            case 'pending':
                this.loadPendingAudits(page);
                break;
            case 'approved':
                this.loadApprovedAudits(page);
                break;
            case 'rejected':
                this.loadRejectedAudits(page);
                break;
            case 'records':
                this.loadAuditorRecords(page);
                break;
            case 'group-audit':
                this.loadGroupAuditData(page);
                break;
            default:
        }
    }
    
    // 获取认证令牌
    getAuthToken() {
        return localStorage.getItem('adminToken') || sessionStorage.getItem('adminToken');
    }
    
    // 获取默认的AJAX配置
    getDefaultAjaxConfig() {
        const token = this.getAuthToken();
        return {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': token ? `Bearer ${token}` : ''
            }
        };
    }
    
    // 处理认证错误
    handleAuthError() {
        localStorage.removeItem('adminToken');
        sessionStorage.removeItem('adminToken');
        alert('登录已过期，请重新登录');
        window.location.href = '/admin/login';
    }
    
    // API请求
    async fetchAPI(url, options = {}) {
        const token = this.getAuthToken();
        
        if (!token) {
            this.handleAuthError();
            return;
        }
        
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        // 合并headers，确保Authorization不被覆盖
        if (options.headers) {
            finalOptions.headers = { ...defaultOptions.headers, ...options.headers };
        }
        
        try {
        const response = await fetch(url, finalOptions);
        
        if (!response.ok) {
            if (response.status === 401) {
                    this.handleAuthError();
                return;
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
            const data = await response.json();
            return data;
        } catch (error) {
            console.error(`API请求失败: ${url}`, error);
            throw error;
        }
    }

    // 检查响应是否成功（兼容两种格式）
    isResponseSuccess(response) {
        return (response.code === 200) || (response.success === true);
    }

    // 获取响应数据
    getResponseData(response) {
        return response.data;
    }

    // 获取响应消息
    getResponseMessage(response) {
        return response.message || '操作失败';
    }
    
    // 显示加载状态
    showLoading(containerId) {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                    <p class="mt-2 text-muted">加载中...</p>
                </div>
            `;
        }
    }
    
    // 空状态
    getEmptyState(message) {
        return `
            <div class="empty-state">
                <i class="fas fa-inbox"></i>
                <h5>暂无数据</h5>
                <p>${message}</p>
            </div>
        `;
    }
    
    // 转义HTML
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // 格式化时间
    formatTime(timeString) {
        if (!timeString) return '-';
        const date = new Date(timeString);
        return date.toLocaleString('zh-CN');
    }
    
    // 显示成功消息
    showSuccess(message) {
        this.showMessage(message, 'success');
    }
    
    // 显示错误消息
    showError(message) {
        this.showMessage(message, 'error');
    }
    
    // 显示消息
    showMessage(message, type) {
        // 使用Bootstrap Toast或者简单的alert
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: type === 'success' ? '成功' : '错误',
                text: message,
                icon: type === 'success' ? 'success' : 'error',
                timer: 3000,
                showConfirmButton: false
            });
        } else {
            alert(message);
        }
    }

    /**
     * 处理审核请求
     */
    async handleAuditRequest(auditIds, auditResult, comment) {
        try {
            showLoading('处理审核请求中...');
            
            // 确保auditIds是数组
            const ids = Array.isArray(auditIds) ? auditIds : [auditIds];
            
            // 逐个处理审核请求
            const results = [];
            for (const auditId of ids) {
                const endpoint = auditResult === 1 
                    ? `/api/admin/audit/quick-approve/${auditId}`
                    : `/api/admin/audit/quick-reject/${auditId}`;
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
                    },
                    body: JSON.stringify({
                        comment: comment || ''
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    results.push(result);
                } else {
                    const errorData = await response.json();
                    throw new Error(errorData.message || '审核请求失败');
                }
            }

            hideLoading();

            // 显示成功提示
            const statusText = auditResult === 1 ? '通过' : '拒绝';
            const successCount = ids.length;
            
            showToast(`✅ 审核${statusText}成功！已处理 ${successCount} 个题目`, 'success');
            
            // 自动刷新数据
            setTimeout(() => {
                this.refreshAuditData();
            }, 1000);
            
            return results;
        } catch (error) {
            hideLoading();
            console.error('审核请求失败:', error);
            showToast(`❌ 审核失败: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * 审核通过单个题目
     */
    async approveAudit(auditId) {
        try {
            const confirmed = await showConfirmDialog({
                title: '确认审核通过',
                message: '确定要通过这个题目的审核吗？',
                confirmText: '通过',
                confirmClass: 'btn-success'
            });
            
            if (confirmed) {
                await this.handleAuditRequest([auditId], 1, '审核通过');
            }
        } catch (error) {
            console.error('审核通过失败:', error);
        }
    }
    
    /**
     * 审核拒绝单个题目
     */
    async rejectAudit(auditId) {
        try {
            const rejectReason = await showPromptDialog({
                title: '审核拒绝',
                message: '请输入拒绝原因：',
                placeholder: '请说明拒绝的具体原因...',
                required: true
            });
            
            if (rejectReason) {
                await this.handleAuditRequest([auditId], 2, rejectReason);
            }
        } catch (error) {
            console.error('审核拒绝失败:', error);
        }
    }
    
    /**
     * 查看审核详情（使用增强预览）
     */
    async viewAuditDetail(auditId) {
        try {
            showLoading('加载审核详情...');
            
            const response = await this.fetchAPI(`/api/admin/audit/detail/${auditId}`);
            
            hideLoading();
            
            if (this.isResponseSuccess(response)) {
                const auditDetail = this.getResponseData(response);
                // 使用增强的预览模态框
                this.showEnhancedAuditDetailModal(auditDetail);
            } else {
                throw new Error(this.getResponseMessage(response) || '获取审核详情失败');
            }
        } catch (error) {
            hideLoading();
            console.error('获取审核详情失败:', error);
            this.showError('❌ 获取审核详情失败: ' + error.message);
        }
    }

    /**
     * 显示审核详情模态框
     */
    showAuditDetailModal(auditDetail) {
        // 使用TopicAuditDTO的正确字段名
        const title = auditDetail.title || '题目标题';
        const content = auditDetail.options || '题目内容';
        const knowledgePoint = auditDetail.knowledgeName || auditDetail.knowId || '知识点';
        const submitterName = auditDetail.username || '提交者';
        const auditorName = auditDetail.auditorName || '审核员';
        const auditStatus = auditDetail.auditStatus || 0;
        const auditComment = auditDetail.auditComment || '';
        const submitTime = auditDetail.submitTime;
        const auditTime = auditDetail.auditTime;
        const answer = auditDetail.answer || '';
        const parse = auditDetail.parse || '';
        const score = auditDetail.score || '';
        const difficulty = auditDetail.difficulty || '';
        const tags = auditDetail.tags || '';
        const source = auditDetail.source || '';
        
        // 创建详情模态框HTML
        const modalHtml = `
            <div class="modal fade" id="auditDetailModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">审核详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label fw-bold">题目标题</label>
                                <div class="form-control-plaintext">${this.escapeHtml(title)}</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">题目内容</label>
                                <div class="form-control-plaintext border p-2 rounded bg-light" style="max-height: 200px; overflow-y: auto;">${this.escapeHtml(content)}</div>
                            </div>
                            ${answer ? `
                                <div class="mb-3">
                                    <label class="form-label fw-bold">答案</label>
                                    <div class="form-control-plaintext border p-2 rounded bg-light">${this.escapeHtml(answer)}</div>
                                </div>
                            ` : ''}
                            ${parse ? `
                                <div class="mb-3">
                                    <label class="form-label fw-bold">解析</label>
                                    <div class="form-control-plaintext border p-2 rounded bg-light">${this.escapeHtml(parse)}</div>
                                </div>
                            ` : ''}
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">知识点</label>
                                    <div class="form-control-plaintext">
                                        <span class="badge bg-info">${this.escapeHtml(knowledgePoint)}</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">提交者</label>
                                    <div class="form-control-plaintext">${this.escapeHtml(submitterName)}</div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">审核状态</label>
                                    <div class="form-control-plaintext">
                                        <span class="badge bg-${auditStatus === 0 ? 'warning' : (auditStatus === 1 ? 'success' : 'danger')}">
                                            ${auditStatus === 0 ? '待审核' : (auditStatus === 1 ? '已通过' : '已拒绝')}
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">提交时间</label>
                                    <div class="form-control-plaintext">${this.formatTime(submitTime)}</div>
                                </div>
                            </div>
                            ${auditStatus !== 0 ? `
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label fw-bold">审核人</label>
                                        <div class="form-control-plaintext">${this.escapeHtml(auditorName)}</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label fw-bold">审核时间</label>
                                        <div class="form-control-plaintext">${this.formatTime(auditTime)}</div>
                                    </div>
                                </div>
                            ` : ''}
                            ${auditComment ? `
                                <div class="mb-3">
                                    <label class="form-label fw-bold">审核意见</label>
                                    <div class="form-control-plaintext border p-2 rounded bg-light">${this.escapeHtml(auditComment)}</div>
                                </div>
                            ` : ''}
                            <div class="row">
                                ${score ? `
                                    <div class="col-md-4">
                                        <label class="form-label fw-bold">分值</label>
                                        <div class="form-control-plaintext">${score}</div>
                                    </div>
                                ` : ''}
                                ${difficulty ? `
                                    <div class="col-md-4">
                                        <label class="form-label fw-bold">难度</label>
                                        <div class="form-control-plaintext">${difficulty}</div>
                                    </div>
                                ` : ''}
                                ${source ? `
                                    <div class="col-md-4">
                                        <label class="form-label fw-bold">来源</label>
                                        <div class="form-control-plaintext">${this.escapeHtml(source)}</div>
                                    </div>
                                ` : ''}
                            </div>
                            ${tags ? `
                                <div class="mt-3">
                                    <label class="form-label fw-bold">标签</label>
                                    <div class="form-control-plaintext">${this.escapeHtml(tags)}</div>
                                </div>
                            ` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 移除已存在的模态框
        const existingModal = document.getElementById('auditDetailModal');
        if (existingModal) {
            existingModal.remove();
        }
        
        // 添加新模态框到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('auditDetailModal'));
        modal.show();
        
        // 模态框关闭后清理
        document.getElementById('auditDetailModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }

    /**
     * 撤销审核
     */
    async revertAudit(auditId) {
        if (!confirm('确定要撤销这个审核吗？撤销后题目将重新进入待审核状态。')) {
            return;
        }
        
        try {
            showLoading('撤销审核中...');
            
            const response = await this.fetchAPI(`/api/admin/audit/revert/${auditId}`, {
                method: 'POST'
            });
            
            hideLoading();
            
            if (this.isResponseSuccess(response)) {
                this.showSuccess('撤销审核成功');
                this.loadAuditStats();
                this.loadTabData(this.currentTab, this.currentPage);
            } else {
                throw new Error(this.getResponseMessage(response) || '撤销审核失败');
            }
        } catch (error) {
            hideLoading();
            console.error('撤销审核失败:', error);
            this.showError('撤销审核失败: ' + error.message);
        }
    }

    // 分组审核数据加载方法（调用全局函数）
    async loadGroupAuditData(page = 1, keyword = '') {
        // 调用全局的 loadGroupAuditData 函数
        if (typeof window.loadGroupAuditData === 'function') {
            await window.loadGroupAuditData(page, keyword);
        } else if (typeof loadGroupAuditData === 'function') {
            // 如果全局函数可用，直接调用
            await loadGroupAuditData(page, keyword);
        } else {
            console.error('loadGroupAuditData function not found');
            // 显示错误状态
            const container = document.getElementById('groupAuditContainer');
            if (container) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <p class="text-muted">分组审核功能加载失败</p>
                        <button class="btn btn-outline-primary btn-sm" onclick="window.location.reload()">
                            <i class="fas fa-redo"></i> 刷新页面
                        </button>
                    </div>
                `;
            }
        }
    }
    
    // 批量通过
    async batchApprove() {
        const selected = Array.from(document.querySelectorAll('.audit-checkbox:checked')).map(cb => parseInt(cb.value));
        if (selected.length === 0) {
            this.showError('请选择要通过的审核项');
            return;
        }
        
        if (selected.length > 5000) {
            this.showError('单次批量操作不能超过5000个题目，请联系系统管理员或分批处理');
            return;
        }
        
        // 智能选择API端点和提示方式
        const isLargeBatch = selected.length > 1000;
        const apiEndpoint = isLargeBatch ? '/api/admin/audit/batch-large' : '/api/admin/audit/batch';
        
        // 根据数据量大小给出不同的确认提示
        let confirmMessage;
        if (selected.length > 1000) {
            confirmMessage = `您选择了 ${selected.length} 个题目进行超大批量审核，这可能需要较长时间。\n\n处理说明：\n• 系统将分批处理以确保稳定性\n• 预计耗时：${Math.ceil(selected.length / 200)} 分钟\n• 建议在处理期间不要关闭页面\n\n确定要继续吗？`;
        } else if (selected.length > 500) {
            confirmMessage = `您选择了 ${selected.length} 个题目进行大批量审核，可能需要等待一段时间。\n\n建议：\n• 500个以下操作更快\n• 可以分批处理提高效率\n\n确定继续吗？`;
        } else {
            confirmMessage = `确定要批量通过 ${selected.length} 个审核项吗？`;
        }
        
        if (!confirm(confirmMessage)) {
            return;
        }
        
        try {
            const loadingMessage = selected.length > 1000 ? 
                `超大批量审核中（${selected.length}个）...` : 
                '批量审核中...';
            showLoading(loadingMessage);
            
            // action和comment作为URL参数，auditIds作为JSON body
            const params = new URLSearchParams({
                action: 'approve',
                comment: selected.length > 1000 ? '超大批量通过' : '批量通过'
            });
            
            const response = await this.fetchAPI(`${apiEndpoint}?${params}`, {
                method: 'POST',
                body: JSON.stringify(selected)
            });
            
            hideLoading();
            
            if (this.isResponseSuccess(response)) {
                const data = this.getResponseData(response);
                const successCount = data.successCount || selected.length;
                const failureCount = data.failureCount || 0;
                const successRate = data.successRate || 0;
                
                let successMessage = `批量通过完成！成功 ${successCount} 个`;
                if (failureCount > 0) {
                    successMessage += `，失败 ${failureCount} 个`;
                }
                if (selected.length > 1000) {
                    successMessage += `（成功率: ${successRate.toFixed(1)}%）`;
                }
                
                this.showSuccess(successMessage);
                this.loadAuditStats();
                this.loadPendingAudits();
            } else {
                this.showError(this.getResponseMessage(response) || '批量审核失败');
            }
        } catch (error) {
            hideLoading();
            console.error('批量审核失败:', error);
            this.showError('批量审核失败: ' + error.message);
        }
    }
    
    // 批量拒绝
    async batchReject() {
        const selected = Array.from(document.querySelectorAll('.audit-checkbox:checked')).map(cb => parseInt(cb.value));
        if (selected.length === 0) {
            this.showError('请选择要拒绝的审核项');
            return;
        }
        
        if (selected.length > 5000) {
            this.showError('单次批量操作不能超过5000个题目，请联系系统管理员或分批处理');
            return;
        }
        
        // 智能选择API端点
        const isLargeBatch = selected.length > 1000;
        const apiEndpoint = isLargeBatch ? '/api/admin/audit/batch-large' : '/api/admin/audit/batch';
        
        // 根据数据量大小给出不同的提示
        let promptMessage;
        if (selected.length > 1000) {
            promptMessage = `您正在对 ${selected.length} 个题目进行超大批量拒绝操作。\n\n请输入拒绝原因（将应用于所有题目）:`;
        } else {
            promptMessage = `请输入批量拒绝的原因（将应用于 ${selected.length} 个审核项）:`;
        }
        
        const reason = prompt(promptMessage);
        if (!reason || reason.trim() === '') {
            return;
        }
        
        // 根据数据量大小给出不同的确认提示
        let confirmMessage;
        if (selected.length > 1000) {
            confirmMessage = `确认要拒绝 ${selected.length} 个题目吗？这是超大批量操作。\n\n处理说明：\n• 系统将分批处理以确保稳定性\n• 预计耗时：${Math.ceil(selected.length / 200)} 分钟\n• 拒绝原因："${reason}"\n\n确定要继续吗？`;
        } else if (selected.length > 500) {
            confirmMessage = `确认要拒绝 ${selected.length} 个题目吗？这是大批量操作，可能需要等待一段时间。\n\n拒绝原因："${reason}"\n\n确定继续吗？`;
        } else {
            confirmMessage = `确认要批量拒绝 ${selected.length} 个审核项吗？\n\n拒绝原因："${reason}"`;
        }
        
        if (!confirm(confirmMessage)) {
            return;
        }
        
        try {
            const loadingMessage = selected.length > 1000 ? 
                `超大批量拒绝中（${selected.length}个）...` : 
                '批量拒绝中...';
            showLoading(loadingMessage);
            
            // action和comment作为URL参数，auditIds作为JSON body
            const params = new URLSearchParams({
                action: 'reject',
                comment: reason.trim()
            });
            
            const response = await this.fetchAPI(`${apiEndpoint}?${params}`, {
                method: 'POST',
                body: JSON.stringify(selected)
            });
            
            hideLoading();
            
            if (this.isResponseSuccess(response)) {
                const data = this.getResponseData(response);
                const successCount = data.successCount || selected.length;
                const failureCount = data.failureCount || 0;
                const successRate = data.successRate || 0;
                
                let successMessage = `批量拒绝完成！成功 ${successCount} 个`;
                if (failureCount > 0) {
                    successMessage += `，失败 ${failureCount} 个`;
                }
                if (selected.length > 1000) {
                    successMessage += `（成功率: ${successRate.toFixed(1)}%）`;
                }
                
                this.showSuccess(successMessage);
                this.loadAuditStats();
                this.loadPendingAudits();
            } else {
                this.showError(this.getResponseMessage(response) || '批量审核失败');
            }
        } catch (error) {
            hideLoading();
            console.error('批量审核失败:', error);
            this.showError('批量审核失败: ' + error.message);
        }
    }
    
    // 智能审核
    async smartAudit() {
        if (!confirm('智能审核将基于题目质量、格式规范等自动判断，确定要启动吗？')) {
            return;
        }
        
        try {
            showLoading('智能审核分析中...');
            
            const response = await this.fetchAPI('/api/admin/audit/smart-audit', {
                method: 'POST'
            });
            
            hideLoading();
            
            if (this.isResponseSuccess(response)) {
                const result = this.getResponseData(response);
                this.showSmartAuditResult(result);
            } else {
                this.showError(this.getResponseMessage(response) || '智能审核失败');
            }
        } catch (error) {
            hideLoading();
            console.error('智能审核失败:', error);
            this.showError('智能审核失败: ' + error.message);
        }
    }
    
    // 显示智能审核结果
    showSmartAuditResult(result) {
        const modalHtml = `
            <div class="modal fade" id="smartAuditResultModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">智能审核结果</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <div class="card text-center bg-success text-white">
                                        <div class="card-body">
                                            <h4>${result.approved || 0}</h4>
                                            <p>建议通过</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card text-center bg-danger text-white">
                                        <div class="card-body">
                                            <h4>${result.rejected || 0}</h4>
                                            <p>建议拒绝</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card text-center bg-warning text-white">
                                        <div class="card-body">
                                            <h4>${result.manual || 0}</h4>
                                            <p>需人工审核</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <h6>智能审核规则:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> 格式规范、内容完整</li>
                                    <li><i class="fas fa-check text-success"></i> 知识点匹配度高</li>
                                    <li><i class="fas fa-times text-danger"></i> 格式错误、内容不完整</li>
                                    <li><i class="fas fa-times text-danger"></i> 重复题目</li>
                                    <li><i class="fas fa-exclamation text-warning"></i> 需要人工判断的边界情况</li>
                                </ul>
                            </div>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                智能审核仅供参考，最终审核结果请以人工审核为准。
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-success" onclick="adminAudit.applySmartAuditResult('approved')">
                                应用通过建议
                            </button>
                            <button type="button" class="btn btn-danger" onclick="adminAudit.applySmartAuditResult('rejected')">
                                应用拒绝建议
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 移除已存在的模态框
        const existingModal = document.getElementById('smartAuditResultModal');
        if (existingModal) {
            existingModal.remove();
        }
        
        // 添加新模态框到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('smartAuditResultModal'));
        modal.show();
    }
    
    // 应用智能审核结果
    async applySmartAuditResult(type) {
        if (!confirm(`确定要应用${type === 'approved' ? '通过' : '拒绝'}建议吗？`)) {
            return;
        }
        
        try {
            showLoading('应用智能审核结果...');
            
            const response = await this.fetchAPI('/api/admin/audit/apply-smart-result', {
                method: 'POST',
                body: JSON.stringify({ type })
            });
            
            hideLoading();
            
            if (this.isResponseSuccess(response)) {
                this.showSuccess('智能审核结果已应用');
                this.loadAuditStats();
                this.loadPendingAudits();
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('smartAuditResultModal'));
                modal.hide();
            } else {
                this.showError(this.getResponseMessage(response) || '应用失败');
            }
        } catch (error) {
            hideLoading();
            console.error('应用智能审核结果失败:', error);
            this.showError('应用失败: ' + error.message);
        }
    }
    
    // 按用户批量通过
    async approveAllByUser(userId) {
        if (!confirm(`确定要通过用户 ${userId} 的所有待审核题目吗？`)) {
            return;
        }
        
        try {
            showLoading('批量审核中...');
            
            const response = await this.fetchAPI('/api/admin/audit/batch-approve-by-user', {
                method: 'POST',
                body: JSON.stringify({ userId })
            });
            
            hideLoading();
            
            if (this.isResponseSuccess(response)) {
                const result = this.getResponseData(response);
                this.showSuccess(`成功通过 ${result.count || 0} 个审核项`);
                this.loadAuditStats();
                this.loadPendingAudits();
            } else {
                this.showError(this.getResponseMessage(response) || '批量审核失败');
            }
        } catch (error) {
            hideLoading();
            console.error('批量审核失败:', error);
            this.showError('批量审核失败: ' + error.message);
        }
    }

    /**
     * 增强的审核详情模态框显示
     */
    showEnhancedAuditDetailModal(auditDetail) {
        // 使用TopicAuditDTO的正确字段名
        const title = auditDetail.title || '题目标题';
        const type = auditDetail.type || '';
        const options = auditDetail.options || '';
        const knowledgeName = auditDetail.knowledgeName || auditDetail.knowId || '知识点';
        const submitterName = auditDetail.username || '提交者';
        const auditorName = auditDetail.auditorName || '审核员';
        const auditStatus = auditDetail.auditStatus || 0;
        const auditComment = auditDetail.auditComment || '';
        const submitTime = auditDetail.submitTime;
        const auditTime = auditDetail.auditTime;
        const answer = auditDetail.answer || '';
        const parse = auditDetail.parse || '';
        const score = auditDetail.score || '';
        const difficulty = auditDetail.difficulty || '';
        const tags = auditDetail.tags || '';
        const source = auditDetail.source || '';
        
        // 创建增强的详情模态框HTML
        const modalHtml = `
            <div class="modal fade" id="enhancedAuditDetailModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-light">
                            <h5 class="modal-title">
                                <i class="fas fa-clipboard-list text-primary"></i> 
                                题目详细预览
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="container-fluid">
                                <!-- 题目信息区域 -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <div class="card border-primary">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-question-circle"></i> 题目信息
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <label class="form-label fw-bold text-primary">题目标题</label>
                                                    <div class="p-3 bg-light rounded border">
                                                        <h6 class="mb-0">${this.escapeHtml(title)}</h6>
                                                    </div>
                                                </div>
                                                
                                                <div class="row mb-3">
                                                    <div class="col-md-4">
                                                        <label class="form-label fw-bold">题目类型</label>
                                                        <div>
                                                            <span class="badge bg-info fs-6">
                                                                ${this.getTopicTypeIcon(type)} ${this.getTopicTypeText(type)}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label class="form-label fw-bold">分值</label>
                                                        <div>
                                                            <span class="badge bg-success fs-6">
                                                                <i class="fas fa-star"></i> ${score || '未设置'} 分
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label class="form-label fw-bold">难度</label>
                                                        <div>
                                                            <span class="badge bg-warning fs-6">
                                                                ${this.getDifficultyIcon(difficulty)} ${this.getDifficultyText(difficulty)}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="mb-3">
                                                    <label class="form-label fw-bold text-primary">题目内容</label>
                                                    <div class="topic-content-preview p-3 border rounded bg-white">
                                                        ${this.formatTopicContent(options, type)}
                                                    </div>
                                                </div>
                                                
                                                ${answer ? `
                                                    <div class="mb-3">
                                                        <label class="form-label fw-bold text-success">正确答案</label>
                                                        <div class="answer-preview p-3 border rounded bg-success bg-opacity-10">
                                                            <i class="fas fa-check-circle text-success"></i>
                                                            ${this.formatAnswer(answer, type)}
                                                        </div>
                                                    </div>
                                                ` : ''}
                                                
                                                ${parse ? `
                                                    <div class="mb-3">
                                                        <label class="form-label fw-bold text-info">题目解析</label>
                                                        <div class="parse-preview p-3 border rounded bg-info bg-opacity-10">
                                                            <i class="fas fa-lightbulb text-info"></i>
                                                            <div class="mt-2">${this.escapeHtml(parse).replace(/\\n/g, '<br>')}</div>
                                                        </div>
                                                    </div>
                                                ` : ''}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 审核信息区域 -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="card border-secondary">
                                            <div class="card-header bg-secondary text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-user"></i> 提交信息
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <label class="form-label fw-bold">提交者</label>
                                                    <div>
                                                        <span class="badge bg-primary">
                                                            <i class="fas fa-user"></i> ${this.escapeHtml(submitterName)}
                                                        </span>
                                                    </div>
                                                </div>
                                                
                                                <div class="mb-3">
                                                    <label class="form-label fw-bold">知识点</label>
                                                    <div>
                                                        <span class="badge bg-info">
                                                            <i class="fas fa-tag"></i> ${this.escapeHtml(knowledgeName)}
                                                        </span>
                                                    </div>
                                                </div>
                                                
                                                <div class="mb-3">
                                                    <label class="form-label fw-bold">提交时间</label>
                                                    <div class="text-muted">
                                                        <i class="fas fa-clock"></i> ${this.formatTime(submitTime)}
                                                    </div>
                                                </div>
                                                
                                                ${source ? `
                                                    <div class="mb-3">
                                                        <label class="form-label fw-bold">题目来源</label>
                                                        <div class="text-muted">
                                                            <i class="fas fa-book"></i> ${this.escapeHtml(source)}
                                                        </div>
                                                    </div>
                                                ` : ''}
                                                
                                                ${tags ? `
                                                    <div class="mb-3">
                                                        <label class="form-label fw-bold">标签</label>
                                                        <div>
                                                            ${this.formatTags(tags)}
                                                        </div>
                                                    </div>
                                                ` : ''}
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="card border-${auditStatus === 0 ? 'warning' : (auditStatus === 1 ? 'success' : 'danger')}">
                                            <div class="card-header bg-${auditStatus === 0 ? 'warning' : (auditStatus === 1 ? 'success' : 'danger')} text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-clipboard-check"></i> 审核信息
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <label class="form-label fw-bold">审核状态</label>
                                                    <div>
                                                        <span class="badge bg-${auditStatus === 0 ? 'warning' : (auditStatus === 1 ? 'success' : 'danger')} fs-6">
                                                            ${auditStatus === 0 ? '<i class="fas fa-clock"></i> 待审核' : 
                                                              (auditStatus === 1 ? '<i class="fas fa-check"></i> 已通过' : 
                                                               '<i class="fas fa-times"></i> 已拒绝')}
                                                        </span>
                                                    </div>
                                                </div>
                                                
                                                ${auditStatus !== 0 ? `
                                                    <div class="mb-3">
                                                        <label class="form-label fw-bold">审核人</label>
                                                        <div>
                                                            <span class="badge bg-secondary">
                                                                <i class="fas fa-user-check"></i> ${this.escapeHtml(auditorName)}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="mb-3">
                                                        <label class="form-label fw-bold">审核时间</label>
                                                        <div class="text-muted">
                                                            <i class="fas fa-calendar-check"></i> ${this.formatTime(auditTime)}
                                                        </div>
                                                    </div>
                                                ` : ''}
                                                
                                                ${auditComment ? `
                                                    <div class="mb-3">
                                                        <label class="form-label fw-bold">审核意见</label>
                                                        <div class="p-3 border rounded bg-light">
                                                            <i class="fas fa-comment"></i>
                                                            <span class="ms-2">${this.escapeHtml(auditComment)}</span>
                                                        </div>
                                                    </div>
                                                ` : ''}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer bg-light">
                            ${auditStatus === 0 ? `
                                <button type="button" class="btn btn-success" onclick="quickApproveFromPreview('${auditDetail.id}')">
                                    <i class="fas fa-check"></i> 快速通过
                                </button>
                                <button type="button" class="btn btn-danger" onclick="quickRejectFromPreview('${auditDetail.id}')">
                                    <i class="fas fa-times"></i> 快速拒绝
                                </button>
                                <button type="button" class="btn btn-primary" onclick="showDetailedAuditForm('${auditDetail.id}')">
                                    <i class="fas fa-edit"></i> 详细审核
                                </button>
                            ` : ''}
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times"></i> 关闭
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 移除已存在的模态框
        const existingModal = document.getElementById('enhancedAuditDetailModal');
        if (existingModal) {
            existingModal.remove();
        }
        
        // 添加新模态框到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('enhancedAuditDetailModal'));
        modal.show();
        
        // 模态框关闭后清理
        document.getElementById('enhancedAuditDetailModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }
    
    /**
     * 格式化题目内容
     */
    formatTopicContent(content, type) {
        if (!content) return '<span class="text-muted">暂无内容</span>';
        
        // 1. 如果content已经是对象或数组，直接处理
        if (Array.isArray(content)) {
            return this.formatMultipleChoiceOptions(content);
        }
        
        // 2. 如果content是对象，尝试提取选项
        if (typeof content === 'object' && content !== null) {
            
            // 检查是否有options属性
            if (content.options && Array.isArray(content.options)) {
                return this.formatMultipleChoiceOptions(content.options);
            }
            
            // 检查是否有A、B、C、D等属性（对象形式的选项）
            const optionKeys = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
            const objectOptions = [];
            let hasOptions = false;
            
            for (const key of optionKeys) {
                if (content[key] !== undefined && content[key] !== null) {
                    objectOptions.push(content[key]);
                    hasOptions = true;
                }
            }
            
            if (hasOptions) {
                return this.formatMultipleChoiceOptions(objectOptions);
            }
            
            // 如果是其他对象格式，尝试转换为字符串
            try {
                const jsonString = JSON.stringify(content);
                return this.formatTopicContent(jsonString, type);
            } catch (e) {
                return '<span class="text-warning">选项格式错误</span>';
            }
        }
        
        // 3. 字符串格式处理
        const contentStr = String(content);
        
        try {
            // 尝试解析JSON格式的选项
            if (contentStr.startsWith('[') || contentStr.startsWith('{')) {
                const options = JSON.parse(contentStr);
                
                if (Array.isArray(options)) {
                    return this.formatMultipleChoiceOptions(options);
                }
                
                // 如果是对象，递归调用
                if (typeof options === 'object' && options !== null) {
                    return this.formatTopicContent(options, type);
                }
            }
        } catch (e) {
            // JSON解析失败，按普通文本处理
        }
        
        // 4. 处理普通文本内容
        let formattedContent = this.escapeHtml(contentStr);
        
        // 检测并格式化选择题选项（A. B. C. D. 格式）
        if (formattedContent.includes('A.') && formattedContent.includes('B.')) {
            formattedContent = this.formatChoiceOptions(formattedContent);
        }
        
        // 格式化填空题（下划线）
        formattedContent = formattedContent.replace(/_{3,}/g, '<span class="fill-blank">____</span>');
        
        // 换行处理
        formattedContent = formattedContent.replace(/\\n/g, '<br>').replace(/\n/g, '<br>');
        
        return formattedContent;
    }
    
    /**
     * 格式化选择题选项（JSON数组格式）
     */
    formatMultipleChoiceOptions(options) {
        if (!options || !Array.isArray(options)) {
            console.warn('formatMultipleChoiceOptions: 选项不是数组格式', options);
            return '<span class="text-warning">选项格式错误</span>';
        }
        
        const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
        let html = '<div class="options-container">';
        
        options.forEach((option, index) => {
            let optionLabel = '';
            let optionText = '';
            
            if (option === null || option === undefined) {
                optionLabel = optionLabels[index] || (index + 1);
                optionText = '(空选项)';
            } else if (typeof option === 'string') {
                // 简单字符串选项
                optionLabel = optionLabels[index] || (index + 1);
                optionText = option;
            } else if (typeof option === 'object') {
                // 处理 OptionDTO 格式：{key: "A", name: "选项内容"}
                if (option.key && option.name) {
                    optionLabel = option.key;
                    optionText = option.name;
                } 
                // 处理其他常见的对象格式
                else if (option.text) {
                    optionLabel = optionLabels[index] || (index + 1);
                    optionText = option.text;
                } else if (option.content) {
                    optionLabel = optionLabels[index] || (index + 1);
                    optionText = option.content;
                } else if (option.value) {
                    optionLabel = optionLabels[index] || (index + 1);
                    optionText = option.value;
                } else if (option.label && option.value) {
                    optionLabel = option.label;
                    optionText = option.value;
                } else {
                    // 如果是其他对象格式，尝试智能提取
                    optionLabel = optionLabels[index] || (index + 1);
                    
                    // 检查是否有 A、B、C、D 等键
                    const possibleKeys = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
                    let found = false;
                    for (const key of possibleKeys) {
                        if (option[key] !== undefined && option[key] !== null) {
                            optionLabel = key;
                            optionText = String(option[key]);
                            found = true;
                            break;
                        }
                    }
                    
                    if (!found) {
                        // 最后尝试转换为JSON字符串
                        try {
                            optionText = JSON.stringify(option);
                        } catch (e) {
                            optionText = '[无法解析的选项对象]';
                        }
                    }
                }
            } else {
                // 其他类型直接转换为字符串
                optionLabel = optionLabels[index] || (index + 1);
                optionText = String(option);
            }
            
            html += `
                <div class="option-item d-flex align-items-start mb-2 p-2 border rounded bg-light">
                    <span class="option-label badge bg-primary me-3">${optionLabel}</span>
                    <span class="option-text flex-grow-1">${this.escapeHtml(optionText)}</span>
                </div>
            `;
        });
        
        html += '</div>';
        return html;
    }
    
    /**
     * 格式化选择题选项（文本格式）
     */
    formatChoiceOptions(content) {
        const optionPattern = /([A-H]\.)\s*([^A-H\.]*?)(?=[A-H]\.|$)/g;
        let formattedContent = content;
        
        // 替换选项格式
        formattedContent = formattedContent.replace(optionPattern, (match, label, text) => {
            return `
                <div class="option-item d-flex align-items-start mb-2 p-2 border rounded bg-light">
                    <span class="option-label badge bg-primary me-3">${label}</span>
                    <span class="option-text flex-grow-1">${text.trim()}</span>
                </div>
            `;
        });
        
        return `<div class="options-container">${formattedContent}</div>`;
    }
    
    /**
     * 格式化答案
     */
    formatAnswer(answer, type) {
        if (!answer) return '<span class="text-muted">无答案</span>';
        
        let formattedAnswer = this.escapeHtml(answer);
        
        // 根据题目类型格式化答案
        switch (type) {
            case 'choice':
            case 'single':
                // 单选题答案
                if (formattedAnswer.length === 1 && /[A-H]/i.test(formattedAnswer)) {
                    return `<span class="badge bg-success fs-6">${formattedAnswer.toUpperCase()}</span>`;
                }
                break;
            case 'multiple':
                // 多选题答案
                if (/^[A-H,\s]+$/i.test(formattedAnswer)) {
                    const options = formattedAnswer.split(/[,\s]+/).filter(o => o.trim());
                    return options.map(opt => 
                        `<span class="badge bg-success me-1">${opt.toUpperCase()}</span>`
                    ).join('');
                }
                break;
            case 'judge':
            case 'judgment':
                // 判断题答案
                if (/^(对|错|是|否|true|false|T|F|√|×)$/i.test(formattedAnswer.trim())) {
                    const isCorrect = /^(对|是|true|T|√)$/i.test(formattedAnswer.trim());
                    return `<span class="badge bg-${isCorrect ? 'success' : 'danger'} fs-6">
                        <i class="fas fa-${isCorrect ? 'check' : 'times'}"></i> 
                        ${isCorrect ? '正确' : '错误'}
                    </span>`;
                }
                break;
        }
        
        // 默认格式化（填空题、简答题等）
        return `<div class="answer-text">${formattedAnswer.replace(/\\n/g, '<br>').replace(/\n/g, '<br>')}</div>`;
    }
    
    /**
     * 格式化标签
     */
    formatTags(tags) {
        if (!tags) return '';
        
        const tagArray = tags.split(/[,，\s]+/).filter(tag => tag.trim());
        return tagArray.map(tag => 
            `<span class="badge bg-outline-secondary me-1">${this.escapeHtml(tag.trim())}</span>`
        ).join('');
    }
    
    /**
     * 获取题目类型图标
     */
    getTopicTypeIcon(type) {
        const iconMap = {
            'choice': '<i class="fas fa-list-ul"></i>',
            'single': '<i class="fas fa-dot-circle"></i>',
            'multiple': '<i class="fas fa-check-square"></i>',
            'judge': '<i class="fas fa-balance-scale"></i>',
            'judgment': '<i class="fas fa-balance-scale"></i>',
            'fill': '<i class="fas fa-edit"></i>',
            'blank': '<i class="fas fa-edit"></i>',
            'short': '<i class="fas fa-align-left"></i>',
            'essay': '<i class="fas fa-file-alt"></i>',
            'subjective': '<i class="fas fa-file-alt"></i>'
        };
        return iconMap[type] || '<i class="fas fa-question"></i>';
    }
    
    /**
     * 获取题目类型文本
     */
    getTopicTypeText(type) {
        const typeMap = {
            'choice': '单选题',
            'single': '单选题',
            'multiple': '多选题',
            'judge': '判断题',
            'judgment': '判断题',
            'fill': '填空题',
            'blank': '填空题',
            'short': '简答题',
            'essay': '论述题',
            'subjective': '主观题'
        };
        return typeMap[type] || '未知类型';
    }
    
    /**
     * 获取难度图标
     */
    getDifficultyIcon(difficulty) {
        const level = parseFloat(difficulty) || 0;
        if (level <= 2) return '<i class="fas fa-star"></i>';
        if (level <= 4) return '<i class="fas fa-star"></i><i class="fas fa-star"></i>';
        return '<i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i>';
    }
    
    /**
     * 获取难度文本（与后端难度定义保持一致）
     */
    getDifficultyText(difficulty) {
        if (difficulty === null || difficulty === undefined) {
            return '未设置';
        }
        
        const difficultyValue = parseFloat(difficulty);
        if (isNaN(difficultyValue)) {
            return '未知';
        }
        
        // 与后端难度定义保持一致：≤0.4为简单，≤0.7为中等，≤1为难题
        if (difficultyValue <= 0.4) {
            return '简单';
        } else if (difficultyValue <= 0.7) {
            return '中等';
        } else if (difficultyValue <= 1.0) {
            return '困难';
        } else {
            return '超难';
        }
    }
    
    /**
     * 获取题目内容的简洁预览（用于列表显示）
     */
    getTopicContentPreview(content, maxLength = 50) {
        if (!content) return '';
        
        let previewText = '';
        
        try {
            // 1. 如果content是数组，直接处理
            if (Array.isArray(content)) {
                previewText = this.extractOptionsText(content);
            }
            // 2. 如果content是对象，处理
            else if (typeof content === 'object' && content !== null) {
                previewText = this.extractOptionsText(content);
            }
            // 3. 如果content是字符串，尝试解析
            else {
                const contentStr = String(content);
                
                // 尝试JSON解析
                if (contentStr.startsWith('[') || contentStr.startsWith('{')) {
                    try {
                        const parsed = JSON.parse(contentStr);
                        previewText = this.extractOptionsText(parsed);
                    } catch (e) {
                        // JSON解析失败，按普通文本处理
                        previewText = contentStr;
                    }
                } else {
                    previewText = contentStr;
                }
            }
        } catch (e) {
            previewText = String(content);
        }
        
        // 清理和截断文本
        if (!previewText) {
            previewText = String(content);
        }
        
        // 移除HTML标签和多余空白
        previewText = previewText.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
        
        // 截断并添加省略号
        if (previewText.length > maxLength) {
            previewText = previewText.substring(0, maxLength) + '...';
        }
        
        return this.escapeHtml(previewText);
    }
    
    /**
     * 从选项数据中提取纯文本内容
     */
    extractOptionsText(options) {
        if (!options) return '';
        
        try {
            // 如果是数组
            if (Array.isArray(options)) {
                const optionTexts = [];
                
                options.forEach((option, index) => {
                    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
                    
                    if (option === null || option === undefined) {
                        return;
                    } else if (typeof option === 'string') {
                        optionTexts.push(`${optionLabels[index] || (index + 1)}. ${option}`);
                    } else if (typeof option === 'object') {
                        // 处理 OptionDTO 格式
                        if (option.key && option.name) {
                            optionTexts.push(`${option.key}. ${option.name}`);
                        } else if (option.text) {
                            optionTexts.push(`${optionLabels[index] || (index + 1)}. ${option.text}`);
                        } else if (option.content) {
                            optionTexts.push(`${optionLabels[index] || (index + 1)}. ${option.content}`);
                        } else if (option.value) {
                            optionTexts.push(`${optionLabels[index] || (index + 1)}. ${option.value}`);
                        } else {
                            // 检查是否有 A、B、C、D 等键
                            const possibleKeys = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
                            let found = false;
                            for (const key of possibleKeys) {
                                if (option[key] !== undefined && option[key] !== null) {
                                    optionTexts.push(`${key}. ${String(option[key])}`);
                                    found = true;
                                    break;
                                }
                            }
                            if (!found) {
                                optionTexts.push(`${optionLabels[index] || (index + 1)}. [选项内容]`);
                            }
                        }
                    } else {
                        optionTexts.push(`${optionLabels[index] || (index + 1)}. ${String(option)}`);
                    }
                });
                
                return optionTexts.join(' ');
            }
            // 如果是对象
            else if (typeof options === 'object') {
                // 检查是否有options属性
                if (options.options && Array.isArray(options.options)) {
                    return this.extractOptionsText(options.options);
                }
                
                // 检查是否有A、B、C、D等属性
                const optionKeys = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
                const objectOptions = [];
                
                for (const key of optionKeys) {
                    if (options[key] !== undefined && options[key] !== null) {
                        objectOptions.push(`${key}. ${String(options[key])}`);
                    }
                }
                
                if (objectOptions.length > 0) {
                    return objectOptions.join(' ');
                }
                
                // 否则返回JSON字符串的简化版本
                return JSON.stringify(options);
            }
            // 如果是字符串
            else {
                return String(options);
            }
        } catch (e) {
            return String(options);
        }
    }
}

// 全局变量
let adminAudit;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    adminAudit = new AdminAuditManagement();
    window.adminAudit = adminAudit;
    
    // 确保全局函数可用
    window.loadGroupAuditData = loadGroupAuditData;
});

// ================== 增强的题目预览支持函数 ==================

/**
 * 从预览模态框快速通过
 */
async function quickApproveFromPreview(auditId) {
    if (!confirm('确定要快速通过这个题目吗？')) {
        return;
    }
    
    try {
        const response = await window.adminAudit.fetchAPI(`/api/admin/audit/quick-approve/${auditId}`, {
            method: 'POST',
            body: JSON.stringify({
                comment: '从预览快速通过'
            })
        });
        
        if (window.adminAudit.isResponseSuccess(response)) {
            window.adminAudit.showSuccess('快速通过成功');
            
            // 关闭预览模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('enhancedAuditDetailModal'));
            if (modal) modal.hide();
            
            // 刷新数据
            window.adminAudit.loadAuditStats();
            window.adminAudit.loadTabData(window.adminAudit.currentTab, window.adminAudit.currentPage);
        } else {
            window.adminAudit.showError(window.adminAudit.getResponseMessage(response) || '快速通过失败');
        }
    } catch (error) {
        console.error('快速通过失败:', error);
        window.adminAudit.showError('快速通过失败: ' + error.message);
    }
}

/**
 * 从预览模态框快速拒绝
 */
async function quickRejectFromPreview(auditId) {
    const reason = prompt('请输入拒绝原因：');
    if (!reason) {
        return;
    }
    
    try {
        const response = await window.adminAudit.fetchAPI(`/api/admin/audit/quick-reject/${auditId}?reason=${encodeURIComponent(reason)}`, {
            method: 'POST'
        });
        
        if (window.adminAudit.isResponseSuccess(response)) {
            window.adminAudit.showSuccess('快速拒绝成功');
            
            // 关闭预览模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('enhancedAuditDetailModal'));
            if (modal) modal.hide();
            
            // 刷新数据
            window.adminAudit.loadAuditStats();
            window.adminAudit.loadTabData(window.adminAudit.currentTab, window.adminAudit.currentPage);
        } else {
            window.adminAudit.showError(window.adminAudit.getResponseMessage(response) || '快速拒绝失败');
        }
    } catch (error) {
        console.error('快速拒绝失败:', error);
        window.adminAudit.showError('快速拒绝失败: ' + error.message);
    }
}

/**
 * 显示详细审核表单
 */
async function showDetailedAuditForm(auditId) {
    // 关闭预览模态框
    const previewModal = bootstrap.Modal.getInstance(document.getElementById('enhancedAuditDetailModal'));
    if (previewModal) previewModal.hide();
    
    // 延迟显示审核表单，确保预览模态框完全关闭
    setTimeout(() => {
        showAuditModal(auditId);
    }, 300);
}

/**
 * 增强的题目预览（全局函数）
 */
async function showEnhancedTopicPreview(auditId) {
    if (window.adminAudit) {
        await window.adminAudit.viewAuditDetail(auditId);
    } else {
        console.error('adminAudit对象不存在');
    }
}

/**
 * 生成增强的题目显示内容（用于审核模态框）
 */
function generateEnhancedTopicDisplay(audit) {
    // 适配TopicAuditDTO字段名
    const title = audit.title || audit.topicTitle || '题目标题';
    const type = audit.type || '';
    const options = audit.options || audit.content || audit.topicContent || '';
    const answer = audit.answer || '';
    const parse = audit.parse || '';
    const score = audit.score || '';
    const difficulty = audit.difficulty || '';
    const knowledgeName = audit.knowledgeName || audit.knowledgePoint || audit.knowId || '知识点';
    const submitterName = audit.username || audit.submitterName || '提交者';
    const submitTime = audit.submitTime;
    const tags = audit.tags || '';
    const source = audit.source || '';
    const auditStatus = audit.auditStatus !== undefined ? audit.auditStatus : audit.status;
    const auditorName = audit.auditorName || '';
    const auditTime = audit.auditTime;
    const auditComment = audit.auditComment || '';
    
    // 生成完整的HTML内容
    let html = `
        <div class="enhanced-audit-topic-display">
            <!-- 题目标题和基本信息 -->
            <div class="topic-header mb-3">
                <h5 class="topic-title text-primary mb-2">
                    <i class="fas fa-question-circle me-2"></i>
                    ${window.adminAudit.escapeHtml(title)}
                </h5>
                
                <div class="topic-meta d-flex flex-wrap gap-2 mb-3">
                    <span class="badge bg-secondary fs-6">
                        ${getTopicTypeIcon(type)} ${getTopicTypeText(type)}
                    </span>
                    ${score ? `
                        <span class="badge bg-success fs-6">
                            <i class="fas fa-star"></i> ${score} 分
                        </span>
                    ` : ''}
                    ${difficulty ? `
                        <span class="badge bg-warning fs-6">
                            ${getDifficultyIcon(difficulty)} ${getDifficultyText(difficulty)}
                        </span>
                    ` : ''}
                    <span class="badge bg-info fs-6">
                        <i class="fas fa-tag"></i> ${window.adminAudit.escapeHtml(knowledgeName)}
                    </span>
                </div>
            </div>
            
            <!-- 题目内容 -->
            <div class="topic-content-section mb-3">
                <label class="form-label fw-bold text-primary">
                    <i class="fas fa-file-text"></i> 题目内容
                </label>
                <div class="topic-content-display p-3 border rounded bg-light">
                    ${window.adminAudit.formatTopicContent(options, type)}
                </div>
            </div>
            
            <!-- 答案部分 -->
            ${answer ? `
                <div class="answer-section mb-3">
                    <label class="form-label fw-bold text-success">
                        <i class="fas fa-check-circle"></i> 正确答案
                    </label>
                    <div class="answer-display p-3 border rounded bg-success bg-opacity-10 border-success">
                        ${formatAnswerForDisplay(answer, type)}
                    </div>
                </div>
            ` : ''}
            
            <!-- 解析部分 -->
            ${parse ? `
                <div class="parse-section mb-3">
                    <label class="form-label fw-bold text-info">
                        <i class="fas fa-lightbulb"></i> 题目解析
                    </label>
                    <div class="parse-display p-3 border rounded bg-info bg-opacity-10 border-info">
                        ${window.adminAudit.escapeHtml(parse).replace(/\\n/g, '<br>').replace(/\n/g, '<br>')}
                    </div>
                </div>
            ` : ''}
            
            <!-- 提交信息 -->
            <div class="submission-info mb-3">
                <label class="form-label fw-bold text-secondary">
                    <i class="fas fa-info-circle"></i> 提交信息
                </label>
                <div class="info-grid row g-2">
                    <div class="col-md-6">
                        <div class="info-item p-2 bg-light rounded">
                            <small class="text-muted">提交者</small><br>
                            <span class="badge bg-primary">${window.adminAudit.escapeHtml(submitterName)}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item p-2 bg-light rounded">
                            <small class="text-muted">提交时间</small><br>
                            <span class="text-dark">${window.adminAudit.formatTime(submitTime)}</span>
                        </div>
                    </div>
                    ${source ? `
                        <div class="col-md-6">
                            <div class="info-item p-2 bg-light rounded">
                                <small class="text-muted">题目来源</small><br>
                                <span class="text-dark">${window.adminAudit.escapeHtml(source)}</span>
                            </div>
                        </div>
                    ` : ''}
                    ${tags ? `
                        <div class="col-md-6">
                            <div class="info-item p-2 bg-light rounded">
                                <small class="text-muted">标签</small><br>
                                <span class="text-dark">${window.adminAudit.escapeHtml(tags)}</span>
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
            
            <!-- 审核状态 -->
            ${auditStatus !== undefined && auditStatus !== 0 ? `
                <div class="audit-status mb-3">
                    <label class="form-label fw-bold">
                        <i class="fas fa-clipboard-check"></i> 审核状态
                    </label>
                    <div class="status-display p-3 border rounded bg-${auditStatus === 1 ? 'success' : 'danger'} bg-opacity-10 border-${auditStatus === 1 ? 'success' : 'danger'}">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge bg-${auditStatus === 1 ? 'success' : 'danger'} fs-6 me-2">
                                <i class="fas fa-${auditStatus === 1 ? 'check' : 'times'}"></i> 
                                ${auditStatus === 1 ? '已通过' : '已拒绝'}
                            </span>
                            ${auditorName ? `<span class="text-muted">审核人: ${window.adminAudit.escapeHtml(auditorName)}</span>` : ''}
                        </div>
                        ${auditTime ? `<div class="small text-muted mb-2">审核时间: ${window.adminAudit.formatTime(auditTime)}</div>` : ''}
                        ${auditComment ? `
                            <div class="audit-comment">
                                <strong>审核意见:</strong><br>
                                ${window.adminAudit.escapeHtml(auditComment)}
                            </div>
                        ` : ''}
                    </div>
                </div>
            ` : ''}
        </div>
    `;
    
    return html;
}

/**
 * 格式化答案用于显示（简化版本）
 */
function formatAnswerForDisplay(answer, type) {
    if (!answer) return '<span class="text-muted">无答案</span>';
    
    let formattedAnswer = window.adminAudit.escapeHtml(answer);
    
    // 根据题目类型格式化答案
    switch (type) {
        case 'choice':
        case 'single':
            // 单选题答案
            if (formattedAnswer.length === 1 && /[A-H]/i.test(formattedAnswer)) {
                return `<span class="badge bg-success fs-6">${formattedAnswer.toUpperCase()}</span>`;
            }
            break;
        case 'multiple':
            // 多选题答案
            if (/^[A-H,\s]+$/i.test(formattedAnswer)) {
                const options = formattedAnswer.split(/[,\s]+/).filter(o => o.trim());
                return options.map(opt => 
                    `<span class="badge bg-success me-1">${opt.toUpperCase()}</span>`
                ).join('');
            }
            break;
        case 'judge':
        case 'judgment':
            // 判断题答案
            if (/^(对|错|是|否|true|false|T|F|√|×)$/i.test(formattedAnswer.trim())) {
                const isCorrect = /^(对|是|true|T|√)$/i.test(formattedAnswer.trim());
                return `<span class="badge bg-${isCorrect ? 'success' : 'danger'} fs-6">
                    <i class="fas fa-${isCorrect ? 'check' : 'times'}"></i> 
                    ${isCorrect ? '正确' : '错误'}
                </span>`;
            }
            break;
    }
    
    // 默认格式化（填空题、简答题等）
    return `<div class="answer-text">${formattedAnswer.replace(/\\n/g, '<br>').replace(/\n/g, '<br>')}</div>`;
}

/**
 * 获取题目类型图标（简化版本）
 */
function getTopicTypeIcon(type) {
    const iconMap = {
        'choice': '<i class="fas fa-dot-circle"></i>',
        'single': '<i class="fas fa-dot-circle"></i>',
        'multiple': '<i class="fas fa-check-square"></i>',
        'judge': '<i class="fas fa-balance-scale"></i>',
        'judgment': '<i class="fas fa-balance-scale"></i>',
        'fill': '<i class="fas fa-edit"></i>',
        'blank': '<i class="fas fa-edit"></i>',
        'short': '<i class="fas fa-align-left"></i>',
        'essay': '<i class="fas fa-file-alt"></i>',
        'subjective': '<i class="fas fa-file-alt"></i>'
    };
    return iconMap[type] || '<i class="fas fa-question"></i>';
}

/**
 * 获取题目类型文本（简化版本）
 */
function getTopicTypeText(type) {
    const typeMap = {
        'choice': '单选题',
        'single': '单选题',
        'multiple': '多选题',
        'judge': '判断题',
        'judgment': '判断题',
        'fill': '填空题',
        'blank': '填空题',
        'short': '简答题',
        'essay': '论述题',
        'subjective': '主观题'
    };
    return typeMap[type] || '未知类型';
}

/**
 * 获取难度图标（简化版本）
 */
function getDifficultyIcon(difficulty) {
    const level = parseFloat(difficulty) || 0;
    if (level <= 2) return '<i class="fas fa-star"></i>';
    if (level <= 4) return '<i class="fas fa-star"></i><i class="fas fa-star"></i>';
    return '<i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i>';
}

/**
 * 获取难度文本（与后端难度定义保持一致）
 */
function getDifficultyText(difficulty) {
    if (difficulty === null || difficulty === undefined) {
        return '未设置';
    }
    
    const difficultyValue = parseFloat(difficulty);
    if (isNaN(difficultyValue)) {
        return '未知';
    }
    
    // 与后端难度定义保持一致：≤0.4为简单，≤0.7为中等，≤1为难题
    if (difficultyValue <= 0.4) {
        return '简单';
    } else if (difficultyValue <= 0.7) {
        return '中等';
    } else if (difficultyValue <= 1.0) {
        return '困难';
    } else {
        return '超难';
    }
}

// 全局函数
function toggleSelectAll(checkbox) {
    if (window.adminAudit) {
        window.adminAudit.toggleSelectAll(checkbox);
    } else {
        // 降级处理
        const checkboxes = document.querySelectorAll('.audit-checkbox');
        checkboxes.forEach(cb => cb.checked = checkbox.checked);
    }
}

async function showAuditModal(auditId) {
    try {
        const response = await window.adminAudit.fetchAPI(`/api/admin/audit/detail/${auditId}`);
        
        if (window.adminAudit.isResponseSuccess(response)) {
            const audit = window.adminAudit.getResponseData(response);
            
            // 填充审核表单
            document.getElementById('auditId').value = audit.id;
            
            // 生成完整的题目展示内容
            const topicContentHtml = generateEnhancedTopicDisplay(audit);
            document.getElementById('auditTopicContent').innerHTML = topicContentHtml;
            
            // 重置表单
            document.getElementById('auditForm').reset();
            document.getElementById('auditId').value = audit.id;
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('auditModal'));
            modal.show();
        } else {
            window.adminAudit.showError('获取审核详情失败: ' + window.adminAudit.getResponseMessage(response));
        }
    } catch (error) {
        console.error('Failed to show audit modal:', error);
        window.adminAudit.showError('网络错误，无法获取审核详情');
    }
}

async function showAuditDetail(auditId) {
    // 显示审核详情（只读模式）
    await showAuditModal(auditId);
    
    // 禁用表单元素，变为只读模式
    const form = document.getElementById('auditForm');
    const inputs = form.querySelectorAll('input, textarea');
    inputs.forEach(input => input.disabled = true);
    
    // 隐藏提交按钮
    const submitBtn = form.querySelector('button[onclick="submitAudit()"]');
    if (submitBtn) {
        submitBtn.style.display = 'none';
    }
}

async function submitAudit() {
    const form = document.getElementById('auditForm');
    const formData = new FormData(form);
    
    // 验证表单
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const auditData = {
        auditId: formData.get('auditId'),
        auditResult: parseInt(formData.get('auditResult')),
        auditComment: formData.get('auditComment')
    };
    
    // 如果是拒绝但没有填写意见
    if (auditData.auditResult === 2 && !auditData.auditComment) {
        window.adminAudit.showError('拒绝时必须填写审核意见');
        return;
    }
    
    try {
        const response = await window.adminAudit.fetchAPI('/api/admin/audit/submit', {
            method: 'POST',
            body: JSON.stringify(auditData)
        });
        
        if (window.adminAudit.isResponseSuccess(response)) {
            window.adminAudit.showSuccess('审核提交成功');
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('auditModal'));
            modal.hide();
            
            // 刷新数据
            window.adminAudit.loadAuditStats();
            window.adminAudit.loadTabData(window.adminAudit.currentTab, window.adminAudit.currentPage);
        } else {
            window.adminAudit.showError(window.adminAudit.getResponseMessage(response) || '审核提交失败');
        }
    } catch (error) {
        console.error('Failed to submit audit:', error);
        window.adminAudit.showError('网络错误，审核提交失败');
    }
}

async function quickApprove(auditId) {
    if (!confirm('确定要快速通过这个题目吗？')) {
        return;
    }
    
    try {
        const response = await window.adminAudit.fetchAPI(`/api/admin/audit/quick-approve/${auditId}`, {
            method: 'POST'
        });
        
        if (window.adminAudit.isResponseSuccess(response)) {
            window.adminAudit.showSuccess('快速通过成功');
            window.adminAudit.loadAuditStats();
            window.adminAudit.loadTabData(window.adminAudit.currentTab, window.adminAudit.currentPage);
        } else {
            window.adminAudit.showError(window.adminAudit.getResponseMessage(response) || '快速通过失败');
        }
    } catch (error) {
        console.error('Failed to quick approve:', error);
        window.adminAudit.showError('网络错误，快速通过失败');
    }
}

async function quickReject(auditId) {
    const reason = prompt('请输入拒绝原因：');
    if (!reason) {
        return;
    }
    
    try {
        const response = await window.adminAudit.fetchAPI(`/api/admin/audit/quick-reject/${auditId}?reason=${encodeURIComponent(reason)}`, {
            method: 'POST'
        });
        
        if (window.adminAudit.isResponseSuccess(response)) {
            window.adminAudit.showSuccess('快速拒绝成功');
            window.adminAudit.loadAuditStats();
            window.adminAudit.loadTabData(window.adminAudit.currentTab, window.adminAudit.currentPage);
        } else {
            window.adminAudit.showError(window.adminAudit.getResponseMessage(response) || '快速拒绝失败');
        }
    } catch (error) {
        console.error('Failed to quick reject:', error);
        window.adminAudit.showError('网络错误，快速拒绝失败');
    }
}

function showBatchAuditModal() {
    const selectedAudits = document.querySelectorAll('.audit-checkbox:checked');
    if (selectedAudits.length === 0) {
        window.adminAudit.showError('请先选择要批量操作的审核项');
        return;
    }
    
    const modal = new bootstrap.Modal(document.getElementById('batchAuditModal'));
    modal.show();
}

async function submitBatchAudit() {
    const selectedAudits = document.querySelectorAll('.audit-checkbox:checked');
    const auditIds = Array.from(selectedAudits).map(cb => parseInt(cb.value));
    
    if (auditIds.length === 0) {
        window.adminAudit.showError('请先选择要批量操作的审核项');
        return;
    }
    
    if (auditIds.length > 1000) {
        window.adminAudit.showError('单次批量操作不能超过1000个题目，请分批处理');
        return;
    }
    
    // 对于大批量操作给出友好提示
    if (auditIds.length > 500) {
        if (!confirm(`您选择了 ${auditIds.length} 个题目进行批量操作，数量较大可能需要等待一段时间。\n\n建议：\n• 500个以下操作更快\n• 可以分批处理提高效率\n\n确定继续吗？`)) {
            return;
        }
    }
    
    const form = document.getElementById('batchAuditForm');
    const formData = new FormData(form);
    
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const action = formData.get('batchAction');
    const comment = formData.get('batchComment');
    
    if (action === 'reject' && (!comment || comment.trim() === '')) {
        window.adminAudit.showError('拒绝时必须填写原因');
        return;
    }
    
    if (!confirm(`确定要批量${action === 'approve' ? '通过' : '拒绝'} ${auditIds.length} 个审核项吗？`)) {
        return;
    }
    
    try {
        showLoading('批量审核中...');
        
        const response = await window.adminAudit.fetchAPI(`/api/admin/audit/batch?action=${action}&comment=${encodeURIComponent(comment || '')}`, {
            method: 'POST',
            body: JSON.stringify(auditIds)
        });
        
        hideLoading();
        
        if (window.adminAudit.isResponseSuccess(response)) {
            const data = window.adminAudit.getResponseData(response);
            const successCount = data.successCount || auditIds.length;
            const failureCount = data.failureCount || 0;
            
            window.adminAudit.showSuccess(`批量${action === 'approve' ? '通过' : '拒绝'}完成！成功 ${successCount} 个${failureCount > 0 ? `，失败 ${failureCount} 个` : ''}`);
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('batchAuditModal'));
            modal.hide();
            
            // 刷新数据
            window.adminAudit.loadAuditStats();
            window.adminAudit.loadTabData(window.adminAudit.currentTab, window.adminAudit.currentPage);
        } else {
            window.adminAudit.showError(window.adminAudit.getResponseMessage(response) || '批量操作失败');
        }
    } catch (error) {
        hideLoading();
        console.error('Failed to batch audit:', error);
        window.adminAudit.showError('网络错误，批量操作失败');
    }
}

function exportAuditData() {
    window.adminAudit.showError('导出功能开发中...');
}

function showUserProfile() {
    window.adminAudit.showError('个人资料功能开发中...');
}

function showSettings() {
    window.adminAudit.showError('系统设置功能开发中...');
}

async function logout() {
    if (!confirm('确定要退出登录吗？')) {
        return;
    }
    
    try {
        const token = localStorage.getItem('adminToken') || sessionStorage.getItem('adminToken');
        
        if (token) {
            // 调用后端退出登录API
            await fetch('/api/admin/logout', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
        }
        
        // 清除本地存储
        localStorage.removeItem('adminToken');
        sessionStorage.removeItem('adminToken');
        
        // 跳转到管理员登录页
        window.location.href = '/admin/login';
    } catch (error) {
        console.error('退出登录失败:', error);
        // 即使失败也要清除本地存储并跳转
        localStorage.removeItem('adminToken');
        sessionStorage.removeItem('adminToken');
        window.location.href = '/admin/login';
    }
}

// 分组审核相关变量
let currentGroupPage = 1;
let currentUserId = null;
let currentDate = null;

/**
 * 渲染分组审核表格
 */
function renderGroupAuditTable(data) {
    const container = document.getElementById('groupAuditContainer');
    
    if (!data.records || data.records.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无用户审核分组数据</p>
            </div>
        `;
        return;
    }
    
    let tableHtml = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>用户信息</th>
                        <th>提交日期</th>
                        <th>批次信息</th>
                        <th>题目数量</th>
                        <th>待审核</th>
                        <th>已通过</th>
                        <th>已拒绝</th>
                        <th>题目类型分布</th>
                        <th>提交时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    data.records.forEach(group => {
        const approvalRate = group.topicCount > 0 ? (group.approvedCount / group.topicCount * 100).toFixed(1) : '0.0';
        const statusClass = group.pendingCount > 0 ? 'warning' : 
                           group.approvedCount > 0 && group.rejectedCount === 0 ? 'success' : 
                           group.rejectedCount > 0 && group.approvedCount === 0 ? 'danger' : 'info';
        
        // 构建题目类型分布
        const typeDistribution = [];
        if (group.choiceCount > 0) typeDistribution.push(`单选:${group.choiceCount}`);
        if (group.multipleCount > 0) typeDistribution.push(`多选:${group.multipleCount}`);
        if (group.judgeCount > 0) typeDistribution.push(`判断:${group.judgeCount}`);
        if (group.fillCount > 0) typeDistribution.push(`填空:${group.fillCount}`);
        if (group.shortCount > 0) typeDistribution.push(`简答:${group.shortCount}`);
        if (group.essayCount > 0) typeDistribution.push(`论述:${group.essayCount}`);
        
        const typeDistributionText = typeDistribution.length > 0 ? typeDistribution.join('<br>') : '无';
        
        // 批次信息处理
        const batchInfo = group.batchInfo || '单批次';
        const batchClass = batchInfo.includes('多批次') ? 'text-warning' : 'text-info';
        
        tableHtml += `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div>
                            <strong>${group.username}</strong>
                            <br><small class="text-muted">ID: ${safeParseId(group.userId)}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="fw-bold">${group.submitDate}</span>
                    ${group.submitHour ? `<br><small class="text-muted">${group.submitHour}</small>` : ''}
                </td>
                <td>
                    <span class="badge bg-light ${batchClass} border">
                        <i class="fas fa-layer-group me-1"></i>
                        ${batchInfo}
                    </span>
                </td>
                <td>
                    <span class="badge bg-primary fs-6">${group.topicCount}</span>
                </td>
                <td>
                    <span class="badge bg-warning fs-6">${group.pendingCount}</span>
                </td>
                <td>
                    <span class="badge bg-success fs-6">${group.approvedCount}</span>
                </td>
                <td>
                    <span class="badge bg-danger fs-6">${group.rejectedCount}</span>
                </td>
                <td>
                    <small class="text-muted">${typeDistributionText}</small>
                </td>
                <td>
                    <small class="text-muted">
                        ${group.earliestSubmitTime || '---'}
                        ${group.latestSubmitTime && group.earliestSubmitTime !== group.latestSubmitTime ? 
                          `<br>~ ${group.latestSubmitTime}` : ''}
                    </small>
                </td>
                <td>
                    <span class="badge bg-${statusClass}">${group.statusDescription || getStatusText(group)}</span>
                    ${group.topicCount > 0 ? `<br><small class="text-muted">通过率: ${approvalRate}%</small>` : ''}
                </td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <button class="btn btn-outline-info" onclick="showUserDailyDetail('${safeParseId(group.userId)}', '${group.submitDate}')" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${group.pendingCount > 0 ? `
                            <button class="btn btn-outline-success" onclick="quickBatchAudit('${safeParseId(group.userId)}', '${group.submitDate}', 'approve')" title="批量通过">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="showQuickRejectModal('${safeParseId(group.userId)}', '${group.submitDate}')" title="批量拒绝">
                                <i class="fas fa-times"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    });
    
    tableHtml += `
                </tbody>
            </table>
        </div>
    `;
    
    container.innerHTML = tableHtml;
}

/**
 * 渲染分组审核分页 - 使用统一的分页系统
 */
function renderGroupAuditPagination(data) {
    if (window.adminAudit && window.adminAudit.updatePagination) {
        // 使用统一的分页系统
        window.adminAudit.updatePagination('groupAuditPagination', data.current, data.pages, 'group-audit');
    } else {
        // 降级到原有的分页系统
        const pagination = document.getElementById('groupAuditPagination');
        
        if (data.pages <= 1) {
            pagination.style.display = 'none';
            return;
        }
        
        pagination.style.display = 'block';
        
        let paginationHtml = '';
        
        // 上一页
        if (data.current > 1) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="event.preventDefault(); loadGroupAuditData(${data.current - 1})">上一页</a></li>`;
        } else {
            paginationHtml += `<li class="page-item disabled"><span class="page-link">上一页</span></li>`;
        }
        
        // 页码
        const startPage = Math.max(1, data.current - 2);
        const endPage = Math.min(data.pages, data.current + 2);
        
        if (startPage > 1) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="event.preventDefault(); loadGroupAuditData(1)">1</a></li>`;
            if (startPage > 2) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            if (i === data.current) {
                paginationHtml += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
            } else {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="event.preventDefault(); loadGroupAuditData(${i})">${i}</a></li>`;
            }
        }
        
        if (endPage < data.pages) {
            if (endPage < data.pages - 1) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="event.preventDefault(); loadGroupAuditData(${data.pages})">${data.pages}</a></li>`;
        }
        
        // 下一页
        if (data.current < data.pages) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="event.preventDefault(); loadGroupAuditData(${data.current + 1})">下一页</a></li>`;
        } else {
            paginationHtml += `<li class="page-item disabled"><span class="page-link">下一页</span></li>`;
        }
        
        const paginationContainer = pagination.querySelector('.pagination');
        if (paginationContainer) {
            paginationContainer.innerHTML = paginationHtml;
        } else {
            pagination.innerHTML = `<ul class="pagination justify-content-center">${paginationHtml}</ul>`;
        }
    }
}

/**
 * 显示用户每日审核详情
 */
async function showUserDailyDetail(userId, date) {
    // 安全处理用户ID
    const safeUserId = safeParseId(userId);
    currentUserId = safeUserId;
    currentDate = date;
    
    const modal = new bootstrap.Modal(document.getElementById('userDailyDetailModal'));
    modal.show();
    
    // 更新模态框标题
    document.querySelector('#userDailyDetailModal .modal-title').textContent = `用户每日审核详情 - ${date}`;
    
    // 显示加载状态
    document.getElementById('userDailyDetailContent').innerHTML = `
        <div class="text-center py-4">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">加载详情中...</p>
        </div>
    `;
    
    try {

        const response = await window.adminAudit.fetchAPI(`/api/admin/audit/user-daily-detail?userId=${safeUserId}&date=${date}`);
        
        if (window.adminAudit.isResponseSuccess(response)) {
            const audits = window.adminAudit.getResponseData(response);
            renderUserDailyDetail(audits);
        } else {
            window.adminAudit.showError(window.adminAudit.getResponseMessage(response) || '加载详情失败');
        }
    } catch (error) {
        console.error('Failed to load user daily detail:', error);
        window.adminAudit.showError('网络错误，加载详情失败');
    }
}

/**
 * 渲染用户每日审核详情
 */
function renderUserDailyDetail(audits) {
    const container = document.getElementById('userDailyDetailContent');
    
    if (!audits || audits.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-3x text-muted"></i>
                <p class="text-muted mt-2">暂无审核记录</p>
            </div>
        `;
        return;
    }
    
    let detailHtml = `
        <div class="mb-3">
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h5>${audits.length}</h5>
                            <small>总题目数</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h5>${audits.filter(a => a.auditStatus === 0).length}</h5>
                            <small>待审核</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h5>${audits.filter(a => a.auditStatus === 1).length}</h5>
                            <small>已通过</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h5>${audits.filter(a => a.auditStatus === 2).length}</h5>
                            <small>已拒绝</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>提交时间</th>
                        <th>题目标题</th>
                        <th>题目类型</th>
                        <th>审核状态</th>
                        <th>审核意见</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    audits.forEach(audit => {
        const statusBadge = audit.auditStatus === 0 ? '<span class="badge bg-warning">待审核</span>' :
                           audit.auditStatus === 1 ? '<span class="badge bg-success">已通过</span>' :
                           '<span class="badge bg-danger">已拒绝</span>';
        
        detailHtml += `
            <tr>
                <td><small>${audit.submitTime}</small></td>
                <td>
                    <strong>${audit.title || '未知题目'}</strong>
                    ${audit.questionText ? `<br><small class="text-muted">${audit.questionText.substring(0, 50)}...</small>` : ''}
                </td>
                <td>${getTopicTypeText(audit.type)}</td>
                <td>${statusBadge}</td>
                <td>
                    ${audit.auditComment || '-'}
                    ${audit.auditorName ? `<br><small class="text-muted">审核人: ${audit.auditorName}</small>` : ''}
                </td>
                <td>
                    ${audit.auditStatus === 0 ? `
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-success" onclick="quickAuditSingle(${audit.id}, 'approve')" title="通过">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="quickAuditSingle(${audit.id}, 'reject')" title="拒绝">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    ` : '-'}
                </td>
            </tr>
        `;
    });
    
    detailHtml += `
                </tbody>
            </table>
        </div>
    `;
    
    container.innerHTML = detailHtml;
}

/**
 * 获取题目类型文本
 */
function getTopicTypeText(type) {
    const typeMap = {
        1: '选择题',
        2: '多选题', 
        3: '填空题',
        4: '简答题',
        5: '论述题'
    };
    return typeMap[type] || '未知类型';
}

/**
 * 快速批量审核（修复版 - 使用专用的用户日期批量审核API）
 */
async function quickBatchAudit(userId, date, action) {
    // 安全处理用户ID
    const safeUserId = safeParseId(userId);
    
    if (!confirm(`确定要${action === 'approve' ? '通过' : '拒绝'}该用户在 ${date} 的所有待审核题目吗？`)) {
        return;
    }
    
    try {
        showLoading('处理批量审核中...');
        

        
        // 🚀 直接使用专用的用户日期批量审核API
        const comment = action === 'approve' ? '分组批量通过' : '';
        const response = await window.adminAudit.fetchAPI(`/api/admin/audit/batch-audit-user-daily?userId=${safeUserId}&date=${date}&action=${action}&comment=${encodeURIComponent(comment)}`, {
            method: 'POST'
        });
        
        hideLoading();
        
        if (window.adminAudit.isResponseSuccess(response)) {
            const message = window.adminAudit.getResponseData(response) || window.adminAudit.getResponseMessage(response);
            window.adminAudit.showSuccess(`✅ ${message}`);
            
            // 刷新数据
            loadGroupAuditData(currentGroupPage);
            window.adminAudit.loadAuditStats();
        } else {
            const errorMsg = window.adminAudit.getResponseMessage(response) || '批量操作失败';
            window.adminAudit.showError('批量审核失败: ' + errorMsg);
        }
        
    } catch (error) {
        hideLoading();
        console.error('批量审核出错:', error);
        window.adminAudit.showError('批量审核出错: ' + error.message);
    }
}

/**
 * 显示快速拒绝模态框
 */
function showQuickRejectModal(userId, date) {
    // 安全处理用户ID
    const safeUserId = safeParseId(userId);
    currentUserId = safeUserId;
    currentDate = date;
    
    
    
    const modal = new bootstrap.Modal(document.getElementById('batchRejectModal'));
    modal.show();
}

/**
 * 提交批量拒绝
 */
async function submitBatchReject() {
    const reason = document.getElementById('batchRejectReason').value.trim();
    
    if (!reason) {
        window.adminAudit.showError('请输入拒绝原因');
        return;
    }
    
    try {
        showLoading('批量拒绝中...');
        
        const response = await window.adminAudit.fetchAPI(`/api/admin/audit/batch-audit-user-daily?userId=${currentUserId}&date=${currentDate}&action=reject&comment=${encodeURIComponent(reason)}`, {
            method: 'POST'
        });
        
        hideLoading();
        
        if (window.adminAudit.isResponseSuccess(response)) {
            const message = window.adminAudit.getResponseData(response) || window.adminAudit.getResponseMessage(response);
            window.adminAudit.showSuccess(`✅ ${message}`);
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('batchRejectModal'));
            modal.hide();
            
            // 刷新数据
            loadGroupAuditData(currentGroupPage);
            window.adminAudit.loadAuditStats();
            
            // 清空表单
            document.getElementById('batchRejectReason').value = '';
        } else {
            window.adminAudit.showError('❌ 批量拒绝失败: ' + (window.adminAudit.getResponseMessage(response) || '未知错误'));
        }
    } catch (error) {
        hideLoading();
        console.error('Failed to batch reject:', error);
        window.adminAudit.showError('网络错误，批量拒绝失败');
    }
}

/**
 * 用户每日批量审核
 */
function batchAuditUserDaily(action) {
    if (action === 'approve') {
        quickBatchAudit(currentUserId, currentDate, 'approve');
        
        // 关闭详情模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('userDailyDetailModal'));
        modal.hide();
    } else {
        showBatchRejectModal();
    }
}

/**
 * 显示批量拒绝模态框
 */
function showBatchRejectModal() {
    const modal = new bootstrap.Modal(document.getElementById('batchRejectModal'));
    modal.show();
}

// 辅助函数：获取状态文本
function getStatusText(group) {
    if (group.pendingCount > 0) {
        return "待审核";
    } else if (group.approvedCount > 0 && group.rejectedCount === 0) {
        return "全部通过";
    } else if (group.rejectedCount > 0 && group.approvedCount === 0) {
        return "全部拒绝";
    } else if (group.approvedCount > 0 && group.rejectedCount > 0) {
        return "部分通过";
    } else {
        return "无数据";
    }
}

/**
 * 搜索分组审核数据
 */
function searchGroupAudit() {
    const keyword = document.getElementById('groupSearchInput').value.trim();
    loadGroupAuditData(1, keyword);
}

/**
 * 显示分组审核帮助信息
 */
function showGroupAuditHelp() {
    const helpModal = new bootstrap.Modal(document.getElementById('helpModal'));
    document.getElementById('helpModalTitle').textContent = '分组审核帮助';
    document.getElementById('helpModalBody').innerHTML = `
        <h6>分组审核说明：</h6>
        <ul>
            <li><strong>按用户分组</strong>：相同用户在同一天提交的题目会被分组在一起</li>
            <li><strong>批次检测</strong>：系统会自动识别多次提交，标记为"多批次"</li>
            <li><strong>快速审核</strong>：可以对整个分组进行批量通过或拒绝</li>
            <li><strong>详情查看</strong>：点击"查看详情"可以查看该分组下的所有题目</li>
        </ul>
        
        <h6>状态说明：</h6>
        <ul>
            <li><span class="badge bg-warning">待审核</span>：有题目待审核</li>
            <li><span class="badge bg-success">已通过</span>：所有题目已通过</li>
            <li><span class="badge bg-danger">已拒绝</span>：所有题目已拒绝</li>
            <li><span class="badge bg-info">部分通过</span>：部分题目通过，部分拒绝</li>
        </ul>
    `;
    helpModal.show();
}

// 为搜索框添加回车键监听
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('groupSearchInput');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchGroupAudit();
            }
        });
    }
});

// 全局错误处理 - 捕获浏览器扩展相关错误
window.addEventListener('error', function(event) {
    const errorMessage = event.error ? event.error.message : event.message;
    
    // 忽略浏览器扩展相关的连接错误
    if (errorMessage && (
        errorMessage.includes('Could not establish connection') ||
        errorMessage.includes('Receiving end does not exist') ||
        errorMessage.includes('Extension context invalidated') ||
        errorMessage.includes('chrome-extension://')
    )) {
        console.debug('忽略浏览器扩展相关错误:', errorMessage);
        event.preventDefault();
        return false;
    }
});

// 全局Promise错误处理
window.addEventListener('unhandledrejection', function(event) {
    const errorMessage = event.reason ? event.reason.message || event.reason : '';
    
    // 忽略浏览器扩展相关的Promise错误
    if (typeof errorMessage === 'string' && (
        errorMessage.includes('Could not establish connection') ||
        errorMessage.includes('Receiving end does not exist') ||
        errorMessage.includes('Extension context invalidated')
    )) {
        console.debug('忽略浏览器扩展相关Promise错误:', errorMessage);
        event.preventDefault();
        return false;
    }
});

/**
 * 加载分组审核数据（全局函数）
 */
async function loadGroupAuditData(page = 1, keyword = '') {
    currentGroupPage = page;
    
    // 显示加载状态
    const container = document.getElementById('groupAuditContainer');
    if (!container) {
        console.error('groupAuditContainer not found');
        return;
    }
    
    container.innerHTML = `
        <div class="text-center py-5">
            <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
            <p class="text-muted">加载分组数据中...</p>
        </div>
    `;
    
    try {
        // 获取认证token
        const token = localStorage.getItem('adminToken') || sessionStorage.getItem('adminToken');
        
        const url = `/api/admin/audit/user-daily-groups?page=${page}&size=10&keyword=${encodeURIComponent(keyword)}`;
        
        // 构建请求headers
        const headers = {
            'Content-Type': 'application/json'
        };
        
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }
        
        // 发送请求
        const response = await fetch(url, {
            method: 'GET',
            headers: headers
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        // 检查API响应格式
        if (result.code === 200 && result.data) {
            const data = result.data;
            
            if (data && data.records) {
                renderGroupAuditTable(data);
                renderGroupAuditPagination(data);
            } else {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <p class="text-muted">数据格式异常</p>
                        <button class="btn btn-outline-primary btn-sm mt-3" onclick="loadGroupAuditData(${page}, '${keyword}')">
                            <i class="fas fa-redo"></i> 重试
                        </button>
                    </div>
                `;
            }
        } else {
            const errorMsg = result.message || '加载分组数据失败';
            
            // 显示详细错误信息
            container.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-circle fa-3x text-danger mb-3"></i>
                    <p class="text-danger">${errorMsg}</p>
                    <button class="btn btn-outline-primary btn-sm mt-3" onclick="loadGroupAuditData(${page}, '${keyword}')">
                        <i class="fas fa-redo"></i> 重试
                    </button>
                </div>
            `;
        }
    } catch (error) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-exclamation-circle fa-3x text-danger mb-3"></i>
                <p class="text-danger">网络错误：${error.message}</p>
                <button class="btn btn-outline-primary btn-sm mt-3" onclick="loadGroupAuditData(${page}, '${keyword}')">
                    <i class="fas fa-redo"></i> 重试
                </button>
            </div>
        `;
    }
}

/**
 * 显示确认对话框
 */
function showConfirmDialog(options) {
    return new Promise((resolve) => {
        const { title = '确认', message = '确定要执行此操作吗？', confirmText = '确定', confirmClass = 'btn-primary' } = options;
        
        const confirmed = window.confirm(`${title}\n\n${message}`);
        resolve(confirmed);
    });
}

/**
 * 显示输入对话框
 */
function showPromptDialog(options) {
    return new Promise((resolve) => {
        const { title = '输入', message = '请输入内容：', placeholder = '', required = false } = options;
        
        const input = window.prompt(`${title}\n\n${message}`, '');
        
        if (required && (!input || input.trim() === '')) {
            resolve(null);
        } else {
            resolve(input);
        }
    });
}

/**
 * 显示加载状态
 */
function showLoading(message = '加载中...') {
    // 简单的加载提示，实际项目中可以用更好的UI组件
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    // 隐藏加载状态
}

/**
 * 显示Toast提示
 */
function showToast(message, type = 'info') {
    // 简单的toast提示，实际项目中可以用更好的UI组件
    
    // 创建简单的alert提示
    if (type === 'error') {
        alert('错误: ' + message);
    }
}







// 高级搜索功能
async function performSearch() {
    try {
        // 获取所有筛选条件
        const searchParams = getSearchParams();
        
        // 更新当前页为第一页
        adminAudit.currentPage = 1;
        
        // 根据当前激活的标签页执行搜索
        const activeTab = document.querySelector('#auditTabs .nav-link.active');
        if (activeTab) {
            const tabId = activeTab.getAttribute('data-bs-target').replace('#', '');
            await searchByTab(tabId, searchParams);
        }
        
        // 显示搜索结果统计
        updateSearchStats(searchParams);
        
    } catch (error) {
        adminAudit.showError('搜索失败: ' + error.message);
    }
}

// 获取搜索参数
function getSearchParams() {
    return {
        keyword: document.getElementById('auditSearchInput')?.value || '',
        topicType: document.getElementById('topicTypeFilter')?.value || '',
        submitter: document.getElementById('submitterFilter')?.value || '',
        knowledge: document.getElementById('knowledgeFilter')?.value || '',
        status: document.getElementById('statusFilter')?.value || '',
        dateRange: document.getElementById('dateRangeFilter')?.value || '',
        startDate: document.getElementById('startDate')?.value || '',
        endDate: document.getElementById('endDate')?.value || '',
        viewMode: document.getElementById('viewModeSelect')?.value || 'list'
    };
}

// 根据标签页执行搜索（适配后台API）
async function searchByTab(tabId, searchParams) {
    const page = adminAudit.currentPage;
    const pageSize = adminAudit.pageSize;
    
    // 构建API URL（使用后台实际的API路径）
    let url = '';
    switch (tabId) {
        case 'pending':
            url = `/api/admin/audit/pending?pageNum=${page}&pageSize=${pageSize}`;
            break;
        case 'approved':
            url = `/api/admin/audit/approved?pageNum=${page}&pageSize=${pageSize}`;
            break;
        case 'rejected':
            url = `/api/admin/audit/rejected?pageNum=${page}&pageSize=${pageSize}`;
            break;
        case 'group-audit':
            url = `/api/admin/audit/user-daily-groups?page=${page}&size=${pageSize}`;
            break;
        case 'auditor-records':
            url = `/api/admin/audit/auditor-records?pageNum=${page}&pageSize=${pageSize}`;
            break;
        default:
            console.warn('未知的标签页:', tabId);
            return;
    }
    
    // 添加后台支持的搜索参数
    url = addSearchParamsToUrl(url, searchParams);
    
    // 执行搜索请求
    try {
        const response = await adminAudit.fetchAPI(url);
        
        if (adminAudit.isResponseSuccess(response)) {
            const data = adminAudit.getResponseData(response);
            const containerId = getContainerIdByTab(tabId);
            
            if (containerId) {
                renderSearchResults(containerId, data, searchParams, tabId);
                
                // 更新分页（适配后台数据结构）
                const paginationId = getPaginationIdByTab(tabId);
                if (paginationId) {
                    const currentPage = data.current || page;
                    const totalPages = data.pages || Math.ceil((data.total || 0) / pageSize);
                    adminAudit.updatePagination(paginationId, currentPage, totalPages, tabId);
                }
                
                // 更新搜索结果统计
                updateSearchResultsStats(data, searchParams);
            }
        } else {
            adminAudit.showError('搜索失败: ' + adminAudit.getResponseMessage(response));
        }
    } catch (error) {
        adminAudit.showError('搜索请求失败: ' + error.message);
    }
}

// 添加搜索参数到URL（适配后台API参数）
function addSearchParamsToUrl(url, params) {
    const urlObj = new URL(url, window.location.origin);
    
    // 后台支持的参数映射
    const paramMapping = {
        'keyword': 'keyword',           // 关键词搜索 - 后台支持
        'submitter': 'submitter',       // 提交者 - 后台支持
        'knowledge': 'knowledge',       // 知识点 - 后台支持
        // 以下参数需要前端处理或等待后台支持
        'topicType': null,              // 题目类型 - 前端筛选
        'status': null,                 // 状态筛选 - 通过不同API实现
        'dateRange': null,              // 时间范围 - 前端筛选
        'startDate': null,              // 开始日期 - 前端筛选  
        'endDate': null                 // 结束日期 - 前端筛选
    };
    
    // 只添加后台支持的参数
    Object.entries(params).forEach(([key, value]) => {
        if (value && value.trim() !== '' && paramMapping[key] !== null) {
            urlObj.searchParams.append(paramMapping[key], value);
        }
    });
    
    return urlObj.pathname + urlObj.search;
}

// 根据标签页获取容器ID
function getContainerIdByTab(tabId) {
    const mapping = {
        'pending': 'pendingAuditsContainer',
        'approved': 'approvedAuditsContainer', 
        'rejected': 'rejectedAuditsContainer',
        'group-audit': 'groupAuditContainer',
        'auditor-records': 'auditorRecordsContainer'
    };
    return mapping[tabId];
}

// 根据标签页获取分页ID
function getPaginationIdByTab(tabId) {
    const mapping = {
        'pending': 'pendingPagination',
        'approved': 'approvedPagination',
        'rejected': 'rejectedPagination', 
        'group-audit': 'groupAuditPagination',
        'auditor-records': 'auditorRecordsPagination'
    };
    return mapping[tabId];
}

// 渲染搜索结果（适配TopicAuditDTO数据结构）
function renderSearchResults(containerId, data, searchParams, tabId) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    // 适配后台分页数据结构
    const records = data.records || data.list || [];
    
    if (records.length === 0) {
        container.innerHTML = adminAudit.getEmptyState('未找到符合条件的记录');
        return;
    }
    
    // 前端进行额外的筛选（对于后台不支持的参数）
    const filteredRecords = applyClientSideFilters(records, searchParams);
    
    // 根据视图模式和标签页类型渲染结果
    if (tabId === 'group-audit') {
        renderGroupSearchResults(container, filteredRecords, searchParams);
    } else if (searchParams.viewMode === 'group') {
        adminAudit.renderGroupedView(filteredRecords, container);
    } else if (searchParams.viewMode === 'card') {
        renderCardView(filteredRecords, container);
    } else {
        adminAudit.renderListView(filteredRecords, container);
    }
}

// 渲染分组搜索结果
function renderGroupSearchResults(container, data, searchParams) {
    // 使用现有的分组渲染逻辑
    renderGroupAuditTable(data);
}

// 渲染卡片视图（适配TopicAuditDTO）
function renderCardView(records, container) {
    let html = '<div class="row g-3 card-view-container">';
    
    records.forEach(audit => {
        // 适配TopicAuditDTO字段名
        const title = audit.title || '题目标题';
        const content = audit.content || audit.options || '';
        const username = audit.username || '提交者';
        const knowledgeName = audit.knowledgeName || audit.knowledge || '知识点';
        const type = audit.type || '';
        const submitTime = audit.submitTime;
        const auditStatus = audit.auditStatus !== undefined ? audit.auditStatus : audit.status;
        
        // 状态样式
        const statusClass = getStatusClass(auditStatus);
        const statusText = getStatusText(auditStatus);
        
        html += `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card h-100 shadow-sm audit-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <input type="checkbox" class="form-check-input me-2 audit-checkbox" value="${audit.id}">
                            <span class="badge bg-info">${adminAudit.escapeHtml(type)}</span>
                        </div>
                        <span class="badge ${statusClass}">${statusText}</span>
                    </div>
                    <div class="card-body">
                        <h6 class="card-title">${adminAudit.escapeHtml(title)}</h6>
                        <p class="card-text small text-muted">${adminAudit.getTopicContentPreview(content, 100)}</p>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="badge bg-secondary">${adminAudit.escapeHtml(username)}</span>
                            <span class="badge bg-info">${adminAudit.escapeHtml(knowledgeName)}</span>
                        </div>
                        <small class="text-muted">${adminAudit.formatTime(submitTime)}</small>
                    </div>
                    <div class="card-footer">
                        <div class="btn-group w-100">
                            ${auditStatus === 0 ? `
                                <button class="btn btn-success btn-sm" onclick="quickApprove(${audit.id})">
                                    <i class="fas fa-check"></i> 通过
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="quickReject(${audit.id})">
                                    <i class="fas fa-times"></i> 拒绝
                                </button>
                            ` : ''}
                            <button class="btn btn-info btn-sm" onclick="showEnhancedTopicPreview(${audit.id})" title="详细预览">
                                <i class="fas fa-eye"></i> 预览
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="showAuditModal(${audit.id})" title="审核表单">
                                <i class="fas fa-edit"></i> 审核
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// 状态样式映射
function getStatusClass(status) {
    switch (status) {
        case 0: return 'bg-warning';
        case 1: return 'bg-success';
        case 2: return 'bg-danger';
        default: return 'bg-secondary';
    }
}

// 状态文本映射
function getStatusText(status) {
    switch (status) {
        case 0: return '待审核';
        case 1: return '已通过';
        case 2: return '已拒绝';
        default: return '未知';
    }
}

// 前端客户端筛选（处理后台不支持的参数）
function applyClientSideFilters(records, searchParams) {
    let filteredRecords = [...records];
    
    // 题目类型筛选
    if (searchParams.topicType) {
        filteredRecords = filteredRecords.filter(record => {
            const recordType = record.type || '';
            return matchTopicType(recordType, searchParams.topicType);
        });
    }
    
    // 时间范围筛选
    if (searchParams.dateRange || searchParams.startDate || searchParams.endDate) {
        filteredRecords = filteredRecords.filter(record => {
            return matchDateRange(record.submitTime, searchParams);
        });
    }
    
    return filteredRecords;
}

// 题目类型匹配
function matchTopicType(recordType, filterType) {
    // 题目类型映射（适配数据库中的实际值）
    const typeMapping = {
        'choice': ['单选题', 'singleChoice', 'choice'],
        'multiple': ['多选题', 'multipleChoice', 'multiple'],
        'judge': ['判断题', 'judgment', 'judge'],
        'fill': ['填空题', 'fillBlank', 'fill'],
        'short': ['简答题', 'shortAnswer', 'short'],
        'essay': ['论述题', 'essay', 'subjective'],
        'calculation': ['计算题', 'calculation'],
        'analysis': ['分析题', 'analysis']
    };
    
    const allowedTypes = typeMapping[filterType] || [filterType];
    return allowedTypes.some(type => recordType.includes(type));
}

// 时间范围匹配
function matchDateRange(submitTime, searchParams) {
    if (!submitTime) return true;
    
    const submitDate = new Date(submitTime);
    const now = new Date();
    
    if (searchParams.dateRange) {
        switch (searchParams.dateRange) {
            case 'today':
                return isToday(submitDate);
            case 'yesterday':
                return isYesterday(submitDate);
            case 'week':
                return isWithinDays(submitDate, 7);
            case 'month':
                return isWithinDays(submitDate, 30);
            default:
                return true;
        }
    }
    
    if (searchParams.startDate || searchParams.endDate) {
        const startDate = searchParams.startDate ? new Date(searchParams.startDate) : new Date('1970-01-01');
        const endDate = searchParams.endDate ? new Date(searchParams.endDate + ' 23:59:59') : new Date('2099-12-31');
        return submitDate >= startDate && submitDate <= endDate;
    }
    
    return true;
}

// 日期判断辅助函数
function isToday(date) {
    const today = new Date();
    return date.toDateString() === today.toDateString();
}

function isYesterday(date) {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return date.toDateString() === yesterday.toDateString();
}

function isWithinDays(date, days) {
    const limitDate = new Date();
    limitDate.setDate(limitDate.getDate() - days);
    return date >= limitDate;
}

// 更新搜索结果统计
function updateSearchResultsStats(data, searchParams) {
    const statsContainer = document.getElementById('searchResultStats');
    const statsText = document.getElementById('searchStatsText');
    
    if (!statsContainer || !statsText) return;
    
    const total = data.total || (data.records ? data.records.length : 0);
    const activeFilters = Object.values(searchParams).filter(value => value && value.trim() !== '').length;
    
    if (activeFilters > 0) {
        statsText.textContent = `搜索结果: 找到 ${total} 条记录，已应用 ${activeFilters} 个筛选条件`;
        statsContainer.style.display = 'block';
    } else {
        statsContainer.style.display = 'none';
    }
}

// 加载提交者选项（提供模拟数据或从API获取）
async function loadSubmitterOptions() {
    try {
        // 首先尝试从API获取
        const response = await adminAudit.fetchAPI('/api/admin/audit/submitters');
        
        if (adminAudit.isResponseSuccess(response)) {
            const submitters = adminAudit.getResponseData(response);
            
            if (submitters && submitters.length > 0) {
                populateSubmitterSelect(submitters);
            } else {
                await loadSubmittersFromAuditData();
            }
        } else {
            // API请求失败，使用备用方案
            await loadSubmittersFromAuditData();
        }
    } catch (error) {
        await loadSubmittersFromAuditData();
    }
}

// 从现有审核数据中提取提交者信息
async function loadSubmittersFromAuditData() {
    try {
        // 先尝试从后端获取提交者列表（使用专门的API）
        let response = await adminAudit.fetchAPI('/api/admin/audit/submitters');
        
        if (adminAudit.isResponseSuccess(response)) {
            const submitters = adminAudit.getResponseData(response);
            
            if (submitters && submitters.length > 0) {
                populateSubmitterSelect(submitters);
                return;
            }
        }
        
        // 备用方案：获取更多数据来确保覆盖所有用户
        let allRecords = [];
        let pageNum = 1;
        const pageSize = 50;
        
        // 获取待审核数据（多页）
        while (pageNum <= 10) { // 最多获取10页，避免无限循环
            response = await adminAudit.fetchAPI(`/api/admin/audit/pending?pageNum=${pageNum}&pageSize=${pageSize}`);
            
            if (adminAudit.isResponseSuccess(response)) {
                const data = adminAudit.getResponseData(response);
                const records = data.records || [];
                
                if (records.length === 0) {
                    break;
                }
                
                allRecords.push(...records);
                
                // 如果这页数据少于pageSize，说明已经是最后一页
                if (records.length < pageSize) {
                    break;
                }
                
                pageNum++;
            } else {
                break;
            }
        }
        
        // 如果待审核数据不足，补充已通过和已拒绝的数据
        if (allRecords.length < 50) {
            pageNum = 1;
            while (pageNum <= 5) { // 最多5页已通过数据
                response = await adminAudit.fetchAPI(`/api/admin/audit/approved?pageNum=${pageNum}&pageSize=${pageSize}`);
                
                if (adminAudit.isResponseSuccess(response)) {
                    const data = adminAudit.getResponseData(response);
                    const records = data.records || [];
                    
                    if (records.length === 0) break;
                    
                    allRecords.push(...records);
                    
                    if (records.length < pageSize) break;
                    pageNum++;
                } else {
                    break;
                }
            }
        }
        
        const records = allRecords;
        
        if (records.length === 0) {
            const select = document.getElementById('submitterFilter');
            if (select) {
                select.innerHTML = '<option value="">暂无数据</option>';
            }
            return;
        }
        
        // 提取唯一的提交者（使用安全ID处理）
        const submitterMap = new Map();
        
        records.forEach(record => {
            const safeUserId = safeParseId(record.userId);
            const username = record.username;
            
            if (safeUserId && username) {
                if (!submitterMap.has(safeUserId)) {
                    submitterMap.set(safeUserId, {
                        id: safeUserId, // 使用安全处理后的ID
                        username: username,
                        count: 0
                    });
                }
                submitterMap.get(safeUserId).count++;
            }
        });
        
        const submitters = Array.from(submitterMap.values());
        
        if (submitters.length > 0) {
            populateSubmitterSelect(submitters);
        } else {
            const select = document.getElementById('submitterFilter');
            if (select) {
                select.innerHTML = '<option value="">暂无数据</option>';
            }
        }
    } catch (error) {
        const select = document.getElementById('submitterFilter');
        if (select) {
            select.innerHTML = '<option value="">加载失败</option>';
        }
    }
}

// 填充提交者选择框
function populateSubmitterSelect(submitters) {
    const select = document.getElementById('submitterFilter');
    if (!select) return;
    
    // 清空现有选项（保留"全部用户"）
    select.innerHTML = '<option value="">全部用户</option>';
    
    submitters.forEach(submitter => {
        const option = document.createElement('option');
        // 确保ID以字符串形式处理
        const safeid = safeParseId(submitter.id || submitter.userId);
        option.value = safeid;
        option.textContent = `${submitter.username} (${submitter.count || 0}条)`;
        select.appendChild(option);
    });
}

// 加载知识点选项（提供模拟数据或从API获取）
async function loadKnowledgeOptions() {
    try {
        // 首先尝试从API获取
        const response = await adminAudit.fetchAPI('/api/admin/audit/knowledge-points');
        
        if (adminAudit.isResponseSuccess(response)) {
            const knowledgePoints = adminAudit.getResponseData(response);
            populateKnowledgeSelect(knowledgePoints);
        } else {
            // API不存在，使用模拟数据
            await loadKnowledgeFromAuditData();
        }
    } catch (error) {
        await loadKnowledgeFromAuditData();
    }
}

// 从现有审核数据中提取知识点信息
async function loadKnowledgeFromAuditData() {
    try {
        // 获取待审核数据来提取知识点信息
        const response = await adminAudit.fetchAPI('/api/admin/audit/pending?pageNum=1&pageSize=100');
        
        if (adminAudit.isResponseSuccess(response)) {
            const data = adminAudit.getResponseData(response);
            const records = data.records || [];
            
            // 提取唯一的知识点
            const knowledgeMap = new Map();
            records.forEach(record => {
                // 优先使用knowId作为知识点标识
                const knowId = record.knowId || record.knowledgeId;
                const knowledgeName = record.knowledgeName || record.knowledge || record.tags || knowId;
                
                if (knowId && knowledgeName) {
                    const key = knowId.toString();
                    if (!knowledgeMap.has(key)) {
                        knowledgeMap.set(key, {
                            id: knowId,
                            name: knowledgeName,
                            groupName: record.groupName || '未分类',
                            count: 0
                        });
                    }
                    const kp = knowledgeMap.get(key);
                    kp.count = (kp.count || 0) + 1;
                }
            });
            
            const knowledgePoints = Array.from(knowledgeMap.values());
            populateKnowledgeSelect(knowledgePoints);
        }
    } catch (error) {
        console.error('从审核数据提取知识点信息失败:', error);
    }
}

// 填充知识点选择框
function populateKnowledgeSelect(knowledgePoints) {
    const select = document.getElementById('knowledgeFilter');
    if (!select) return;
    
    // 清空现有选项（保留"全部知识点"）
    select.innerHTML = '<option value="">全部知识点</option>';
    
    knowledgePoints.forEach(kp => {
        const option = document.createElement('option');
        option.value = kp.id;
        // 使用displayName如果存在，否则使用组合格式
        option.textContent = kp.displayName || `${kp.groupName ? kp.groupName + ' - ' : ''}${kp.name} (${kp.count || 0}条)`;
        select.appendChild(option);
    });
}

// 显示导出选项
function showExportOptions() {
    // 检查当前搜索参数和结果
    const searchParams = getSearchParams();
    const hasSearchParams = Object.values(searchParams).some(value => value && value.trim() !== '');
    
    // 创建导出选项模态框
    const modalHTML = `
        <div class="modal fade" id="exportOptionsModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-download"></i> 导出审核数据
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <p class="text-muted">
                                <i class="fas fa-info-circle"></i> 
                                ${hasSearchParams ? '准备导出符合当前搜索条件的数据' : '准备导出当前页面的数据'}
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">导出范围：</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="exportRange" id="exportSearchResults" value="search" ${hasSearchParams ? 'checked' : ''}>
                                <label class="form-check-label" for="exportSearchResults">
                                    <i class="fas fa-filter text-primary"></i> 当前搜索结果
                                </label>
                                <small class="form-text text-muted d-block">导出符合搜索条件的所有数据</small>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="exportRange" id="exportCurrentPage" value="page" ${!hasSearchParams ? 'checked' : ''}>
                                <label class="form-check-label" for="exportCurrentPage">
                                    <i class="fas fa-table text-info"></i> 当前页面数据
                                </label>
                                <small class="form-text text-muted d-block">只导出当前页面显示的数据</small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">导出格式：</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="exportFormat" id="exportCSV" value="csv" checked>
                                <label class="form-check-label" for="exportCSV">
                                    <i class="fas fa-file-csv text-success"></i> CSV格式 (推荐)
                                </label>
                                <small class="form-text text-muted d-block">兼容Excel，支持中文，文件较小</small>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="exportFormat" id="exportExcel" value="excel">
                                <label class="form-check-label" for="exportExcel">
                                    <i class="fas fa-file-excel text-success"></i> Excel格式
                                </label>
                                <small class="form-text text-muted d-block">通过后端API导出(如可用)</small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="exportFileName" class="form-label">文件名：</label>
                            <input type="text" class="form-control" id="exportFileName" 
                                   value="审核数据导出_${new Date().toISOString().slice(0,10).replace(/-/g,'')}" 
                                   placeholder="请输入文件名">
                            <small class="form-text text-muted">不需要包含文件扩展名</small>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-lightbulb"></i>
                            <strong>提示：</strong>导出的数据包含题目标题、类型、知识点、提交者、状态等完整信息。
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="executeExport()">
                            <i class="fas fa-download"></i> 开始导出
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 移除之前的模态框（如果存在）
    const existingModal = document.getElementById('exportOptionsModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // 添加模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('exportOptionsModal'));
    modal.show();
}

// 执行导出
function executeExport() {
    try {
        const range = document.querySelector('input[name="exportRange"]:checked').value;
        const format = document.querySelector('input[name="exportFormat"]:checked').value;
        const fileName = document.getElementById('exportFileName').value.trim() || '审核数据导出';
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('exportOptionsModal'));
        modal.hide();
        
        // 延迟执行导出，确保模态框完全关闭
        setTimeout(() => {
            if (range === 'search') {
                exportSearchResults(fileName, format);
            } else {
                exportCurrentPageData(fileName, format);
            }
        }, 300);
        
    } catch (error) {
        console.error('执行导出失败:', error);
        adminAudit.showError('导出失败: ' + error.message);
    }
}

// 导出搜索结果（适配后台API）
function exportSearchResults(customFileName, format = 'csv') {
    const searchParams = getSearchParams();
    
    if (format === 'excel') {
        // 尝试使用后端Excel导出API
        exportViaBackendAPI(searchParams, customFileName);
    } else {
        // 使用前端CSV导出
        exportSearchResultsAsCSV(searchParams, customFileName);
    }
}

// 通过后端API导出Excel
function exportViaBackendAPI(searchParams, fileName) {
    // 构建导出URL（需要后台支持）
    let exportUrl = '/api/admin/audit/export?';
    const urlParams = new URLSearchParams();
    
    // 只添加后台支持的参数
    const supportedParams = ['keyword', 'submitter', 'knowledge'];
    Object.entries(searchParams).forEach(([key, value]) => {
        if (value && value.trim() !== '' && supportedParams.includes(key)) {
            urlParams.append(key, value);
        }
    });
    
    exportUrl += urlParams.toString();
    
    // 检查是否有有效的导出参数
    if (urlParams.toString()) {
        // 创建下载链接
        const link = document.createElement('a');
        link.href = exportUrl;
        link.download = `${fileName}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        adminAudit.showSuccess('正在通过后端API导出Excel文件...');
    } else {
        adminAudit.showError('没有有效的搜索条件，无法使用后端导出');
        // 降级为CSV导出
        exportSearchResultsAsCSV(searchParams, fileName);
    }
}

// 前端CSV导出搜索结果
function exportSearchResultsAsCSV(searchParams, fileName) {
    try {
        // 这里需要实现从搜索结果中获取数据的逻辑
        // 暂时使用当前页面数据作为示例
        exportCurrentPageData(fileName, 'csv');
        
        adminAudit.showSuccess('搜索结果已导出为CSV文件');
    } catch (error) {
        console.error('CSV导出失败:', error);
        adminAudit.showError('CSV导出失败: ' + error.message);
    }
}

// 导出当前页面数据（备用方案）
function exportCurrentPageData(customFileName, format = 'csv') {
    try {
        // 获取当前显示的数据
        const activeTab = document.querySelector('#auditTabs .nav-link.active');
        if (!activeTab) {
            adminAudit.showError('无法确定当前活动标签页');
            return;
        }
        
        const tabId = activeTab.getAttribute('data-bs-target').replace('#', '');
        const container = document.getElementById(getContainerIdByTab(tabId));
        
        if (!container) {
            adminAudit.showError('无法找到数据容器');
            return;
        }
        
        // 简单的CSV导出
        let csvContent = "\uFEFF"; // 添加BOM头，确保Excel正确显示中文
        csvContent += "题目标题,题目类型,知识点,提交者,提交时间,审核状态,审核意见\n";
        
        const rows = container.querySelectorAll('tr[data-audit-id]');
        let exportCount = 0;
        
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length >= 4) {
                const title = cells[1]?.textContent?.trim() || '';
                const type = cells[2]?.textContent?.trim() || '';
                const knowledge = cells[3]?.textContent?.trim() || '';
                const submitter = cells[4]?.textContent?.trim() || '';
                const time = cells[5]?.textContent?.trim() || '';
                const status = cells[6]?.textContent?.trim() || '待审核';
                const comment = cells[7]?.textContent?.trim() || '';
                
                // 转义CSV特殊字符
                const escapeCSV = (str) => {
                    if (str.includes(',') || str.includes('"') || str.includes('\n')) {
                        return '"' + str.replace(/"/g, '""') + '"';
                    }
                    return str;
                };
                
                csvContent += `${escapeCSV(title)},${escapeCSV(type)},${escapeCSV(knowledge)},${escapeCSV(submitter)},${escapeCSV(time)},${escapeCSV(status)},${escapeCSV(comment)}\n`;
                exportCount++;
            }
        });
        
        if (exportCount === 0) {
            adminAudit.showError('当前页面没有可导出的数据');
            return;
        }
        
        // 生成文件名
        const fileName = customFileName || `审核数据_${new Date().toISOString().split('T')[0]}`;
        const fullFileName = `${fileName}.csv`;
        
        // 创建并下载CSV文件
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = fullFileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // 清理URL对象
        setTimeout(() => {
            URL.revokeObjectURL(link.href);
        }, 100);
        
        adminAudit.showSuccess(`当前页面 ${exportCount} 条数据已导出为CSV文件: ${fullFileName}`);
    } catch (error) {
        console.error('导出失败:', error);
        adminAudit.showError('导出失败: ' + error.message);
    }
}

// 重置搜索
function resetSearch() {
    // 清空所有搜索字段
    document.getElementById('auditSearchInput').value = '';
    document.getElementById('topicTypeFilter').value = '';
    document.getElementById('submitterFilter').value = '';
    document.getElementById('knowledgeFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('dateRangeFilter').value = '';
    document.getElementById('startDate').value = '';
    document.getElementById('endDate').value = '';
    document.getElementById('viewModeSelect').value = 'list';
    
    // 隐藏自定义时间范围
    document.getElementById('customDateRange').style.display = 'none';
    document.getElementById('customDateRange2').style.display = 'none';
    
    // 隐藏搜索结果统计
    document.getElementById('searchResultStats').style.display = 'none';
    
    // 重新加载默认数据
    performSearch();
}

// 应用快捷筛选
function applyQuickFilter(type) {
    // 先重置搜索
    resetSearch();
    
    switch (type) {
        case 'urgent':
            // 紧急待审：超过24小时未审核的题目
            document.getElementById('dateRangeFilter').value = 'yesterday';
            document.getElementById('statusFilter').value = '0';
            break;
        case 'today':
            // 今日提交
            document.getElementById('dateRangeFilter').value = 'today';
            break;
        case 'frequent':
            // 高频用户：这里可以设置特定的用户筛选逻辑
            // 暂时使用状态筛选作为示例
            break;
        case 'new':
            // 新用户：可以根据用户注册时间等条件筛选
            break;
    }
    
    // 执行搜索
    performSearch();
}

// 更新搜索结果统计
function updateSearchStats(searchParams) {
    const statsContainer = document.getElementById('searchResultStats');
    const statsText = document.getElementById('searchStatsText');
    
    if (!statsContainer || !statsText) return;
    
    // 统计筛选条件数量
    const filterCount = Object.values(searchParams).filter(value => value && value.trim() !== '').length;
    
    if (filterCount > 0) {
        statsText.textContent = `已应用 ${filterCount} 个筛选条件`;
        statsContainer.style.display = 'block';
    } else {
        statsContainer.style.display = 'none';
    }
}

// 保存搜索预设
function saveSearchPreset() {
    const searchParams = getSearchParams();
    const presetName = prompt('请输入预设名称:', '搜索预设-' + new Date().toLocaleDateString());
    
    if (presetName) {
        // 保存到localStorage
        const presets = JSON.parse(localStorage.getItem('auditSearchPresets') || '{}');
        presets[presetName] = searchParams;
        localStorage.setItem('auditSearchPresets', JSON.stringify(presets));
        
        adminAudit.showSuccess('搜索预设已保存: ' + presetName);
        
        // 更新预设选择器（如果存在）
        updatePresetSelector();
    }
}

// 更新预设选择器
function updatePresetSelector() {
    const presets = JSON.parse(localStorage.getItem('auditSearchPresets') || '{}');
    // 这里可以添加预设选择器的更新逻辑
}

// 初始化高级搜索功能（适配后台API）
async function initAdvancedSearch() {
    try {
        // 加载提交者选项
        await loadSubmitterOptions();
        
        // 加载知识点选项
        await loadKnowledgeOptions();
        
        // 绑定时间范围变化事件
        bindDateRangeEvents();
        
        // 绑定搜索框回车事件
        bindSearchInputEvents();
        
    } catch (error) {
        // 初始化失败，静默处理
    }
}

// 绑定时间范围事件
function bindDateRangeEvents() {
    const dateRangeSelect = document.getElementById('dateRangeFilter');
    const customDateRange = document.getElementById('customDateRange');
    const customDateRange2 = document.getElementById('customDateRange2');
    
    if (dateRangeSelect) {
        dateRangeSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                customDateRange.style.display = 'block';
                customDateRange2.style.display = 'block';
            } else {
                customDateRange.style.display = 'none';
                customDateRange2.style.display = 'none';
            }
        });
    }
}

// 绑定搜索框事件
function bindSearchInputEvents() {
    const searchInput = document.getElementById('auditSearchInput');
    
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
        
        // 实时搜索（可选，延迟执行）
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                // 可以在这里实现实时搜索提示
                // performSearch();
            }, 500);
        });
    }
}



// 页面加载完成后初始化高级搜索
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保主要功能先加载
    setTimeout(() => {
        initAdvancedSearch();
    }, 1000);
});


// ================== 题目预览样式注入 ==================

// 注入增强题目预览的CSS样式
function injectTopicPreviewStyles() {
    const styleId = 'enhanced-topic-preview-styles';
    
    // 检查是否已经注入过样式
    if (document.getElementById(styleId)) {
        return;
    }
    
    const styles = `
        <style id="${styleId}">
        /* 增强题目预览样式 */
        .enhanced-topic-preview {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .topic-content-preview {
            max-height: 400px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .options-container {
            margin: 15px 0;
        }
        
        .option-item {
            transition: all 0.2s ease;
            cursor: default;
        }
        
        .option-item:hover {
            background-color: #f8f9fa !important;
            border-color: #dee2e6 !important;
        }
        
        .option-label {
            min-width: 30px;
            text-align: center;
            font-weight: bold;
            flex-shrink: 0;
        }
        
        .option-text {
            word-break: break-word;
        }
        
        .answer-preview, .parse-preview {
            border-left: 4px solid;
            position: relative;
        }
        
        .answer-preview {
            border-left-color: #198754;
        }
        
        .parse-preview {
            border-left-color: #0dcaf0;
        }
        
        .answer-text {
            font-size: 14px;
            line-height: 1.5;
        }
        
        .fill-blank {
            border-bottom: 2px solid #6c757d;
            display: inline-block;
            min-width: 60px;
            text-align: center;
            margin: 0 4px;
            color: #6c757d;
            font-weight: bold;
        }
        
        /* 题目类型图标样式 */
        .badge .fas {
            margin-right: 4px;
        }
        
        /* 难度星级样式 */
        .difficulty-stars .fas.fa-star {
            color: #ffc107;
            margin-right: 2px;
        }
        
        /* 审核状态颜色 */
        .bg-warning.text-white {
            background-color: #f59e0b !important;
        }
        
        /* 模态框响应式调整 */
        @media (max-width: 768px) {
            .modal-xl {
                max-width: 95%;
            }
            
            .topic-content-preview {
                max-height: 300px;
            }
            
            .option-item {
                flex-direction: column;
                align-items: flex-start !important;
            }
            
            .option-label {
                margin-bottom: 8px;
            }
        }
        
        /* 打印样式 */
        @media print {
            .modal-header, .modal-footer {
                display: none;
            }
            
            .modal-body {
                padding: 0;
            }
        }
        
        /* 增强审核模态框样式 */
        .enhanced-audit-topic-display {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .enhanced-audit-topic-display .topic-title {
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 8px;
        }
        
        .enhanced-audit-topic-display .topic-meta .badge {
            font-size: 0.75rem;
        }
        
        .enhanced-audit-topic-display .topic-content-display {
            max-height: 300px;
            overflow-y: auto;
            border: 2px dashed #dee2e6 !important;
        }
        
        .enhanced-audit-topic-display .answer-display,
        .enhanced-audit-topic-display .parse-display {
            border-left: 4px solid currentColor !important;
        }
        
        .enhanced-audit-topic-display .info-grid .info-item {
            transition: all 0.2s ease;
            border: 1px solid #e9ecef;
        }
        
        .enhanced-audit-topic-display .info-grid .info-item:hover {
            background-color: #f8f9fa !important;
            border-color: #dee2e6 !important;
        }
        
        .enhanced-audit-topic-display .status-display {
            position: relative;
        }
        
        .enhanced-audit-topic-display .status-display::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background-color: currentColor;
            opacity: 0.3;
        }
        
        /* 审核模态框响应式 */
        @media (max-width: 768px) {
            .enhanced-audit-topic-display .topic-content-display {
                max-height: 200px;
            }
            
            .enhanced-audit-topic-display .topic-meta {
                flex-direction: column;
                align-items: flex-start !important;
            }
            
            .enhanced-audit-topic-display .info-grid .col-md-6 {
                width: 100%;
            }
        }
        </style>
    `;
    
    document.head.insertAdjacentHTML('beforeend', styles);
}

// 页面加载时注入样式
document.addEventListener('DOMContentLoaded', injectTopicPreviewStyles);