/**
 * 管理员用户上传统计页面JavaScript
 */

// 全局变量
let currentPage = 1;
let pageSize = 10;
let currentUserId = null; // 当前查看详情的用户ID

/**
 * 获取认证token
 */
function getAuthToken() {
    return localStorage.getItem('adminToken') || sessionStorage.getItem('adminToken');
}

/**
 * 获取默认的AJAX配置，包含认证头
 */
function getDefaultAjaxConfig() {
    const token = getAuthToken();
    const config = {
        headers: {
            'Content-Type': 'application/json'
        }
    };
    
    if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
    }
    
    return config;
}

/**
 * 处理认证错误
 */
function handleAuthError() {
    localStorage.removeItem('adminToken');
    sessionStorage.removeItem('adminToken');
    window.location.href = '/admin/login';
}

/**
 * 检查响应是否成功
 */
function isResponseSuccess(response) {
    // 兼容两种格式：
    // 1. ApiResponse格式：{code: 200, message: "success", data: xxx}
    // 2. 传统格式：{success: true, message: "xxx", data: xxx}
    return (response.code === 200) || (response.success === true);
}

/**
 * 获取响应数据
 */
function getResponseData(response) {
    return response.data;
}

/**
 * 获取响应消息
 */
function getResponseMessage(response) {
    return response.message || '操作失败';
}

// 页面加载完成后初始化
$(document).ready(function() {
    initializePage();
    bindEvents();
    loadOverviewData();
    loadUploadStats();
    
    // 设置默认日期范围（最近7天）
    const today = new Date();
    const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    $('#startDate').val(formatDate(sevenDaysAgo));
    $('#endDate').val(formatDate(today));
    $('#anomalyDate').val(formatDate(today));
});

/**
 * 初始化页面
 */
function initializePage() {
    // 检查认证状态
    if (!getAuthToken()) {
        handleAuthError();
        return;
    }
    
    console.log('管理员用户上传统计页面初始化');
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 刷新按钮
    $('#refreshBtn').on('click', function() {
        loadOverviewData();
        loadUploadStats();
    });
    
    // 搜索按钮
    $('#usernameFilter').on('keypress', function(e) {
        if (e.which === 13) { // Enter键
            searchUploadStats();
        }
    });
    
    // 日期筛选变化
    $('#startDate, #endDate').on('change', function() {
        currentPage = 1;
        loadUploadStats();
    });
    
    // 排序变化
    $('#orderBy, #orderType').on('change', function() {
        currentPage = 1;
        loadUploadStats();
    });
}

/**
 * 加载概览数据
 */
function loadOverviewData() {
    const config = getDefaultAjaxConfig();
    
    $.ajax({
        url: '/api/admin/user-upload/overview',
        method: 'GET',
        headers: config.headers,
        success: function(response) {
            if (isResponseSuccess(response)) {
                updateOverviewCards(getResponseData(response));
            } else {
                showError('加载概览数据失败: ' + getResponseMessage(response));
            }
        },
        error: function(xhr, status, error) {
            if (xhr.status === 401) {
                handleAuthError();
                return;
            }
            showError('网络错误，无法加载概览数据');
            console.error('加载概览数据失败:', error);
        }
    });
}

/**
 * 更新概览卡片
 */
function updateOverviewCards(data) {
    $('#todayTotal').text(data.todayTotal || 0);
    $('#activeUsers').text(data.activeUsers || 0);
    $('#anomalousUsers').text(data.anomalousUsers || 0);
    $('#avgUploadPerUser').text(data.avgUploadPerUser || 0);
}

/**
 * 加载用户上传统计数据
 */
function loadUploadStats() {
    const params = {
        page: currentPage,
        size: pageSize,
        startDate: $('#startDate').val() || null,
        endDate: $('#endDate').val() || null,
        username: $('#usernameFilter').val() || null,
        orderBy: $('#orderBy').val() || 'date',
        orderType: $('#orderType').val() || 'desc'
    };
    
    // 显示加载状态
    $('#uploadStatsContainer').html(`
        <div class="text-center py-4">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">加载中...</p>
        </div>
    `);
    
    const config = getDefaultAjaxConfig();
    
    $.ajax({
        url: '/api/admin/user-upload/daily-stats',
        method: 'GET',
        data: params,
        headers: config.headers,
        success: function(response) {
            if (isResponseSuccess(response)) {
                renderUploadStatsTable(getResponseData(response));
                renderPagination(getResponseData(response));
            } else {
                showError('加载统计数据失败: ' + getResponseMessage(response));
            }
        },
        error: function(xhr, status, error) {
            if (xhr.status === 401) {
                handleAuthError();
                return;
            }
            showError('网络错误，无法加载统计数据');
            console.error('加载统计数据失败:', error);
            $('#uploadStatsContainer').html(`
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle text-warning fa-2x"></i>
                    <p class="mt-2">加载失败</p>
                </div>
            `);
        }
    });
}

/**
 * 渲染上传统计表格
 */
function renderUploadStatsTable(data) {
    const list = data.list || [];
    
    if (list.length === 0) {
        $('#uploadStatsContainer').html(`
            <div class="text-center py-4">
                <i class="fas fa-inbox text-muted fa-2x"></i>
                <p class="mt-2">暂无数据</p>
            </div>
        `);
        return;
    }
    
    let tableHtml = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>日期</th>
                        <th>用户</th>
                        <th>上传量</th>
                        <th>时间跨度</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    list.forEach(function(item) {
        const statusClass = item.isAnomalous ? 'text-danger' : 'text-success';
        const statusIcon = item.isAnomalous ? 'fas fa-exclamation-triangle' : 'fas fa-check-circle';
        const statusText = item.isAnomalous ? '异常' : '正常';
        const rowClass = item.uploadCount > 2000 ? 'anomaly-high' : 
                        item.uploadCount > 1000 ? 'anomaly-medium' : '';
        
        tableHtml += `
            <tr class="${rowClass}">
                <td>${item.date}</td>
                <td>
                    <strong>${item.username}</strong>
                    <small class="text-muted d-block">ID: ${item.userId}</small>
                </td>
                <td>
                    <span class="badge ${item.uploadCount > 2000 ? 'bg-danger' : 
                                       item.uploadCount > 1000 ? 'bg-warning' : 'bg-success'}">
                        ${item.uploadCount}
                    </span>
                </td>
                <td>
                    <small class="text-muted">${item.timeSpan || '无'}</small>
                </td>
                <td>
                    <i class="${statusIcon} ${statusClass}"></i>
                    <span class="${statusClass}">${statusText}</span>
                </td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="showUserDetail('${item.userId}', '${item.date}')">
                        <i class="fas fa-eye"></i> 详情
                    </button>
                    ${item.isAnomalous ? `
                        <button class="btn btn-sm btn-warning ms-1" onclick="showSetLimitModal('${item.userId}', '${item.username}')">
                            <i class="fas fa-ban"></i> 限制
                        </button>
                    ` : ''}
                </td>
            </tr>
        `;
    });
    
    tableHtml += `
                </tbody>
            </table>
        </div>
    `;
    
    $('#uploadStatsContainer').html(tableHtml);
}

/**
 * 渲染分页
 */
function renderPagination(data) {
    const totalPages = data.pages || 1;
    const current = data.current || 1;
    
    if (totalPages <= 1) {
        $('#uploadStatsPagination').hide();
        return;
    }
    
    let paginationHtml = '';
    
    // 上一页
    if (current > 1) {
        paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="goToPage(${current - 1})">&laquo;</a>
            </li>
        `;
    }
    
    // 页码
    const startPage = Math.max(1, current - 2);
    const endPage = Math.min(totalPages, current + 2);
    
    if (startPage > 1) {
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(1)">1</a></li>`;
        if (startPage > 2) {
            paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === current ? 'active' : ''}">
                <a class="page-link" href="#" onclick="goToPage(${i})">${i}</a>
            </li>
        `;
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${totalPages})">${totalPages}</a></li>`;
    }
    
    // 下一页
    if (current < totalPages) {
        paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="goToPage(${current + 1})">&raquo;</a>
            </li>
        `;
    }
    
    $('#uploadStatsPagination ul').html(paginationHtml);
    $('#uploadStatsPagination').show();
}

/**
 * 跳转到指定页面
 */
function goToPage(page) {
    currentPage = page;
    loadUploadStats();
}

/**
 * 搜索上传统计
 */
function searchUploadStats() {
    currentPage = 1;
    loadUploadStats();
}

/**
 * 显示用户详情
 */
function showUserDetail(userId, date) {
    if (!userId || userId === 'undefined') {
        showError('用户ID无效');
        return;
    }
    
    currentUserId = userId;
    
    // 显示加载状态
    $('#userDetailContent').html(`
        <div class="text-center py-4">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">加载用户详情中...</p>
        </div>
    `);
    
    // 显示模态框
    $('#userDetailModal').modal('show');
    
    const config = getDefaultAjaxConfig();
    
    $.ajax({
        url: `/api/admin/user-upload/user-detail/${userId}`,
        method: 'GET',
        data: { date: date },
        headers: config.headers,
        success: function(response) {
            if (isResponseSuccess(response)) {
                const data = getResponseData(response);
                
                // 检查模态框是否已经完全显示
                if ($('#userDetailModal').hasClass('show')) {
                    // 模态框已显示，直接渲染
                    renderUserDetail(data);
                } else {
                    // 模态框还未完全显示，等待显示完成
                    $('#userDetailModal').one('shown.bs.modal', function() {
                        renderUserDetail(data);
                    });
                }
            } else {
                showError('加载用户详情失败: ' + getResponseMessage(response));
            }
        },
        error: function(xhr, status, error) {
            if (xhr.status === 401) {
                handleAuthError();
                return;
            }
            showError('网络错误，无法加载用户详情');
            console.error('加载用户详情失败:', error);
        }
    });
}

/**
 * 渲染用户详情 (原版本 - 传统方式)
 */
function renderUserDetail(data) {
    if (!data) {
        $('#userDetailContent').html('<div class="alert alert-warning">没有获取到用户详情数据</div>');
        return;
    }
    
    const hourlyStats = data.hourlyStats || {};
    const timeline = data.timeline || [];
    
    // 安全地获取数据，提供默认值
    const userId = data.userId !== undefined && data.userId !== null ? data.userId : '未知';
    const username = data.username || '未知用户';
    const userStatus = data.userStatus || '未知状态';
    const date = data.date || '未知日期';
    const totalUploads = data.totalUploads !== undefined ? data.totalUploads : 0;
    
    // 生成按小时统计的图表数据
    const hours = Array.from({length: 24}, (_, i) => i);
    const hourlyCounts = hours.map(hour => hourlyStats[hour] || 0);
    
    let detailHtml = `
        <div class="row">
            <div class="col-md-6">
                <h6>用户信息</h6>
                <table class="table table-sm">
                    <tr><td>用户ID:</td><td>${userId}</td></tr>
                    <tr><td>用户名:</td><td>${username}</td></tr>
                    <tr><td>用户状态:</td><td>${userStatus}</td></tr>
                    <tr><td>查询日期:</td><td>${date}</td></tr>
                    <tr><td>总上传量:</td><td><strong>${totalUploads}</strong></td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>按小时统计</h6>
                <div id="hourlyChart" class="hourly-chart"></div>
            </div>
        </div>
        
        <div class="mt-4">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">上传时间线</h6>
                <div>
                    <button class="btn btn-sm btn-outline-primary" onclick="switchToLazyTimeline('${userId}', '${date}')">
                        <i class="fas fa-rocket"></i> 使用懒加载模式
                    </button>
                </div>
            </div>
            <div class="timeline-container" style="max-height: 300px; overflow-y: auto;">
    `;
    
    if (!timeline || timeline.length === 0) {
        detailHtml += '<p class="text-muted">该日期无上传记录</p>';
    } else {
        // 如果记录过多，显示警告
        if (timeline.length > 100) {
            detailHtml += `
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle"></i>
                    检测到大量上传记录 (${timeline.length} 条)，建议使用懒加载模式以获得更好的性能。
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
        
        timeline.forEach(function(item) {
            const auditStatus = item.auditStatus !== undefined ? item.auditStatus : 0;
            const statusClass = auditStatus === 1 ? 'text-success' : 
                               auditStatus === 2 ? 'text-danger' : 'text-warning';
            const statusText = auditStatus === 1 ? '已通过' : 
                              auditStatus === 2 ? '已拒绝' : '待审核';
            const time = item.time || '未知时间';
            const title = item.title || '未知题目';
            
            detailHtml += `
                <div class="timeline-item">
                    <small class="text-muted">${time}</small>
                    <strong class="ms-2">${title}</strong>
                    <span class="badge ${statusClass === 'text-success' ? 'bg-success' : 
                                       statusClass === 'text-danger' ? 'bg-danger' : 'bg-warning'} ms-2">
                        ${statusText}
                    </span>
                </div>
            `;
        });
    }
    
    detailHtml += `
            </div>
        </div>
    `;
    
    $('#userDetailContent').html(detailHtml);
    
    // 渲染小时统计图表
    renderHourlyChart(hours, hourlyCounts);
}

/**
 * 渲染用户详情 (懒加载时间线版本)
 */
function renderUserDetailWithLazyTimeline(data) {
    if (!data) {
        $('#userDetailContent').html('<div class="alert alert-warning">没有获取到用户详情数据</div>');
        return;
    }
    
    const hourlyStats = data.hourlyStats || {};
    
    // 安全地获取数据，提供默认值
    const userId = data.userId !== undefined && data.userId !== null ? data.userId : '未知';
    const username = data.username || '未知用户';
    const userStatus = data.userStatus || '未知状态';
    const date = data.date || '未知日期';
    const totalUploads = data.totalUploads !== undefined ? data.totalUploads : 0;
    
    // 生成按小时统计的图表数据
    const hours = Array.from({length: 24}, (_, i) => i);
    const hourlyCounts = hours.map(hour => hourlyStats[hour] || 0);
    
    let detailHtml = `
        <div class="row">
            <div class="col-md-6">
                <h6>用户信息</h6>
                <table class="table table-sm">
                    <tr><td>用户ID:</td><td>${userId}</td></tr>
                    <tr><td>用户名:</td><td>${username}</td></tr>
                    <tr><td>用户状态:</td><td>${userStatus}</td></tr>
                    <tr><td>查询日期:</td><td>${date}</td></tr>
                    <tr><td>总上传量:</td><td><strong>${totalUploads}</strong></td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>按小时统计</h6>
                <div id="hourlyChart" class="hourly-chart"></div>
            </div>
        </div>
        
        <div class="mt-4" id="lazy-timeline-container">
            <!-- 懒加载时间线将在这里渲染 -->
        </div>
    `;
    
    $('#userDetailContent').html(detailHtml);
    
    // 渲染小时统计图表
    renderHourlyChart(hours, hourlyCounts);
    
    // 初始化懒加载时间线
    const timeline = new LazyLoadTimeline(userId, date, '#lazy-timeline-container');
}

/**
 * 切换到懒加载时间线模式
 */
function switchToLazyTimeline(userId, date) {
    // 重新渲染为懒加载模式
    const data = {
        userId: userId,
        date: date,
        username: $('#userDetailContent').find('td:contains("用户名:")').next().text(),
        userStatus: $('#userDetailContent').find('td:contains("用户状态:")').next().text(),
        totalUploads: parseInt($('#userDetailContent').find('td:contains("总上传量:")').next().text()) || 0
    };
    
    renderUserDetailWithLazyTimeline(data);
}

/**
 * 渲染按小时统计图表
 */
function renderHourlyChart(hours, counts) {
    const chartDom = document.getElementById('hourlyChart');
    if (!chartDom) {
        console.error('图表容器不存在');
        return;
    }
    
    // 确保容器有尺寸，延迟初始化图表
    setTimeout(function() {
        // 再次检查容器
        if (!chartDom.offsetWidth || !chartDom.offsetHeight) {
            console.warn('图表容器尺寸为0，延迟再次尝试');
            setTimeout(function() {
                renderHourlyChart(hours, counts);
            }, 100);
            return;
        }
        
        // 销毁可能存在的图表实例
        if (chartDom._echarts_instance) {
            chartDom._echarts_instance.dispose();
        }
        
        const myChart = echarts.init(chartDom);
        
        // 计算最大值，用于设置合适的Y轴范围
        const maxCount = Math.max(...counts);
        const yAxisMax = maxCount > 0 ? Math.ceil(maxCount * 1.2) : 10;
        
        const option = {
            title: {
                text: '用户上传活动分布',
                textStyle: {
                    fontSize: 12,
                    fontWeight: 'normal'
                },
                left: 'center',
                top: '2%'
            },
            xAxis: {
                type: 'category',
                data: hours.map(h => h + '时'),
                axisLabel: {
                    fontSize: 10,
                    interval: 1, // 显示所有小时
                    rotate: 0
                },
                name: '时间',
                nameTextStyle: {
                    fontSize: 10
                }
            },
            yAxis: {
                type: 'value',
                name: '上传次数',
                nameTextStyle: {
                    fontSize: 10,
                    padding: [0, 0, 0, 20]
                },
                axisLabel: {
                    fontSize: 10,
                    formatter: '{value}次'
                },
                min: 0,
                max: yAxisMax,
                splitNumber: 5, // 分成5段显示
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#f0f0f0'
                    }
                }
            },
            series: [{
                data: counts,
                type: 'bar',
                itemStyle: {
                    color: '#007bff',
                    borderRadius: [2, 2, 0, 0]
                },
                barWidth: '60%',
                label: {
                    show: maxCount > 0 && maxCount <= 20, // 只在数据较少时显示标签
                    position: 'top',
                    fontSize: 9
                }
            }],
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    if (params && params.length > 0) {
                        const dataIndex = params[0].dataIndex;
                        const hour = hours[dataIndex];
                        const count = counts[dataIndex];
                        return `${hour}:00-${hour + 1}:00<br/>上传次数：${count}次`;
                    }
                    return '';
                },
                backgroundColor: 'rgba(0,0,0,0.8)',
                textStyle: {
                    color: '#fff',
                    fontSize: 12
                }
            },
            grid: {
                left: '15%',  // 增加左侧空间给Y轴名称
                right: '10%',
                bottom: '15%', // 增加底部空间给X轴名称
                top: '20%',    // 增加顶部空间给标题
                containLabel: true
            }
        };
        
        myChart.setOption(option);
        
        // 响应式调整
        window.addEventListener('resize', function() {
            myChart.resize();
        });
        
        // 销毁之前的图表实例（避免内存泄漏）
        $('#userDetailModal').on('hidden.bs.modal', function() {
            if (myChart) {
                myChart.dispose();
            }
        });
    }, 50); // 50毫秒延迟确保DOM完全准备
}

/**
 * 显示设置限制模态框
 */
function showSetLimitModal(userId, username) {
    if (userId && username) {
        currentUserId = userId;
        $('#limitUserId').val(userId); // 确保userId作为字符串传递
        $('#setLimitModal .modal-title').text(`设置用户上传限制 - ${username}`);
    }
    
    // 关闭用户详情模态框
    $('#userDetailModal').modal('hide');
    
    // 显示设置限制模态框
    $('#setLimitModal').modal('show');
}

/**
 * 提交设置限制
 */
function submitSetLimit() {
    const formData = {
        userId: $('#limitUserId').val(),
        dailyLimit: $('#dailyLimit').val().trim(),
        reason: $('#limitReason').val().trim()
    };
    
    // 验证必需字段
    if (!formData.userId) {
        showError('用户ID不能为空');
        return;
    }
    
    if (!formData.dailyLimit || formData.dailyLimit === '') {
        showError('请输入每日限制数量');
        return;
    }
    
    if (!formData.reason || formData.reason === '') {
        showError('请输入限制原因');
        return;
    }
    
    // 验证每日限制是否为有效数字
    const dailyLimitNum = parseInt(formData.dailyLimit);
    if (isNaN(dailyLimitNum) || dailyLimitNum < 0) {
        showError('每日限制必须是一个大于等于0的整数');
        return;
    }
    
    const config = getDefaultAjaxConfig();
    
    $.ajax({
        url: '/api/admin/user-upload/set-limit',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        headers: config.headers,
        success: function(response) {
            if (isResponseSuccess(response)) {
                showSuccess('设置用户上传限制成功');
                $('#setLimitModal').modal('hide');
                loadUploadStats(); // 刷新数据
            } else {
                showError('设置限制失败: ' + getResponseMessage(response));
            }
        },
        error: function(xhr, status, error) {
            if (xhr.status === 401) {
                handleAuthError();
                return;
            }
            showError('网络错误，设置限制失败');
            console.error('设置限制失败:', error);
        }
    });
}

/**
 * 显示异常检测
 */
function showAnomalyDetection() {
    $('#anomalyModal').modal('show');
    runAnomalyDetection();
}

/**
 * 运行异常检测
 */
function runAnomalyDetection() {
    const date = $('#anomalyDate').val();
    
    // 显示加载状态
    $('#anomalyResults').html(`
        <div class="text-center py-4">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">检测异常行为中...</p>
        </div>
    `);
    
    const config = getDefaultAjaxConfig();
    
    $.ajax({
        url: '/api/admin/user-upload/anomaly-detection',
        method: 'GET',
        data: { date: date },
        headers: config.headers,
        success: function(response) {
            if (isResponseSuccess(response)) {
                renderAnomalyResults(getResponseData(response));
            } else {
                showError('异常检测失败: ' + getResponseMessage(response));
            }
        },
        error: function(xhr, status, error) {
            if (xhr.status === 401) {
                handleAuthError();
                return;
            }
            showError('网络错误，异常检测失败');
            console.error('异常检测失败:', error);
        }
    });
}

/**
 * 渲染异常检测结果
 */
function renderAnomalyResults(anomalies) {
    if (!anomalies || anomalies.length === 0) {
        $('#anomalyResults').html(`
            <div class="text-center py-4">
                <i class="fas fa-check-circle text-success fa-2x"></i>
                <p class="mt-2">未发现异常上传行为</p>
            </div>
        `);
        return;
    }
    
    let resultsHtml = '<div class="list-group">';
    
    anomalies.forEach(function(anomaly) {
        const riskClass = anomaly.riskLevel === '高风险' ? 'anomaly-high' : 
                         anomaly.riskLevel === '中风险' ? 'anomaly-medium' : 'anomaly-low';
        const badgeClass = anomaly.riskLevel === '高风险' ? 'bg-danger' : 
                          anomaly.riskLevel === '中风险' ? 'bg-warning' : 'bg-info';
        
        resultsHtml += `
            <div class="list-group-item ${riskClass}">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${anomaly.username} (ID: ${anomaly.userId})</h6>
                    <span class="badge ${badgeClass} risk-badge">${anomaly.riskLevel}</span>
                </div>
                <p class="mb-1">
                    <strong>上传量:</strong> ${anomaly.uploadCount} 
                    <strong>时间跨度:</strong> ${anomaly.timeSpan}
                </p>
                <div class="mb-1">
                    <strong>异常类型:</strong>
                    ${anomaly.anomalyTypes.map(type => 
                        `<span class="badge bg-secondary me-1">${type}</span>`
                    ).join('')}
                </div>
                <div class="d-flex gap-2 mt-2">
                    <button class="btn btn-sm btn-primary" onclick="showUserDetail('${anomaly.userId}', '${anomaly.date}')">
                        查看详情
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="showSetLimitModal('${anomaly.userId}', '${anomaly.username}')">
                        设置限制
                    </button>
                </div>
            </div>
        `;
    });
    
    resultsHtml += '</div>';
    $('#anomalyResults').html(resultsHtml);
}

/**
 * 导出上传统计
 */
function exportUploadStats() {
    const startDate = $('#startDate').val();
    const endDate = $('#endDate').val();
    
    const config = getDefaultAjaxConfig();
    
    $.ajax({
        url: '/api/admin/user-upload/export',
        method: 'GET',
        data: { startDate: startDate, endDate: endDate },
        headers: config.headers,
        success: function(response) {
            if (isResponseSuccess(response)) {
                // 触发文件下载
                const downloadUrl = getResponseData(response);
                window.open(downloadUrl, '_blank');
                showSuccess('导出成功，正在下载...');
            } else {
                showError('导出失败: ' + getResponseMessage(response));
            }
        },
        error: function(xhr, status, error) {
            if (xhr.status === 401) {
                handleAuthError();
                return;
            }
            showError('网络错误，导出失败');
            console.error('导出失败:', error);
        }
    });
}

/**
 * 懒加载时间线类
 */
class LazyLoadTimeline {
    constructor(userId, date, containerId) {
        this.userId = userId;
        this.date = date;
        this.container = $(containerId);
        this.currentPage = 1;
        this.pageSize = 20;
        this.isLoading = false;
        this.hasNext = true;
        this.timelineData = [];
        
        this.init();
    }
    
    init() {
        this.container.empty();
        this.container.html(`
            <div class="timeline-header d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">上传时间线</h6>
                <small class="text-muted">
                    <span id="timeline-loaded-count">0</span> / 
                    <span id="timeline-total-count">...</span>
                </small>
            </div>
            <div class="timeline-container" style="max-height: 400px; overflow-y: auto;" id="timeline-scroll-container">
                <div id="timeline-items"></div>
                <div id="timeline-loading" class="text-center py-3" style="display: none;">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <span class="ms-2">加载中...</span>
                </div>
                <div id="timeline-end" class="text-center py-3 text-muted" style="display: none;">
                    <small>已加载全部记录</small>
                </div>
            </div>
        `);
        
        this.bindScrollEvent();
        this.loadFirstPage();
    }
    
    bindScrollEvent() {
        const scrollContainer = this.container.find('#timeline-scroll-container');
        scrollContainer.on('scroll', () => {
            if (this.shouldLoadMore()) {
                this.loadNextPage();
            }
        });
    }
    
    shouldLoadMore() {
        const scrollContainer = this.container.find('#timeline-scroll-container')[0];
        const threshold = 50; // 距离底部50px时开始加载
        
        return !this.isLoading && 
               this.hasNext && 
               (scrollContainer.scrollTop + scrollContainer.clientHeight >= 
                scrollContainer.scrollHeight - threshold);
    }
    
    async loadFirstPage() {
        this.currentPage = 1;
        this.timelineData = [];
        this.hasNext = true;
        await this.loadPage(1);
    }
    
    async loadNextPage() {
        if (!this.hasNext || this.isLoading) return;
        
        this.currentPage++;
        await this.loadPage(this.currentPage);
    }
    
    async loadPage(page) {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading();
        
        try {
            const config = getDefaultAjaxConfig();
            const response = await $.ajax({
                url: `/api/admin/user-upload/user-timeline/${this.userId}`,
                method: 'GET',
                data: {
                    date: this.date,
                    page: page,
                    size: this.pageSize
                },
                headers: config.headers
            });
            
            if (isResponseSuccess(response)) {
                const data = getResponseData(response);
                this.handleTimelineData(data);
            } else {
                this.showError('加载时间线失败: ' + getResponseMessage(response));
            }
        } catch (error) {
            if (error.status === 401) {
                handleAuthError();
                return;
            }
            this.showError('网络错误，加载时间线失败');
            console.error('加载时间线失败:', error);
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }
    
    handleTimelineData(data) {
        const timeline = data.timeline || [];
        const total = data.total || 0;
        const hasNext = data.hasNext || false;
        
        // 合并数据
        this.timelineData = [...this.timelineData, ...timeline];
        this.hasNext = hasNext;
        
        // 更新显示
        this.renderTimeline(timeline, this.currentPage === 1);
        this.updateCounter(this.timelineData.length, total);
        
        // 如果是第一页且包含统计信息，更新用户信息
        if (this.currentPage === 1 && data.totalUploads !== undefined) {
            this.updateUserInfo(data);
        }
        
        // 检查是否需要显示结束提示
        if (!hasNext) {
            this.showEndMessage();
        }
    }
    
    renderTimeline(timeline, isFirstPage) {
        const container = this.container.find('#timeline-items');
        
        if (isFirstPage) {
            container.empty();
        }
        
        if (timeline.length === 0 && isFirstPage) {
            container.html('<div class="text-center text-muted py-4">该日期无上传记录</div>');
            return;
        }
        
        timeline.forEach(item => {
            const timelineItem = this.createTimelineItem(item);
            container.append(timelineItem);
        });
        
        // 添加淡入动画
        container.find('.timeline-item:last-child').hide().fadeIn(300);
    }
    
    createTimelineItem(item) {
        const auditStatus = item.auditStatus !== undefined ? item.auditStatus : 0;
        const statusConfig = this.getStatusConfig(auditStatus);
        const time = item.time || '未知时间';
        const title = item.title || '未知题目';
        
        return $(`
            <div class="timeline-item border-start border-2 ps-3 pb-3 mb-2 position-relative" 
                 style="border-color: ${statusConfig.borderColor} !important;">
                <div class="timeline-point position-absolute" 
                     style="left: -6px; top: 8px; width: 10px; height: 10px; 
                            background-color: ${statusConfig.pointColor}; 
                            border-radius: 50%; border: 2px solid white;"></div>
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <small class="text-muted d-block">${time}</small>
                        <span class="fw-medium">${title}</span>
                    </div>
                    <span class="badge ${statusConfig.badgeClass} ms-2">
                        ${statusConfig.text}
                    </span>
                </div>
            </div>
        `);
    }
    
    getStatusConfig(auditStatus) {
        switch (auditStatus) {
            case 1:
                return {
                    borderColor: '#28a745',
                    pointColor: '#28a745',
                    badgeClass: 'bg-success',
                    text: '已通过'
                };
            case 2:
                return {
                    borderColor: '#dc3545',
                    pointColor: '#dc3545',
                    badgeClass: 'bg-danger',
                    text: '已拒绝'
                };
            default:
                return {
                    borderColor: '#ffc107',
                    pointColor: '#ffc107',
                    badgeClass: 'bg-warning text-dark',
                    text: '待审核'
                };
        }
    }
    
    updateCounter(loaded, total) {
        this.container.find('#timeline-loaded-count').text(loaded);
        this.container.find('#timeline-total-count').text(total);
    }
    
    updateUserInfo(data) {
        // 如果页面有用户信息区域，更新统计数据
        const totalElement = $('#userDetailContent').find('td:contains("总上传量:")').next();
        if (totalElement.length > 0) {
            totalElement.html(`<strong>${data.totalUploads}</strong>`);
        }
    }
    
    showLoading() {
        this.container.find('#timeline-loading').show();
    }
    
    hideLoading() {
        this.container.find('#timeline-loading').hide();
    }
    
    showEndMessage() {
        this.container.find('#timeline-end').show();
    }
    
    showError(message) {
        const container = this.container.find('#timeline-items');
        container.append(`
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);
    }
}

/**
 * 工具函数
 */

/**
 * 格式化日期
 */
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

/**
 * 显示成功消息
 */
function showSuccess(message) {
    // 这里可以使用任何通知库，比如toastr、SweetAlert等
    alert('成功: ' + message);
}

/**
 * 显示错误消息
 */
function showError(message) {
    // 这里可以使用任何通知库，比如toastr、SweetAlert等
    alert('错误: ' + message);
}

/**
 * 用户资料
 */
function showUserProfile() {
    // TODO: 实现用户资料功能
    console.log('显示用户资料');
}

/**
 * 系统设置
 */
function showSettings() {
    // TODO: 实现系统设置功能
    console.log('显示系统设置');
}

/**
 * 退出登录
 */
async function logout() {
    if (!confirm('确定要退出登录吗？')) {
        return;
    }
    
    try {
        const token = getAuthToken();
        
        if (token) {
            // 调用后端退出登录API
            await fetch('/api/admin/logout', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
        }
        
        // 清除本地存储
        localStorage.removeItem('adminToken');
        sessionStorage.removeItem('adminToken');
        
        // 跳转到管理员登录页
        window.location.href = '/admin/login';
    } catch (error) {
        console.error('退出登录失败:', error);
        // 即使失败也要清除本地存储并跳转
        localStorage.removeItem('adminToken');
        sessionStorage.removeItem('adminToken');
        window.location.href = '/admin/login';
    }
} 