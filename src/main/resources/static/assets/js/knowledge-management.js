/**
 * 知识点管理JavaScript
 */
class KnowledgeManagement {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 1;
        this.searchTerm = '';
        this.groupFilter = '';
        this.freeFilter = '';
        this.isEditing = false;
        this.editingId = null;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadKnowledgeStats();
        this.loadGroupOptions();
        this.loadKnowledgeList();
    }

    bindEvents() {
        // 搜索事件
        $('#searchInput').on('keypress', (e) => {
            if (e.which === 13) {
                this.searchKnowledge();
            }
        });

        // 筛选器变化事件
        $('#groupFilter, #freeFilter').on('change', () => {
            this.searchKnowledge();
        });

        // 分页事件
        $(document).on('click', '.page-link', (e) => {
            e.preventDefault();
            const page = $(e.target).data('page');
            if (page && page !== this.currentPage) {
                this.currentPage = page;
                this.loadKnowledgeList();
            }
        });
    }

    /**
     * 加载知识点统计数据
     */
    async loadKnowledgeStats() {
        try {
            const response = await this.fetchAPI('/api/admin/knowledge/stats');
            if (response && response.code === 200) {
                this.updateStats(response.data);
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    /**
     * 更新统计显示
     */
    updateStats(stats) {
        $('#totalKnowledgeCount').text(stats.total || 0);
        
        if (stats.groupStats && stats.groupStats.length > 0) {
            $('#groupCount').text(stats.groupStats.length);
        }
        
        // 计算免费/付费数量（这里需要额外的API或从列表中计算）
        this.calculateFreeStats();
    }

    /**
     * 加载分组选项
     */
    async loadGroupOptions() {
        try {
            const response = await this.fetchAPI('/api/admin/knowledge/groups');
            if (response && response.code === 200) {
                const select = $('#groupFilter');
                select.empty().append('<option value="">所有分组</option>');
                
                response.data.forEach(group => {
                    select.append(`<option value="${group}">${group}</option>`);
                });
            }
        } catch (error) {
            console.error('加载分组选项失败:', error);
        }
    }

    /**
     * 加载知识点列表
     */
    async loadKnowledgeList() {
        try {
            this.showLoading();
            
            const params = new URLSearchParams({
                page: this.currentPage,
                size: this.pageSize,
                search: this.searchTerm,
                groupName: this.groupFilter
            });

            const response = await this.fetchAPI(`/api/admin/knowledge?${params}`);
            
            if (response && response.code === 200) {
                this.renderKnowledgeList(response.data.data);
                this.renderPagination(response.data);
                this.calculateFreeStats(); // 重新计算统计
            } else {
                this.showError('加载知识点列表失败: ' + (response.message || ''));
            }
        } catch (error) {
            console.error('加载知识点列表失败:', error);
            this.showError('加载知识点列表失败: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * 渲染知识点列表
     */
    renderKnowledgeList(knowledgeList) {
        const tbody = $('#knowledgeTableBody');
        tbody.empty();

        if (!knowledgeList || knowledgeList.length === 0) {
            tbody.append(`
                <tr>
                    <td colspan="8" class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <div>暂无知识点数据</div>
                    </td>
                </tr>
            `);
            return;
        }

        knowledgeList.forEach(knowledge => {
            const row = this.createKnowledgeRow(knowledge);
            tbody.append(row);
        });
    }

    /**
     * 创建知识点行
     */
    createKnowledgeRow(knowledge) {
        const freeText = knowledge.isFree === 1 ? '免费' : '付费';
        const freeBadgeClass = knowledge.isFree === 1 ? 'bg-success' : 'bg-warning';
        const formattedDate = knowledge.createdAt ? new Date(knowledge.createdAt).toLocaleDateString() : '';

        return `
            <tr>
                <td>${knowledge.id}</td>
                <td><strong>${knowledge.knowledgeId}</strong></td>
                <td>${knowledge.knowledgeName}</td>
                <td>
                    <span class="knowledge-group-badge badge bg-info">${knowledge.groupName}</span>
                </td>
                <td>
                    <span class="badge ${freeBadgeClass}">${freeText}</span>
                </td>
                <td>${knowledge.sort}</td>
                <td>${formattedDate}</td>
                <td class="action-buttons">
                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="knowledgeManagement.editKnowledge(${knowledge.id})">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="knowledgeManagement.deleteKnowledge(${knowledge.id})">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </td>
            </tr>
        `;
    }

    /**
     * 渲染分页
     */
    renderPagination(response) {
        const pagination = $('#pagination');
        pagination.empty();

        if (!response || !response.pages || response.pages <= 1) {
            return;
        }

        this.totalPages = response.pages;
        const current = response.current || this.currentPage;
        
        // 上一页
        const prevDisabled = current <= 1 ? 'disabled' : '';
        pagination.append(`
            <li class="page-item ${prevDisabled}">
                <a class="page-link" data-page="${current - 1}" href="#">上一页</a>
            </li>
        `);

        // 页码
        const startPage = Math.max(1, current - 2);
        const endPage = Math.min(this.totalPages, current + 2);

        if (startPage > 1) {
            pagination.append('<li class="page-item"><a class="page-link" data-page="1" href="#">1</a></li>');
            if (startPage > 2) {
                pagination.append('<li class="page-item disabled"><span class="page-link">...</span></li>');
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === current ? 'active' : '';
            pagination.append(`
                <li class="page-item ${activeClass}">
                    <a class="page-link" data-page="${i}" href="#">${i}</a>
                </li>
            `);
        }

        if (endPage < this.totalPages) {
            if (endPage < this.totalPages - 1) {
                pagination.append('<li class="page-item disabled"><span class="page-link">...</span></li>');
            }
            pagination.append(`<li class="page-item"><a class="page-link" data-page="${this.totalPages}" href="#">${this.totalPages}</a></li>`);
        }

        // 下一页
        const nextDisabled = current >= this.totalPages ? 'disabled' : '';
        pagination.append(`
            <li class="page-item ${nextDisabled}">
                <a class="page-link" data-page="${current + 1}" href="#">下一页</a>
            </li>
        `);
    }

    /**
     * 搜索知识点
     */
    searchKnowledge() {
        this.searchTerm = $('#searchInput').val().trim();
        this.groupFilter = $('#groupFilter').val();
        this.freeFilter = $('#freeFilter').val();
        this.currentPage = 1;
        this.loadKnowledgeList();
    }

    /**
     * 重置搜索
     */
    resetSearch() {
        $('#searchInput').val('');
        $('#groupFilter').val('');
        $('#freeFilter').val('');
        this.searchTerm = '';
        this.groupFilter = '';
        this.freeFilter = '';
        this.currentPage = 1;
        this.loadKnowledgeList();
    }

    /**
     * 显示添加知识点模态框
     */
    showAddKnowledgeModal() {
        this.isEditing = false;
        this.editingId = null;
        
        $('#knowledgeModalTitle').text('添加知识点');
        $('#knowledgeForm')[0].reset();
        $('#knowledgeFormId').val('');
        $('#knowledgeFormSort').val('1');
        $('#knowledgeFormIsFree').val('1');
        
        const modal = new bootstrap.Modal(document.getElementById('knowledgeModal'));
        modal.show();
    }

    /**
     * 编辑知识点
     */
    async editKnowledge(id) {
        try {
            const response = await this.fetchAPI(`/api/admin/knowledge/${id}`);
            if (response && response.code === 200) {
                const knowledge = response.data;
                
                this.isEditing = true;
                this.editingId = id;
                
                $('#knowledgeModalTitle').text('编辑知识点');
                $('#knowledgeFormId').val(knowledge.id);
                $('#knowledgeFormKnowledgeId').val(knowledge.knowledgeId);
                $('#knowledgeFormName').val(knowledge.name);
                $('#knowledgeFormGroupName').val(knowledge.groupName);
                $('#knowledgeFormIsFree').val(knowledge.isFree);
                $('#knowledgeFormSort').val(knowledge.sort);
                
                const modal = new bootstrap.Modal(document.getElementById('knowledgeModal'));
                modal.show();
            } else {
                this.showError('获取知识点详情失败: ' + (response.message || ''));
            }
        } catch (error) {
            this.showError('获取知识点详情失败: ' + error.message);
        }
    }

    /**
     * 保存知识点
     */
    async saveKnowledge() {
        const form = document.getElementById('knowledgeForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const knowledgeData = {
            knowledgeId: parseInt($('#knowledgeFormKnowledgeId').val()),
            name: $('#knowledgeFormName').val().trim(),
            groupName: $('#knowledgeFormGroupName').val().trim(),
            isFree: parseInt($('#knowledgeFormIsFree').val()),
            sort: parseInt($('#knowledgeFormSort').val()) || 1
        };

        try {
            let response;
            if (this.isEditing) {
                response = await this.fetchAPI(`/api/admin/knowledge/${this.editingId}`, {
                    method: 'PUT',
                    body: knowledgeData
                });
            } else {
                response = await this.fetchAPI('/api/admin/knowledge', {
                    method: 'POST',
                    body: knowledgeData
                });
            }

            if (response && response.code === 200) {
                this.showSuccess(this.isEditing ? '知识点更新成功' : '知识点创建成功');
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('knowledgeModal'));
                modal.hide();
                
                // 刷新数据
                this.loadKnowledgeStats();
                this.loadGroupOptions();
                this.loadKnowledgeList();
            } else {
                this.showError(response.message || '操作失败');
            }
        } catch (error) {
            this.showError('操作失败: ' + error.message);
        }
    }

    /**
     * 删除知识点
     */
    async deleteKnowledge(id) {
        if (!confirm('确定要删除这个知识点吗？删除后不可恢复。')) {
            return;
        }

        try {
            const response = await this.fetchAPI(`/api/admin/knowledge/${id}`, {
                method: 'DELETE'
            });

            if (response && response.code === 200) {
                this.showSuccess('知识点删除成功');
                this.loadKnowledgeStats();
                this.loadGroupOptions();
                this.loadKnowledgeList();
            } else {
                this.showError(response.message || '删除知识点失败');
            }
        } catch (error) {
            this.showError('删除知识点失败: ' + error.message);
        }
    }

    /**
     * 计算免费/付费统计
     */
    calculateFreeStats() {
        const rows = $('#knowledgeTableBody tr');
        let freeCount = 0;
        let paidCount = 0;
        
        rows.each(function() {
            const badge = $(this).find('.badge:contains("免费"), .badge:contains("付费")');
            if (badge.text().includes('免费')) {
                freeCount++;
            } else if (badge.text().includes('付费')) {
                paidCount++;
            }
        });
        
        $('#freeKnowledgeCount').text(freeCount);
        $('#paidKnowledgeCount').text(paidCount);
    }

    /**
     * API请求封装
     */
    async fetchAPI(url, options = {}) {
        const token = localStorage.getItem('adminToken') || sessionStorage.getItem('adminToken');
        
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': token ? `Bearer ${token}` : ''
            }
        };

        const mergedOptions = { ...defaultOptions, ...options };
        
        if (mergedOptions.body && typeof mergedOptions.body === 'object') {
            mergedOptions.body = JSON.stringify(mergedOptions.body);
        }

        const response = await fetch(url, mergedOptions);
        
        if (!response.ok) {
            if (response.status === 401) {
                this.handleUnauthorized();
                throw new Error('未授权访问');
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    /**
     * 处理未授权
     */
    handleUnauthorized() {
        localStorage.removeItem('adminToken');
        sessionStorage.removeItem('adminToken');
        window.location.href = '/admin/login';
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        const tbody = $('#knowledgeTableBody');
        tbody.html(`
            <tr>
                <td colspan="8" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2">加载中...</div>
                </td>
            </tr>
        `);
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        // 加载完成后会重新渲染表格，所以这里不需要特别处理
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        // 这里可以使用更好的通知组件，比如 Toast
        alert(message);
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        console.error(message);
        alert(message);
    }
}

// 全局函数，供HTML调用
function showAddKnowledgeModal() {
    window.knowledgeManagement.showAddKnowledgeModal();
}

function searchKnowledge() {
    window.knowledgeManagement.searchKnowledge();
}

function resetSearch() {
    window.knowledgeManagement.resetSearch();
}

function saveKnowledge() {
    window.knowledgeManagement.saveKnowledge();
}

// 页面加载完成后初始化
$(document).ready(function() {
    window.knowledgeManagement = new KnowledgeManagement();
}); 