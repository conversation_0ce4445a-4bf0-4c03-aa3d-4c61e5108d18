/**
 * 题目管理页面JavaScript
 */

class TopicManagement {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 0;
        this.searchTerm = '';
        this.typeFilter = '';
        this.difficultyFilter = '';
        this.statusFilter = '';
        this.knowledgeFilter = '';
        this.idsFilter = '';
        this.currentKnowledgeId = '';
        this.currentEditMode = 'add'; // 'add' or 'edit'
        this.currentTopicId = null;
        this.currentViewingTopic = null; // 当前查看的题目数据
        this.init();
    }

    /**
     * 初始化
     */
    init() {
        this.bindEvents();
        this.loadTopicStats();
        this.loadKnowledgeGroups(); // 加载知识点分类
        this.loadTopicList();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 搜索事件
        $('#searchInput').on('keypress', (e) => {
            if (e.which === 13) {
                this.searchTopics();
            }
        });

        // ID搜索事件 - 新增
        $('#idsInput').on('keypress', (e) => {
            if (e.which === 13) {
                this.searchTopics();
            }
        });

        // 筛选器变化事件
        $('#typeFilter, #knowledgeFilter, #difficultyFilter, #statusFilter').on('change', () => {
            this.searchTopics();
        });

        // 分页事件
        $(document).on('click', '.pagination .page-link', (e) => {
            e.preventDefault();
            const page = parseInt($(e.target).data('page'));
            if (page && page !== this.currentPage && page > 0 && page <= this.totalPages) {
                this.currentPage = page;
                this.loadTopicList();
            }
        });
        
        // 每页显示数量变化事件
        $(document).on('change', '#pageSizeSelect', (e) => {
            const newPageSize = parseInt($(e.target).val());
            if (newPageSize !== this.pageSize) {
                this.pageSize = newPageSize;
                this.currentPage = 1; // 重置到第一页
                console.log('页面大小已更改为:', this.pageSize);
                this.loadTopicList();
            }
        });

        // 批量选择
        $('#selectAll').on('change', this.toggleSelectAll.bind(this));
        
        $(document).on('change', '.topic-checkbox', this.updateBatchActionsVisibility.bind(this));
        
        // 窗口大小改变时重新检查滚动内容
        $(window).on('resize', () => {
            this.checkScrollContent();
        });
    }

    /**
     * 加载题目统计数据
     */
    async loadTopicStats() {
        try {
            const response = await this.fetchAPI('/api/admin/topics/stats');
            if (response && response.code === 200) {
                this.updateStats(response.data);
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    /**
     * 加载题目列表
     */
    async loadTopicList() {
        try {
            this.showLoading();
            
            const params = new URLSearchParams();
            params.append('page', this.currentPage);
            params.append('size', this.pageSize);
            
            // 只添加非空的参数
            if (this.searchTerm) params.append('search', this.searchTerm);
            if (this.typeFilter) params.append('type', this.typeFilter);
            if (this.difficultyFilter) params.append('difficulty', this.difficultyFilter);
            if (this.statusFilter) params.append('status', this.statusFilter);
            if (this.knowledgeFilter || this.currentKnowledgeId) {
                params.append('knowledgeId', this.knowledgeFilter || this.currentKnowledgeId);
            }
            if (this.idsFilter) params.append('ids', this.idsFilter);

            const response = await this.fetchAPI(`/api/admin/topics?${params}`);
            
            if (response && response.code === 200) {
                // 处理分页数据结构
                const data = response.data;
                const topics = data.data || data.records || data.content || [];
                
                this.renderTopicList(topics);
                this.renderPagination(data);
                
                // 输出调试信息
                console.log('分页数据:', {
                    current: data.current || this.currentPage,
                    pages: data.pages || data.totalPages,
                    total: data.total || data.totalElements,
                    size: data.size || this.pageSize,
                    records: topics.length
                });
            } else {
                this.showError('加载题目列表失败: ' + (response.message || ''));
            }
        } catch (error) {
            console.error('加载题目列表失败:', error);
            this.showError('加载题目列表失败: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * 渲染题目列表
     */
    renderTopicList(topics) {
        const tbody = $('#topicTableBody');
        tbody.empty();

        if (!topics || topics.length === 0) {
            tbody.append(`
                <tr>
                    <td colspan="10" class="text-center text-muted py-4 empty-state">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <div>暂无题目数据</div>
                        <small class="text-muted mt-2">试试调整筛选条件或添加新题目</small>
                    </td>
                </tr>
            `            );
            // 移除滚动状态类
            $('.table-scrollable').removeClass('has-vertical-scroll has-horizontal-scroll');
            // 更新状态指示器
            this.updateTableStatusIndicator(0);
            return;
        }

        topics.forEach(topic => {
            const row = this.createTopicRow(topic);
            tbody.append(row);
        });
        
        // 更新状态指示器
        this.updateTableStatusIndicator(topics.length);
        
        // 检查是否有更多内容需要滚动查看
        this.checkScrollContent();
        
        // 确保表格滚动功能正常
        this.ensureTableScrollable();
    }

    /**
     * 创建题目行
     */
    createTopicRow(topic) {
        const typeNames = {
            'choice': '单选题',
            'multiple': '多选题',
            'judge': '判断题',
            'fill': '填空题',
            'short': '简答题'
        };

        const difficultyNames = {
            1: '简单',
            2: '中等',
            3: '困难'
        };

        const difficultyClass = {
            1: 'difficulty-easy',
            2: 'difficulty-medium',
            3: 'difficulty-hard'
        };

        const typeClass = `topic-type-${topic.type}`;

        // 格式化创建时间
        const formatDate = (dateStr) => {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {hour12: false});
        };

        // 表头列顺序：ID, 题目标题, 类型, 知识点, 难度, 分值, 标签, 创建时间, 操作
        return `
            <tr>
                <td>
                    <input type="checkbox" class="topic-checkbox" value="${topic.id}">
                </td>
                <td>${topic.id}</td>
                <td class="topic-title" title="${topic.title}">
                    ${topic.title && topic.title.length > 50 ? topic.title.substring(0, 50) + '...' : (topic.title || '未知标题')}
                </td>
                <td>
                    <span class="badge ${typeClass}">${typeNames[topic.type] || topic.type}</span>
                </td>
                <td>${topic.knowledgePointName || topic.knowId || '未分类'}</td>
                <td>
                    <span class="badge ${difficultyClass[topic.difficulty] || 'difficulty-medium'}">
                        ${difficultyNames[topic.difficulty] || topic.difficulty}
                    </span>
                </td>
                <td><span class="badge bg-info">${topic.score || 3}</span></td>
                <td>${topic.tags || '-'}</td>
                <td>${formatDate(topic.createdAt)}</td>
                <td class="action-buttons">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="topicManagement.viewTopic(${topic.id})">
                        <i class="fas fa-eye"></i> 查看
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="topicManagement.editTopic(${topic.id})">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="topicManagement.deleteTopic(${topic.id})">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </td>
            </tr>
        `;
    }

    /**
     * 渲染分页
     */
    renderPagination(response) {
        const pagination = $('#pagination');
        const paginationNav = $('#pagination');
        
        pagination.empty();

        // 处理不同的分页数据结构
        const totalPages = response.pages || response.totalPages || 1;
        const current = response.current || response.page || this.currentPage;
        const total = response.total || response.totalElements || 0;
        
        // 如果只有一页或没有数据，显示基本信息但隐藏分页按钮
        if (totalPages <= 1) {
            const pageInfo = $(`
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        <small>共 <strong>${total}</strong> 条记录</small>
                    </div>
                    <div class="input-group" style="width: 200px;">
                        <span class="input-group-text">每页显示</span>
                        <select class="form-select form-select-sm" id="pageSizeSelect">
                            <option value="10" ${this.pageSize === 10 ? 'selected' : ''}>10</option>
                            <option value="20" ${this.pageSize === 20 ? 'selected' : ''}>20</option>
                            <option value="50" ${this.pageSize === 50 ? 'selected' : ''}>50</option>
                            <option value="100" ${this.pageSize === 100 ? 'selected' : ''}>100</option>
                        </select>
                        <span class="input-group-text">条</span>
                    </div>
                </div>
            `);
            pagination.append(pageInfo);
            paginationNav.show(); // 仍然显示，但只显示分页大小选择器
            return;
        }

        // 显示分页器
        paginationNav.show();
        
        this.totalPages = totalPages;
        
        // 创建分页ul容器
        const paginationUl = $('<ul class="pagination justify-content-center mb-0"></ul>');
        pagination.append(paginationUl);
        
        // 上一页
        const prevDisabled = current <= 1 ? 'disabled' : '';
        paginationUl.append(`
            <li class="page-item ${prevDisabled}">
                <a class="page-link" data-page="${current - 1}" href="#" ${prevDisabled ? '' : 'style="cursor: pointer;"'}>
                    <i class="fas fa-angle-left"></i> 上一页
                </a>
            </li>
        `);

        // 页码
        const startPage = Math.max(1, current - 2);
        const endPage = Math.min(this.totalPages, current + 2);

        if (startPage > 1) {
            paginationUl.append('<li class="page-item"><a class="page-link" data-page="1" href="#" style="cursor: pointer;">1</a></li>');
            if (startPage > 2) {
                paginationUl.append('<li class="page-item disabled"><span class="page-link">...</span></li>');
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === current ? 'active' : '';
            paginationUl.append(`
                <li class="page-item ${activeClass}">
                    <a class="page-link" data-page="${i}" href="#" style="cursor: pointer;">${i}</a>
                </li>
            `);
        }

        if (endPage < this.totalPages) {
            if (endPage < this.totalPages - 1) {
                paginationUl.append('<li class="page-item disabled"><span class="page-link">...</span></li>');
            }
            paginationUl.append(`<li class="page-item"><a class="page-link" data-page="${this.totalPages}" href="#" style="cursor: pointer;">${this.totalPages}</a></li>`);
        }

        // 下一页
        const nextDisabled = current >= this.totalPages ? 'disabled' : '';
        paginationUl.append(`
            <li class="page-item ${nextDisabled}">
                <a class="page-link" data-page="${current + 1}" href="#" ${nextDisabled ? '' : 'style="cursor: pointer;"'}>
                    下一页 <i class="fas fa-angle-right"></i>
                </a>
            </li>
        `);
        
        // 添加分页信息
        const startRecord = ((current - 1) * this.pageSize) + 1;
        const endRecord = Math.min(current * this.pageSize, response.total);
        const totalRecords = response.total || 0;
        
        const pageInfo = $(`
            <div class="d-flex justify-content-between align-items-center mt-3 flex-wrap">
                <div class="text-muted mb-2 mb-md-0">
                    <small>
                        显示第 <strong>${startRecord}</strong> - <strong>${endRecord}</strong> 条，
                        共 <strong>${totalRecords}</strong> 条记录
                        (第 <strong>${current}</strong> 页，共 <strong>${this.totalPages}</strong> 页)
                    </small>
                </div>
                <div class="input-group" style="width: 200px;">
                    <span class="input-group-text">每页显示</span>
                    <select class="form-select form-select-sm" id="pageSizeSelect">
                        <option value="10" ${this.pageSize === 10 ? 'selected' : ''}>10</option>
                        <option value="20" ${this.pageSize === 20 ? 'selected' : ''}>20</option>
                        <option value="50" ${this.pageSize === 50 ? 'selected' : ''}>50</option>
                        <option value="100" ${this.pageSize === 100 ? 'selected' : ''}>100</option>
                    </select>
                    <span class="input-group-text">条</span>
                </div>
            </div>
        `);
        pagination.append(pageInfo);
    }

    /**
     * 查看题目
     */
    async viewTopic(topicId) {
        try {
            const response = await this.fetchAPI(`/api/admin/topics/${topicId}`);
            if (response && response.code === 200) {
                this.showTopicModal(response.data, 'view');
            } else {
                this.showError('获取题目详情失败: ' + (response.message || ''));
            }
        } catch (error) {
            this.showError('获取题目详情失败: ' + error.message);
        }
    }

    /**
     * 编辑题目
     */
    async editTopic(topicId) {
        try {
            const response = await this.fetchAPI(`/api/admin/topics/${topicId}`);
            if (response && response.code === 200) {
                this.showTopicModal(response.data, 'edit');
            } else {
                this.showError('获取题目详情失败: ' + (response.message || ''));
            }
        } catch (error) {
            this.showError('获取题目详情失败: ' + error.message);
        }
    }

    /**
     * 删除题目
     */
    async deleteTopic(topicId) {
        if (!confirm('确定要删除这个题目吗？删除后不可恢复。')) {
            return;
        }

        try {
            const response = await this.fetchAPI(`/api/admin/topics/${topicId}`, {
                method: 'DELETE'
            });

            if (response && response.code === 200) {
                this.showSuccess('题目删除成功');
                this.loadTopicList();
                this.loadTopicStats();
            } else {
                this.showError(response.message || '删除题目失败');
            }
        } catch (error) {
            this.showError('删除题目失败: ' + error.message);
        }
    }

    /**
     * 显示题目模态框
     */
    showTopicModal(topic, mode) {
        if (mode === 'view') {
            this.showTopicDetailModal(topic);
        } else if (mode === 'edit') {
            this.showTopicEditModal(topic);
        }
    }

    /**
     * 显示题目详情模态框（只读）
     */
    showTopicDetailModal(topic) {
        const modal = new bootstrap.Modal(document.getElementById('topicDetailModal'));
        const content = document.getElementById('topicDetailContent');
        
        // 保存当前查看的题目数据
        this.currentViewingTopic = topic;
        
        // 生成题目详情HTML
        const detailHtml = this.generateTopicDetailHtml(topic);
        content.innerHTML = detailHtml;
        
        modal.show();
    }

    /**
     * 显示题目编辑模态框
     */
    showTopicEditModal(topic) {
        const modal = new bootstrap.Modal(document.getElementById('topicModal'));
        
        // 设置模态框标题
        document.getElementById('topicModalTitle').textContent = '编辑题目';
        
        // 填充表单数据
        this.populateTopicForm(topic);
        
        // 设置模式为编辑
        this.currentEditMode = 'edit';
        this.currentTopicId = topic.id;
        
        modal.show();
    }

    /**
     * 显示添加题目模态框
     */
    showAddTopicModal() {
        const modal = new bootstrap.Modal(document.getElementById('topicModal'));
        
        // 设置模态框标题
        document.getElementById('topicModalTitle').textContent = '添加题目';
        
        // 清空表单
        this.clearTopicForm();
        
        // 设置模式为添加
        this.currentEditMode = 'add';
        this.currentTopicId = null;
        
        modal.show();
    }

    /**
     * 显示导入模态框
     */
    showImportModal() {
        console.log('显示导入模态框');
        alert('批量导入功能待实现');
    }

    /**
     * 导出题目
     */
    async exportTopics() {
        try {
            const response = await this.fetchAPI('/api/admin/topics/export', {
                method: 'POST'
            });
            
            if (response && response.code === 200) {
                // 处理文件下载
                this.downloadFile(response.data.url, response.data.filename);
            } else {
                this.showError(response.message || '导出失败');
            }
        } catch (error) {
            this.showError('导出题目失败: ' + error.message);
        }
    }

    /**
     * 下载模板
     */
    downloadTemplate() {
        const link = document.createElement('a');
        link.href = '/api/admin/topics/template';
        link.download = '题目导入模板.xlsx';
        link.click();
    }

    /**
     * 下载文件
     */
    downloadFile(url, filename) {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
    }

    /**
     * API请求封装
     */
    async fetchAPI(url, options = {}) {
        const token = localStorage.getItem('adminToken') || sessionStorage.getItem('adminToken');
        
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': token ? `Bearer ${token}` : ''
            }
        };

        const mergedOptions = { ...defaultOptions, ...options };
        
        if (mergedOptions.body && typeof mergedOptions.body === 'object') {
            mergedOptions.body = JSON.stringify(mergedOptions.body);
        }

        const response = await fetch(url, mergedOptions);
        
        if (!response.ok) {
            if (response.status === 401) {
                this.handleUnauthorized();
                throw new Error('未授权访问');
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    /**
     * 处理未授权
     */
    handleUnauthorized() {
        localStorage.removeItem('adminToken');
        sessionStorage.removeItem('adminToken');
        window.location.href = '/admin/login';
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        const tbody = $('#topicTableBody');
        tbody.html(`
            <tr>
                <td colspan="10" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2">加载中...</div>
                </td>
            </tr>
        `);
        
        // 确保滚动容器可见
        $('.table-scrollable').css('overflow-y', 'auto');
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        // 加载完成后会重新渲染表格，所以这里不需要特别处理
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        // 创建或更新 Toast 通知
        this.showToast(message, 'success');
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        console.error(message);
        this.showToast(message, 'error');
    }

    /**
     * 显示 Toast 通知
     */
    showToast(message, type = 'info') {
        // 移除现有的 toast
        const existingToast = document.querySelector('.custom-toast');
        if (existingToast) {
            existingToast.remove();
        }

        // 创建新的 toast
        const toast = document.createElement('div');
        toast.className = `custom-toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                <span class="toast-message">${this.escapeHtml(message)}</span>
            </div>
        `;

        // 添加样式
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            zIndex: '9999',
            padding: '12px 20px',
            borderRadius: '8px',
            color: 'white',
            fontSize: '14px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease',
            minWidth: '300px',
            maxWidth: '500px'
        });

        // 设置颜色
        if (type === 'success') {
            toast.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
        } else if (type === 'error') {
            toast.style.background = 'linear-gradient(135deg, #dc3545, #e74c3c)';
        } else {
            toast.style.background = 'linear-gradient(135deg, #007bff, #6610f2)';
        }

        document.body.appendChild(toast);

        // 动画显示
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, type === 'error' ? 5000 : 3000);

        // 点击关闭
        toast.addEventListener('click', () => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        });
    }

    /**
     * 更新统计显示
     */
    updateStats(stats) {
        const elements = {
            'totalTopicsCount': stats.totalTopics,
            'choiceCount': stats.choiceCount,
            'multipleCount': stats.multipleCount,
            'judgeCount': stats.judgeCount,
            'fillCount': stats.fillCount,
            'shortCount': stats.shortCount
        };
        
        Object.keys(elements).forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = elements[elementId] || 0;
            }
        });
    }

    /**
     * 加载知识点分组
     */
    async loadKnowledgeGroups() {
        try {
            const response = await this.fetchAPI('/api/admin/topics/knowledge-groups');
            if (response && response.code === 200) {
                this.renderKnowledgeGroups(response.data);
                this.loadKnowledgeOptions(response.data);
            }
        } catch (error) {
            console.error('加载知识点失败:', error);
            // 如果知识点API不存在，提供默认的知识点列表
            const defaultGroups = [
                {
                    groupName: '数学',
                    knowledgePoints: [
                        { knowledgeId: 1, knowledgeName: '数学基础', topicCount: 0 },
                        { knowledgeId: 2, knowledgeName: '代数', topicCount: 0 }
                    ],
                    totalTopicCount: 0
                },
                {
                    groupName: '语文',
                    knowledgePoints: [
                        { knowledgeId: 3, knowledgeName: '语文基础', topicCount: 0 },
                        { knowledgeId: 4, knowledgeName: '阅读理解', topicCount: 0 }
                    ],
                    totalTopicCount: 0
                }
            ];
            this.renderKnowledgeGroups(defaultGroups);
            this.loadKnowledgeOptions(defaultGroups);
        }
    }

    /**
     * 渲染知识点分组侧边栏（二级分类）
     */
    renderKnowledgeGroups(groups) {
        const container = $('#knowledgeGroupsList');
        
        // 保留"全部题目"选项
        const allOption = container.find('li:first');
        container.empty().append(allOption);

        if (!groups || groups.length === 0) {
            container.append(`
                <li class="nav-item">
                    <span class="nav-link text-muted">
                        <i class="fas fa-info-circle me-2"></i>暂无知识点
                    </span>
                </li>
            `);
            return;
        }

        groups.forEach((group, index) => {
            const groupId = `group-${index}`;
            const isDefaultExpanded = index < 2; // 默认展开前两个分组
            
            // 添加分组标题（可点击展开/折叠）
            const chevronClass = isDefaultExpanded ? 'fa-chevron-down' : 'fa-chevron-right';
            const groupHeader = `
                <li class="nav-item">
                    <div class="knowledge-group-header d-flex align-items-center p-2 ${isDefaultExpanded ? '' : 'collapsed'}" 
                         onclick="toggleKnowledgeGroup('${groupId}')" 
                         ondblclick="filterByKnowledgeGroup('${group.groupName}')"
                         data-group-id="${groupId}"
                         title="单击展开/折叠，双击筛选该分组下所有题目">
                        <i class="fas ${chevronClass} group-toggle-icon me-2"></i>
                        <i class="fas fa-folder me-2"></i>
                        <span class="flex-grow-1">${group.groupName}</span>
                        <span class="knowledge-group-badge">${group.totalTopicCount}</span>
                    </div>
                </li>
            `;
            container.append(groupHeader);
            
            // 添加该分组下的知识点列表
            if (group.knowledgePoints && group.knowledgePoints.length > 0) {
                const sublistId = `sublist-${groupId}`;
                const sublistContainer = `
                    <li class="nav-item">
                        <ul class="knowledge-sublist nav flex-column ${isDefaultExpanded ? '' : 'collapsed'}" 
                            id="${sublistId}">
                        </ul>
                    </li>
                `;
                container.append(sublistContainer);
                
                const sublist = $(`#${sublistId}`);
                group.knowledgePoints.forEach(knowledge => {
                    const listItem = `
                        <li class="nav-item">
                            <a class="nav-link" href="#" 
                               onclick="filterByKnowledge(${knowledge.knowledgeId})" 
                               data-knowledge-id="${knowledge.knowledgeId}"
                               title="筛选${knowledge.knowledgeName}相关题目">
                                <i class="fas fa-bookmark me-2"></i>
                                ${knowledge.knowledgeName}
                                <span class="knowledge-item-badge">${knowledge.topicCount}</span>
                            </a>
                        </li>
                    `;
                    sublist.append(listItem);
                });
            }
        });
        
        // 检查是否需要滚动
        this.checkKnowledgeScrollNeeded();
    }

    /**
     * 加载知识点选项到筛选器（二级分类）
     */
    loadKnowledgeOptions(groups) {
        const select = $('#knowledgeFilter');
        select.empty().append('<option value="">所有知识点</option>');
        
        // 同时更新编辑表单中的知识点选择器
        const editSelect = $('#knowId');
        editSelect.empty().append('<option value="">请选择知识点</option>');
        
        if (groups && groups.length > 0) {
            groups.forEach(group => {
                // 筛选器中添加分组标题（不可选择）
                select.append(`<option disabled style="font-weight: bold; background-color: #f8f9fa;">── ${group.groupName} (${group.totalTopicCount}) ──</option>`);
                editSelect.append(`<option disabled style="font-weight: bold; background-color: #f8f9fa;">── ${group.groupName} ──</option>`);
                
                // 添加该分组下的知识点
                if (group.knowledgePoints && group.knowledgePoints.length > 0) {
                    group.knowledgePoints.forEach(knowledge => {
                        select.append(`<option value="${knowledge.knowledgeId}" style="padding-left: 20px;">　${knowledge.knowledgeName} (${knowledge.topicCount})</option>`);
                        editSelect.append(`<option value="${knowledge.knowledgeId}" style="padding-left: 20px;">　${knowledge.knowledgeName}</option>`);
                    });
                }
            });
        }
    }

    /**
     * 搜索题目
     */
    searchTopics() {
        this.searchTerm = $('#searchInput').val().trim();
        this.idsFilter = $('#idsInput').val().trim();  // 新增：获取ID筛选
        this.typeFilter = $('#typeFilter').val();
        this.knowledgeFilter = $('#knowledgeFilter').val();
        this.difficultyFilter = $('#difficultyFilter').val();
        this.statusFilter = $('#statusFilter').val();
        this.currentPage = 1;
        this.loadTopicList();
    }

    /**
     * 重置筛选条件
     */
    resetFilters() {
        $('#searchInput').val('');
        $('#idsInput').val('');  // 新增：重置ID输入
        $('#typeFilter').val('');
        $('#knowledgeFilter').val('');
        $('#difficultyFilter').val('');
        $('#statusFilter').val('');
        
        this.searchTerm = '';
        this.idsFilter = '';  // 新增
        this.typeFilter = '';
        this.knowledgeFilter = '';
        this.difficultyFilter = '';
        this.statusFilter = '';
        this.currentKnowledgeId = '';  // 新增：重置知识点选择
        this.currentPage = 1;
        
        // 重置知识点侧边栏选择状态
        this.resetKnowledgeGroupSelection();
        
        this.loadTopicList();
    }

    /**
     * 按知识点筛选
     */
    filterByKnowledge(knowledgeId) {
        this.currentKnowledgeId = knowledgeId;
        this.currentPage = 1;
        
        // 更新侧边栏选择状态
        $('#knowledgeGroupsList .nav-link').removeClass('active');
        if (knowledgeId) {
            const targetLink = $(`#knowledgeGroupsList .nav-link[data-knowledge-id="${knowledgeId}"]`);
            targetLink.addClass('active');
            
            // 确保包含该知识点的分组是展开状态
            const parentSublist = targetLink.closest('.knowledge-sublist');
            if (parentSublist.length > 0 && parentSublist.hasClass('collapsed')) {
                const groupId = parentSublist.attr('id').replace('sublist-', '');
                this.toggleKnowledgeGroup(groupId);
            }
        } else {
            // 选择"全部题目"
            $('#knowledgeGroupsList .nav-link').first().addClass('active');
        }
        
        // 同步更新筛选器
        $('#knowledgeFilter').val(knowledgeId);
        
        this.loadTopicList();
    }

    /**
     * 清除知识点筛选
     */
    clearKnowledgeFilter() {
        this.currentKnowledgeId = '';
        $('#knowledgeFilter').val('');
        this.resetKnowledgeGroupSelection();
        this.currentPage = 1;
        this.loadTopicList();
    }

    /**
     * 重置知识点分组选择状态
     */
    resetKnowledgeGroupSelection() {
        $('#knowledgeGroupsList .nav-link').removeClass('active');
        $('#knowledgeGroupsList .nav-link[data-knowledge-id=""]').addClass('active');
    }

    /**
     * 切换全选
     */
    toggleSelectAll() {
        const isChecked = $('#selectAll').prop('checked');
        $('.topic-checkbox').prop('checked', isChecked);
        this.updateBatchActionsVisibility();
    }

    /**
     * 更新批量操作按钮显示状态
     */
    updateBatchActionsVisibility() {
        const checkedCount = $('.topic-checkbox:checked').length;
        const totalCount = $('.topic-checkbox').length;
        
        // 更新全选按钮状态
        $('#selectAll').prop('checked', checkedCount === totalCount);
        $('#selectAll').prop('indeterminate', checkedCount > 0 && checkedCount < totalCount);
        
        // 更新选中数量显示
        $('#selectedCount').text(checkedCount);
        
        // 显示/隐藏批量操作
        if (checkedCount > 0) {
            $('#batchActions').show();
        } else {
            $('#batchActions').hide();
        }
    }

    /**
     * 批量导出题目
     */
    batchExportTopics() {
        const selectedIds = this.getSelectedTopicIds();
        if (selectedIds.length === 0) {
            this.showError('请选择要导出的题目');
            return;
        }
        
        // TODO: 实现批量导出逻辑
        console.log('批量导出题目:', selectedIds);
        this.showSuccess(`已选择 ${selectedIds.length} 个题目进行导出`);
    }

    /**
     * 批量移动题目
     */
    batchMoveTopics() {
        const selectedIds = this.getSelectedTopicIds();
        if (selectedIds.length === 0) {
            this.showError('请选择要移动的题目');
            return;
        }
        
        // TODO: 实现批量移动逻辑
        console.log('批量移动题目:', selectedIds);
        this.showSuccess(`已选择 ${selectedIds.length} 个题目进行移动`);
    }

    /**
     * 批量删除题目
     */
    batchDeleteTopics() {
        const selectedIds = this.getSelectedTopicIds();
        if (selectedIds.length === 0) {
            this.showError('请选择要删除的题目');
            return;
        }
        
        if (!confirm(`确定要删除选中的 ${selectedIds.length} 个题目吗？此操作不可恢复。`)) {
            return;
        }
        
        // TODO: 实现批量删除逻辑
        console.log('批量删除题目:', selectedIds);
        this.showSuccess(`已删除 ${selectedIds.length} 个题目`);
    }

    /**
     * 获取选中的题目ID
     */
    getSelectedTopicIds() {
        const selectedIds = [];
        $('.topic-checkbox:checked').each(function() {
            selectedIds.push($(this).val());
        });
        return selectedIds;
    }

    /**
     * 检查表格内容是否需要滚动
     */
    checkScrollContent() {
        setTimeout(() => {
            const scrollContainer = $('.table-scrollable');
            if (scrollContainer.length > 0) {
                // 强制设置滚动属性
                scrollContainer.css({
                    'overflow-y': 'auto',
                    'overflow-x': 'auto',
                    'max-height': '100%'
                });
                
                const container = scrollContainer[0];
                const hasVerticalScroll = container.scrollHeight > container.clientHeight;
                const hasHorizontalScroll = container.scrollWidth > container.clientWidth;
                
                // 添加滚动状态类 - 仅用于微妙的视觉提示
                scrollContainer
                    .toggleClass('has-vertical-scroll', hasVerticalScroll)
                    .toggleClass('has-horizontal-scroll', hasHorizontalScroll);
                
                // 确保表格有最小宽度
                const table = scrollContainer.find('table');
                if (table.length > 0) {
                    table.css('min-width', '1200px');
                }
                
                // 如果没有数据，确保容器仍然可以滚动
                if (scrollContainer.find('tbody tr').length <= 1) {
                    scrollContainer.css('min-height', '200px');
                }
            }
        }, 100);
    }

    /**
     * 切换知识点分组的展开/折叠状态
     */
    toggleKnowledgeGroup(groupId) {
        const groupHeader = $(`.knowledge-group-header[data-group-id="${groupId}"]`);
        const sublist = $(`#sublist-${groupId}`);
        const icon = groupHeader.find('.group-toggle-icon');
        
        if (groupHeader.hasClass('collapsed')) {
            // 展开
            groupHeader.removeClass('collapsed');
            sublist.removeClass('collapsed');
            icon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
        } else {
            // 折叠
            groupHeader.addClass('collapsed');
            sublist.addClass('collapsed');
            icon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
        }
        
        // 重新检查滚动状态
        setTimeout(() => this.checkKnowledgeScrollNeeded(), 100);
    }

    /**
     * 按知识点分组筛选（显示该分组下所有题目）
     */
    filterByKnowledgeGroup(groupName) {
        // 阻止事件冒泡，避免触发单击事件
        event.stopPropagation();
        
        // 清除当前知识点筛选，设置搜索关键词为分组名（这里可以根据实际需求调整逻辑）
        this.currentKnowledgeId = '';
        this.searchTerm = groupName;
        this.currentPage = 1;
        
        // 更新UI状态
        $('#knowledgeGroupsList .nav-link').removeClass('active');
        $('#searchInput').val(groupName);
        $('#knowledgeFilter').val('');
        
        // 高亮对应的分组头
        $('.knowledge-group-header').removeClass('active');
        $(event.target).closest('.knowledge-group-header').addClass('active');
        
        this.loadTopicList();
        
        // 显示提示
        this.showSuccess(`正在显示"${groupName}"分组下的所有题目`);
    }

    /**
     * 展开/折叠所有知识点分组
     */
    toggleAllKnowledgeGroups() {
        const collapsedGroups = $('.knowledge-group-header.collapsed');
        const expandedGroups = $('.knowledge-group-header:not(.collapsed)');
        
        if (collapsedGroups.length >= expandedGroups.length) {
            // 如果折叠的分组更多，则全部展开
            $('.knowledge-group-header.collapsed').each((index, element) => {
                const groupId = $(element).data('group-id');
                this.toggleKnowledgeGroup(groupId);
            });
        } else {
            // 如果展开的分组更多，则全部折叠
            $('.knowledge-group-header:not(.collapsed)').each((index, element) => {
                const groupId = $(element).data('group-id');
                this.toggleKnowledgeGroup(groupId);
            });
        }
        
        // 重新检查滚动状态
        setTimeout(() => this.checkKnowledgeScrollNeeded(), 100);
    }
    
    /**
     * 检查知识点容器是否需要滚动
     */
    checkKnowledgeScrollNeeded() {
        const container = $('.knowledge-filter-container');
        if (container.length > 0) {
            const hasScroll = container[0].scrollHeight > container[0].clientHeight;
            container.toggleClass('has-scroll', hasScroll);
        }
    }

    /**
     * 切换知识点筛选面板的展开状态
     */
    toggleKnowledgeFilterExpand() {
        const filterCard = $('.knowledge-filter-card');
        const mainContentCol = $('.main-content-col');
        const expandIcon = $('#expandIcon');
        
        if (filterCard.hasClass('fullscreen')) {
            // 从全屏模式恢复
            filterCard.removeClass('fullscreen expanded');
            mainContentCol.removeClass('d-none');
            expandIcon.removeClass('fa-compress').addClass('fa-expand');
            
            // 恢复布局
            filterCard.parent().removeClass('col-12').addClass('col-md-4');
            mainContentCol.removeClass('col-12').addClass('col-md-8');
            
        } else if (filterCard.hasClass('expanded')) {
            // 进入全屏模式
            filterCard.addClass('fullscreen');
            mainContentCol.addClass('d-none');
            expandIcon.removeClass('fa-expand').addClass('fa-compress');
            
            // 调整布局
            filterCard.parent().removeClass('col-md-4').addClass('col-12');
            
        } else {
            // 进入展开模式
            filterCard.addClass('expanded');
            expandIcon.removeClass('fa-expand').addClass('fa-expand-arrows-alt');
            
            // 调整布局：筛选面板占更多空间
            filterCard.parent().removeClass('col-md-4').addClass('col-md-6');
            mainContentCol.removeClass('col-md-8').addClass('col-md-6');
        }
        
        // 重新检查滚动状态
        setTimeout(() => this.checkKnowledgeScrollNeeded(), 100);
    }

    /**
     * 确保表格可以滚动
     */
    ensureTableScrollable() {
        const scrollableContainer = $('.table-scrollable');
        const table = scrollableContainer.find('table');
        
        if (scrollableContainer.length > 0 && table.length > 0) {
            // 强制设置容器样式
            scrollableContainer.css({
                'overflow-y': 'auto',
                'overflow-x': 'auto',
                'max-height': '100%',
                'position': 'relative'
            });
            
            // 设置表格最小宽度以确保可以水平滚动
            table.css({
                'min-width': '1200px',
                'width': '100%'
            });
            
            // 监听滚动事件以更新滚动提示
            scrollableContainer.off('scroll.tableScroll').on('scroll.tableScroll', () => {
                this.updateScrollIndicators();
            });
            
            // 初始化滚动指示器
            this.updateScrollIndicators();
        }
    }

    /**
     * 更新滚动指示器
     */
    updateScrollIndicators() {
        const scrollableContainer = $('.table-scrollable');
        if (scrollableContainer.length === 0) return;
        
        const container = scrollableContainer[0];
        const hasVerticalScroll = container.scrollHeight > container.clientHeight;
        const hasHorizontalScroll = container.scrollWidth > container.clientWidth;
        
                // 更新类名以显示滚动状态 - 仅用于微妙的视觉提示
        scrollableContainer
            .toggleClass('has-vertical-scroll', hasVerticalScroll)
            .toggleClass('has-horizontal-scroll', hasHorizontalScroll);
     }

     /**
      * 更新表格状态指示器
      */
     updateTableStatusIndicator(recordCount) {
         const indicator = $('#tableStatusIndicator');
         const statusText = $('#tableStatusText');
         
         // 只在特殊情况下显示指示器
         if (recordCount === 0) {
             statusText.html('<i class="fas fa-search"></i> 无数据');
             indicator.removeClass('d-none');
             
             // 无数据时显示稍长时间
             setTimeout(() => {
                 indicator.addClass('d-none');
             }, 5000);
         } else if (recordCount > 0 && recordCount < this.pageSize && this.currentPage === 1) {
             // 仅在第一页且数据量小于页面大小时显示"全部数据"提示
             statusText.html(`<i class="fas fa-check"></i> ${recordCount} 条`);
             indicator.removeClass('d-none');
             
             // 1.5秒后隐藏
             setTimeout(() => {
                 indicator.addClass('d-none');
             }, 1500);
         } else {
             // 其他情况不显示指示器，让用户专注于数据本身
             indicator.addClass('d-none');
         }
     }

     /**
      * 生成题目详情HTML
      */
     generateTopicDetailHtml(topic) {
         const typeNames = {
             'choice': '单选题',
             'multiple': '多选题',
             'judge': '判断题',
             'fill': '填空题',
             'short': '简答题'
         };

         const difficultyNames = {
             1: '简单',
             2: '中等',
             3: '困难'
         };

         const difficultyColors = {
             1: 'success',
             2: 'warning',
             3: 'danger'
         };

         const formatDate = (dateStr) => {
             if (!dateStr) return '未知';
             const date = new Date(dateStr);
             return date.toLocaleString('zh-CN');
         };

         return `
             <div class="topic-detail-view">
                 <!-- 基本信息 -->
                 <div class="row mb-4">
                     <div class="col-12">
                         <h4 class="text-primary mb-3">
                             <i class="fas fa-question-circle me-2"></i>${this.escapeHtml(topic.title || '未知标题')}
                         </h4>
                         <div class="row">
                             <div class="col-md-6">
                                 <p><strong>题目ID：</strong>${topic.id}</p>
                                 <p><strong>题目类型：</strong>
                                     <span class="badge bg-secondary">${typeNames[topic.type] || topic.type}</span>
                                 </p>
                                 <p><strong>知识点：</strong>${this.escapeHtml(topic.knowledgePointName || topic.knowId || '未分类')}</p>
                             </div>
                             <div class="col-md-6">
                                 <p><strong>难度：</strong>
                                     <span class="badge bg-${difficultyColors[topic.difficulty] || 'secondary'}">${difficultyNames[topic.difficulty] || topic.difficulty}</span>
                                 </p>
                                 <p><strong>分值：</strong>${topic.score || 3} 分</p>
                                 <p><strong>标签：</strong>${this.escapeHtml(topic.tags || '无')}</p>
                             </div>
                         </div>
                     </div>
                 </div>

                 <!-- 题目内容 -->
                 ${this.generateTopicContentHtml(topic)}

                 <!-- 答案和解析 -->
                 <div class="row mb-3">
                     <div class="col-md-6">
                         <div class="card border-success">
                             <div class="card-header bg-success text-white">
                                 <h6 class="mb-0"><i class="fas fa-check-circle me-2"></i>正确答案</h6>
                             </div>
                             <div class="card-body">
                                 ${this.formatAnswerDisplay(topic.answer, topic.type)}
                             </div>
                         </div>
                     </div>
                     <div class="col-md-6">
                         <div class="card border-info">
                             <div class="card-header bg-info text-white">
                                 <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>题目解析</h6>
                             </div>
                             <div class="card-body">
                                 ${this.escapeHtml(topic.parse || '暂无解析')}
                             </div>
                         </div>
                     </div>
                 </div>

                 <!-- 其他信息 -->
                 <div class="row">
                     <div class="col-12">
                         <div class="card border-light">
                             <div class="card-header bg-light">
                                 <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>其他信息</h6>
                             </div>
                             <div class="card-body">
                                 <div class="row">
                                     <div class="col-md-6">
                                         <p><strong>题目来源：</strong>${this.escapeHtml(topic.source || '无')}</p>
                                         <p><strong>创建时间：</strong>${formatDate(topic.createdAt)}</p>
                                     </div>
                                     <div class="col-md-6">
                                         <p><strong>更新时间：</strong>${formatDate(topic.updatedAt)}</p>
                                         <p><strong>状态：</strong><span class="badge bg-primary">正常</span></p>
                                     </div>
                                 </div>
                             </div>
                         </div>
                     </div>
                 </div>
             </div>
         `;
     }

     /**
      * 生成题目内容HTML
      */
     generateTopicContentHtml(topic) {
         if (topic.type === 'choice' || topic.type === 'multiple') {
             // 选择题显示选项
             const options = this.parseOptions(topic.options || topic.content);
             let optionsHtml = '<div class="options-list">';
             
             if (options && options.length > 0) {
                 options.forEach((option, index) => {
                     const letter = String.fromCharCode(65 + index); // A, B, C, D...
                     optionsHtml += `
                         <div class="option-item mb-2 p-2 border rounded">
                             <span class="option-label badge bg-primary me-2">${letter}</span>
                             <span class="option-text">${this.escapeHtml(option)}</span>
                         </div>
                     `;
                 });
             } else {
                 optionsHtml += '<p class="text-muted">选项信息缺失</p>';
             }
             
             optionsHtml += '</div>';
             return `
                 <div class="mb-4">
                     <h5 class="text-secondary mb-3"><i class="fas fa-list me-2"></i>题目选项</h5>
                     ${optionsHtml}
                 </div>
             `;
         } else {
             // 其他题型显示题目内容
             return `
                 <div class="mb-4">
                     <h5 class="text-secondary mb-3"><i class="fas fa-file-text me-2"></i>题目内容</h5>
                     <div class="p-3 border rounded bg-light">
                         ${this.escapeHtml(topic.content || topic.options || '内容缺失')}
                     </div>
                 </div>
             `;
         }
     }

         /**
     * 解析选项数据（支持字符串和对象数组格式）
     */
    parseOptions(optionsData) {
        if (!optionsData) return [];
        
        // 如果是数组格式（新格式：[{key: "A", name: "选项内容"}]）
        if (Array.isArray(optionsData)) {
            return optionsData.map(option => {
                if (typeof option === 'object' && option.name) {
                    return option.name;
                } else if (typeof option === 'string') {
                    return option;
                } else {
                    return String(option);
                }
            }).filter(opt => opt && opt.trim());
        }
        
        // 如果是字符串格式（旧格式，向后兼容）
        if (typeof optionsData === 'string') {
            const optionsStr = optionsData;
            let options = [];
            
            // 尝试解析JSON字符串
            try {
                const parsed = JSON.parse(optionsStr);
                if (Array.isArray(parsed)) {
                    return this.parseOptions(parsed); // 递归处理解析后的数组
                }
            } catch (e) {
                // 不是JSON，继续按字符串处理
            }
            
            // 尝试多种分隔符
            if (optionsStr.includes('|')) {
                options = optionsStr.split('|');
            } else if (optionsStr.includes('\n')) {
                options = optionsStr.split('\n');
            } else if (optionsStr.includes(';')) {
                options = optionsStr.split(';');
            } else {
                // 尝试按A. B. C. D.格式解析
                const matches = optionsStr.match(/[A-Z]\.\s*[^A-Z]*(?=[A-Z]\.|$)/g);
                if (matches) {
                    options = matches.map(opt => opt.replace(/^[A-Z]\.\s*/, '').trim());
                } else {
                    options = [optionsStr];
                }
            }
            
            return options.filter(opt => opt.trim()).map(opt => opt.trim());
        }
        
        // 其他情况，转为字符串处理
        return [String(optionsData)];
    }

     /**
      * 格式化答案显示
      */
     formatAnswerDisplay(answer, type) {
         if (!answer) return '<span class="text-muted">无答案</span>';
         
         if (type === 'choice') {
             return `<span class="badge bg-success fs-6">${this.escapeHtml(answer)}</span>`;
         } else if (type === 'multiple') {
             const answers = answer.split('').join(', ');
             return `<span class="badge bg-success fs-6">${this.escapeHtml(answers)}</span>`;
         } else if (type === 'judge') {
             const judgeText = answer === 'true' || answer === '是' ? '正确' : '错误';
             return `<span class="badge bg-success fs-6">${judgeText}</span>`;
         } else {
             return `<div class="text-break">${this.escapeHtml(answer)}</div>`;
         }
     }

     /**
      * 填充编辑表单
      */
     populateTopicForm(topic) {
         document.getElementById('topicId').value = topic.id || '';
         document.getElementById('title').value = topic.title || '';
         document.getElementById('type').value = topic.type || '';
         document.getElementById('knowId').value = topic.knowId || '';
         document.getElementById('parse').value = topic.parse || '';
         document.getElementById('score').value = topic.score || 3;
         document.getElementById('difficulty').value = topic.difficulty || 2;
         document.getElementById('tags').value = topic.tags || '';
         document.getElementById('source').value = topic.source || '';
         
         // 处理题目类型变化以显示相应的输入区域
         this.handleTypeChange(topic.type);
         
         // 如果是选择题，填充选项
         if (topic.type === 'choice' || topic.type === 'multiple') {
             this.populateOptions(topic.options || topic.content);
         }
         
         // 最后设置答案，因为答案输入框可能会在 handleTypeChange 中被重新创建
         setTimeout(() => {
             const answerElement = document.getElementById('answer');
             if (answerElement) {
                 answerElement.value = topic.answer || '';
             }
         }, 100);
     }

     /**
      * 清空表单
      */
     clearTopicForm() {
         document.getElementById('topicForm').reset();
         document.getElementById('topicId').value = '';
         document.getElementById('score').value = 3;
         document.getElementById('difficulty').value = 2;
         
         // 隐藏选项区域
         document.getElementById('optionsSection').style.display = 'none';
         document.getElementById('optionsContainer').innerHTML = '';
     }

     /**
      * 处理题目类型变化
      */
     handleTypeChange(type) {
         const optionsSection = document.getElementById('optionsSection');
         const answerContainer = document.getElementById('answerContainer');
         const answerHint = document.getElementById('answerHint');
         
         if (type === 'choice' || type === 'multiple') {
             // 显示选项区域
             optionsSection.style.display = 'block';
             
             // 更新答案提示
             if (type === 'choice') {
                 answerHint.textContent = '请输入正确答案的字母，如：A';
                 answerContainer.innerHTML = '<input type="text" class="form-control" id="answer" name="answer" required placeholder="如：A">';
             } else {
                 answerHint.textContent = '请输入正确答案的字母组合，如：ABC';
                 answerContainer.innerHTML = '<input type="text" class="form-control" id="answer" name="answer" required placeholder="如：ABC">';
             }
         } else {
             // 隐藏选项区域
             optionsSection.style.display = 'none';
             
             // 更新答案输入
             if (type === 'judge') {
                 answerHint.textContent = '请选择正确答案';
                 answerContainer.innerHTML = `
                     <select class="form-control" id="answer" name="answer" required>
                         <option value="">请选择</option>
                         <option value="true">正确</option>
                         <option value="false">错误</option>
                     </select>
                 `;
             } else if (type === 'fill') {
                 answerHint.textContent = '请输入填空答案，多个答案用分号分隔';
                 answerContainer.innerHTML = '<textarea class="form-control" id="answer" name="answer" required placeholder="答案1;答案2;答案3"></textarea>';
             } else if (type === 'short') {
                 answerHint.textContent = '请输入参考答案';
                 answerContainer.innerHTML = '<textarea class="form-control" id="answer" name="answer" rows="3" required placeholder="请输入参考答案"></textarea>';
             } else {
                 answerHint.textContent = '请输入正确答案';
                 answerContainer.innerHTML = '<input type="text" class="form-control" id="answer" name="answer" required>';
             }
         }
     }

         /**
     * 填充选项
     */
    populateOptions(optionsData) {
        const container = document.getElementById('optionsContainer');
        container.innerHTML = '';
        
        let options = [];
        
        // 处理新格式的选项数据
        if (Array.isArray(optionsData)) {
            options = optionsData.map(option => {
                if (typeof option === 'object' && option.name) {
                    return option.name;
                } else if (typeof option === 'string') {
                    return option;
                } else {
                    return String(option);
                }
            }).filter(opt => opt && opt.trim());
        } else {
            // 使用现有的解析方法处理字符串格式
            options = this.parseOptions(optionsData);
        }
        
        options.forEach((option, index) => {
            this.addOptionToForm(option, String.fromCharCode(65 + index));
        });
        
        // 如果没有选项，添加4个空选项
        if (options.length === 0) {
            for (let i = 0; i < 4; i++) {
                this.addOptionToForm('', String.fromCharCode(65 + i));
            }
        }
    }

     /**
      * 添加选项到表单
      */
     addOptionToForm(text, label) {
         const container = document.getElementById('optionsContainer');
         const optionDiv = document.createElement('div');
         optionDiv.className = 'input-group mb-2';
         optionDiv.innerHTML = `
             <span class="input-group-text">${label}</span>
             <input type="text" class="form-control option-input" value="${this.escapeHtml(text)}" placeholder="请输入选项内容">
             <button type="button" class="btn btn-outline-danger" onclick="this.parentElement.remove()">
                 <i class="fas fa-times"></i>
             </button>
         `;
         container.appendChild(optionDiv);
     }

     /**
      * HTML转义
      */
     escapeHtml(text) {
         if (!text) return '';
         const div = document.createElement('div');
         div.textContent = text;
         return div.innerHTML;
     }
 }

// 全局函数，供HTML调用
function showAddTopicModal() {
    window.topicManagement.showAddTopicModal();
}

function showImportModal() {
    window.topicManagement.showImportModal();
}

function exportTopics() {
    window.topicManagement.exportTopics();
}

function downloadTemplate() {
    window.topicManagement.downloadTemplate();
}

function searchTopics() {
    window.topicManagement.searchTopics();
}

function resetFilters() {
    window.topicManagement.resetFilters();
}

function filterByKnowledge(knowledgeId) {
    window.topicManagement.filterByKnowledge(knowledgeId);
}

function clearKnowledgeFilter() {
    window.topicManagement.clearKnowledgeFilter();
}

function batchExportTopics() {
    window.topicManagement.batchExportTopics();
}

function batchMoveTopics() {
    window.topicManagement.batchMoveTopics();
}

function batchDeleteTopics() {
    window.topicManagement.batchDeleteTopics();
}

function toggleSelectAll() {
    window.topicManagement.toggleSelectAll();
}

function toggleKnowledgeGroup(groupId) {
    window.topicManagement.toggleKnowledgeGroup(groupId);
}

function filterByKnowledgeGroup(groupName) {
    window.topicManagement.filterByKnowledgeGroup(groupName);
}

function toggleAllKnowledgeGroups() {
    window.topicManagement.toggleAllKnowledgeGroups();
}

function toggleKnowledgeFilterExpand() {
    window.topicManagement.toggleKnowledgeFilterExpand();
}

// 题目表单相关函数
function handleTypeChange() {
    const type = document.getElementById('type').value;
    window.topicManagement.handleTypeChange(type);
}

function addOption() {
    const container = document.getElementById('optionsContainer');
    const currentOptions = container.querySelectorAll('.option-input').length;
    const label = String.fromCharCode(65 + currentOptions); // A, B, C, D...
    window.topicManagement.addOptionToForm('', label);
}

async function saveTopic() {
    const form = document.getElementById('topicForm');
    const formData = new FormData(form);
    
    // 验证必填字段
    if (!formData.get('title').trim()) {
        window.topicManagement.showError('请输入题目标题');
        return;
    }
    
    if (!formData.get('type')) {
        window.topicManagement.showError('请选择题目类型');
        return;
    }
    
    if (!formData.get('knowId')) {
        window.topicManagement.showError('请选择知识点');
        return;
    }
    
    if (!formData.get('answer').trim()) {
        window.topicManagement.showError('请输入正确答案');
        return;
    }
    
    // 收集选项数据（如果是选择题）
    const type = formData.get('type');
    if (type === 'choice' || type === 'multiple') {
        const options = [];
        const optionInputs = document.querySelectorAll('.option-input');
        let hasValidOption = false;
        
        optionInputs.forEach((input, index) => {
            const value = input.value.trim();
            if (value) {
                // 创建符合后端期望的选项对象格式
                options.push({
                    key: String.fromCharCode(65 + index), // A, B, C, D...
                    name: value
                });
                hasValidOption = true;
            }
        });
        
        if (!hasValidOption) {
            window.topicManagement.showError('请至少输入一个选项');
            return;
        }
        
        // 将选项数组设置为JSON字符串
        formData.set('options', JSON.stringify(options));
    }
    
    // 构建请求数据
    const topicData = {
        title: formData.get('title'),
        type: formData.get('type'),
        knowId: parseInt(formData.get('knowId')),
        answer: formData.get('answer'),
        parse: formData.get('parse') || '',
        score: parseInt(formData.get('score')) || 3,
        difficulty: parseInt(formData.get('difficulty')) || 2,
        tags: formData.get('tags') || '',
        source: formData.get('source') || ''
    };
    
    // 添加选项数据
    if (formData.get('options')) {
        topicData.options = formData.get('options');
    }
    
    try {
        let response;
        const isEdit = window.topicManagement.currentEditMode === 'edit';
        
        if (isEdit) {
            // 编辑题目
            topicData.id = window.topicManagement.currentTopicId;
            response = await window.topicManagement.fetchAPI(`/api/admin/topics/${topicData.id}`, {
                method: 'PUT',
                body: topicData
            });
        } else {
            // 添加题目
            response = await window.topicManagement.fetchAPI('/api/admin/topics', {
                method: 'POST',
                body: topicData
            });
        }
        
        if (response && response.code === 200) {
            window.topicManagement.showSuccess(isEdit ? '题目更新成功' : '题目添加成功');
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('topicModal'));
            modal.hide();
            
            // 刷新数据
            window.topicManagement.loadTopicList();
            window.topicManagement.loadTopicStats();
        } else {
            window.topicManagement.showError(response.message || (isEdit ? '更新题目失败' : '添加题目失败'));
        }
    } catch (error) {
        console.error('保存题目失败:', error);
        window.topicManagement.showError('保存题目失败: ' + error.message);
    }
}

// 页面加载完成后初始化
$(document).ready(function() {
    window.topicManagement = new TopicManagement();
});

// 题目详情相关函数
function printTopic() {
    window.print();
}

function editCurrentTopic() {
    if (window.topicManagement.currentViewingTopic) {
        // 关闭详情模态框
        const detailModal = bootstrap.Modal.getInstance(document.getElementById('topicDetailModal'));
        detailModal.hide();
        
        // 打开编辑模态框
        setTimeout(() => {
            window.topicManagement.showTopicEditModal(window.topicManagement.currentViewingTopic);
        }, 300);
    }
} 