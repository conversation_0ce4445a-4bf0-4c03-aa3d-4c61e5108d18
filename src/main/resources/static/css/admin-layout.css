/* 管理员布局样式 */

/* 侧边栏基础样式 */
.sidebar {
    position: fixed;
    top: 56px; /* 导航栏高度 */
    bottom: 0;
    left: 0;
    z-index: 1020;
    background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
    border-right: 1px solid #dee2e6;
    box-shadow: 2px 0 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    width: 250px;
    overflow-x: hidden;
    overflow-y: auto;
}

/* 侧边栏折叠状态 */
.sidebar.collapsed {
    width: 60px;
}

/* 主要内容区域调整 */
.main-content {
    margin-left: 250px;
    transition: all 0.3s ease;
    padding-top: 56px; /* 导航栏高度 */
}

.sidebar.collapsed + .main-content,
.sidebar.collapsed ~ .main-content {
    margin-left: 60px;
}

/* 导航链接样式 */
.sidebar .nav-link {
    color: #495057;
    padding: 0.75rem 1rem;
    margin: 0.125rem 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    white-space: nowrap;
    display: flex;
    align-items: center;
}

.sidebar .nav-link:hover {
    background-color: #e9ecef;
    color: #0d6efd;
    border-left-color: #0d6efd;
    transform: translateX(2px);
    text-decoration: none;
}

.sidebar .nav-link.active {
    background-color: #0d6efd;
    color: white;
    border-left-color: #0a58ca;
    box-shadow: 0 2px 4px rgba(13, 110, 253, 0.3);
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
    margin-right: 0.5rem;
    flex-shrink: 0;
}

/* 折叠状态下的样式 */
.sidebar.collapsed .nav-link {
    padding: 0.75rem 0.5rem;
    text-align: center;
    justify-content: center;
}

.sidebar.collapsed .nav-link i {
    margin-right: 0;
}

.sidebar.collapsed .nav-link-text,
.sidebar.collapsed .sidebar-divider,
.sidebar.collapsed .sidebar-heading {
    opacity: 0;
    visibility: hidden;
}

.nav-link-text {
    transition: all 0.3s ease;
}

/* 折叠按钮样式 */
.sidebar-toggle {
    position: absolute;
    top: 10px;
    right: -15px;
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: #f8f9fa;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.sidebar-toggle i {
    color: #495057;
    font-size: 12px;
    transition: transform 0.3s ease;
}

.sidebar.collapsed .sidebar-toggle i {
    transform: rotate(180deg);
}

/* 分隔线样式 */
.sidebar-divider {
    border-color: #dee2e6;
    margin: 1rem 0;
    transition: all 0.3s ease;
}

/* Tooltip样式（折叠状态） */
.sidebar.collapsed .nav-item {
    position: relative;
}

.sidebar.collapsed .nav-link::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    margin-left: 10px;
    z-index: 1050;
    pointer-events: none;
}

.sidebar.collapsed .nav-link:hover::after {
    opacity: 1;
    visibility: visible;
}

/* 移动端样式 */
@media (max-width: 767.98px) {
    .sidebar {
        width: 280px;
        position: fixed;
        height: 100vh;
        z-index: 1040;
        transform: translateX(-100%);
        top: 0;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .sidebar-toggle {
        display: none;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .sidebar.collapsed + .main-content,
    .sidebar.collapsed ~ .main-content {
        margin-left: 0;
    }
}

/* 移动端遮罩 */
.mobile-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1039;
    display: none;
}

/* 顶部导航栏样式 */
.navbar {
    z-index: 1030;
}

.navbar-brand .brand-text {
    font-weight: 600;
}

/* 页面内容区域 */
.page-content {
    padding: 1rem;
    min-height: calc(100vh - 56px);
}

/* 统计卡片样式 */
.card.border-left-primary {
    border-left: 4px solid #007bff !important;
}

.card.border-left-success {
    border-left: 4px solid #28a745 !important;
}

.card.border-left-info {
    border-left: 4px solid #17a2b8 !important;
}

.card.border-left-warning {
    border-left: 4px solid #ffc107 !important;
}

.card.border-left-danger {
    border-left: 4px solid #dc3545 !important;
}

.card.border-left-secondary {
    border-left: 4px solid #6c757d !important;
}

.card.border-left-dark {
    border-left: 4px solid #343a40 !important;
}

/* 表格样式优化 */
.table-responsive {
    border-radius: 0.375rem;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 按钮组样式 */
.btn-group .btn {
    margin-right: 0;
}

.btn-group .btn:not(:last-child) {
    border-right: none;
}

/* 搜索框样式 */
.input-group .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 分页样式 */
.pagination .page-link {
    color: #0d6efd;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* 模态框样式 */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    color: #495057;
    font-weight: 600;
}

/* 响应式调整 */
@media (min-width: 768px) {
    .main-content {
        padding: 2rem;
    }
}

@media (min-width: 992px) {
    .main-content {
        padding: 2rem 3rem;
    }
}

/* 知识点侧边栏样式 */
.knowledge-sidebar {
    background-color: #f8f9fa;
    border-right: 1px solid #dee2e6;
    padding-top: 1rem;
}

.knowledge-sidebar .nav-link {
    color: #6c757d;
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 0;
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
}

.knowledge-sidebar .nav-link:hover {
    background-color: #e9ecef;
    color: #495057;
    border-left-color: #0d6efd;
}

.knowledge-sidebar .nav-link.active {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
    border-left-color: #0d6efd;
    font-weight: 500;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
} 