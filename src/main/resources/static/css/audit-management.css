/**
 * 审核管理页面高级搜索功能样式
 */

/* 高级搜索卡片样式 */
.advanced-search-card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s ease;
}

.advanced-search-card:hover {
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

/* 搜索表单样式 */
.search-form-row {
    margin-bottom: 1rem;
}

.search-form-row .form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.search-form-row .form-select,
.search-form-row .form-control {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    font-size: 0.85rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.search-form-row .form-select:focus,
.search-form-row .form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* 搜索按钮组样式 */
.search-button-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.search-button-group .btn {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
    border-radius: 0.35rem;
    transition: all 0.2s ease;
}

.search-button-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.2rem 0.5rem rgba(0, 0, 0, 0.15);
}

/* 快捷筛选标签样式 */
.quick-filter-tags {
    padding: 1rem 0;
    border-top: 1px solid #e3e6f0;
    margin-top: 1rem;
}

.quick-filter-tags .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    margin: 0.25rem;
    border-radius: 1rem;
    transition: all 0.2s ease;
}

.quick-filter-tags .btn:hover {
    transform: scale(1.05);
}

.quick-filter-tags .btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
}

.quick-filter-tags .btn-outline-info:hover {
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.quick-filter-tags .btn-outline-success:hover {
    background-color: #28a745;
    border-color: #28a745;
}

.quick-filter-tags .btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
}

/* 搜索结果统计样式 */
.search-result-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    border-radius: 0.5rem;
    margin-top: 1rem;
    padding: 1rem;
}

.search-result-stats .alert-info {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
}

.search-result-stats .btn-outline-primary {
    border-color: white;
    color: white;
}

.search-result-stats .btn-outline-primary:hover {
    background-color: white;
    color: #667eea;
}

/* 自定义时间范围样式 */
.custom-date-range {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 搜索输入框增强样式 */
.enhanced-search-input {
    position: relative;
}

.enhanced-search-input .search-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    pointer-events: none;
}

.enhanced-search-input .form-control {
    padding-right: 2.5rem;
}

/* 视图模式切换样式 */
.view-mode-selector .form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
}

/* 搜索卡片响应式样式 */
@media (max-width: 768px) {
    .search-form-row {
        margin-bottom: 0.75rem;
    }
    
    .search-button-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-button-group .btn {
        margin-bottom: 0.25rem;
    }
    
    .quick-filter-tags {
        text-align: center;
    }
    
    .quick-filter-tags .btn {
        display: block;
        width: 100%;
        margin: 0.25rem 0;
    }
}

/* 搜索结果视图样式 */
.search-results-container {
    margin-top: 1.5rem;
}

/* 列表视图样式 */
.list-view-container .table {
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.list-view-container .table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.list-view-container .table tbody tr {
    transition: all 0.2s ease;
}

.list-view-container .table tbody tr:hover {
    background-color: #f8f9fc;
    transform: translateX(2px);
}

/* 卡片视图样式 */
.card-view-container .audit-card {
    transition: all 0.3s ease;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
}

.card-view-container .audit-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-color: #4e73df;
}

.card-view-container .audit-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.card-view-container .audit-card .card-footer {
    background-color: #f8f9fc;
    border-top: 1px solid #e3e6f0;
}

/* 分组视图样式 */
.group-view-container .group-card {
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.group-view-container .group-card:hover {
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
    border-color: #4e73df;
}

.group-view-container .group-header {
    background: linear-gradient(135deg, #36b9cc 0%, #3498db 100%);
    color: white;
    padding: 1rem;
    border-radius: 0.5rem 0.5rem 0 0;
}

/* 加载动画样式 */
.search-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fc 0%, #e9ecef 100%);
    border-radius: 0.5rem;
}

.search-loading .spinner {
    width: 2rem;
    height: 2rem;
    border: 3px solid #e3e6f0;
    border-top: 3px solid #4e73df;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-search-state {
    text-align: center;
    padding: 3rem 1rem;
    background: linear-gradient(135deg, #f8f9fc 0%, #e9ecef 100%);
    border-radius: 0.5rem;
    margin: 1rem 0;
}

.empty-search-state .empty-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.empty-search-state .empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.empty-search-state .empty-description {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

/* 筛选条件徽章样式 */
.filter-badges {
    margin: 1rem 0;
    padding: 1rem;
    background-color: #f8f9fc;
    border-radius: 0.5rem;
    border: 1px solid #e3e6f0;
}

.filter-badge {
    display: inline-block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    margin: 0.25rem;
    font-size: 0.75rem;
    position: relative;
}

.filter-badge .remove-filter {
    margin-left: 0.5rem;
    cursor: pointer;
    font-weight: bold;
}

.filter-badge .remove-filter:hover {
    color: #ff6b6b;
}

/* 搜索建议样式 */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e3e6f0;
    border-top: none;
    border-radius: 0 0 0.35rem 0.35rem;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.search-suggestion-item {
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fc;
    transition: background-color 0.2s ease;
}

.search-suggestion-item:hover {
    background-color: #f8f9fc;
}

.search-suggestion-item:last-child {
    border-bottom: none;
}

/* 导出按钮样式 */
.export-button {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.export-button:hover {
    background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(40, 167, 69, 0.3);
}

/* 响应式适配 */
@media (max-width: 576px) {
    .advanced-search-card .card-body {
        padding: 1rem 0.75rem;
    }
    
    .search-form-row .col-md-2,
    .search-form-row .col-md-3,
    .search-form-row .col-md-4 {
        margin-bottom: 1rem;
    }
    
    .search-button-group {
        flex-wrap: wrap;
        justify-content: center;
    }
} 