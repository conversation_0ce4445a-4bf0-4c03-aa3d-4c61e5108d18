:root {
    --primary-color: #0066FF;
    --text-color: #1A1A1A;
    --secondary-text: #666666;
    --border-color: #E5E5E5;
    --background-color: #F5F5F5;
    --hover-color: #0052CC;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.5;
}

.page-container {
    display: flex;
    min-height: 100vh;
}

.left-section {
    flex: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.left-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: pulse 4s ease-in-out infinite;
}

.brand {
    text-align: center;
}

.brand h1 {
    font-size: 3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.brand .slogan {
    font-size: 1.2rem;
    font-weight: 300;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

.right-section {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    background: white;
}

.form-container {
    width: 100%;
    max-width: 460px;
    padding: 2rem;
}

/* 美化的登录卡片边框 */
.login-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    animation: slideInUp 0.6s ease-out;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 20px 20px 0 0;
}

.form-header {
    margin-bottom: 2rem;
    text-align: center;
}

.form-header h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.form-header .subtitle {
    color: #666;
    font-size: 0.9rem;
}

.input-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.input-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

/* 带图标的输入框 */
.input-with-icon {
    position: relative;
}

.input-with-icon i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 1rem;
    z-index: 2;
}

/* 密码显示/隐藏图标 */
.password-input {
    position: relative;
}

.password-toggle {
    position: absolute !important;
    right: 1rem !important;
    left: auto !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    color: #999 !important;
    font-size: 1rem !important;
    cursor: pointer !important;
    z-index: 3 !important;
    transition: all 0.3s ease !important;
    padding: 0.25rem !important;
    border-radius: 4px !important;
}

.password-toggle:hover {
    color: #667eea !important;
    background-color: rgba(102, 126, 234, 0.1) !important;
}

.password-toggle.fa-eye-slash {
    color: #667eea !important;
}

/* 密码输入框右侧留出空间给图标 */
.password-input input {
    padding-right: 3rem !important;
}

.input-group input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 2.5rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fff;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-group input:focus + i {
    color: #667eea;
}

.forgot-password {
    color: #667eea;
    font-size: 0.85rem;
    text-decoration: none;
    cursor: pointer;
    transition: color 0.3s ease;
    display: inline-block;
    margin-top: 0.5rem;
}

.forgot-password:hover {
    color: #5a67d8;
    text-decoration: underline;
}

.submit-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.submit-btn:active {
    transform: translateY(0);
}

.submit-btn.secondary {
    background: #e2e8f0;
    color: #666;
    box-shadow: none;
}

.submit-btn.secondary:hover {
    background: #cbd5e0;
}

.switch-form {
    margin-top: 1.5rem;
    text-align: center;
    color: #666;
    font-size: 0.9rem;
}

.switch-form a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.switch-form a:hover {
    text-decoration: underline;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInUp {
    from { 
        opacity: 0; 
        transform: translateY(30px) scale(0.95); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0) scale(1); 
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.5; }
    50% { transform: scale(1.05); opacity: 0.8; }
}

form {
    animation: fadeIn 0.3s ease-out;
}

.login-card {
    animation: slideInUp 0.5s ease-out;
}

.submit-btn:hover {
    animation: pulse 0.3s ease-in-out;
}

/* 输入框焦点动画 */
.input-group {
    position: relative;
}

.input-group::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #0066FF, #00A3FF);
    transform: scaleX(0);
    transition: transform 0.3s ease;
    border-radius: 0 0 8px 8px;
}

.input-group:focus-within::after {
    transform: scaleX(1);
}

/* 验证码容器 */
.captcha-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.captcha-container .input-with-icon {
    flex: 1;
}

.captcha-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

#captchaCanvas {
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: #f8f9fa;
    cursor: pointer;
    height: 40px;
}

.refresh-btn {
    background: #667eea;
    border: none;
    border-radius: 8px;
    color: white;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.refresh-btn:hover {
    background: #5a67d8;
    transform: rotate(180deg);
}

.refresh-btn:active {
    transform: scale(0.95);
}

/* 验证码输入框样式调整 */
.captcha-container .input-group input {
    padding-left: 2.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-container {
        flex-direction: column;
    }
    
    .left-section {
        padding: 1rem;
        min-height: auto;
    }
    
    .brand h1 {
        font-size: 2rem;
    }
    
    .brand .slogan {
        font-size: 1rem;
    }
    
    .right-section {
        padding: 1rem;
    }
    
    .login-card {
        padding: 1.5rem;
    }
    
    .captcha-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .captcha-display {
        justify-content: center;
        margin-top: 0.5rem;
    }
    
    .modal-content {
        width: 95%;
        margin: 5% auto;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --text-color: #FFFFFF;
        --secondary-text: #A0A0A0;
        --border-color: #333333;
        --background-color: #1A1A1A;
    }
    
    .right-section {
        background: #222222;
    }
    
    .input-group input {
        background: #333333;
        color: white;
    }
}

/* Toast 提示样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    background: white;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 4px 16px rgba(0, 0, 0, 0.08);
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    border: 1px solid rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.toast.show {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.toast-success {
    background: linear-gradient(135deg, #38a169, #48bb78);
    color: white;
    border-color: rgba(56, 161, 105, 0.3);
}

.toast-error {
    background: linear-gradient(135deg, #e53e3e, #f56565);
    color: white;
    border-color: rgba(229, 62, 62, 0.3);
}

.toast-info {
    background: linear-gradient(135deg, #3182ce, #4299e1);
    color: white;
    border-color: rgba(50, 126, 225, 0.3);
}

/* 验证消息样式 */
.validation-message {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    padding: 0.25rem 0;
    border-radius: 4px;
    animation: fadeIn 0.2s ease-out;
}

.validation-error {
    color: #EF4444;
}

.validation-success {
    color: #10B981;
}

/* 找回密码模态框 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    animation: slideInUp 0.3s ease-out;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
}

.close {
    color: #999;
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #666;
}

.modal-body {
    padding: 2rem;
}

.forgot-step {
    display: none;
}

.forgot-step.active {
    display: block;
}

.forgot-step p {
    margin-bottom: 1.5rem;
    color: #666;
    line-height: 1.6;
}

.success-message {
    text-align: center;
    padding: 2rem 1rem;
}

.success-message i {
    font-size: 3rem;
    color: #38a169;
    margin-bottom: 1rem;
}

.success-message h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.success-message p {
    color: #666;
    margin-bottom: 1rem;
}

.success-message .note {
    font-size: 0.85rem;
    color: #999;
    font-style: italic;
}