/* 历史试卷管理页面样式 */

/* 页面容器 */
.paper-history-container {
    padding: 20px;
    background-color: #f8f9fa;
    min-height: 100vh;
}

/* 页面标题 */
.page-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 30px;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
}

/* 统计卡片区域 */
.stats-cards {
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 15px;
    color: white;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0,0,0,0.15);
}

.stat-card.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.stat-card.info {
    background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
}

.stat-card.warning {
    background: linear-gradient(135deg, #f39c12 0%, #f7dc6f 100%);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.stat-icon {
    font-size: 3rem;
    opacity: 0.3;
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
}

/* 搜索和筛选区域 */
.search-filter-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.search-input {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 20px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.filter-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.filter-select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.btn-search {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border: none;
    border-radius: 10px;
    padding: 12px 25px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-search:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.3);
    color: white;
}

.btn-reset {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    border: none;
    border-radius: 10px;
    padding: 12px 25px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-reset:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(149, 165, 166, 0.3);
    color: white;
}

/* 批量操作工具栏 */
.batch-toolbar {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    color: white;
    display: none;
    animation: slideDown 0.3s ease;
}

.batch-toolbar.show {
    display: block;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.batch-info {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.batch-actions .btn {
    margin-right: 10px;
    margin-bottom: 10px;
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.batch-actions .btn:hover {
    transform: translateY(-2px);
}

.btn-batch-download {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    border: none;
    color: white;
}

.btn-batch-download:hover {
    box-shadow: 0 6px 20px rgba(39, 174, 96, 0.3);
    color: white;
}

.btn-batch-edit {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    border: none;
    color: white;
}

.btn-batch-edit:hover {
    box-shadow: 0 6px 20px rgba(243, 156, 18, 0.3);
    color: white;
}

.btn-batch-delete {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border: none;
    color: white;
}

.btn-batch-delete:hover {
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.3);
    color: white;
}

.btn-batch-export {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    border: none;
    color: white;
}

.btn-batch-export:hover {
    box-shadow: 0 6px 20px rgba(155, 89, 182, 0.3);
    color: white;
}

.btn-cancel-selection {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    border: none;
    color: white;
}

.btn-cancel-selection:hover {
    box-shadow: 0 6px 20px rgba(149, 165, 166, 0.3);
    color: white;
}

/* 试卷列表区域 */
.papers-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.section-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

/* 试卷卡片 */
.paper-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.paper-card:hover {
    border-color: #3498db;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.paper-card.selected {
    border-color: #e74c3c;
    background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
}

.paper-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.paper-card:hover::before {
    opacity: 1;
}

.paper-card.selected::before {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    opacity: 1;
}

.paper-checkbox {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.paper-title {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.2rem;
    margin-bottom: 10px;
    padding-right: 40px;
}

.paper-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.paper-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.paper-description {
    color: #5a6c7d;
    margin-bottom: 15px;
    line-height: 1.5;
}

.paper-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.badge-difficulty {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
}

.badge-status {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
}

.badge-status.draft {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
}

.badge-status.archived {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.paper-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.paper-actions .btn {
    padding: 8px 15px;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.paper-actions .btn:hover {
    transform: translateY(-2px);
}

.btn-preview {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border: none;
    color: white;
}

.btn-preview:hover {
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    color: white;
}

.btn-download {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    border: none;
    color: white;
}

.btn-download:hover {
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
    color: white;
}

.btn-edit {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    border: none;
    color: white;
}

.btn-edit:hover {
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
    color: white;
}

.btn-clone {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    border: none;
    color: white;
}

.btn-clone:hover {
    box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
    color: white;
}

.btn-delete {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border: none;
    color: white;
}

.btn-delete:hover {
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
    color: white;
}

/* 分页 */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}

.pagination .page-link {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    margin: 0 3px;
    padding: 10px 15px;
    color: #3498db;
    font-weight: 600;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border-color: #3498db;
    color: white;
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border-color: #3498db;
    color: white;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
}

.modal-header {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 20px 25px;
}

.modal-title {
    font-weight: 600;
    margin: 0;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* 加载状态 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #e9ecef;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h4 {
    color: #5a6c7d;
    margin-bottom: 10px;
}

.empty-state p {
    font-size: 1.1rem;
    margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .paper-history-container {
        padding: 15px;
    }
    
    .search-filter-section {
        padding: 20px;
    }
    
    .paper-card {
        padding: 15px;
    }
    
    .paper-meta {
        flex-direction: column;
        gap: 8px;
    }
    
    .paper-actions {
        justify-content: center;
    }
    
    .batch-actions {
        text-align: center;
    }
    
    .batch-actions .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .stat-icon {
        font-size: 2.5rem;
    }
}

@media (max-width: 576px) {
    .page-title {
        font-size: 1.5rem;
    }
    
    .paper-title {
        font-size: 1.1rem;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .paper-actions .btn {
        font-size: 0.8rem;
        padding: 6px 12px;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 提示信息 */
.alert {
    border: none;
    border-radius: 10px;
    padding: 15px 20px;
    margin-bottom: 20px;
    font-weight: 600;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}