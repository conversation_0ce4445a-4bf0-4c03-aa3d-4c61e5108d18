/**
 * 头像样式文件
 * 为各种头像状态提供统一的视觉效果
 */

/* 基础头像样式 */
.avatar, .user-avatar, .profile-avatar {
    object-fit: cover;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

/* 头像悬停效果 */
.avatar:hover, .user-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 大尺寸头像（个人资料页面） */
.profile-avatar {
    width: 120px;
    height: 120px;
    border: 3px solid #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 中等尺寸头像（用户列表、卡片） */
.avatar-medium {
    width: 48px;
    height: 48px;
}

/* 小尺寸头像（导航栏、评论） */
.avatar-small {
    width: 32px;
    height: 32px;
}

/* 超小尺寸头像（标签、徽章） */
.avatar-xs {
    width: 24px;
    height: 24px;
    border-width: 1px;
}

/* 头像容器 */
.avatar-container {
    position: relative;
    display: inline-block;
}

/* 头像上传按钮覆盖层 */
.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
    cursor: pointer;
    font-size: 12px;
}

.avatar-container:hover .avatar-overlay {
    opacity: 1;
}

/* 用户状态指示器 */
.avatar-status {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #ffffff;
}

.avatar-status.online {
    background-color: #28a745;
}

.avatar-status.offline {
    background-color: #6c757d;
}

.avatar-status.away {
    background-color: #ffc107;
}

.avatar-status.busy {
    background-color: #dc3545;
}

/* 头像组（多个头像堆叠显示） */
.avatar-group {
    display: flex;
    align-items: center;
}

.avatar-group .avatar {
    margin-left: -8px;
    border: 2px solid #ffffff;
    z-index: 1;
}

.avatar-group .avatar:first-child {
    margin-left: 0;
}

.avatar-group .avatar:hover {
    z-index: 2;
}

/* 头像加载状态 */
.avatar-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 头像错误状态 */
.avatar-error {
    background-color: #f8f9fa;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    border: 2px dashed #dee2e6;
}

/* 无头像状态的首字母头像样式 */
.avatar-initials {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: bold;
    text-align: center;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 响应式头像 */
@media (max-width: 768px) {
    .profile-avatar {
        width: 80px;
        height: 80px;
    }
    
    .avatar-medium {
        width: 40px;
        height: 40px;
    }
    
    .avatar-small {
        width: 28px;
        height: 28px;
    }
}

/* 头像上传区域样式 */
.avatar-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.avatar-upload-area:hover {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.avatar-upload-area.drag-over {
    border-color: #007bff;
    background-color: #e3f2fd;
}

/* 头像预览 */
.avatar-preview {
    position: relative;
    display: inline-block;
    margin-bottom: 10px;
}

.avatar-preview img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 头像操作按钮 */
.avatar-actions {
    margin-top: 10px;
}

.avatar-actions .btn {
    margin: 0 5px;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
    .avatar, .user-avatar, .profile-avatar {
        border-color: #495057;
        background-color: #343a40;
    }
    
    .avatar-upload-area {
        border-color: #495057;
        background-color: #343a40;
    }
    
    .avatar-upload-area:hover {
        border-color: #0d6efd;
        background-color: #1e2125;
    }
} 