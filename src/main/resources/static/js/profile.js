// 设置头像显示的函数 - 使用新的头像处理逻辑
function updateAvatarDisplay(avatarPath, userInfo = {}) {
    console.log('更新头像显示:', avatarPath);
    
    if (window.AvatarUtils) {
        // 使用新的AvatarUtils处理头像
        const avatarElements = document.querySelectorAll('.avatar, .large-avatar, #userAvatar, .profile-avatar');
        
        avatarElements.forEach(img => {
            window.AvatarUtils.setAvatarElement(img, avatarPath, userInfo, {
                showInitials: true,        // 显示首字母头像
                allowRandomAvatar: false,  // 不允许随机头像
                showPlaceholder: false,    // 不显示占位符
                hideIfNoAvatar: false      // 不隐藏元素
            });
        });
    } else {
        // 降级处理
        console.warn('AvatarUtils不可用，使用降级处理');
        const avatarUrl = getAvatarUrl(avatarPath, userInfo);
        document.querySelectorAll('.avatar, .large-avatar, #userAvatar, .profile-avatar').forEach(img => {
            img.src = avatarUrl;
            img.onerror = function() {
                // 生成简单的首字母头像
                this.src = generateFallbackAvatar(userInfo.username || 'U');
                console.log('Avatar load failed, using initials avatar');
            };
        });
    }
}

document.addEventListener('DOMContentLoaded', async function() {
    // 初始化用户信息
    const userInfo = await checkAuth();
    if (userInfo) {
        // 设置用户信息
        document.querySelector('.username').textContent = userInfo.username;
        document.querySelector('.email').textContent = userInfo.email || '未设置';
        document.querySelector('.role').textContent =
            userInfo.role === 1 ? '管理员' :
            userInfo.role === 2 ? '普通用户' : 
            userInfo.role === 3 ? '教师' : '未知';

        // 设置表单默认值
        document.querySelector('input[name="username"]').value = userInfo.username;
        document.querySelector('input[name="email"]').value = userInfo.email || '';
        document.querySelector('input[name="phone"]').value = userInfo.phone || '';

        // 设置头像
        updateAvatarDisplay(userInfo.avatar, userInfo);
    }

    // 头像上传
    const avatarInput = document.getElementById('avatarInput');
    const uploadBtn = document.querySelector('.upload-btn');

    uploadBtn.addEventListener('click', () => avatarInput.click());

    avatarInput.addEventListener('change', async function() {
        const file = this.files[0];
        if (!file) return;

        const formData = new FormData();
        formData.append('file', file);

        try {
            const response = await fetch('/api/user/avatar', {
                method: 'POST',
                headers: {
                    'Authorization': localStorage.getItem('token')
                },
                body: formData
            });

            const data = await response.json();
            if (data.code === 200) {
                updateAvatarDisplay(data.data, data.userInfo);
                showToast('头像更新成功', 'success');
            } else {
                showToast(data.message, 'error');
            }
        } catch (error) {
            showToast('头像上传失败', 'error');
            console.error('Avatar upload failed:', error);
        }
    });
});

// 在表单提交处理中添加
async function handleFormSubmit(form, submitHandler) {
    form.addEventListener('submit', async (e) => {
        e.preventDefault();
        const submitBtn = form.querySelector('button[type="submit"]');

        if (submitBtn.classList.contains('loading')) {
            return;
        }

        try {
            submitBtn.classList.add('loading');
            await submitHandler(new FormData(form));
            showToast('保存成功', 'success');
        } catch (error) {
            handleError(error);
        } finally {
            submitBtn.classList.remove('loading');
        }
    });
}

// 添加表单验证
function validateForm(form) {
    const email = form.querySelector('input[name="email"]');
    const phone = form.querySelector('input[name="phone"]');

    if (email && !isValidEmail(email.value)) {
        showToast('请输入有效的邮箱地址', 'error');
        return false;
    }

    if (phone && !isValidPhone(phone.value)) {
        showToast('请输入有效的手机号码', 'error');
        return false;
    }

    return true;
}

function isValidEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}

function isValidPhone(phone) {
    return /^1[3-9]\d{9}$/.test(phone);
}

// 在表单提交前调用验证
handleFormSubmit(document.getElementById('profileForm'), async (formData) => {
    if (!validateForm(form)) {
        return;
    }
    // ... 提交逻辑
});

// 获取头像URL的函数 - 更新逻辑
function getAvatarUrl(avatarPath, userInfo = {}) {
    if (avatarPath && avatarPath.trim() !== '') {
        // 用户有头像
        if (avatarPath.startsWith('http') || avatarPath.startsWith('/static/') || avatarPath.startsWith('/uploads/')) {
            return avatarPath;
        }
        
        if (avatarPath.startsWith('avatars/')) {
            return `/uploads/${avatarPath}`;
        }
        
        if (avatarPath.startsWith('main/avatars/')) {
            return `/uploads/avatars/${avatarPath.replace('main/avatars/', '')}`;
        }
        
        // 如果是纯文件名，添加路径
        return `/uploads/avatars/${avatarPath}`;
    }
    
    // 用户没有头像，生成首字母头像
    return generateFallbackAvatar(userInfo.username || 'U');
}

// 生成降级头像的函数
function generateFallbackAvatar(username) {
    const canvas = document.createElement('canvas');
    canvas.width = 128;
    canvas.height = 128;
    const ctx = canvas.getContext('2d');
    
    // 颜色数组
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'];
    
    // 根据用户名生成固定颜色
    let hash = 0;
    if (username && username.length > 0) {
        for (let i = 0; i < username.length; i++) {
            hash = ((hash << 5) - hash) + username.charCodeAt(i);
            hash = hash & hash;
        }
    }
    const colorIndex = Math.abs(hash) % colors.length;
    const bgColor = colors[colorIndex];
    
    // 获取首字母
    let initials = 'U';
    if (username && username.length > 0) {
        if (/[\u4e00-\u9fa5]/.test(username)) {
            // 中文用户名取第一个字
            initials = username.charAt(0);
        } else {
            // 英文用户名取前两个字母
            initials = username.substring(0, 2).toUpperCase();
        }
    }
    
    // 绘制背景
    ctx.fillStyle = bgColor;
    ctx.fillRect(0, 0, 128, 128);
    
    // 绘制字母
    ctx.fillStyle = '#FFFFFF';
    ctx.font = 'bold 48px Arial, sans-serif';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(initials, 64, 64);
    
    return canvas.toDataURL();
}

// 通用头像错误处理函数
function handleAvatarError(imgElement, userInfo = {}) {
    console.log('头像加载失败，使用首字母头像');
    
    if (window.AvatarUtils && userInfo.username) {
        // 使用AvatarUtils生成首字母头像
        window.AvatarUtils.setAvatarElement(imgElement, null, userInfo, { showInitials: true });
    } else {
        // 降级处理
        const username = userInfo.username || imgElement.getAttribute('data-username') || 'U';
        imgElement.src = generateFallbackAvatar(username);
    }
}