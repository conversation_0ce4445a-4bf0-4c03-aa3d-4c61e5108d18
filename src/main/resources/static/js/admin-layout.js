/**
 * 管理员界面布局控制
 * 处理侧边栏折叠/展开、移动端适配等功能
 */

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeAdminLayout();
});

/**
 * 初始化管理员布局
 */
function initializeAdminLayout() {
    // 恢复侧边栏状态
    restoreSidebarState();
    
    // 添加移动端导航按钮
    addMobileNavButton();
    
    // 绑定键盘快捷键
    bindKeyboardShortcuts();
}

/**
 * 恢复侧边栏状态
 */
function restoreSidebarState() {
    const isCollapsed = localStorage.getItem('adminSidebarCollapsed') === 'true';
    
    if (isCollapsed) {
        const sidebar = document.getElementById('sidebar');
        
        if (sidebar) {
            sidebar.classList.add('collapsed');
            adjustLayoutForSidebar(true);
        }
    }
}

/**
 * 添加移动端导航按钮
 */
function addMobileNavButton() {
    // 检查是否已存在移动端按钮
    if (document.querySelector('.mobile-nav-toggle')) {
        return;
    }
    
    // 查找顶部导航栏
    const navbar = document.querySelector('.navbar');
    const topBar = document.querySelector('.admin-header');
    const targetContainer = navbar || topBar;
    
    if (!targetContainer) {
        return;
    }
    
    // 创建移动端菜单按钮
    const mobileButton = document.createElement('button');
    mobileButton.className = 'btn btn-outline-secondary mobile-nav-toggle d-md-none me-2';
    mobileButton.type = 'button';
    mobileButton.title = '显示菜单';
    mobileButton.innerHTML = '<i class="fas fa-bars"></i>';
    mobileButton.onclick = toggleMobileSidebar;
    
    // 插入到导航栏的开始位置
    const container = targetContainer.querySelector('.container-fluid') || targetContainer;
    container.insertBefore(mobileButton, container.firstChild);
}

/**
 * 绑定键盘快捷键
 */
function bindKeyboardShortcuts() {
    document.addEventListener('keydown', function(event) {
        // Ctrl + B 切换侧边栏
        if (event.ctrlKey && event.key === 'b') {
            event.preventDefault();
            toggleSidebar();
        }
        
        // ESC 键关闭移动端侧边栏
        if (event.key === 'Escape' && window.innerWidth <= 767.98) {
            const sidebar = document.getElementById('sidebar');
            if (sidebar && sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
            }
        }
    });
}

/**
 * 切换侧边栏
 */
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    if (!sidebar) return;
    
    const isCollapsed = !sidebar.classList.contains('collapsed');
    sidebar.classList.toggle('collapsed');
    
    // 调整所有相关的布局元素
    adjustLayoutForSidebar(isCollapsed);
    
    // 保存状态
    localStorage.setItem('adminSidebarCollapsed', isCollapsed.toString());
    
    // 触发相关事件和更新
    triggerLayoutUpdate(isCollapsed);
}

/**
 * 调整布局以适应侧边栏状态
 */
function adjustLayoutForSidebar(isCollapsed) {
    // 主要内容区域
    const mainContent = document.querySelector('main');
    const containerFluid = document.querySelector('.container-fluid > .row');
    
    // 知识点侧边栏
    const knowledgeSidebar = document.querySelector('.col-md-2.d-none.d-md-block.bg-light.border-end');
    
    if (isCollapsed) {
        // 侧边栏折叠时
        if (mainContent) {
            // 移除现有的栅格类
            mainContent.classList.remove('col-md-9', 'col-lg-10', 'col-md-7', 'col-lg-8');
            // 添加折叠状态的栅格类
            if (knowledgeSidebar) {
                mainContent.classList.add('col-md-10', 'col-lg-11');
            } else {
                mainContent.classList.add('col-md-11', 'col-lg-11');
            }
        }
        
        if (knowledgeSidebar) {
            knowledgeSidebar.classList.remove('col-md-2');
            knowledgeSidebar.classList.add('col-md-1');
        }
    } else {
        // 侧边栏展开时
        if (mainContent) {
            // 移除折叠状态的栅格类
            mainContent.classList.remove('col-md-11', 'col-lg-11', 'col-md-10');
            // 恢复原始栅格类
            if (knowledgeSidebar) {
                mainContent.classList.add('col-md-7', 'col-lg-8');
            } else {
                mainContent.classList.add('col-md-9', 'col-lg-10');
            }
        }
        
        if (knowledgeSidebar) {
            knowledgeSidebar.classList.remove('col-md-1');
            knowledgeSidebar.classList.add('col-md-2');
        }
    }
}

/**
 * 触发布局更新事件
 */
function triggerLayoutUpdate(isCollapsed) {
    setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
        
        // 如果存在DataTable，重新调整列宽
        if (window.jQuery && window.jQuery.fn.DataTable) {
            window.jQuery.fn.DataTable.tables({ visible: true, api: true }).columns.adjust();
        }
        
        // 通知其他组件布局已改变
        window.dispatchEvent(new CustomEvent('sidebarToggled', {
            detail: { collapsed: isCollapsed }
        }));
        
        // 如果有图表组件，触发重绘
        if (window.Chart) {
            Object.values(Chart.instances).forEach(chart => {
                chart.resize();
            });
        }
        
        // 如果有ECharts组件，触发重绘
        if (window.echarts) {
            window.echarts.getInstanceByDom && 
            document.querySelectorAll('[_echarts_instance_]').forEach(element => {
                const chart = window.echarts.getInstanceByDom(element);
                if (chart) {
                    chart.resize();
                }
            });
        }
    }, 300);
}

/**
 * 移动端侧边栏切换
 */
function toggleMobileSidebar() {
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
        sidebar.classList.toggle('show');
        
        // 添加或移除页面遮罩
        toggleMobileOverlay(sidebar.classList.contains('show'));
    }
}

/**
 * 切换移动端遮罩
 */
function toggleMobileOverlay(show) {
    let overlay = document.querySelector('.mobile-sidebar-overlay');
    
    if (show && !overlay) {
        overlay = document.createElement('div');
        overlay.className = 'mobile-sidebar-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1039;
            display: none;
        `;
        
        overlay.onclick = function() {
            toggleMobileSidebar();
        };
        
        document.body.appendChild(overlay);
    }
    
    if (overlay) {
        overlay.style.display = show ? 'block' : 'none';
    }
}

// 移动端点击外部区域关闭侧边栏
document.addEventListener('click', function(event) {
    if (window.innerWidth > 767.98) return;
    
    const sidebar = document.getElementById('sidebar');
    const isClickInsideSidebar = sidebar && sidebar.contains(event.target);
    const isToggleButton = event.target.closest('.sidebar-toggle') || 
                          event.target.closest('.mobile-nav-toggle') ||
                          event.target.closest('[data-bs-toggle="offcanvas"]');
    
    if (!isClickInsideSidebar && !isToggleButton && sidebar && sidebar.classList.contains('show')) {
        toggleMobileSidebar();
    }
});

// 窗口大小变化时的处理
window.addEventListener('resize', function() {
    const sidebar = document.getElementById('sidebar');
    if (!sidebar) return;
    
    // 大屏幕时自动关闭移动端侧边栏
    if (window.innerWidth > 767.98 && sidebar.classList.contains('show')) {
        sidebar.classList.remove('show');
        toggleMobileOverlay(false);
    }
});

// 导出函数供其他模块使用
window.adminLayout = {
    toggleSidebar,
    toggleMobileSidebar,
    restoreSidebarState
}; 