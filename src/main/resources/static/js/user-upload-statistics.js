/**
 * 用户个人上传统计JavaScript
 */
class UserUploadStatistics {
    constructor() {
        this.chart = null;
        this.currentUser = null;
        this.refreshInterval = null;
        
        this.init();
    }
    
    init() {
        this.loadCurrentUser();
        this.loadPersonalStats();
        this.initChart();
        this.bindEvents();
        this.startAutoRefresh();
    }
    
    // 加载当前用户信息
    loadCurrentUser() {
        // 从localStorage或API获取当前用户信息
        this.currentUser = JSON.parse(localStorage.getItem('currentUser') || 'null');
        if (!this.currentUser) {
            // 如果没有用户信息，尝试从API获取
            this.fetchCurrentUser();
        }
    }
    
    // 从API获取当前用户信息
    async fetchCurrentUser() {
        try {
            const response = await this.fetchAPI('/api/user/current');
            if (response.success) {
                this.currentUser = response.data;
                localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
            }
        } catch (error) {
            console.warn('Failed to fetch current user:', error);
            // 使用默认用户信息作为fallback
            this.currentUser = { id: 1, username: '当前用户' };
        }
    }
    
    // 绑定事件
    bindEvents() {
        // 统计类型切换
        const statTypeSelect = document.getElementById('statType');
        if (statTypeSelect) {
            statTypeSelect.addEventListener('change', () => {
                this.loadChartData();
            });
        }
        
        // 日期范围筛选
        const searchBtn = document.getElementById('searchBtn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.loadChartData();
            });
        }
        
        // 初始化日期选择器默认值
        this.initDatePickers();
    }
    
    // 初始化日期选择器
    initDatePickers() {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setMonth(endDate.getMonth() - 1); // 默认显示最近一个月
        
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');
        
        if (startDateInput) {
            startDateInput.value = startDate.toISOString().split('T')[0];
        }
        if (endDateInput) {
            endDateInput.value = endDate.toISOString().split('T')[0];
        }
    }
    
    // 加载个人统计数据
    async loadPersonalStats() {
        try {
            const response = await this.fetchAPI('/api/user/upload/stats/personal');
            if (response && response.code === 200 && response.success !== false) {

                const stats = response.data;
                this.updateStatCards(stats);
                this.updateAchievements(stats.achievements || {});
                this.updateRankings(stats.rankings || {});
                this.updateTopicTypeDistribution(stats.typeDistribution || {});
            } else {
                throw new Error(response.message || '获取统计数据失败');
            }
        } catch (error) {
            throw new Error("系统响应错误")
        }
    }
    
    // 更新统计卡片
    updateStatCards(stats) {
        const elements = {
            'userTotalUploads': stats.totalUploads || 0,
            'userApprovedUploads': stats.approvedUploads || 0,
            'userPendingUploads': stats.pendingUploads || 0,
            'userWeeklyUploads': stats.weeklyUploads || 0
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
        
        // 更新通过率
        const approvalRate = stats.totalUploads > 0 
            ? Math.round((stats.approvedUploads / stats.totalUploads) * 100) 
            : 0;
        const approvalRateElement = document.getElementById('approvalRate');
        if (approvalRateElement) {
            approvalRateElement.textContent = `${approvalRate}%`;
        }
        
        // 更新总计数和通过率徽章
        const totalCountElement = document.getElementById('totalCount');
        if (totalCountElement) {
            totalCountElement.textContent = stats.totalUploads || 0;
        }
    }
    
    // 更新个人成就
    updateAchievements(achievements) {
        const elements = {
            'consecutiveDays': achievements.consecutiveDays || 0,
            'maxDailyUploads': achievements.maxDailyUploads || 0,
            'avgAuditTime': achievements.avgAuditTime || '-'
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                if (id === 'avgAuditTime' && value !== '-') {
                    element.textContent = `${value}小时`;
                } else {
                    element.textContent = value;
                }
            }
        });
    }
    
    // 更新排名信息
    updateRankings(rankings) {
        const elements = {
            'totalUploadRank': rankings.totalUploadRank ? `第${rankings.totalUploadRank}名` : '-',
            'monthlyRank': rankings.monthlyRank ? `第${rankings.monthlyRank}名` : '-',
            'qualityScore': rankings.qualityScore ? `${rankings.qualityScore}分` : '-'
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }
    
    // 更新题目类型分布
    updateTopicTypeDistribution(distribution) {
        const elements = {
            'choiceCount': distribution.choice || 0,
            'multipleCount': distribution.multiple || 0,
            'judgeCount': distribution.judge || 0,
            'fillCount': distribution.fill || 0,
            'shortCount': distribution.short || 0,
            'essayCount': distribution.essay || 0
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }
    
    // 初始化图表
    initChart() {
        const chartContainer = document.getElementById('uploadStatsChart');
        if (!chartContainer) return;
        
        this.chart = echarts.init(chartContainer);
        
        // 设置默认选项
        const option = {
            title: {
                text: '我的题目上传趋势',
                left: 'center',
                textStyle: {
                    color: '#333',
                    fontSize: 16
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                },
                formatter: function(params) {
                    let result = params[0].name + '<br/>';
                    params.forEach(param => {
                        result += `${param.seriesName}: ${param.value}<br/>`;
                    });
                    return result;
                }
            },
            legend: {
                data: ['上传数量', '通过数量'],
                bottom: 10
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: []
            },
            yAxis: {
                type: 'value',
                minInterval: 1
            },
            series: [
                {
                    name: '上传数量',
                    type: 'line',
                    smooth: true,
                    itemStyle: {
                        color: '#667eea'
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0, color: 'rgba(102, 126, 234, 0.3)'
                            }, {
                                offset: 1, color: 'rgba(102, 126, 234, 0.1)'
                            }]
                        }
                    },
                    data: []
                },
                {
                    name: '通过数量',
                    type: 'line',
                    smooth: true,
                    itemStyle: {
                        color: '#28a745'
                    },
                    data: []
                }
            ]
        };
        
        this.chart.setOption(option);
        
        // 响应式调整
        window.addEventListener('resize', () => {
            if (this.chart) {
                this.chart.resize();
            }
        });
        
        // 加载图表数据
        this.loadChartData();
    }
    
    // 加载图表数据
    async loadChartData() {
        try {
            const statType = document.getElementById('statType')?.value || 'day';
            const startDate = document.getElementById('startDate')?.value;
            const endDate = document.getElementById('endDate')?.value;
            
            const params = new URLSearchParams({
                type: statType,
                ...(startDate && { startDate }),
                ...(endDate && { endDate })
            });
            
            const response = await this.fetchAPI(`/api/user/upload/stats/chart?${params}`);
            
            if (response.success) {
                const data = response.data;
                this.updateChart(data);
            }
        } catch (error) {
            console.error('Failed to load chart data:', error);
            this.loadMockChartData(); // 加载模拟数据
        }
    }
    
    // 更新图表
    updateChart(data) {
        if (!this.chart) return;
        
        const option = {
            xAxis: {
                data: data.labels || []
            },
            series: [
                {
                    name: '上传数量',
                    data: data.uploadData || []
                },
                {
                    name: '通过数量',
                    data: data.approvedData || []
                }
            ]
        };
        
        this.chart.setOption(option);
    }
    
    // 开始自动刷新
    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            this.loadPersonalStats();
        }, 60000); // 每分钟刷新一次
    }
    
    // 停止自动刷新
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    
    // API请求方法
    async fetchAPI(url, options = {}) {
        try {
            // 使用与common.js一致的token获取方法
            const token = localStorage.getItem('token');
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json'
                }
            };
            
            // 如果有token，添加Authorization头
            if (token) {
                // 检查token是否已包含Bearer前缀
                if (token.startsWith('Bearer ')) {
                    defaultOptions.headers['Authorization'] = token;
                } else {
                    defaultOptions.headers['Authorization'] = `Bearer ${token}`;
                }
            }
            const finalOptions = { ...defaultOptions, ...options };
            const response = await fetch(url, finalOptions);
            // 检查认证失败
            if (response.status === 401) {
                console.warn('认证失败，可能需要重新登录');
                localStorage.removeItem('token');
                localStorage.removeItem('currentUser');
                if (!window.location.pathname.includes('/auth/login')) {
                    window.location.href = '/auth/login';
                }
                return { success: false, message: '认证失败，请重新登录' };
            }
            const result = await response.json();
            // 处理不同的响应格式
            if (result.code === 200 || result.success === true) {
                return { success: true, data: result.data, ...result };
            } else {
                console.warn('API返回业务错误:', result);
                return { success: false, message: result.message, ...result };
            }
        } catch (error) {
            console.error('API请求失败:', error);
            return { success: false, message: error.message };
        }
    }
    
    // 加载模拟数据（开发阶段使用）
    loadMockData() {
        const mockStats = {
            totalUploads: 156,
            approvedUploads: 142,
            pendingUploads: 8,
            weeklyUploads: 12,
            achievements: {
                consecutiveDays: 15,
                maxDailyUploads: 8,
                avgAuditTime: '2.5'
            },
            rankings: {
                totalUploadRank: 15,
                monthlyRank: 8,
                qualityScore: 92
            },
            typeDistribution: {
                choice: 45,
                multiple: 32,
                judge: 28,
                fill: 25,
                short: 18,
                essay: 8
            }
        };
        
        this.updateStatCards(mockStats);
        this.updateAchievements(mockStats.achievements);
        this.updateRankings(mockStats.rankings);
        this.updateTopicTypeDistribution(mockStats.typeDistribution);
    }
    
    // 加载模拟图表数据
    loadMockChartData() {
        const mockChartData = {
            labels: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07'],
            uploadData: [3, 5, 2, 8, 4, 6, 3],
            approvedData: [2, 4, 2, 7, 3, 5, 3]
        };
        
        this.updateChart(mockChartData);
    }
    
    // 销毁实例
    destroy() {
        this.stopAutoRefresh();
        if (this.chart) {
            this.chart.dispose();
            this.chart = null;
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化统计组件
    if (document.getElementById('uploadStatsChart')) {
        window.userUploadStats = new UserUploadStatistics();
        
        // 添加全局调试函数
        window.debugUserStats = function() {
            if (window.userUploadStats) {
                // 手动测试API
                window.userUploadStats.fetchAPI('/api/user/upload/stats/personal')
                    .then(response => {
                    })
                    .catch(error => {
                    });
            } else {
            }
        };

    } else {
    }
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (window.userUploadStats) {
        window.userUploadStats.destroy();
    }
});

// 导出类以供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserUploadStatistics;
} 