/**
 * 试卷生成页面主脚本
 * 处理知识点加载、试卷配置和生成逻辑
 */
// 显示错误消息
function showError(title, message) {
    Swal.fire({
        icon: 'error',
        title: title,
        text: message,
        confirmButtonText: '确定'
    });
}

// 全局变量，存储已选择的知识点
const selectedKnowledgePoints = new Map();

// 当前历史试卷页码
let currentHistoryPage = 0;

$(document).ready(function() {
    // 确保工具栏初始隐藏
    $('.selected-points-toolbar').hide();

    // 确保知识点选择事件正确绑定
    $(document).on('change', '.knowledge-checkbox', function() {
        const isChecked = $(this).prop('checked');
        const id = $(this).val();
        const card = $(this).closest('.card');
        const name = card.find('.card-title').text().trim();
        const topicCount = card.find('.badge-info').text().trim();
        const isFree = card.find('.badge-success').length > 0;

        // 更新全局选择状态
        if (isChecked) {
            // 检查是否已经存在，避免重复添加
            if (!selectedKnowledgePoints.has(id)) {
                // 获取知识点的真实ID（know_id）
                let knowId = id; // 默认使用id
                const knowledgeIdBadge = card.find('.badge-secondary').text();
                if (knowledgeIdBadge && knowledgeIdBadge.includes('知识点ID:')) {
                    const extractedId = knowledgeIdBadge.replace('知识点ID:', '').trim();
                    if (!isNaN(extractedId)) {
                        knowId = parseInt(extractedId);
                    }
                }

                selectedKnowledgePoints.set(id, {
                    id: id,
                    name: name,
                    topicCount: topicCount,
                    isFree: isFree,
                    knowId: knowId // 存储真实的知识点ID
                });
                console.log(`添加知识点: ${name} (ID: ${id}, knowId: ${knowId})`);
            } else {
                console.log(`知识点已存在，跳过添加: ${name} (ID: ${id})`);
            }
        } else {
            // 支持多种ID类型的删除
            const idsToTry = [id, id.toString(), parseInt(id)];
            let deleted = false;

            idsToTry.forEach(idToTry => {
                if (selectedKnowledgePoints.has(idToTry)) {
                    selectedKnowledgePoints.delete(idToTry);
                    console.log(`删除知识点: ${name} (ID: ${idToTry}, 类型: ${typeof idToTry})`);
                    deleted = true;
                }
            });

            if (!deleted) {
                console.warn(`⚠️ 未找到要删除的知识点: ${id}`);
            }
        }

        console.log(`当前选中的知识点数量: ${selectedKnowledgePoints.size}`);

        // 更新选中计数和显示
        updateSelectionCounter();
    });

    // 图表对象
    window.difficultyChart = null;
    window.questionDistributionChart = null;

    // 加载历史试卷
    window.loadPaperHistory = function(page = 0, size = 5) {
        currentHistoryPage = page;

        return $.ajax({
            url: `/api/papers?page=${page}&size=${size}&sort=createTime,desc`,
            method: 'GET',
            success: function(response) {
                if (response && response.success && response.data) {
                    renderPaperHistory(response.data);
                } else {
                    $('#paperHistoryTable').html(`
                        <tr>
                            <td colspan="4" class="text-center py-3 text-muted">
                                加载历史试卷失败
                            </td>
                        </tr>
                    `);
                }
            },
            error: function() {
                $('#paperHistoryTable').html(`
                    <tr>
                        <td colspan="4" class="text-center py-3 text-muted">
                            服务器连接错误
                        </td>
                    </tr>
                `);
            }
        });
    };

    // 延迟初始化加载，确保所有函数都已定义
    setTimeout(function() {
        // 静默初始化加载
        if (typeof loadKnowledgeGroups === 'function') {
            loadKnowledgeGroups();
        }

        if (typeof window.loadPaperHistory === 'function') {
            window.loadPaperHistory();
        }

        if (typeof initCharts === 'function') {
            initCharts();
        }

        // 初始化批量操作功能
        if (typeof initBatchOperationEvents === 'function') {
            initBatchOperationEvents();
        }

        // 显示批量操作工具栏（初始禁用状态）
        if (typeof showBatchOperationToolbar === 'function') {
            showBatchOperationToolbar();
        }
        
        // 确保批量操作工具栏在页面加载时可见
        $('#batchOperationToolbar').show();
        $('#batchOperationToolbarMobile').show();
    }, 500);

    // 绑定历史试卷头部按钮事件
    $('#refreshHistoryBtn').on('click', function() {
        $(this).find('i').addClass('fa-spin');
        window.loadPaperHistory().always(() => {
            setTimeout(() => {
                $(this).find('i').removeClass('fa-spin');
            }, 500);
        });
    });

    $('#clearHistoryBtn').on('click', function() {
        clearAllHistory();
    });

    // 绑定移动端历史试卷按钮事件
    $('#refreshHistoryBtnMobile').on('click', function() {
        $(this).find('i').addClass('fa-spin');
        window.loadPaperHistory().always(() => {
            setTimeout(() => {
                $(this).find('i').removeClass('fa-spin');
            }, 500);
        });
    });

    $('#clearHistoryBtnMobile').on('click', function() {
        clearAllHistory();
    });

    // 绑定底部生成试卷按钮事件
    $('#generatePaperBtnBottom').on('click', function() {
        $('#generatePaperBtn').click();
    });

    // 绑定清空所有知识点按钮事件
    $('#clearAllKnowledgePointsBtn').on('click', function() {
        Swal.fire({
            title: '确认清空',
            text: '确定要清空所有已选择的知识点吗？',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '确定清空',
            cancelButtonText: '取消'
        }).then((result) => {
            if (result.isConfirmed) {
                // 取消选中所有知识点
                $('.knowledge-checkbox:checked').prop('checked', false);

                // 清空全局选择状态
                selectedKnowledgePoints.clear();

                // 更新计数和显示
                updateSelectionCounter();

                Swal.fire({
                    title: '已清空',
                    text: '已清空所有选择的知识点',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            }
        });
    });

    // 添加清理重复选择的功能
    function cleanupDuplicateSelections() {
        // 清理重复选择
        const uniqueSelections = new Map();

        // 遍历当前选择，保留第一次出现的
        selectedKnowledgePoints.forEach((pointData, pointId) => {
            if (!uniqueSelections.has(pointId)) {
                uniqueSelections.set(pointId, pointData);
            } else {
                console.warn(`发现重复选择的知识点: ${pointId}, 名称: ${pointData.name}`);
            }
        });

        // 如果发现重复，更新全局状态
        if (uniqueSelections.size !== selectedKnowledgePoints.size) {
            console.log(`清理前: ${selectedKnowledgePoints.size} 个知识点，清理后: ${uniqueSelections.size} 个知识点`);
            selectedKnowledgePoints.clear();
            uniqueSelections.forEach((pointData, pointId) => {
                selectedKnowledgePoints.set(pointId, pointData);
            });

            // 更新显示
            updateSelectionCounter();
        }
    }

    // 在页面加载完成后执行清理
    setTimeout(() => {
        cleanupDuplicateSelections();
    }, 2000);

    // 初始化版本选择器事件
    function initializeVersionSelectorEvents() {
        // 版本选择器变化事件
        $(document).on('change', '.version-selector', function() {
            const selectedVersion = $(this).val();
            const descElement = $(this).siblings('.version-description');
            updateVersionDescription(selectedVersion, descElement);
        });

        // 带版本的下载按钮点击事件
        $(document).on('click', '.download-with-version', function(e) {
            e.preventDefault();
            const paperId = $(this).data('paper-id');
            const format = $(this).data('format');
            downloadPaperWithVersion(paperId, format);
        });
    }

    // 版本描述信息
    const PAPER_VERSION_DESCRIPTIONS = {
        regular: '只包含题目和选项，适合考试使用',
        teacher: '只显示答案和解析，适合教师批改',
        standard: '包含完整的题目、答案和解析，适合学习使用'
    };

    // 更新版本描述
    function updateVersionDescription(version, descriptionElement) {
        const description = PAPER_VERSION_DESCRIPTIONS[version];
        if (description && descriptionElement.length > 0) {
            descriptionElement.text(description);
        }
    }

    // 带版本的下载函数
    function downloadPaperWithVersion(paperId, format, version) {
        if (!version) {
            version = $(`.version-selector[data-paper-id="${paperId}"]`).val() || 'standard';
        }

        const url = `/api/papers/download/${paperId}?format=${format}&paperType=${version}`;

        // 显示下载提示
        showToast(`正在下载${getVersionDisplayName(version)}${format.toUpperCase()}文件...`, 'info');

        // 执行下载
        window.open(url, '_blank');
    }

    // 获取版本显示名称
    function getVersionDisplayName(version) {
        const names = {
            regular: '学生版',
            teacher: '教师版',
            standard: '标准版'
        };
        return names[version] || '标准版';
    }

    // 初始化试卷版本选择器
    function initializePaperVersionSelector() {
        // 为试卷生成表单添加版本选择事件
        $('#paperType, #customPaperType').on('change', function() {
            const selectedVersion = $(this).val();
            const descriptionElement = $(this).closest('.form-group').find('[id$="Description"]');
            updateVersionDescription(selectedVersion, descriptionElement);
        });
    }

    // 初始化版本选择功能
    initializeVersionSelectorEvents();
    initializePaperVersionSelector();

    // 延迟初始化主页面搜索功能，确保DOM完全加载
    setTimeout(function() {
        initMainPageSearch();
    }, 500);

    // 渲染历史试卷
    function renderPaperHistory(data) {
        const papers = data.content || [];
        const totalElements = data.totalElements || 0;
        const currentPage = data.number || 0;
        const totalPages = data.totalPages || 0;

        // 更新历史数量
        $('#historyCount').text(totalElements);

        // 更新分页信息
        $('#currentPageInfo').text(currentPage + 1);
        $('#totalPagesInfo').text(totalPages);

        // 启用/禁用清空历史按钮
        $('#clearHistoryBtn').prop('disabled', totalElements === 0);

        if (!papers || papers.length === 0) {
            $('#paperHistoryTable').html(`
                <tr>
                    <td colspan="4" class="text-center py-5">
                        <div class="empty-state">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <p class="lead">暂无试卷历史记录</p>
                            <small class="text-muted">生成的试卷将显示在这里</small>
                        </div>
                    </td>
                </tr>
            `);
            $('#paperHistoryTableMobile').html(`
                <tr>
                    <td colspan="5" class="text-center py-5">
                        <div class="empty-state">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <p class="lead">暂无试卷历史记录</p>
                            <small class="text-muted">生成的试卷将显示在这里</small>
                        </div>
                    </td>
                </tr>
            `);
            $('#paperHistoryPagination').empty();
            // 没有试卷时隐藏批量操作工具栏
            hideBatchOperationToolbar();
            return;
        }

        let html = '';
        papers.forEach(function(paper) {
            const createTime = new Date(paper.createTime).toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
            const totalScore = paper.totalScore || 0;

            // 检查是否为右侧简化版本
            const isRightSidebar = $('#paperHistoryTableContainer').closest('.col-lg-3').length > 0;

            if (isRightSidebar) {
                // 右侧简化版本
                const shortTime = new Date(paper.createTime).toLocaleDateString('zh-CN', {
                    month: '2-digit',
                    day: '2-digit'
                });

                html += `
                    <tr class="fade-in">
                        <td>
                            <div class="form-check form-check-sm">
                                <input class="form-check-input paper-checkbox" type="checkbox" value="${paper.id}" data-title="${paper.title || '未命名试卷'}">
                            </div>
                        </td>
                        <td>
                            <a href="/papers/preview/${paper.id}" target="_blank" class="paper-title" title="${paper.title || '未命名试卷'}">
                                ${(paper.title || '未命名试卷').length > 15 ? (paper.title || '未命名试卷').substring(0, 15) + '...' : (paper.title || '未命名试卷')}
                            </a>
                        </td>
                        <td>
                            <span class="paper-create-time">${shortTime}</span>
                        </td>
                        <td>
                            <div class="paper-actions d-flex flex-wrap gap-1">
                                <button class="btn btn-outline-primary btn-xs" onclick="previewPaper(${paper.id})" title="预览试卷">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-info btn-xs difficulty-distribution-btn" data-id="${paper.id}" title="难度分布">
                                    <i class="fas fa-chart-bar"></i>
                                </button>
                                <button class="btn btn-outline-success btn-xs download-pdf-btn" data-id="${paper.id}" title="下载PDF">
                                    <i class="fas fa-file-pdf"></i>
                                </button>
                                <button class="btn btn-outline-warning btn-xs download-word-btn" data-id="${paper.id}" title="下载Word">
                                    <i class="fas fa-file-word"></i>
                                </button>
                                <button class="btn btn-outline-secondary btn-xs clone-paper-btn" data-id="${paper.id}" title="克隆试卷">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-xs delete-paper-btn" data-id="${paper.id}" data-title="${paper.title || '未命名试卷'}" title="删除试卷">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            } else {
                // 移动端完整版本
                html += `
                    <tr class="fade-in">
                        <td>
                            <div class="form-check form-check-sm">
                                <input class="form-check-input paper-checkbox-mobile" type="checkbox" value="${paper.id}" data-title="${paper.title || '未命名试卷'}">
                            </div>
                        </td>
                        <td>
                            <a href="/papers/preview/${paper.id}" target="_blank" class="paper-title" title="${paper.title || '未命名试卷'}">
                                ${paper.title || '未命名试卷'}
                            </a>
                        </td>
                        <td>
                            <span class="paper-create-time">${createTime}</span>
                        </td>
                        <td>
                            <span class="paper-total-score">${totalScore}分</span>
                        </td>
                        <td>
                            <div class="paper-actions d-flex flex-wrap gap-1">
                                <button class="btn btn-outline-primary btn-xs" onclick="previewPaper(${paper.id})" title="预览试卷">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-info btn-xs difficulty-distribution-btn" data-id="${paper.id}" title="难度分布">
                                    <i class="fas fa-chart-bar"></i>
                                </button>
                                <button class="btn btn-outline-success btn-xs download-pdf-btn" data-id="${paper.id}" title="下载PDF">
                                    <i class="fas fa-file-pdf"></i>
                                </button>
                                <button class="btn btn-outline-warning btn-xs download-word-btn" data-id="${paper.id}" title="下载Word">
                                    <i class="fas fa-file-word"></i>
                                </button>
                                <button class="btn btn-outline-secondary btn-xs clone-paper-btn" data-id="${paper.id}" title="克隆试卷">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-xs delete-paper-btn" data-id="${paper.id}" data-title="${paper.title || '未命名试卷'}" title="删除试卷">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }
        });

        $('#paperHistoryTable').html(html);

        // 同时更新移动端表格
        $('#paperHistoryTableMobile').html(html);
        $('#historyCountMobile').text(totalElements);
        $('#currentPageInfoMobile').text(currentPage + 1);
        $('#totalPagesInfoMobile').text(totalPages);
        $('#clearHistoryBtnMobile').prop('disabled', totalElements === 0);

        // 渲染分页
        renderPagination(data);

        // 绑定checkbox事件
        bindCheckboxEvents();

        // 有试卷时显示批量操作工具栏（初始状态为禁用）
        showBatchOperationToolbar();

        // 绑定难度分布按钮事件
        $('.difficulty-distribution-btn').on('click', function() {
            const paperId = $(this).data('id');
            showDifficultyDistribution(paperId);
        });

        // 绑定PDF下载按钮事件
        $('.download-pdf-btn').on('click', function() {
            const paperId = $(this).data('id');
            downloadPaper(paperId, 'pdf');
        });

        // 绑定Word下载按钮事件
        $('.download-word-btn').on('click', function() {
            const paperId = $(this).data('id');
            downloadPaper(paperId, 'word');
        });

        // 绑定克隆按钮事件
        $('.clone-paper-btn').on('click', function() {
            const paperId = $(this).data('id');

            $.ajax({
                url: `/api/papers/${paperId}`,
                method: 'GET',
                success: function(response) {
                    if (response && response.success && response.data) {
                        const paper = response.data;

                        // 填充表单
                        $('#paperTitle').val(paper.title + ' (复制)');

                        // TODO: 填充其他字段

                        // 显示模态框
                        $('#paperGenerationModal').modal('show');
                    } else {
                        showError('加载失败', '无法获取试卷信息');
                    }
                },
                error: function() {
                    showError('加载失败', '服务器连接错误');
                }
            });
        });

        // 绑定删除按钮事件
        $('.delete-paper-btn').on('click', function() {
            const paperId = $(this).data('id');
            const paperTitle = $(this).data('title');
            deletePaper(paperId, paperTitle);
        });
    }

    // 绑定checkbox事件
    function bindCheckboxEvents() {
        // 全选/取消全选 - 桌面端
        $('#selectAllPapers').off('change').on('change', function() {
            const isChecked = $(this).is(':checked');
            $('.paper-checkbox').prop('checked', isChecked);
            updateSelectionDisplay();
        });

        // 全选/取消全选 - 移动端
        $('#selectAllPapersMobile').off('change').on('change', function() {
            const isChecked = $(this).is(':checked');
            $('.paper-checkbox-mobile').prop('checked', isChecked);
            updateSelectionDisplayMobile();
        });

        // 单个checkbox事件 - 桌面端
        $('.paper-checkbox').off('change').on('change', function() {
            updateSelectionDisplay();
            // 更新全选状态
            const totalCheckboxes = $('.paper-checkbox').length;
            const checkedCheckboxes = $('.paper-checkbox:checked').length;
            $('#selectAllPapers').prop('checked', totalCheckboxes === checkedCheckboxes);
        });

        // 单个checkbox事件 - 移动端
        $('.paper-checkbox-mobile').off('change').on('change', function() {
            updateSelectionDisplayMobile();
            // 更新全选状态
            const totalCheckboxes = $('.paper-checkbox-mobile').length;
            const checkedCheckboxes = $('.paper-checkbox-mobile:checked').length;
            $('#selectAllPapersMobile').prop('checked', totalCheckboxes === checkedCheckboxes);
        });
    }

    // 更新选择状态显示 - 桌面端
    function updateSelectionDisplay() {
        const selectedCount = $('.paper-checkbox:checked').length;
        $('#selectedCount').text(selectedCount);
        
        // 始终显示批量操作工具栏，但根据选择状态启用/禁用按钮
        $('#batchOperationToolbar').show();
        
        if (selectedCount === 0) {
            $('#batchDownloadBtn').prop('disabled', true).attr('title', '请先选择要下载的试卷');
            $('#batchDeleteBtn').prop('disabled', true).attr('title', '请先选择要删除的试卷');
            $('#cancelSelectionBtn').prop('disabled', true).attr('title', '没有选择任何试卷');
        } else {
            $('#batchDownloadBtn').prop('disabled', false).attr('title', `批量下载选中的 ${selectedCount} 个试卷`);
            $('#batchDeleteBtn').prop('disabled', false).attr('title', `批量删除选中的 ${selectedCount} 个试卷`);
            $('#cancelSelectionBtn').prop('disabled', false).attr('title', '取消选择');
        }
    }

    // 更新选择状态显示 - 移动端
    function updateSelectionDisplayMobile() {
        const selectedCount = $('.paper-checkbox-mobile:checked').length;
        $('#selectedCountMobile').text(selectedCount);
        
        // 始终显示批量操作工具栏，但根据选择状态启用/禁用按钮
        $('#batchOperationToolbarMobile').show();
        
        if (selectedCount === 0) {
            $('#batchDownloadBtnMobile').prop('disabled', true).attr('title', '请先选择要下载的试卷');
            $('#batchDeleteBtnMobile').prop('disabled', true).attr('title', '请先选择要删除的试卷');
            $('#cancelSelectionBtnMobile').prop('disabled', true).attr('title', '没有选择任何试卷');
        } else {
            $('#batchDownloadBtnMobile').prop('disabled', false).attr('title', `批量下载选中的 ${selectedCount} 个试卷`);
            $('#batchDeleteBtnMobile').prop('disabled', false).attr('title', `批量删除选中的 ${selectedCount} 个试卷`);
            $('#cancelSelectionBtnMobile').prop('disabled', false).attr('title', '取消选择');
        }
    }

    // 隐藏批量操作工具栏
    function hideBatchOperationToolbar() {
        $('#batchOperationToolbar').hide();
        $('#batchOperationToolbarMobile').hide();
        $('#selectAllPapers').prop('checked', false);
        $('#selectAllPapersMobile').prop('checked', false);
    }

    // 显示批量操作工具栏
    function showBatchOperationToolbar() {
        $('#batchOperationToolbar').show();
        $('#batchOperationToolbarMobile').show();
        
        // 初始状态：没有选择任何试卷，按钮禁用
        $('#selectedCount').text('0');
        $('#selectedCountMobile').text('0');
        
        // 禁用按钮并设置提示信息
        $('#batchDownloadBtn').prop('disabled', true).attr('title', '请先选择要下载的试卷');
        $('#batchDeleteBtn').prop('disabled', true).attr('title', '请先选择要删除的试卷');
        $('#cancelSelectionBtn').prop('disabled', true).attr('title', '没有选择任何试卷');
        $('#batchDownloadBtnMobile').prop('disabled', true).attr('title', '请先选择要下载的试卷');
        $('#batchDeleteBtnMobile').prop('disabled', true).attr('title', '请先选择要删除的试卷');
        $('#cancelSelectionBtnMobile').prop('disabled', true).attr('title', '没有选择任何试卷');
    }

    // 获取选中的试卷ID列表
    function getSelectedPaperIds(isMobile = false) {
        const checkboxClass = isMobile ? '.paper-checkbox-mobile:checked' : '.paper-checkbox:checked';
        const selectedIds = [];
        $(checkboxClass).each(function() {
            selectedIds.push($(this).val());
        });
        return selectedIds;
    }

    // 批量下载试卷
    function batchDownloadPapers(isMobile = false) {
        const selectedIds = getSelectedPaperIds(isMobile);
        
        if (selectedIds.length === 0) {
            Swal.fire('提示', '请先选择要下载的试卷', 'warning');
            return;
        }

        // 更新模态框中的数量显示
        $('#batchDownloadCount').text(selectedIds.length);
        
        // 显示批量下载模态框
        $('#batchDownloadModal').modal('show');
    }

    // 批量删除试卷
    function batchDeletePapers(isMobile = false) {
        const selectedIds = getSelectedPaperIds(isMobile);
        
        if (selectedIds.length === 0) {
            Swal.fire('提示', '请先选择要删除的试卷', 'warning');
            return;
        }

        Swal.fire({
            title: '确认批量删除',
            html: `确定要删除选中的 <strong>${selectedIds.length}</strong> 个试卷吗？<br><small class="text-muted">此操作不可撤销</small>`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash-alt"></i> 确认删除',
            cancelButtonText: '<i class="fas fa-times"></i> 取消'
        }).then((result) => {
            if (result.isConfirmed) {
                // 在批量删除前准备干净的token环境
                const validToken = prepareDownloadEnvironment();
                if (!validToken) {
                    Swal.fire({
                        title: '删除失败',
                        text: '认证失效，请重新登录后再试',
                        icon: 'error',
                        confirmButtonText: '确定'
                    });
                    return;
                }
                
                // 显示删除进度
                Swal.fire({
                    title: '删除中...',
                    text: `正在删除 ${selectedIds.length} 个试卷`,
                    icon: 'info',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: '/api/papers/batch-delete',
                    method: 'DELETE',
                    contentType: 'application/json',
                    headers: {
                        'Authorization': 'Bearer ' + validToken
                    },
                    data: JSON.stringify({ paperIds: selectedIds }),
                    success: function(response) {
                        if (response && response.success) {
                            Swal.fire({
                                title: '删除成功',
                                text: `已成功删除 ${selectedIds.length} 个试卷`,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            });

                            // 重新加载历史试卷列表
                            loadPaperHistory(currentHistoryPage);
                            
                            // 隐藏批量操作工具栏
                            hideBatchOperationToolbar();
                        } else {
                            Swal.fire({
                                title: '删除失败',
                                text: response.message || '批量删除时发生错误',
                                icon: 'error',
                                confirmButtonText: '确定'
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: '删除失败',
                            text: '服务器连接错误',
                            icon: 'error',
                            confirmButtonText: '确定'
                        });
                    }
                });
            }
        });
    }

    // 取消选择
    function cancelSelection(isMobile = false) {
        if (isMobile) {
            $('.paper-checkbox-mobile').prop('checked', false);
            $('#selectAllPapersMobile').prop('checked', false);
            updateSelectionDisplayMobile();
        } else {
            $('.paper-checkbox').prop('checked', false);
            $('#selectAllPapers').prop('checked', false);
            updateSelectionDisplay();
        }
    }

    // 初始化批量操作按钮事件
    function initBatchOperationEvents() {
        // 桌面端批量操作按钮
        $('#batchDownloadBtn').off('click').on('click', function() {
            batchDownloadPapers(false);
        });

        $('#batchDeleteBtn').off('click').on('click', function() {
            batchDeletePapers(false);
        });

        $('#cancelSelectionBtn').off('click').on('click', function() {
            cancelSelection(false);
        });

        // 移动端批量操作按钮
        $('#batchDownloadBtnMobile').off('click').on('click', function() {
            batchDownloadPapers(true);
        });

        $('#batchDeleteBtnMobile').off('click').on('click', function() {
            batchDeletePapers(true);
        });

        $('#cancelSelectionBtnMobile').off('click').on('click', function() {
            cancelSelection(true);
        });

        // 确认批量下载按钮
        $('#confirmBatchDownload').off('click').on('click', function() {
            const format = $('input[name="batchFormat"]:checked').val();
            const version = $('#batchPaperVersion').val();
            const selectedIds = getSelectedPaperIds(false) || getSelectedPaperIds(true);
            
            if (selectedIds.length === 0) {
                Swal.fire('错误', '没有选中的试卷', 'error');
                return;
            }

            // 关闭模态框
            $('#batchDownloadModal').modal('hide');

            // 开始批量下载
            performBatchDownload(selectedIds, format, version);
        });
    }

    // 执行批量下载
    function performBatchDownload(paperIds, format, version) {
        // 在批量下载前准备干净的token环境
        const validToken = prepareDownloadEnvironment();
        if (!validToken) {
            Swal.fire({
                title: '下载失败',
                text: '认证失效，请重新登录后再试',
                icon: 'error',
                confirmButtonText: '确定'
            });
            return;
        }
        
        // 显示下载进度
        Swal.fire({
            title: '准备下载...',
            text: `正在打包 ${paperIds.length} 个试卷`,
            icon: 'info',
            allowOutsideClick: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // 发送批量下载请求（使用XMLHttpRequest处理二进制文件）
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/api/papers/batch-download', true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.setRequestHeader('Authorization', 'Bearer ' + validToken);
        xhr.responseType = 'blob';
        
        xhr.onload = function() {
            if (xhr.status === 200) {
                Swal.fire({
                    title: '下载准备完成',
                    text: '文件已打包完成，开始下载',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });

                // 创建下载链接
                const blob = xhr.response;
                const url = window.URL.createObjectURL(blob);
                
                // 从响应头获取文件名
                let filename = `试卷批量下载_${format}_${new Date().toISOString().slice(0, 10)}.zip`;
                const contentDisposition = xhr.getResponseHeader('Content-Disposition');
                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
                    if (filenameMatch && filenameMatch[1]) {
                        filename = filenameMatch[1].replace(/['"]/g, '');
                    }
                }
                
                const link = document.createElement('a');
                link.href = url;
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                // 清理blob URL
                window.URL.revokeObjectURL(url);

                // 取消选择
                cancelSelection(false);
                cancelSelection(true);
            } else {
                Swal.fire({
                    title: '下载失败',
                    text: '生成下载文件时发生错误',
                    icon: 'error',
                    confirmButtonText: '确定'
                });
            }
        };
        
        xhr.onerror = function() {
            Swal.fire({
                title: '下载失败',
                text: '服务器连接错误',
                icon: 'error',
                confirmButtonText: '确定'
            });
        };
        
        // 发送请求
        xhr.send(JSON.stringify({
            paperIds: paperIds,
            format: format,
            version: version
        }));
    }

    // 渲染分页
    function renderPagination(data) {
        const totalPages = data.totalPages || 0;
        const currentPage = data.number || 0;

        if (totalPages <= 1) {
            $('#paperHistoryPagination').empty();
            return;
        }

        let html = `<ul class="pagination pagination-sm justify-content-center mb-0">`;

        // 首页按钮
        html += `
            <li class="page-item ${currentPage === 0 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="0" title="首页">
                    <i class="fas fa-angle-double-left"></i>
                </a>
            </li>
        `;

        // 上一页按钮
        html += `
            <li class="page-item ${currentPage === 0 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${currentPage - 1}" title="上一页">
                    <i class="fas fa-angle-left"></i>
                </a>
            </li>
        `;

        // 智能分页：只显示当前页附近的页码
        const maxVisiblePages = 5; // 最多显示5个页码
        let startPage = Math.max(0, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 1);

        // 调整起始页，确保显示足够的页码
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(0, endPage - maxVisiblePages + 1);
        }

        // 如果起始页不是第一页，显示省略号
        if (startPage > 0) {
            html += `
                <li class="page-item">
                    <a class="page-link" href="#" data-page="0">1</a>
                </li>
            `;
            if (startPage > 1) {
                html += `
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                `;
            }
        }

        // 显示页码
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i + 1}</a>
                </li>
            `;
        }

        // 如果结束页不是最后一页，显示省略号
        if (endPage < totalPages - 1) {
            if (endPage < totalPages - 2) {
                html += `
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                `;
            }
            html += `
                <li class="page-item">
                    <a class="page-link" href="#" data-page="${totalPages - 1}">${totalPages}</a>
                </li>
            `;
        }

        // 下一页按钮
        html += `
            <li class="page-item ${currentPage === totalPages - 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${currentPage + 1}" title="下一页">
                    <i class="fas fa-angle-right"></i>
                </a>
            </li>
        `;

        // 末页按钮
        html += `
            <li class="page-item ${currentPage === totalPages - 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${totalPages - 1}" title="末页">
                    <i class="fas fa-angle-double-right"></i>
                </a>
            </li>
        `;

        html += `</ul>`;

        $('#paperHistoryPagination').html(html);
        $('#paperHistoryPaginationMobile').html(html);

        // 绑定分页事件
        $('#paperHistoryPagination .page-link, #paperHistoryPaginationMobile .page-link').on('click', function(e) {
            e.preventDefault();

            if ($(this).parent().hasClass('disabled')) {
                return;
            }

            const page = $(this).data('page');
            if (page !== undefined && page !== null) {
                loadPaperHistory(page);
            }
        });
    }

    // 删除试卷
    function deletePaper(paperId, paperTitle) {
        Swal.fire({
            title: '确认删除',
            html: `确定要删除试卷 "<strong>${paperTitle}</strong>" 吗？<br><small class="text-muted">此操作不可撤销</small>`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash-alt"></i> 确认删除',
            cancelButtonText: '<i class="fas fa-times"></i> 取消',
            customClass: {
                popup: 'delete-confirm-modal'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // 显示删除进度
                Swal.fire({
                    title: '删除中...',
                    text: '正在删除试卷，请稍候',
                    icon: 'info',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: `/api/papers/${paperId}`,
                    method: 'DELETE',
                    success: function(response) {
                        if (response && response.success) {
                            Swal.fire({
                                title: '删除成功',
                                text: `试卷 "${paperTitle}" 已成功删除`,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            });

                            // 重新加载历史试卷列表
                            loadPaperHistory(currentHistoryPage);
                        } else {
                            Swal.fire({
                                title: '删除失败',
                                text: response.message || '删除试卷时发生错误',
                                icon: 'error',
                                confirmButtonText: '确定'
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: '删除失败',
                            text: '服务器连接错误',
                            icon: 'error',
                            confirmButtonText: '确定'
                        });
                    }
                });
            }
        });
    }

    // 预览试卷
    window.previewPaper = function(paperId) {
        window.open(`/papers/preview/${paperId}`, '_blank');
    };

    // 显示难度分布
    function showDifficultyDistribution(paperId) {
        // 显示加载状态
        Swal.fire({
            title: '加载中...',
            text: '正在获取试卷难度分布信息',
            allowOutsideClick: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        $.ajax({
            url: `/api/papers/${paperId}/difficulty-distribution`,
            method: 'GET',
            success: function(response) {
                if (response && response.success && response.data) {
                    displayDifficultyDistributionModal(response.data);
                } else {
                    Swal.fire({
                        title: '获取失败',
                        text: '无法获取试卷难度分布信息',
                        icon: 'error',
                        confirmButtonText: '确定'
                    });
                }
            },
            error: function() {
                Swal.fire({
                    title: '获取失败',
                    text: '服务器连接错误',
                    icon: 'error',
                    confirmButtonText: '确定'
                });
            }
        });
    }

    // 显示难度分布模态框
    function displayDifficultyDistributionModal(data) {
        const { easy = [], medium = [], hard = [], paperTitle = '试卷' } = data;

        let easyHtml = '';
        let mediumHtml = '';
        let hardHtml = '';

        // 生成简单题目列表
        if (easy.length > 0) {
            easy.forEach((question, index) => {
                easyHtml += `
                    <div class="question-item mb-2 p-2 border rounded">
                        <span class="badge badge-success mr-2">第${index + 1}题</span>
                        <span class="badge badge-secondary mr-2">${getQuestionTypeText(question.type)}</span>
                        <span class="question-title">${question.title || '题目内容'}</span>
                    </div>
                `;
            });
        } else {
            easyHtml = '<div class="text-muted text-center py-3">无简单题目</div>';
        }

        // 生成中等题目列表
        if (medium.length > 0) {
            medium.forEach((question, index) => {
                mediumHtml += `
                    <div class="question-item mb-2 p-2 border rounded">
                        <span class="badge badge-warning mr-2">第${easy.length + index + 1}题</span>
                        <span class="badge badge-secondary mr-2">${getQuestionTypeText(question.type)}</span>
                        <span class="question-title">${question.title || '题目内容'}</span>
                    </div>
                `;
            });
        } else {
            mediumHtml = '<div class="text-muted text-center py-3">无中等题目</div>';
        }

        // 生成困难题目列表
        if (hard.length > 0) {
            hard.forEach((question, index) => {
                hardHtml += `
                    <div class="question-item mb-2 p-2 border rounded">
                        <span class="badge badge-danger mr-2">第${easy.length + medium.length + index + 1}题</span>
                        <span class="badge badge-secondary mr-2">${getQuestionTypeText(question.type)}</span>
                        <span class="question-title">${question.title || '题目内容'}</span>
                    </div>
                `;
            });
        } else {
            hardHtml = '<div class="text-muted text-center py-3">无困难题目</div>';
        }

        const modalHtml = `
            <div class="modal fade" id="difficultyDistributionModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-info text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-chart-bar mr-2"></i>难度分布 - ${paperTitle}
                            </h5>
                            <button type="button" class="close text-white" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-3">
                                <div class="col-md-4 text-center">
                                    <div class="difficulty-stat">
                                        <div class="stat-number text-success">${easy.length}</div>
                                        <div class="stat-label">简单题目</div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="difficulty-stat">
                                        <div class="stat-number text-warning">${medium.length}</div>
                                        <div class="stat-label">中等题目</div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="difficulty-stat">
                                        <div class="stat-number text-danger">${hard.length}</div>
                                        <div class="stat-label">困难题目</div>
                                    </div>
                                </div>
                            </div>

                            <ul class="nav nav-tabs" id="difficultyTabs" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" id="easy-tab" data-toggle="tab" href="#easy" role="tab">
                                        <i class="fas fa-smile text-success"></i> 简单 (${easy.length})
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="medium-tab" data-toggle="tab" href="#medium" role="tab">
                                        <i class="fas fa-meh text-warning"></i> 中等 (${medium.length})
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="hard-tab" data-toggle="tab" href="#hard" role="tab">
                                        <i class="fas fa-frown text-danger"></i> 困难 (${hard.length})
                                    </a>
                                </li>
                            </ul>

                            <div class="tab-content mt-3" id="difficultyTabContent">
                                <div class="tab-pane fade show active" id="easy" role="tabpanel">
                                    <div class="difficulty-questions" style="max-height: 400px; overflow-y: auto;">
                                        ${easyHtml}
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="medium" role="tabpanel">
                                    <div class="difficulty-questions" style="max-height: 400px; overflow-y: auto;">
                                        ${mediumHtml}
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="hard" role="tabpanel">
                                    <div class="difficulty-questions" style="max-height: 400px; overflow-y: auto;">
                                        ${hardHtml}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">
                                <i class="fas fa-times mr-1"></i>关闭
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        $('#difficultyDistributionModal').remove();

        // 添加新的模态框到页面
        $('body').append(modalHtml);

        // 显示模态框
        $('#difficultyDistributionModal').modal('show');

        // 关闭SweetAlert
        Swal.close();
    }

    // 获取题目类型文本
    function getQuestionTypeText(type) {
        const typeMap = {
            'choice': '单选题',
            'multiple': '多选题',
            'judge': '判断题',
            'fill': '填空题',
            'short': '简答题',
            'subjective': '主观题',
            'group': '题组题'
        };
        return typeMap[type] || '未知类型';
    }

    // 下载试卷
    function downloadPaper(paperId, format) {
        // 显示版本选择对话框
        Swal.fire({
            title: '选择试卷版本',
            html: `
                <div class="form-group">
                    <label for="paperVersion">试卷版本：</label>
                    <select class="form-control" id="paperVersion">
                        <option value="standard">标准版 - 题目+答案+解析</option>
                        <option value="regular">学生版 - 只有题目</option>
                        <option value="teacher">教师版 - 只有答案和解析</option>
                    </select>
                </div>
                <small class="text-muted">
                    <i class="fas fa-info-circle mr-1"></i>
                    选择适合的版本进行下载
                </small>
            `,
            showCancelButton: true,
            confirmButtonText: `<i class="fas fa-download mr-1"></i>下载${format.toUpperCase()}`,
            cancelButtonText: '<i class="fas fa-times mr-1"></i>取消',
            preConfirm: () => {
                const version = document.getElementById('paperVersion').value;
                return version;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                const version = result.value;
                
                // 在下载前准备干净的token环境
                const validToken = prepareDownloadEnvironment();
                if (!validToken) {
                    Swal.fire({
                        title: '下载失败',
                        text: '认证失效，请重新登录后再试',
                        icon: 'error',
                        confirmButtonText: '确定'
                    });
                    return;
                }
                
                // 显示下载进度
                Swal.fire({
                    title: '准备下载...',
                    text: '正在生成试卷文件',
                    icon: 'info',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // 使用POST请求来确保请求体正确传递
                $.ajax({
                    url: `/api/papers/download/${paperId}`,
                    method: 'POST',
                    contentType: 'application/json',
                    headers: {
                        'Authorization': 'Bearer ' + validToken
                    },
                    data: JSON.stringify({
                        format: format,
                        paperType: version
                    }),
                    success: function(response) {
                        if (response && response.success && response.downloadUrl) {
                            Swal.fire({
                                title: '下载开始',
                                text: `${format.toUpperCase()}文件准备完成`,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false,
                                toast: true,
                                position: 'top-end'
                            });

                            // 创建下载链接，添加token参数
                            const downloadUrl = response.downloadUrl + (response.downloadUrl.includes('?') ? '&' : '?') + 'token=' + encodeURIComponent(validToken);
                            
                            const link = document.createElement('a');
                            link.href = downloadUrl;
                            link.download = response.filename || `试卷_${paperId}.${format === 'word' ? 'docx' : 'pdf'}`;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                        } else {
                            // 如果没有返回downloadUrl，尝试直接下载
                            const downloadUrl = `/api/papers/download/${paperId}?format=${format}&paperType=${version}&token=${encodeURIComponent(validToken)}`;
                            
                            // 创建下载链接
                            const link = document.createElement('a');
                            link.href = downloadUrl;
                            link.download = '';
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);

                            Swal.fire({
                                title: '下载开始',
                                text: `${format.toUpperCase()}文件下载已开始`,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false,
                                toast: true,
                                position: 'top-end'
                            });
                        }
                    },
                    error: function(xhr) {
                        // 如果POST失败，尝试GET请求作为后备方案
                        if (xhr.status === 405 || xhr.status === 404) {
                            const downloadUrl = `/api/papers/download/${paperId}?format=${format}&paperType=${version}&token=${encodeURIComponent(validToken)}`;
                            
                            // 创建下载链接
                            const link = document.createElement('a');
                            link.href = downloadUrl;
                            link.download = '';
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);

                            Swal.fire({
                                title: '下载开始',
                                text: `${format.toUpperCase()}文件下载已开始`,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false,
                                toast: true,
                                position: 'top-end'
                            });
                        } else {
                            Swal.fire({
                                title: '下载失败',
                                text: '服务器连接错误或文件生成失败',
                                icon: 'error',
                                confirmButtonText: '确定'
                            });
                        }
                    }
                });
            }
        });
    }

    // 批量删除试卷
    function clearAllHistory() {
        Swal.fire({
            title: '确认清空历史',
            html: '确定要清空所有历史试卷吗？<br><small class="text-muted">此操作将删除所有试卷记录，不可撤销</small>',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash-alt"></i> 确认清空',
            cancelButtonText: '<i class="fas fa-times"></i> 取消',
            customClass: {
                popup: 'delete-confirm-modal'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // 显示加载状态
                Swal.fire({
                    title: '清空中...',
                    text: '正在清空历史记录，请稍候',
                    icon: 'info',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: '/api/papers/clear-history',
                    method: 'DELETE',
                    success: function(response) {
                        if (response && response.success) {
                            Swal.fire({
                                title: '清空成功',
                                text: '所有历史试卷已成功清空',
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            });

                            // 重新加载历史试卷列表
                            loadPaperHistory(0);
                        } else {
                            Swal.fire({
                                title: '清空失败',
                                text: response.message || '清空历史记录时发生错误',
                                icon: 'error',
                                confirmButtonText: '确定'
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: '清空失败',
                            text: '服务器连接错误',
                            icon: 'error',
                            confirmButtonText: '确定'
                        });
                    }
                });
            }
        });
    }

    // 加载知识点分类
    function loadKnowledgeGroups() {
        $.ajax({
            url: '/api/knowledge/groups',
            method: 'GET',
            success: function(response) {
                if (response && response.success && response.data) {
                    renderKnowledgeGroups(response.data);
                } else {
                    showError('加载失败', '无法获取知识点分类');
                }
            },
            error: function() {
                showError('加载失败', '服务器连接错误');
            }
        });
    }

    // 渲染知识点分类
    function renderKnowledgeGroups(groups) {
        let html = '';
        if (groups && groups.length > 0) {
            groups.forEach(function(group) {
                html += `
                    <div class="group-item" data-id="${group.id}" data-name="${group.groupName}">
                        ${group.groupName}
                        <span class="badge badge-pill badge-light">${group.count || 0}</span>
                    </div>
                `;
            });
        } else {
            html = '<div class="text-center py-3 text-muted">暂无知识点分类</div>';
        }
        $('#knowledge-groups').html(html);

        // 绑定点击事件
        $('.group-item').on('click', function() {
            $('.group-item').removeClass('active');
            $(this).addClass('active');

            const groupId = $(this).data('id');
            const groupName = $(this).data('name');

            loadKnowledgePoints(groupId, groupName);
        });
    }

    // 加载知识点
    function loadKnowledgePoints(groupId, groupName) {
        $('#knowledge-points-container').html(`
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">加载中...</span>
                </div>
                <p class="mt-2 text-muted">加载 ${groupName} 知识点...</p>
            </div>
        `);

        $.ajax({
            url: `/api/knowledge/points?groupId=${groupId}`,
            method: 'GET',
            success: function(response) {
                if (response && response.success && response.data) {
                    renderKnowledgePoints(response.data, groupName);
                } else {
                    $('#knowledge-points-container').html(`
                        <div class="text-center py-5 text-muted">
                            <i class="fas fa-exclamation-circle fa-3x mb-3"></i>
                            <p>加载知识点失败</p>
                        </div>
                    `);
                }
            },
            error: function() {
                $('#knowledge-points-container').html(`
                    <div class="text-center py-5 text-muted">
                        <i class="fas fa-exclamation-circle fa-3x mb-3"></i>
                        <p>服务器连接错误</p>
                    </div>
                `);
            }
        });
    }

    // 渲染知识点列表
    function renderKnowledgePoints(points, groupName) {
        if (!points || points.length === 0) {
            $('#knowledge-points-container').html(`
                <div class="text-center py-5 text-muted">
                    <i class="fas fa-info-circle fa-3x mb-3"></i>
                    <p>${escapeHtml(groupName)} 分类下暂无知识点</p>
                </div>
            `);
            return;
        }

        let html = '';
        points.forEach(function(point, index) {
            // 检查是否已经选中（从全局Map中获取状态）
            const isChecked = selectedKnowledgePoints.has(point.id.toString());

            html += `
                <div class="card knowledge-point-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title">${escapeHtml(point.name)}</h5>
                            <div class="form-check">
                                <input class="form-check-input knowledge-checkbox" type="checkbox" value="${point.id}" id="kp-${point.id}" ${isChecked ? 'checked' : ''}>
                                <label class="form-check-label" for="kp-${point.id}">
                                    选择
                                </label>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <div>
                                <span class="badge badge-info">题目: ${point.topicCount || 0}</span>
                                ${point.isFree ? '<span class="badge badge-success">免费</span>' : ''}
                                <span class="badge badge-primary">ID: ${point.id}</span>
                                <span class="badge badge-secondary">知识点ID: ${point.knowledgeId || '未知'}</span>
                            </div>
                            <button class="btn btn-sm btn-primary generate-single-btn"
                                data-id="${point.id}"
                                data-know-id="${point.knowledgeId || point.id}"
                                data-name="${escapeHtml(point.name)}">
                                <i class="fas fa-magic mr-1"></i>出题
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });

        $('#knowledge-points-container').html(html);

        // 注意：不在这里绑定事件，因为已经有全局事件监听器了

        // 单个知识点出题按钮
        $('.generate-single-btn').on('click', function() {
            const knowledgeId = $(this).data('id');
            const knowId = $(this).data('know-id') || knowledgeId; // 优先使用know-id
            const knowledgeName = $(this).data('name');

            // 创建单个知识点配置
            const knowledgeConfig = [{
                knowledgeId: knowId, // 使用正确的知识点ID
                questionCount: 10,
                includeShortAnswer: false // 默认关闭简答题开关
            }];

            // 打开试卷生成模态框
            openPaperGenerationModal(knowledgeConfig, knowledgeName + ' 专项练习');
        });
    }

    // 更新选中计数
    function updateSelectionCounter() {
        const selectedCount = selectedKnowledgePoints.size;
        $('.selectedCount').text(selectedCount);  // 使用类选择器更新所有计数显示

        // 启用/禁用生成按钮
        const isDisabled = selectedCount === 0;
        $('#generatePaperBtn').prop('disabled', isDisabled);
        $('#generatePaperBtnBottom').prop('disabled', isDisabled);
        $('#clearAllKnowledgePointsBtn').prop('disabled', isDisabled);
        $('.left-clear-btn, .left-generate-btn').prop('disabled', isDisabled);

        // 显示或隐藏已选择知识点区域
        if (selectedCount > 0) {
            // 右侧区域显示
            $('#selected-knowledge-points-card').show();
            $('#no-selected-points-message').hide();
            $('.selected-points-toolbar').show();

            // 左侧区域显示（移动设备）
            $('#left-selected-knowledge-points-card').show();
            $('#left-no-selected-points-message').hide();

            // 如果选择了知识点，显示生成试卷按钮的提示
            if (!$('#generatePaperHint').length) {
                $('#generatePaperBtn').tooltip({
                    title: '点击生成试卷',
                    placement: 'bottom',
                    trigger: 'hover'
                });

                // 添加一个提示动画
                $('#generatePaperBtn').addClass('btn-pulse');
                setTimeout(() => {
                    $('#generatePaperBtn').removeClass('btn-pulse');
                }, 2000);
            }

            // 更新知识点统计
            updateKnowledgeStats();
        } else {
            $('#no-selected-points-message').show();
            $('.selected-points-toolbar').hide();
            if ($('#selected-knowledge-points-card').is(':visible')) {
                // 如果卡片已经显示，保持显示状态，只显示提示信息
                $('#selected-knowledge-points-container').empty();
            } else {
                $('#selected-knowledge-points-card').hide();
            }

            // 重置知识点统计
            $('#totalSelectedKnowledgePoints').text('0');
            $('#totalAvailableQuestions').text('0');
        }

        // 更新已选择知识点区域
        updateSelectedKnowledgePointsDisplay();
    }

    // 更新知识点统计
    function updateKnowledgeStats() {
        const count = selectedKnowledgePoints.size;

        // 更新知识点数量
        $('#totalSelectedKnowledgePoints').text(count);

        // 计算可用题目总数
        let totalQuestions = 0;
        selectedKnowledgePoints.forEach(function(pointData) {
            // 从全局状态中获取题目数量
            let topicCount = 0;
            const topicCountText = pointData.topicCount || '';

            if (typeof topicCountText === 'string') {
                if (topicCountText.includes('题目:')) {
                    topicCount = parseInt(topicCountText.replace('题目:', '').trim()) || 0;
                } else if (topicCountText.includes('题')) {
                    topicCount = parseInt(topicCountText.replace('题', '').trim()) || 0;
                } else {
                    topicCount = parseInt(topicCountText) || 0;
                }
            } else {
                topicCount = parseInt(topicCountText) || 0;
            }

            totalQuestions += topicCount;
        });

        $('#totalAvailableQuestions').text(totalQuestions);
    }

    // 更新已选择知识点显示
    function updateSelectedKnowledgePointsDisplay() {
        const container = $('#selected-knowledge-points-container');

        // 清空容器
        container.empty();

        // 基于全局状态而不是当前可见的复选框
        if (selectedKnowledgePoints.size === 0) {
            return;
        }

        // 显示工具栏
        $('.selected-points-toolbar').show();

        // 添加每个选中的知识点（基于全局状态）
        selectedKnowledgePoints.forEach(function(pointData, pointId) {
            const knowledgeId = pointId;
            const knowledgeName = pointData.name;
            const topicCount = pointData.topicCount;
            const isFree = pointData.isFree;

            // 使用存储在全局状态中的知识点ID，不要从当前页面动态获取
            // 这样可以确保知识点ID的稳定性，不会因为用户切换页面而改变
            let knowId = pointData.knowId || pointId; // 优先使用存储的knowId，否则使用pointId

            // 只有在全局状态中没有存储knowId时，才尝试从当前页面获取（仅限首次选择）
            if (!pointData.knowId) {
                const currentCheckbox = $(`#kp-${pointId}`);
                if (currentCheckbox.length > 0) {
                    // 如果当前页面有这个知识点，从页面获取know_id并存储到全局状态
                    const card = currentCheckbox.closest('.card');
                    const knowledgeIdBadge = card.find('.badge-secondary').text();

                    if (knowledgeIdBadge && knowledgeIdBadge.includes('知识点ID:')) {
                        const extractedId = knowledgeIdBadge.replace('知识点ID:', '').trim();
                        if (!isNaN(extractedId)) {
                            knowId = parseInt(extractedId);
                            // 将knowId存储到全局状态中，避免后续重复获取
                            pointData.knowId = knowId;
                            selectedKnowledgePoints.set(pointId, pointData);
                            console.log(`首次获取并存储知识点ID: ${pointData.name} (ID: ${pointId}, knowId: ${knowId})`);
                        }
                    }
                }
            }

            const html = `
                <div class="col-md-6 col-lg-4 selected-knowledge-point fade-in"
                    data-id="${knowledgeId}"
                    data-know-id="${knowId}"
                    data-name="${escapeHtml(knowledgeName)}"
                    data-count="${topicCount}">
                    <div class="card mb-3">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="card-title mb-0 text-truncate" title="${knowledgeName}" style="max-width: 80%;">${knowledgeName}</h5>
                                <button type="button" class="remove-btn" data-id="${knowledgeId}" title="移除此知识点">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="mb-2">
                                <span class="badge badge-info"><i class="fas fa-list-ol mr-1"></i>${topicCount} 题</span>
                                ${isFree ? '<span class="badge badge-success"><i class="fas fa-unlock mr-1"></i>免费</span>' : ''}
                                <span class="badge badge-primary"><i class="fas fa-hashtag mr-1"></i>ID: ${knowledgeId}</span>
                                <span class="badge badge-secondary"><i class="fas fa-key mr-1"></i>知识点ID: ${knowId}</span>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-primary btn-block generate-single-btn"
                                data-id="${knowledgeId}"
                                data-know-id="${knowId}"
                                data-name="${escapeHtml(knowledgeName)}">
                                <i class="fas fa-magic mr-1"></i>单独出题
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // 添加到容器
            container.append(html);
        });

        // 使用事件委托绑定移除按钮事件（修复动态添加元素的事件绑定问题）
        $(document).off('click', '.selected-knowledge-point .remove-btn').on('click', '.selected-knowledge-point .remove-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const knowledgeId = $(this).data('id');
            const knowledgeName = $(this).closest('.selected-knowledge-point').data('name') || '未知知识点';

            console.log(`点击删除按钮: knowledgeId=${knowledgeId}, name=${knowledgeName}`);

            // 显示确认对话框
            Swal.fire({
                title: '移除知识点',
                html: `确定要移除知识点 <strong>${escapeHtml(knowledgeName)}</strong> 吗？`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: '移除',
                cancelButtonText: '取消',
                confirmButtonColor: '#dc3545'
            }).then((result) => {
                if (result.isConfirmed) {
                    // 取消选中对应的复选框（支持多种ID格式）
                    const checkboxSelectors = [
                        `#kp-${knowledgeId}`,           // 原来的知识点列表
                        `#search-kp-${knowledgeId}`,   // 搜索结果中的知识点
                        `input[value="${knowledgeId}"].knowledge-checkbox` // 通用选择器
                    ];

                    let checkboxFound = false;
                    checkboxSelectors.forEach(selector => {
                        const $checkbox = $(selector);
                        if ($checkbox.length > 0) {
                            $checkbox.prop('checked', false);
                            checkboxFound = true;
                            console.log(`找到并取消选中复选框: ${selector}`);
                        }
                    });

                    if (!checkboxFound) {
                        console.warn(`未找到知识点 ${knowledgeId} 对应的复选框`);
                    }

                    // 从全局状态中移除（支持多种ID类型）
                    const idsToTry = [
                        knowledgeId,                    // 原始ID
                        knowledgeId.toString(),         // 字符串类型
                        parseInt(knowledgeId)           // 数字类型
                    ];

                    let removed = false;
                    idsToTry.forEach(id => {
                        if (selectedKnowledgePoints.has(id)) {
                            selectedKnowledgePoints.delete(id);
                            console.log(`从全局状态删除知识点: ${id} (类型: ${typeof id})`);
                            removed = true;
                        }
                    });

                    if (!removed) {
                        console.warn(`⚠️ 未在全局状态中找到知识点: ${knowledgeId}，当前状态:`, Array.from(selectedKnowledgePoints.keys()));
                    }

                    // 保存当前按钮的引用，避免在setTimeout中this上下文丢失
                    const $removeBtn = $(this);
                    const $knowledgePoint = $removeBtn.closest('.selected-knowledge-point');

                    console.log('开始移除知识点卡片:', {
                        knowledgeId: knowledgeId,
                        knowledgeName: knowledgeName,
                        cardElements: $knowledgePoint.length,
                        cardHtml: $knowledgePoint.length > 0 ? $knowledgePoint[0].outerHTML.substring(0, 200) + '...' : 'none'
                    });

                    if ($knowledgePoint.length === 0) {
                        console.error('❌ 未找到要移除的知识点卡片元素');
                        Swal.fire({
                            title: '移除失败',
                            text: '未找到要移除的知识点卡片',
                            icon: 'error'
                        });
                        return;
                    }

                    // 添加移除动画
                    $knowledgePoint.addClass('fade-out');

                    setTimeout(() => {
                        // 确保元素仍然存在再移除
                        if ($knowledgePoint.length > 0 && $knowledgePoint.parent().length > 0) {
                            $knowledgePoint.remove();
                            console.log('✅ 知识点卡片已从DOM中移除');
                        } else {
                            console.warn('⚠️ 知识点卡片已经不存在或已被移除');
                        }

                        // 只更新选择计数器，不重新渲染整个列表
                        updateSelectionCounter();

                        // 检查是否还有其他知识点，如果没有则显示空状态
                        if (selectedKnowledgePoints.size === 0) {
                            $('#no-selected-points-message').show();
                            $('.selected-points-toolbar').hide();
                        }

                        // 显示成功提示
                        Swal.fire({
                            title: '已移除',
                            text: `知识点 ${knowledgeName} 已成功移除`,
                            icon: 'success',
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 2000
                        });
                    }, 300);
                }
            });
        });

        // 使用事件委托绑定单独出题按钮事件（修复动态添加元素的事件绑定问题）
        $(document).off('click', '.selected-knowledge-point .generate-single-btn').on('click', '.selected-knowledge-point .generate-single-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const knowledgeId = $(this).data('id');
            const knowId = $(this).data('know-id') || knowledgeId; // 优先使用know-id
            const knowledgeName = $(this).data('name');



            // 创建单个知识点配置
            const knowledgeConfig = [{
                knowledgeId: knowId, // 使用正确的知识点ID
                questionCount: 10,
                includeShortAnswer: false // 默认关闭简答题开关
            }];

            // 打开试卷生成模态框
            openPaperGenerationModal(knowledgeConfig, knowledgeName + ' 专项练习');
        });

        // 绑定搜索过滤事件
        $('#knowledgePointFilter').on('input', function() {
            const searchText = $(this).val().toLowerCase();
            $('.selected-knowledge-point').each(function() {
                const name = $(this).data('name').toLowerCase();
                if (name.includes(searchText)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });

        // 绑定排序事件
        $('.sort-knowledge-points').on('click', function() {
            const sortBy = $(this).data('sort');
            const points = $('.selected-knowledge-point').get();

            points.sort(function(a, b) {
                if (sortBy === 'name') {
                    return $(a).data('name').localeCompare($(b).data('name'));
                } else if (sortBy === 'count') {
                    return parseInt($(b).data('count')) - parseInt($(a).data('count'));
                }
                return 0;
            });

            $.each(points, function(index, point) {
                $(point).appendTo(container);
            });

            // 更新按钮样式
            $('.sort-knowledge-points').removeClass('active');
            $(this).addClass('active');
        });
    }

    // 生成试卷按钮点击事件
    $('#generatePaperBtn, #generatePaperBtnBottom').on('click', function() {
        //  正常流程：如果选择了知识点，直接进入试卷配置
        if (selectedKnowledgePoints.size > 0) {

            proceedWithPaperGeneration();
            return;
        }

        //  特殊流程：没有选择知识点时，检查是否要生成简答题试卷
        console.log('没有选择知识点，检查简答题配置...');

        // 检查页面上是否有简答题配置（这些输入框可能在其他地方）
        const shortAnswerFromPage = parseInt($('#shortAnswerCount').val()) || 0;

        if (shortAnswerFromPage > 0) {
            // 页面上已经配置了简答题，提示用户确认
            Swal.fire({
                icon: 'question',
                title: '确认生成简答题试卷',
                html: `
                    <div class="text-center">
                        <p>您没有选择知识点，但配置了 <strong>${shortAnswerFromPage}</strong> 道简答题</p>
                        <p class="text-muted">系统将从题库中随机选择简答题生成试卷</p>
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>提示：</strong>选择知识点可以生成更精准的试卷
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '确认生成',
                cancelButtonText: '选择知识点',
                confirmButtonColor: '#28a745'
            }).then((result) => {
                if (result.isConfirmed) {
                    // 用户确认生成简答题试卷
                    proceedWithPaperGeneration();
                }
            });
            return;
        }

        //  没有知识点也没有简答题配置，提供选择
        Swal.fire({
            icon: 'info',
            title: '请选择出卷方式',
            html: `
                <div class="text-center">
                    <p class="mb-4">您还没有进行任何配置，请选择以下方式之一：</p>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <i class="fas fa-brain fa-2x text-primary mb-2"></i>
                                    <h5>知识点模式</h5>
                                    <p class="text-muted small">选择知识点生成精准试卷</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-body">
                                    <i class="fas fa-edit fa-2x text-success mb-2"></i>
                                    <h5>简答题模式</h5>
                                    <p class="text-muted small">直接配置简答题数量</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: '配置简答题',
            cancelButtonText: '选择知识点',
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#007bff'
        }).then((result) => {
            if (result.isConfirmed) {
                // 用户选择配置简答题，直接打开配置界面
                proceedWithPaperGeneration();
            } else if (result.dismiss === Swal.DismissReason.cancel) {
                // 用户选择知识点模式，提示选择知识点
                Swal.fire({
                    icon: 'info',
                    title: '请选择知识点',
                    text: '请从左侧选择一个或多个知识点，然后再点击生成试卷',
                    confirmButtonText: '我知道了'
                });
            }
        });
    });

    /**
     *  执行试卷生成流程
     */
    function proceedWithPaperGeneration() {
        // 显示加载中
        Swal.fire({
            title: '准备中...',
            text: '正在准备试卷配置',
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading();
            },
            timer: 800
        });

        // 收集知识点配置
        const knowledgeConfigs = [];
        const knowledgeNames = [];
        const processedIds = new Set(); // 用于防止重复处理

        selectedKnowledgePoints.forEach(function(pointData, pointId) {
            const knowledgeId = parseInt(pointId);

            // 检查是否已经处理过这个ID
            if (processedIds.has(knowledgeId)) {
                console.warn(`跳过重复的知识点ID: ${knowledgeId}`);
                return;
            }
            processedIds.add(knowledgeId);

            const knowledgeName = pointData.name;

            // 解析题目数量
            let topicCount = 0;
            const topicCountText = pointData.topicCount || '';
            if (typeof topicCountText === 'string') {
                if (topicCountText.includes('题目:')) {
                    topicCount = parseInt(topicCountText.replace('题目:', '').trim()) || 0;
                } else if (topicCountText.includes('题')) {
                    topicCount = parseInt(topicCountText.replace('题', '').trim()) || 0;
                } else {
                    topicCount = parseInt(topicCountText) || 0;
                }
            } else {
                topicCount = parseInt(topicCountText) || 0;
            }

            // 默认配置 - 根据题库题量和选择的知识点数量设置默认题量
            let defaultCount = Math.max(2, Math.floor(10 / selectedKnowledgePoints.size));
            if (topicCount > 0) {
                // 根据题库题量设置合理的默认值，但不超过10题
                defaultCount = Math.min(Math.max(Math.ceil(topicCount / 3), defaultCount), 10);
            }

            // 使用存储在全局状态中的knowId，确保ID的一致性
            let knowId = pointData.knowId || knowledgeId;
            console.log(`生成试卷配置: ${knowledgeName} (pointId: ${pointId}, knowledgeId: ${knowledgeId}, knowId: ${knowId})`);

            knowledgeConfigs.push({
                knowledgeId: knowId, // 使用正确的知识点ID（know_id）
                knowledgeName: knowledgeName, // 添加知识点名称
                questionCount: defaultCount,
                includeShortAnswer: false // 默认关闭简答题开关
            });

            knowledgeNames.push(knowledgeName);
        });

        console.log(`生成试卷：选中知识点数量 ${selectedKnowledgePoints.size}，配置数量 ${knowledgeConfigs.length}`);

        //  创建标题 - 支持无知识点的简答题试卷
        let paperTitle = '';
        if (knowledgeNames.length === 0) {
            // 没有知识点，检查是否有简答题配置
            const shortAnswer = parseInt($('#shortAnswerCount').val()) || 0;
            if (shortAnswer > 0) {
                paperTitle = `简答题专项练习（${shortAnswer}题）`;
            } else {
                paperTitle = '自定义试卷';
            }
        } else if (knowledgeNames.length === 1) {
            paperTitle = knowledgeNames[0] + ' 专项练习';
        } else if (knowledgeNames.length <= 3) {
            paperTitle = knowledgeNames.join('+') + ' 组合练习';
        } else {
            paperTitle = knowledgeNames[0] + '等' + knowledgeNames.length + '个知识点 组合练习';
        }

        // 打开试卷生成模态框
        openPaperGenerationModal(knowledgeConfigs, paperTitle);
    }

    // 打开试卷生成模态框
    function openPaperGenerationModal(knowledgeConfigs, paperTitle) {
        // 保存知识点配置
        $('#generatePaperForm').data('knowledgeConfigs', knowledgeConfigs);

        //  清空试卷标题，让用户自己填写
        $('#paperTitle').val('');

        //  设置试卷版本默认为学生版
        $('#paperType').val('regular');

        // 触发版本选择变化事件，更新描述
        if (typeof updateVersionDescription === 'function') {
            const descriptionElement = $('#paperType').closest('.form-group').find('[id$="Description"]');
            updateVersionDescription('regular', descriptionElement);
        }

        // 设置默认值
        $('#easyPercentage').val(30);
        $('#mediumPercentage').val(50);
        $('#hardPercentage').val(20);

        //  每次进入配置页面时，清空所有题目配置
        console.log('清空所有题目配置...');

        // 清空题型数量
        $('#singleChoiceCount').val(0);
        $('#multipleChoiceCount').val(0);
        $('#judgmentCount').val(0);
        $('#fillCount').val(0);
        $('#shortAnswerCount').val(0);

        // 重置题型分值为默认值
        $('#singleChoiceScore').val(2);
        $('#multipleChoiceScore').val(3);
        $('#judgmentScore').val(2);
        $('#fillScore').val(3);
        $('#shortAnswerScore').val(10);

        //  初始化生成套数
        $('#paperCount').val(1);

        // 初始化总分和题目总数为0，然后立即更新
        $('#totalScore').text('0');
        $('#totalQuestions').text('0');

        //  初始化tooltip
        $('[data-toggle="tooltip"]').tooltip();

        // 更新图表
        if (typeof window.updateDifficultyChart === 'function') {
            window.updateDifficultyChart();
        }

        if (typeof window.updateQuestionDistributionChart === 'function') {
            window.updateQuestionDistributionChart();
        }

        if (typeof window.calculateTotalScore === 'function') {
            window.calculateTotalScore();
        }

        if (typeof window.updatePaperPreview === 'function') {
            window.updatePaperPreview();
        }

        // 更新试卷统计
        updatePaperStats();

        // 渲染知识点配置
        renderKnowledgePointsConfig(knowledgeConfigs);

        // 更新题量统计
        updateQuestionCountStats();

        // 显示模态框
        $('#paperGenerationModal').modal('show');
    }

    // 绑定模态框显示事件，确保在显示后更新统计
    $('#paperGenerationModal').on('shown.bs.modal', function() {
        // 延迟一点时间确保DOM完全渲染
        setTimeout(function() {
            updatePaperStats();
            updateQuestionCountStats();
            updateGenerateButtonText(); //  更新按钮文本
        }, 100);
    });

    // 绑定模态框关闭事件，恢复按钮状态
    $('#paperGenerationModal').on('hidden.bs.modal', function() {
        // 恢复生成试卷按钮状态
        const $submitBtn = $('#submitGeneratePaperBtn');
        if ($submitBtn.prop('disabled') || $submitBtn.html().includes('处理中') || $submitBtn.html().includes('生成中')) {
            // 恢复按钮文本和状态
            $submitBtn.html('<i class="fas fa-magic mr-1"></i><span id="generateBtnText">生成试卷</span>');
            $submitBtn.prop('disabled', false);

            console.log('✅ 模态框关闭时恢复了生成按钮状态');
        }

        // 确保历史记录是最新的
        if (typeof window.loadPaperHistory === 'function') {
            setTimeout(() => {
                window.loadPaperHistory();
            }, 500);
        }
    });

    //  监听生成套数变化
    $(document).on('input change', '#paperCount', function() {
        updateGenerateButtonText();
    });

    // 渲染知识点配置
    function renderKnowledgePointsConfig(knowledgeConfigs) {
        const container = $('#knowledgePointsConfigContainer');
        container.empty();

        if (!knowledgeConfigs || knowledgeConfigs.length === 0) {
            //  没有知识点配置时，显示简答题专用提示
            container.html(`
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>简答题模式</strong>
                    <p class="mb-0 mt-2">您没有选择知识点，系统将从题库中随机选择简答题生成试卷。</p>
                    <p class="mb-0">如需更精准的试卷，建议选择相关知识点。</p>
                </div>
            `);
            return;
        }

        // 去重处理：确保每个知识点ID只出现一次
        const uniqueConfigs = [];
        const seenIds = new Set();

        knowledgeConfigs.forEach(config => {
            if (!seenIds.has(config.knowledgeId)) {
                seenIds.add(config.knowledgeId);
                uniqueConfigs.push(config);
            } else {
                console.warn(`发现重复的知识点ID: ${config.knowledgeId}，已跳过`);
            }
        });

        console.log(`原始知识点配置数量: ${knowledgeConfigs.length}, 去重后数量: ${uniqueConfigs.length}`);

        // 使用去重后的配置
        knowledgeConfigs = uniqueConfigs;

        // 获取知识点名称和题目数量
        const knowledgeInfo = {};

        // 直接从全局状态获取知识点信息，同时建立多种ID映射
        selectedKnowledgePoints.forEach(function(pointData, pointId) {
            // 解析题目数量
            let topicCount = 0;
            const topicCountText = pointData.topicCount || '';
            if (typeof topicCountText === 'string') {
                if (topicCountText.includes('题目:')) {
                    topicCount = parseInt(topicCountText.replace('题目:', '').trim()) || 0;
                } else if (topicCountText.includes('题')) {
                    topicCount = parseInt(topicCountText.replace('题', '').trim()) || 0;
                } else {
                    topicCount = parseInt(topicCountText) || 0;
                }
            } else {
                topicCount = parseInt(topicCountText) || 0;
            }

            const info = {
                name: pointData.name,
                topicCount: topicCount,
                originalId: pointId,
                knowId: pointData.knowId || pointId
            };

            //  建立多种ID映射，确保能够找到知识点信息
            const knowIdValue = pointData.knowId || pointId;

            // 建立多种类型的映射，包括字符串和数字类型
            knowledgeInfo[pointId] = info;                    // 原始ID映射（字符串）
            knowledgeInfo[parseInt(pointId)] = info;          // 原始ID映射（数字）
            knowledgeInfo[knowIdValue] = info;                // knowId映射（原始类型）
            knowledgeInfo[parseInt(knowIdValue)] = info;      // knowId映射（数字）
            knowledgeInfo[knowIdValue.toString()] = info;     // knowId映射（字符串）

            console.log(`知识点信息映射: ${pointData.name} - pointId: ${pointId}, knowId: ${knowIdValue}, 映射键: [${pointId}, ${parseInt(pointId)}, ${knowIdValue}, ${parseInt(knowIdValue)}, ${knowIdValue.toString()}]`);
        });

        // 补充从当前页面获取的信息（如果有的话）
        $('.knowledge-point-card').each(function() {
            const id = $(this).find('.knowledge-checkbox').val();
            const name = $(this).find('.card-title').text().trim();
            const topicCount = $(this).find('.badge-info').text().replace('题目: ', '').trim();

            if (id && name && knowledgeInfo[id]) {
                // 如果全局状态中的名称不完整，使用页面上的名称
                if (!knowledgeInfo[id].name || knowledgeInfo[id].name.includes('#')) {
                    knowledgeInfo[id].name = name;
                }
                // 如果全局状态中的题目数量为0，使用页面上的数量
                if (knowledgeInfo[id].topicCount === 0) {
                    knowledgeInfo[id].topicCount = parseInt(topicCount) || 0;
                }
            }
        });

        // 添加标题和说明
        container.append(`
            <div class="mb-4">
                <h5 class="mb-3">已选择 ${knowledgeConfigs.length} 个知识点</h5>
                <p class="text-muted small">
                    <i class="fas fa-info-circle mr-1"></i>
                    为每个知识点设置题目总数量，并选择是否包含简答题。知识点总题量应与上方题型设置的总题量一致。
                    您可以点击"智能分配题量"按钮自动分配题量。
                </p>
            </div>
        `);

        // 为每个知识点创建配置区域
        knowledgeConfigs.forEach(function(config, index) {
            const knowledgeId = config.knowledgeId;

            console.log(`渲染知识点配置: knowledgeId=${knowledgeId}`);

            //  多种方式查找知识点信息，包括类型转换
            let info = knowledgeInfo[knowledgeId] ||
                      knowledgeInfo[parseInt(knowledgeId)] ||
                      knowledgeInfo[knowledgeId.toString()];

            if (!info) {
                console.log(`直接查找失败，尝试遍历查找 knowledgeId=${knowledgeId} (类型: ${typeof knowledgeId})`);
                // 如果直接查找失败，遍历所有知识点信息查找匹配项
                Object.keys(knowledgeInfo).forEach(key => {
                    const keyInfo = knowledgeInfo[key];
                    if (!info && (
                        key == knowledgeId ||
                        parseInt(key) == parseInt(knowledgeId) ||
                        key.toString() == knowledgeId.toString() ||
                        keyInfo.knowId == knowledgeId ||
                        keyInfo.originalId == knowledgeId
                    )) {
                        info = keyInfo;
                        console.log(`通过遍历找到匹配: ${keyInfo.name} (key: ${key}, keyType: ${typeof key})`);
                    }
                });
            }

            // 如果还是找不到，尝试从全局状态直接查找
            if (!info) {
                console.log(`遍历查找也失败，尝试从全局状态查找 knowledgeId=${knowledgeId}`);
                selectedKnowledgePoints.forEach(function(pointData, pointId) {
                    if (!info && (
                        pointId == knowledgeId ||
                        pointData.id == knowledgeId ||
                        pointData.knowId == knowledgeId
                    )) {
                        // 解析题目数量
                        let topicCount = 0;
                        const topicCountText = pointData.topicCount || '';
                        if (typeof topicCountText === 'string') {
                            if (topicCountText.includes('题目:')) {
                                topicCount = parseInt(topicCountText.replace('题目:', '').trim()) || 0;
                            } else if (topicCountText.includes('题')) {
                                topicCount = parseInt(topicCountText.replace('题', '').trim()) || 0;
                            } else {
                                topicCount = parseInt(topicCountText) || 0;
                            }
                        } else {
                            topicCount = parseInt(topicCountText) || 0;
                        }

                        info = {
                            name: pointData.name,
                            topicCount: topicCount,
                            originalId: pointId,
                            knowId: pointData.knowId || pointId
                        };
                        console.log(`从全局状态找到匹配: ${pointData.name} (pointId: ${pointId})`);
                    }
                });
            }

            // 如果仍然找不到，创建默认信息并发出警告
            if (!info) {
                console.warn(`无法找到知识点信息: knowledgeId=${knowledgeId}`);
                console.log('当前知识点信息映射:', knowledgeInfo);
                console.log('当前全局状态:', Array.from(selectedKnowledgePoints.entries()));

                info = {
                    name: `知识点 #${knowledgeId}`,
                    topicCount: 0,
                    originalId: knowledgeId,
                    knowId: knowledgeId
                };
            }

            const knowledgeName = info.name;
            const topicCount = info.topicCount || 0;

            // 使用配置中的实际值，而不是重置为默认值
            const questionCount = config.questionCount || config.basicQuestionCount || 0;
            const includeShortAnswer = config.includeShortAnswer || false;
            const shortAnswerCount = config.shortAnswerCount || 0;

            console.log(`最终渲染信息: ${knowledgeName} (knowledgeId: ${knowledgeId}, topicCount: ${topicCount}, questionCount: ${questionCount}, includeShortAnswer: ${includeShortAnswer}, shortAnswerCount: ${shortAnswerCount})`);

            const html = `
                <div class="knowledge-point-config" data-id="${knowledgeId}">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="card-title">${escapeHtml(knowledgeName)}</h5>
                            <div class="mb-2">
                                <span class="badge badge-primary">ID: ${knowledgeId}</span>
                                ${topicCount > 0 ? `<span class="badge badge-info">题库: ${topicCount}题</span>` : ''}
                                <span class="badge badge-secondary">序号: ${index + 1}/${knowledgeConfigs.length}</span>
                            </div>
                            <div class="progress mt-3" style="height: 5px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: ${Math.min(100, (questionCount / Math.max(1, topicCount)) * 100)}%"></div>
                            </div>
                            <small class="text-muted d-block mt-1">
                                基础题量: ${questionCount} / 题库题量: ${topicCount || '未知'}
                                ${includeShortAnswer ? ` + ${shortAnswerCount}道简答题（额外）` : ''}
                            </small>
                            <div class="alert alert-info mt-2 mb-0" style="padding: 8px 12px; font-size: 12px;">
                                <i class="fas fa-info-circle mr-1"></i>
                                <strong>配置说明：</strong><br>
                                <small class="text-muted">
                                    • 基础题量：系统会根据题型设置智能分配<br>
                                    • 简答题：独立计算，不计入基础题量<br>
                                    • 总题数 = 基础题量 + 简答题数量
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="kp-count-${knowledgeId}">
                                    <i class="fas fa-list-ol mr-1"></i>基础题量
                                </label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <button class="btn btn-outline-secondary decrease-count" type="button">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                    </div>
                                    <input type="number" class="form-control knowledge-question-count" id="kp-count-${knowledgeId}"
                                           min="1" value="${questionCount}" data-id="${knowledgeId}">
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary increase-count" type="button">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle mr-1"></i>系统会根据题型设置智能分配
                                </small>
                            </div>

                            <!-- 简答题配置 -->
                            <div class="card border-warning mt-3">
                                <div class="card-header bg-warning text-dark py-2">
                                    <h6 class="mb-0">
                                        <i class="fas fa-edit"></i> 简答题（独立）
                                    </h6>
                                </div>
                                <div class="card-body py-2">
                                    <div class="custom-control custom-switch mb-2">
                                        <input type="checkbox" class="custom-control-input include-short-answer"
                                               id="include-short-${knowledgeId}" ${includeShortAnswer ? 'checked' : ''} data-id="${knowledgeId}">
                                        <label class="custom-control-label" for="include-short-${knowledgeId}">启用简答题</label>
                                    </div>
                                    <div class="short-answer-config" id="short-config-${knowledgeId}" style="display: ${includeShortAnswer ? 'block' : 'none'};">
                                        <label class="form-label">数量（额外）</label>
                                        <div class="input-group input-group-sm">
                                            <div class="input-group-prepend">
                                                <button class="btn btn-outline-secondary decrease-short-answer" type="button">
                                                    <i class="fas fa-minus"></i>
                                                </button>
                                            </div>
                                            <input type="number" class="form-control short-answer-count text-center"
                                                   id="short-count-${knowledgeId}" min="0" value="${shortAnswerCount}" data-id="${knowledgeId}">
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-secondary increase-short-answer" type="button">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle"></i>
                                            不计入基础题量
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- 知识点总计显示 -->
                            <div class="border-top pt-2 mt-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="font-weight-bold">该知识点总计：</span>
                                    <span class="badge badge-primary badge-lg knowledge-point-total" id="total-${knowledgeId}">
                                        ${includeShortAnswer ? `${questionCount} + ${shortAnswerCount}(简答)` : questionCount}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            container.append(html);
        });

        // 绑定事件
        $('.knowledge-question-count').on('change', function() {
            updateKnowledgePointConfig($(this));
            updateKnowledgePointTotal($(this).closest('.knowledge-point-config'));
            updateQuestionCountStats();
        });

        $('.include-short-answer').on('change', function() {
            const kpConfig = $(this).closest('.knowledge-point-config');
            const kpId = kpConfig.data('id');
            const enabled = $(this).is(':checked');
            const configDiv = kpConfig.find(`#short-config-${kpId}`);
            const shortAnswerCountInput = kpConfig.find('.short-answer-count');

            if (enabled) {
                configDiv.show();
                // 只有当前值为0时才设置为1，避免覆盖配置中的值
                const currentValue = parseInt(shortAnswerCountInput.val()) || 0;
                if (currentValue === 0) {
                    shortAnswerCountInput.val(1);
                }
            } else {
                configDiv.hide();
                shortAnswerCountInput.val(0);
            }

            updateKnowledgePointConfig($(this));
            updateKnowledgePointTotal(kpConfig);
            updateQuestionCountStats();
        });

        $('.short-answer-count').on('change', function() {
            updateKnowledgePointConfig($(this));
            updateKnowledgePointTotal($(this).closest('.knowledge-point-config'));
            updateQuestionCountStats();
        });

        // 绑定增减按钮事件
        $('.decrease-count').on('click', function() {
            const input = $(this).closest('.input-group').find('.knowledge-question-count');
            let value = parseInt(input.val()) || 0;
            if (value > 1) {
                input.val(value - 1).trigger('change');
            }
        });

        $('.increase-count').on('click', function() {
            const input = $(this).closest('.input-group').find('.knowledge-question-count');
            let value = parseInt(input.val()) || 0;
            input.val(value + 1).trigger('change');
        });

        // 简答题增减按钮事件
        $('.decrease-short-answer').on('click', function() {
            const input = $(this).closest('.input-group').find('.short-answer-count');
            let value = parseInt(input.val()) || 0;
            if (value > 0) {
                input.val(value - 1).trigger('change');
            }
        });

        $('.increase-short-answer').on('click', function() {
            const input = $(this).closest('.input-group').find('.short-answer-count');
            let value = parseInt(input.val()) || 0;
            input.val(value + 1).trigger('change');
        });
    }

    // 更新知识点配置
    function updateKnowledgePointConfig(element) {
        const knowledgeId = element.data('id');
        const knowledgeConfigs = $('#generatePaperForm').data('knowledgeConfigs') || [];

        // 找到对应的配置
        const configIndex = knowledgeConfigs.findIndex(config => config.knowledgeId == knowledgeId);
        if (configIndex === -1) return;

        // 如果是题量输入框
        if (element.hasClass('knowledge-question-count')) {
            const questionCount = parseInt(element.val()) || 5;
            knowledgeConfigs[configIndex].questionCount = questionCount;
        }

        // 如果是简答题开关
        if (element.hasClass('include-short-answer')) {
            const includeShortAnswer = element.prop('checked');
            knowledgeConfigs[configIndex].includeShortAnswer = includeShortAnswer;
        }

        // 如果是简答题数量输入框
        if (element.hasClass('short-answer-count')) {
            const shortAnswerCount = parseInt(element.val()) || 0;
            knowledgeConfigs[configIndex].shortAnswerCount = shortAnswerCount;
        }

        // 更新配置
        $('#generatePaperForm').data('knowledgeConfigs', knowledgeConfigs);
    }

    // 更新知识点总计显示
    function updateKnowledgePointTotal(kpConfig) {
        const basicCount = parseInt(kpConfig.find('.knowledge-question-count').val()) || 0;
        const shortAnswerCount = parseInt(kpConfig.find('.short-answer-count').val()) || 0;
        const includeShortAnswer = kpConfig.find('.include-short-answer').prop('checked');

        // 更新显示：基础题目 + 简答题（额外）
        const totalDisplay = includeShortAnswer && shortAnswerCount > 0 ?
            `${basicCount} + ${shortAnswerCount}(简答)` :
            `${basicCount}`;

        kpConfig.find('.knowledge-point-total').html(totalDisplay);
    }

    // 更新题量统计
    function updateQuestionCountStats() {
        // 计算题型设置的总题量
        const singleChoice = parseInt($('#singleChoiceCount').val()) || 0;
        const multipleChoice = parseInt($('#multipleChoiceCount').val()) || 0;
        const judgment = parseInt($('#judgmentCount').val()) || 0;
        const fillBlank = parseInt($('#fillCount').val()) || 0;
        const shortAnswer = parseInt($('#shortAnswerCount').val()) || 0;

        const totalConfigured = singleChoice + multipleChoice + judgment + fillBlank + shortAnswer;
        $('#totalConfiguredQuestions').text(totalConfigured);

        // 计算知识点配置的基础题量（不包括简答题）
        let totalKnowledgeBasic = 0;
        $('.knowledge-question-count').each(function() {
            totalKnowledgeBasic += parseInt($(this).val()) || 0;
        });
        $('#totalKnowledgeQuestions').text(totalKnowledgeBasic);

        // 计算知识点配置的简答题总数（额外）
        let totalShortAnswer = 0;
        $('.short-answer-count').each(function() {
            const kpConfig = $(this).closest('.knowledge-point-config');
            const includeShortAnswer = kpConfig.find('.include-short-answer').prop('checked');
            if (includeShortAnswer) {
                totalShortAnswer += parseInt($(this).val()) || 0;
            }
        });
        $('#totalShortAnswerQuestions').text(totalShortAnswer);

        // 检查是否匹配（基础题量应该匹配）
        const basicConfigured = singleChoice + multipleChoice + judgment + fillBlank;
        const isMatch = basicConfigured === totalKnowledgeBasic;
        $('#knowledgePointsWarning').toggle(!isMatch);

        // 更新知识点配置区域的样式
        $('.knowledge-point-config').removeClass('mismatch-warning match-success');
        if (totalKnowledgeBasic > 0) {
            if (isMatch) {
                $('.knowledge-point-config').addClass('match-success');
            } else {
                $('.knowledge-point-config').addClass('mismatch-warning');
            }
        }

        return isMatch;
    }

    // 更新试卷统计（总分和题目总数）
    function updatePaperStats() {
        // 获取题型数量和分值
        const singleChoiceCount = parseInt($('#singleChoiceCount').val()) || 0;
        const singleChoiceScore = parseFloat($('#singleChoiceScore').val()) || 0;

        const multipleChoiceCount = parseInt($('#multipleChoiceCount').val()) || 0;
        const multipleChoiceScore = parseFloat($('#multipleChoiceScore').val()) || 0;

        const judgmentCount = parseInt($('#judgmentCount').val()) || 0;
        const judgmentScore = parseFloat($('#judgmentScore').val()) || 0;

        const fillCount = parseInt($('#fillCount').val()) || 0;
        const fillScore = parseFloat($('#fillScore').val()) || 0;

        const shortAnswerCount = parseInt($('#shortAnswerCount').val()) || 0;
        const shortAnswerScore = parseFloat($('#shortAnswerScore').val()) || 0;

        // 计算总分
        const totalScore =
            singleChoiceCount * singleChoiceScore +
            multipleChoiceCount * multipleChoiceScore +
            judgmentCount * judgmentScore +
            fillCount * fillScore +
            shortAnswerCount * shortAnswerScore;

        // 计算题目总数
        const totalQuestions =
            singleChoiceCount +
            multipleChoiceCount +
            judgmentCount +
            fillCount +
            shortAnswerCount;

        // 更新显示
        $('#totalScore').text(totalScore.toFixed(1));
        $('#totalQuestions').text(totalQuestions);
    }

    //  更新生成按钮文本
    function updateGenerateButtonText() {
        const paperCount = parseInt($('#paperCount').val()) || 1;
        const btnText = paperCount > 1 ? `生成 ${paperCount} 套试卷` : '生成试卷';
        $('#generateBtnText').text(btnText);

        // 更新按钮图标
        const btnIcon = paperCount > 1 ? 'fas fa-copy' : 'fas fa-magic';
        $('#submitGeneratePaperBtn i').attr('class', btnIcon + ' mr-1');
    }

    // 智能分配题量
    function smartDistributeQuestions() {
        // 显示加载中
        Swal.fire({
            title: '智能分配中...',
            text: '正在计算最佳题量分配方案',
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading();
            },
            timer: 800
        });

        // 获取题型设置的总题量
        const singleChoice = parseInt($('#singleChoiceCount').val()) || 0;
        const multipleChoice = parseInt($('#multipleChoiceCount').val()) || 0;
        const judgment = parseInt($('#judgmentCount').val()) || 0;
        const fillBlank = parseInt($('#fillCount').val()) || 0;
        const shortAnswer = parseInt($('#shortAnswerCount').val()) || 0;

        //  修复：基础题型总量（不包括简答题，简答题是额外的）
        const basicTypesTotal = singleChoice + multipleChoice + judgment + fillBlank;

        // 获取知识点配置
        const knowledgeConfigs = $('#generatePaperForm').data('knowledgeConfigs') || [];

        //  如果没有知识点配置，但有简答题，允许继续
        if (knowledgeConfigs.length === 0) {
            if (shortAnswer > 0) {
                // 只有简答题的情况，不需要智能分配
                Swal.fire({
                    icon: 'info',
                    title: '简答题模式',
                    text: '当前为简答题模式，无需分配知识点题量',
                    confirmButtonText: '确定'
                });
                return;
            } else {
                Swal.fire({
                    icon: 'error',
                    title: '无法分配',
                    text: '没有找到知识点配置，且未配置简答题',
                    confirmButtonText: '确定'
                });
                return;
            }
        }

        // 获取知识点信息
        const knowledgeInfo = {};
        $('.knowledge-point-config').each(function() {
            const id = $(this).data('id');
            const topicCount = parseInt($(this).find('.badge-info').text().replace('题库: ', '').replace('题', '')) || 0;
            knowledgeInfo[id] = { topicCount: topicCount };
        });

        // 处理简答题
        let shortAnswerPoints = knowledgeConfigs.filter(config => config.includeShortAnswer);
        let remainingShortAnswers = shortAnswer;

        // 如果有简答题但没有知识点开启简答题，提示用户手动开启
        if (shortAnswer > 0 && shortAnswerPoints.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: '需要开启简答题',
                html: `您设置了 <strong>${shortAnswer}</strong> 道简答题，但没有知识点开启简答题开关。<br><br>请手动开启需要包含简答题的知识点开关，然后重新进行智能分配。`,
                confirmButtonText: '我知道了',
                confirmButtonColor: '#007bff'
            });
            return;
        }

        // 如果开启简答题的知识点数量少于简答题总数，每个知识点可能需要多个简答题
        const shortAnswerPerPoint = shortAnswerPoints.length > 0
            ? Math.ceil(shortAnswer / shortAnswerPoints.length)
            : 0;

        //  修复：基础题型分配（简答题独立处理，不占用基础题型配额）
        const remainingTotal = basicTypesTotal;

        // 根据题库题量比例分配题目
        let totalTopicCount = 0;
        knowledgeConfigs.forEach(config => {
            const info = knowledgeInfo[config.knowledgeId] || {};
            totalTopicCount += info.topicCount || 1; // 至少为1，避免除以0
        });

        // 按题库题量比例初步分配
        let allocatedTotal = 0;
        knowledgeConfigs.forEach(config => {
            const info = knowledgeInfo[config.knowledgeId] || {};
            const topicCount = info.topicCount || 1;

            // 按比例分配基础题型题量
            let proportion = topicCount / totalTopicCount;
            let baseAllocation = Math.floor(remainingTotal * proportion);

            //  修复：基础题型和简答题分别处理
            config.questionCount = baseAllocation; // 基础题型数量

            // 如果开启了简答题，单独设置简答题数量
            if (config.includeShortAnswer && shortAnswer > 0) {
                const shortAnswersForThisPoint = Math.min(remainingShortAnswers, shortAnswerPerPoint);
                remainingShortAnswers -= shortAnswersForThisPoint;
                config.shortAnswerCount = shortAnswersForThisPoint; // 简答题数量（独立）
            } else {
                config.shortAnswerCount = 0;
            }

            allocatedTotal += config.questionCount;
        });

        //  修复：处理剩余未分配的基础题型题量
        let remainingQuestions = basicTypesTotal - allocatedTotal;

        // 如果有剩余题量，按顺序分配
        if (remainingQuestions > 0) {
            // 按题库题量排序，优先分配给题量多的知识点
            const sortedConfigs = [...knowledgeConfigs].sort((a, b) => {
                const infoA = knowledgeInfo[a.knowledgeId] || {};
                const infoB = knowledgeInfo[b.knowledgeId] || {};
                return (infoB.topicCount || 0) - (infoA.topicCount || 0);
            });

            for (let i = 0; i < sortedConfigs.length && remainingQuestions > 0; i++) {
                const config = sortedConfigs[i];
                config.questionCount++;
                remainingQuestions--;
            }
        }

        // 如果分配过多，从题量最多的知识点开始减少
        if (remainingQuestions < 0) {
            // 按已分配题量排序，优先从题量多的知识点减少
            const sortedConfigs = [...knowledgeConfigs].sort((a, b) =>
                b.questionCount - a.questionCount
            );

            for (let i = 0; i < sortedConfigs.length && remainingQuestions < 0; i++) {
                const config = sortedConfigs[i];
                if (config.questionCount > 1) { // 确保每个知识点至少有1题
                    config.questionCount--;
                    remainingQuestions++;
                }
            }
        }

        //  修复：更新UI，分别设置基础题型和简答题数量
        knowledgeConfigs.forEach(config => {
            // 更新基础题型数量
            $(`#kp-count-${config.knowledgeId}`).val(config.questionCount).trigger('change');

            // 更新简答题数量和开关状态
            if (config.includeShortAnswer && config.shortAnswerCount > 0) {
                // 开启简答题开关
                $(`#include-short-${config.knowledgeId}`).prop('checked', true).trigger('change');
                // 设置简答题数量
                $(`#short-count-${config.knowledgeId}`).val(config.shortAnswerCount).trigger('change');
            } else {
                // 关闭简答题开关
                $(`#include-short-${config.knowledgeId}`).prop('checked', false).trigger('change');
                $(`#short-count-${config.knowledgeId}`).val(0);
            }
        });

        // 更新统计
        updateQuestionCountStats();
        updatePaperStats();

        // 显示成功消息
        Swal.fire({
            icon: 'success',
            title: '分配完成',
            text: '已智能分配题量到各知识点',
            timer: 1500,
            showConfirmButton: false
        });
    }

    // 绑定智能分配按钮事件
    $('#smartDistributeBtn').on('click', function() {
        smartDistributeQuestions();
    });

    // 使用事件委托绑定题型数量和分值变化事件
    $(document).on('change input', '#singleChoiceCount, #singleChoiceScore, #multipleChoiceCount, #multipleChoiceScore, #judgmentCount, #judgmentScore, #fillCount, #fillScore, #shortAnswerCount, #shortAnswerScore', function() {
        updateQuestionCountStats();
        updatePaperStats();

        // 更新图表
        if (typeof window.updateQuestionDistributionChart === 'function') {
            window.updateQuestionDistributionChart();
        }

        if (typeof window.updatePaperPreview === 'function') {
            window.updatePaperPreview();
        }
    });

    // 初始化图表
    function initCharts() {
        // 难度分布图表
        const difficultyCtx = document.getElementById('difficultyChart');
        if (difficultyCtx) {
            window.difficultyChart = new Chart(difficultyCtx.getContext('2d'), {
                type: 'pie',
                data: {
                    labels: ['简单', '中等', '困难'],
                    datasets: [{
                        data: [30, 50, 20],
                        backgroundColor: ['#28a745', '#17a2b8', '#dc3545']
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // 题型分布图表
        const questionDistributionCtx = document.getElementById('questionDistributionChart');
        if (questionDistributionCtx) {
            window.questionDistributionChart = new Chart(questionDistributionCtx.getContext('2d'), {
                type: 'doughnut',
                data: {
                    labels: ['单选题', '多选题', '判断题', '填空题', '简答题'],
                    datasets: [{
                        data: [5, 3, 5, 3, 2],
                        backgroundColor: ['#007bff', '#6f42c1', '#fd7e14', '#20c997', '#6c757d']
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12
                            }
                        }
                    }
                }
            });
        }
    }

    //  将 openPaperGenerationModal 函数暴露到全局作用域
    // 这样其他脚本（如 paper-config-integration.js）就可以调用它
    window.openPaperGenerationModal = openPaperGenerationModal;

    // openPaperGenerationModal 函数已暴露到全局作用域
});

/**
 * 初始化主页面搜索功能
 */
function initMainPageSearch() {
    const searchInput = $('#knowledgePointSearchMain');
    const searchBtn = $('#searchBtnMain');
    const clearBtn = $('#clearSearchMain');

    if (searchInput.length === 0) {
        return;
    }

    // 移除之前的事件绑定
    searchInput.off('keypress.search click.search');
    searchBtn.off('click.search');
    clearBtn.off('click.search');

    // 搜索按钮点击事件
    searchBtn.on('click.search', function(e) {
        e.preventDefault();
        const searchTerm = searchInput.val().trim();
        console.log('🔍 搜索按钮点击，关键词:', searchTerm);

        if (searchTerm) {
            executeKnowledgeSearch(searchTerm);
        } else {
            alert('请输入搜索关键词');
            searchInput.focus();
        }
    });

    // 回车键搜索
    searchInput.on('keypress.search', function(e) {
        if (e.which === 13) { // Enter键
            e.preventDefault();
            const searchTerm = $(this).val().trim();
            console.log('🔍 回车键搜索，关键词:', searchTerm);

            if (searchTerm) {
                executeKnowledgeSearch(searchTerm);
            } else {
                alert('请输入搜索关键词');
            }
        }
    });

    // 输入框变化事件
    searchInput.on('input.search', function() {
        const searchTerm = $(this).val().trim();
        if (searchTerm) {
            clearBtn.show();
        } else {
            clearBtn.hide();
        }
    });

    // 清空搜索
    clearBtn.on('click.search', function() {
        console.log('🔍 清空搜索');
        searchInput.val('');
        clearBtn.hide();
        restoreDefaultKnowledgeView();
        searchInput.focus();
    });

    console.log('✅ 主页面搜索功能初始化完成');
}

/**
 * 执行知识点搜索
 */
function executeKnowledgeSearch(searchTerm) {
    console.log('🔍 执行知识点搜索:', searchTerm);

    const knowledgeContainer = $('#knowledge-points-container');

    // 显示加载状态
    knowledgeContainer.html(`
        <div class="text-center py-5">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="sr-only">搜索中...</span>
            </div>
            <h5 class="text-muted">正在搜索知识点</h5>
            <p class="text-muted">关键词: <strong>"${searchTerm}"</strong></p>
        </div>
    `);

    // 调用搜索API
    $.ajax({
        url: '/api/knowledge/search',
        method: 'GET',
        data: { keyword: searchTerm },
        timeout: 10000,
        success: function(response) {
            if (response && response.success && response.data) {
                displaySearchResults(response.data, searchTerm);
            } else {
                showSearchError('搜索失败', response.message || '未能获取搜索结果');
            }
        },
        error: function(xhr, status, error) {
            showSearchError('搜索错误', '服务器连接失败，请稍后重试');
        }
    });
}

/**
 * 显示搜索结果
 */
function displaySearchResults(results, searchTerm) {
    console.log('🔍 显示搜索结果:', results.length, '个结果');

    const knowledgeContainer = $('#knowledge-points-container');

    if (!results || results.length === 0) {
        knowledgeContainer.html(`
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">未找到匹配的知识点</h4>
                <p class="text-muted">搜索关键词: <strong>"${searchTerm}"</strong></p>
                <button class="btn btn-primary mt-3" onclick="$('#clearSearchMain').click()">
                    <i class="fas fa-undo mr-1"></i>返回浏览
                </button>
            </div>
        `);
        return;
    }

    // 构建搜索结果HTML
    let html = `
        <div class="search-results">
            <div class="alert alert-info">
                <i class="fas fa-info-circle mr-2"></i>
                搜索 "<strong>${searchTerm}</strong>" 找到 <strong>${results.length}</strong> 个知识点
                <button class="btn btn-sm btn-outline-secondary float-right" onclick="$('#clearSearchMain').click()">
                    <i class="fas fa-times mr-1"></i>清空搜索
                </button>
            </div>
            <div class="row">
    `;

    results.forEach(function(item, index) {
        const isFree = item.isFree === 1;
        html += `
            <div class="col-md-6 mb-3">
                <div class="card h-100 border-primary">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="card-title text-primary mb-2">${item.knowledgeName}</h6>
                                <div class="text-muted small">
                                    <span class="badge badge-secondary mr-1">ID: ${item.knowledgeId}</span>
                                    <span class="badge badge-light mr-1">${item.groupName || '未分类'}</span>
                                    ${isFree ? '<span class="badge badge-success">免费</span>' : '<span class="badge badge-warning">付费</span>'}
                                </div>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input knowledge-checkbox" type="checkbox"
                                       value="${item.id}" id="search-knowledge-${item.id}">
                                <label class="form-check-label" for="search-knowledge-${item.id}">
                                    选择
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += `
            </div>
        </div>
    `;

    knowledgeContainer.html(html);

    console.log('✅ 搜索结果显示完成');
}

/**
 * 显示搜索错误
 */
function showSearchError(title, message) {
    const knowledgeContainer = $('#knowledge-points-container');

    knowledgeContainer.html(`
        <div class="alert alert-danger">
            <h5><i class="fas fa-exclamation-triangle mr-2"></i>${title}</h5>
            <p class="mb-3">${message}</p>
            <button class="btn btn-outline-danger" onclick="$('#searchBtnMain').click()">
                <i class="fas fa-redo mr-1"></i>重试搜索
            </button>
            <button class="btn btn-outline-secondary ml-2" onclick="$('#clearSearchMain').click()">
                <i class="fas fa-times mr-1"></i>取消搜索
            </button>
        </div>
    `);
}







/**
 * 高亮搜索关键词
 */
function highlightSearchTerms(card, keywords) {
    const title = card.find('.card-title');
    const description = card.find('.card-text');

    // 移除之前的高亮
    title.html(title.text());
    description.html(description.text());

    // 添加新的高亮
    keywords.forEach(keyword => {
        if (keyword.length > 0) {
            const regex = new RegExp(`(${escapeRegExp(keyword)})`, 'gi');

            title.html(title.html().replace(regex, '<mark class="search-highlight">$1</mark>'));
            description.html(description.html().replace(regex, '<mark class="search-highlight">$1</mark>'));
        }
    });
}

/**
 * 转义正则表达式特殊字符
 */
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * 转义HTML特殊字符
 */
function escapeHtml(text) {
    if (!text) return '';
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

/**
 * 恢复默认知识点视图
 */
function restoreDefaultKnowledgeView() {
    const knowledgeContainer = $('#knowledge-points-container');
    const defaultMessage = $('#defaultKnowledgeMessage');
    const noResultsMessage = $('#noSearchResults');
    const searchResultsInfo = $('.search-results-info');

    // 检查是否有选中的分类
    const activeGroup = $('.group-item.active');
    if (activeGroup.length > 0) {
        // 如果有选中的分类，重新加载该分类的知识点
        const groupId = activeGroup.data('id');
        const groupName = activeGroup.data('name');
        if (groupId && groupName) {
            loadKnowledgePoints(groupId, groupName);
        }
    } else {
        // 没有选中分类，显示默认消息
        knowledgeContainer.html(`
            <div class="text-center py-5 text-muted" id="defaultKnowledgeMessage">
                <i class="fas fa-hand-point-left fa-3x mb-3"></i>
                <p>请先选择一个知识点分类</p>
            </div>
        `);
    }

    noResultsMessage.hide();
    searchResultsInfo.hide();
}
