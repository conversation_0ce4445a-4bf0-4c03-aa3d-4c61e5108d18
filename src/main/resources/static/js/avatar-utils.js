/**
 * 头像处理工具类
 * 提供完善的头像显示功能，对没有头像的用户不使用默认头像
 */

class AvatarUtils {
    
    /**
     * 格式化头像URL
     * @param {string} avatarPath - 原始头像路径
     * @param {object} userInfo - 用户信息 {id, username, email}
     * @param {object} options - 显示选项 {showPlaceholder: false, hideIfNoAvatar: false, showInitials: true}
     * @returns {string|null} 处理后的头像URL，没有头像时根据选项返回null或占位符
     */
    static formatAvatarUrl(avatarPath, userInfo = {}, options = {}) {
        const defaultOptions = {
            showPlaceholder: false,    // 是否显示占位符图片
            hideIfNoAvatar: false,     // 没有头像时是否隐藏元素
            showInitials: true,        // 是否显示用户名首字母
            allowRandomAvatar: false   // 是否允许生成随机头像（默认不允许）
        };
        
        const opts = { ...defaultOptions, ...options };
        
        if (avatarPath && avatarPath.trim() !== '') {
            // 如果是数据库存储的路径格式如 "main/avatars/xxx.jpg"，转换为正确的访问路径
            if (avatarPath.startsWith('main/avatars/')) {
                return `/uploads/avatars/${avatarPath.replace('main/avatars/', '')}`;
            }
            
            // 如果已经是完整URL或正确的路径，直接返回
            if (avatarPath.startsWith('http') || avatarPath.startsWith('/uploads/') || avatarPath.startsWith('/static/')) {
                return avatarPath;
            }
            
            // 如果是相对路径的文件名，添加头像目录
            if (avatarPath.match(/^\d{14}_[a-f0-9]+\.(jpg|jpeg|png|gif)$/)) {
                return `/uploads/avatars/${avatarPath}`;
            }
            
            // 如果是相对路径（avatars/xxx.jpg），转换为正确的访问路径
            if (avatarPath.startsWith('avatars/')) {
                return `/uploads/${avatarPath}`;
            }
            
            // 默认返回传入的路径
            return avatarPath;
        }
        
        // 没有头像时的处理逻辑
        if (opts.hideIfNoAvatar) {
            return null; // 返回null表示应该隐藏元素
        }
        
        if (opts.showInitials && userInfo.username) {
            // 生成用户名首字母头像
            return this.generateInitialsAvatar(userInfo.username, userInfo.id);
        }
        
        if (opts.showPlaceholder) {
            // 返回占位符图片
            return '/static/images/placeholder-avatar.svg';
        }
        
        if (opts.allowRandomAvatar) {
            // 只有明确允许时才生成随机头像
            return this.generateRandomAvatar(userInfo);
        }
        
        // 默认返回null，表示没有头像
        return null;
    }
    
    /**
     * 生成用户名首字母头像
     * @param {string} username - 用户名
     * @param {string|number} userId - 用户ID（用于生成颜色）
     * @returns {string} Data URL
     */
    static generateInitialsAvatar(username, userId = null) {
        if (!username) return null;
        
        // 获取用户名首字母（支持中英文）
        const initials = this.getInitials(username);
        
        // 根据用户ID或用户名生成固定颜色
        const colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', 
            '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F',
            '#FF9999', '#87CEEB', '#98FB98', '#DEB887'
        ];
        
        const seed = userId || username;
        const colorIndex = Math.abs(this.hashCode(seed.toString())) % colors.length;
        const bgColor = colors[colorIndex];
        
        return this.generateColorAvatar(initials, bgColor);
    }
    
    /**
     * 获取用户名首字母
     * @param {string} username - 用户名
     * @returns {string} 首字母
     */
    static getInitials(username) {
        if (!username) return 'U';
        
        // 处理中文用户名
        if (/[\u4e00-\u9fa5]/.test(username)) {
            return username.charAt(0);
        }
        
        // 处理英文用户名
        const words = username.trim().split(/\s+/);
        if (words.length >= 2) {
            return (words[0].charAt(0) + words[1].charAt(0)).toUpperCase();
        }
        
        return username.substring(0, 2).toUpperCase();
    }
    
    /**
     * 设置头像元素 - 增强版本
     * @param {HTMLImageElement} imgElement - 图片元素
     * @param {string} avatarPath - 头像路径
     * @param {object} userInfo - 用户信息
     * @param {object} options - 显示选项
     */
    static setAvatarElement(imgElement, avatarPath, userInfo = {}, options = {}) {
        if (!imgElement) return;
        
        const avatarUrl = this.formatAvatarUrl(avatarPath, userInfo, options);
        
        if (avatarUrl === null) {
            // 没有头像且选择隐藏
            if (options.hideIfNoAvatar) {
                imgElement.style.display = 'none';
                return;
            }
            
            // 显示空状态
            this.showEmptyAvatar(imgElement, userInfo);
            return;
        }
        
        // 显示头像
        imgElement.style.display = '';
        imgElement.src = avatarUrl;
        
        // 添加错误处理
        imgElement.onerror = () => {
            // 如果头像加载失败，显示首字母头像
            if (userInfo.username) {
                const fallbackUrl = this.generateInitialsAvatar(userInfo.username, userInfo.id);
                if (fallbackUrl) {
                    imgElement.src = fallbackUrl;
                    return;
                }
            }
            
            // 最后的降级方案
            this.showEmptyAvatar(imgElement, userInfo);
        };
    }
    
    /**
     * 显示空头像状态
     * @param {HTMLImageElement} imgElement - 图片元素
     * @param {object} userInfo - 用户信息
     */
    static showEmptyAvatar(imgElement, userInfo = {}) {
        // 创建一个简单的占位符
        const placeholderSvg = `
            <svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
                <circle cx="64" cy="64" r="64" fill="#f0f0f0"/>
                <circle cx="64" cy="50" r="20" fill="#d0d0d0"/>
                <path d="M30 100 Q30 85 64 85 Q98 85 98 100 L98 128 L30 128 Z" fill="#d0d0d0"/>
                <text x="64" y="110" font-family="Arial, sans-serif" font-size="12" fill="#999" text-anchor="middle">
                    无头像
                </text>
            </svg>
        `;
        
        const svgBlob = new Blob([placeholderSvg], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(svgBlob);
        imgElement.src = url;
        
        // 释放URL对象
        imgElement.onload = () => URL.revokeObjectURL(url);
    }
    
    /**
     * 批量处理页面中的头像元素
     * @param {object} userInfo - 当前用户信息
     * @param {object} options - 全局显示选项
     */
    static initPageAvatars(userInfo = {}, options = {}) {
        // 查找所有头像元素
        const avatarSelectors = [
            'img[src*="default-avatar.png"]',
            'img.avatar',
            'img.user-avatar',
            'img[data-avatar]'
        ];
        
        const avatarElements = document.querySelectorAll(avatarSelectors.join(', '));
        
        avatarElements.forEach(img => {
            // 获取元素特定的用户信息
            const elementUserInfo = {
                id: img.getAttribute('data-user-id') || userInfo.id,
                username: img.getAttribute('data-username') || userInfo.username,
                email: img.getAttribute('data-email') || userInfo.email
            };
            
            // 获取元素特定的选项
            const elementOptions = {
                ...options,
                hideIfNoAvatar: img.hasAttribute('data-hide-if-no-avatar'),
                showInitials: !img.hasAttribute('data-no-initials'),
                showPlaceholder: img.hasAttribute('data-show-placeholder')
            };
            
            // 获取头像路径
            const avatarPath = img.getAttribute('data-avatar-path') || null;
            
            this.setAvatarElement(img, avatarPath, elementUserInfo, elementOptions);
        });
    }
    
    /**
     * 为用户卡片/列表创建头像元素
     * @param {object} userInfo - 用户信息
     * @param {object} options - 显示选项
     * @returns {HTMLImageElement} 头像图片元素
     */
    static createAvatarElement(userInfo = {}, options = {}) {
        const img = document.createElement('img');
        img.className = 'avatar rounded-circle';
        img.alt = `${userInfo.username || '用户'}的头像`;
        img.style.width = options.size || '32px';
        img.style.height = options.size || '32px';
        img.style.objectFit = 'cover';
        
        this.setAvatarElement(img, userInfo.avatar, userInfo, options);
        
        return img;
    }
    
    /**
     * 生成随机头像（保留原有功能，但默认不使用）
     * @param {object} userInfo - 用户信息
     * @returns {string} 随机头像URL
     */
    static generateRandomAvatar(userInfo = {}) {
        const { id, username, email } = userInfo;
        
        // 方案1：使用DiceBear API生成可爱的头像（推荐）
        if (id || username) {
            const seed = id || username || email || 'default';
            // 使用不同的头像风格
            const styles = ['avataaars', 'big-smile', 'fun-emoji', 'personas', 'pixel-art'];
            const randomStyle = styles[this.hashCode(seed) % styles.length];
            return `https://api.dicebear.com/7.x/${randomStyle}/svg?seed=${encodeURIComponent(seed)}&backgroundColor=random`;
        }
        
        // 方案2：使用UI Avatars作为备用方案
        if (username) {
            const colors = ['3498db', '9b59b6', 'e74c3c', '2ecc71', 'f39c12', '1abc9c', '34495e', 'e67e22'];
            const colorIndex = this.hashCode(username) % colors.length;
            const bgColor = colors[colorIndex];
            const name = encodeURIComponent(username.substring(0, 2).toUpperCase());
            return `https://ui-avatars.com/api/?name=${name}&background=${bgColor}&color=fff&size=128`;
        }
        
        // 方案3：使用Gravatar的identicon
        if (email) {
            const emailHash = this.md5(email.toLowerCase().trim());
            return `https://www.gravatar.com/avatar/${emailHash}?d=identicon&s=128`;
        }
        
        // 最后的备用方案：本地多彩头像
        const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'];
        const colorIndex = Math.abs(this.hashCode(id || username || 'default')) % colors.length;
        return this.generateColorAvatar(username || 'U', colors[colorIndex]);
    }
    
    /**
     * 简单哈希函数
     * @param {string} str - 要哈希的字符串
     * @returns {number} 哈希值
     */
    static hashCode(str) {
        let hash = 0;
        if (!str || str.length === 0) return hash;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash);
    }
    
    /**
     * 简单MD5哈希（用于Gravatar）
     * @param {string} str - 要哈希的字符串
     * @returns {string} 哈希值
     */
    static md5(str) {
        // 这里使用一个简化的哈希函数，实际项目中可以引入crypto-js库
        let hash = 0;
        if (str.length === 0) return hash.toString();
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(16).padStart(8, '0');
    }
    
    /**
     * 生成彩色字母头像
     * @param {string} letter - 显示的字母
     * @param {string} bgColor - 背景颜色
     * @returns {string} Data URL
     */
    static generateColorAvatar(letter, bgColor) {
        const canvas = document.createElement('canvas');
        canvas.width = 128;
        canvas.height = 128;
        const ctx = canvas.getContext('2d');
        
        // 绘制背景
        ctx.fillStyle = bgColor;
        ctx.fillRect(0, 0, 128, 128);
        
        // 绘制字母
        ctx.fillStyle = '#FFFFFF';
        ctx.font = 'bold 48px Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(letter.charAt(0).toUpperCase(), 64, 64);
        
        return canvas.toDataURL();
    }
}

// 全局函数，兼容现有代码
window.AvatarUtils = AvatarUtils;

// 更新的头像错误处理函数
function handleAvatarError(imgElement, userInfo = {}) {
    if (userInfo.username) {
        AvatarUtils.setAvatarElement(imgElement, null, userInfo, { showInitials: true });
    } else {
        AvatarUtils.showEmptyAvatar(imgElement, userInfo);
    }
}

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    // 尝试从页面获取当前用户信息
    const userInfo = {};
    
    // 从页面中查找用户信息
    const userIdElement = document.querySelector('[data-current-user-id]');
    const usernameElement = document.querySelector('[data-current-username]');
    const emailElement = document.querySelector('[data-current-email]');
    
    if (userIdElement) userInfo.id = userIdElement.getAttribute('data-current-user-id');
    if (usernameElement) userInfo.username = usernameElement.getAttribute('data-current-username');
    if (emailElement) userInfo.email = emailElement.getAttribute('data-current-email');
    
    // 初始化页面头像 - 默认显示首字母，不显示随机头像
    AvatarUtils.initPageAvatars(userInfo, { 
        showInitials: true, 
        allowRandomAvatar: false,
        showPlaceholder: false 
    });
}); 