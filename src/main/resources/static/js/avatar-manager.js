/**
 * 头像管理工具类
 * 统一处理头像上传、显示、错误处理等功能
 * 注意：此类已被AvatarUtils替代，保留用于兼容性
 */
class AvatarManager {
    constructor() {
        this.maxFileSize = 5 * 1024 * 1024; // 5MB
        this.allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    }

    /**
     * 格式化头像URL
     * @param {string} avatarPath - 头像路径
     * @param {object} userInfo - 用户信息
     * @returns {string} 格式化后的头像URL
     */
    formatAvatarUrl(avatarPath, userInfo = {}) {
        // 推荐使用新的AvatarUtils
        if (window.AvatarUtils) {
            return window.AvatarUtils.formatAvatarUrl(avatarPath, userInfo, {
                showInitials: true,
                allowRandomAvatar: false
            });
        }
        
        // 降级处理
        if (!avatarPath || avatarPath.trim() === '') {
            // 没有头像时生成首字母头像
            return this.generateInitialsAvatar(userInfo.username || 'U');
        }
        
        // 如果已经是完整URL，直接返回
        if (avatarPath.startsWith('http') || avatarPath.startsWith('/static/')) {
            return avatarPath;
        }
        
        // 如果是绝对路径，直接返回
        if (avatarPath.startsWith('/')) {
            return avatarPath;
        }
        
        // 处理头像路径
        // 如果路径已包含avatars/，使用uploads路径访问
        if (avatarPath.includes('avatars/')) {
            return `/uploads/${avatarPath}?t=${new Date().getTime()}`;
        } else {
            // 如果是单纯的文件名，添加avatars路径
            return `/uploads/avatars/${avatarPath}?t=${new Date().getTime()}`;
        }
    }

    /**
     * 生成首字母头像（降级方案）
     * @param {string} username - 用户名
     * @returns {string} Data URL
     */
    generateInitialsAvatar(username) {
        const canvas = document.createElement('canvas');
        canvas.width = 128;
        canvas.height = 128;
        const ctx = canvas.getContext('2d');
        
        // 颜色数组
        const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
        
        // 根据用户名生成固定颜色
        let hash = 0;
        if (username && username.length > 0) {
            for (let i = 0; i < username.length; i++) {
                hash = ((hash << 5) - hash) + username.charCodeAt(i);
                hash = hash & hash;
            }
        }
        const colorIndex = Math.abs(hash) % colors.length;
        const bgColor = colors[colorIndex];
        
        // 获取首字母
        let initials = 'U';
        if (username && username.length > 0) {
            if (/[\u4e00-\u9fa5]/.test(username)) {
                // 中文用户名取第一个字
                initials = username.charAt(0);
            } else {
                // 英文用户名取前两个字母
                initials = username.substring(0, 2).toUpperCase();
            }
        }
        
        // 绘制背景
        ctx.fillStyle = bgColor;
        ctx.fillRect(0, 0, 128, 128);
        
        // 绘制字母
        ctx.fillStyle = '#FFFFFF';
        ctx.font = 'bold 48px Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(initials, 64, 64);
        
        return canvas.toDataURL();
    }

    /**
     * 头像加载错误处理
     * @param {HTMLImageElement} img - 图片元素
     * @param {object} userInfo - 用户信息
     */
    handleAvatarError(img, userInfo = {}) {
        console.log('头像加载失败，使用首字母头像');
        
        const username = userInfo.username || img.getAttribute('data-username') || 'U';
        img.src = this.generateInitialsAvatar(username);
        img.onerror = null; // 防止循环调用
    }

    /**
     * 更新所有头像显示
     * @param {string} avatarPath - 头像路径
     * @param {object} userInfo - 用户信息
     */
    updateAllAvatars(avatarPath, userInfo = {}) {
        if (window.AvatarUtils) {
            // 使用新的AvatarUtils
            window.AvatarUtils.initPageAvatars(userInfo, {
                showInitials: true,
                allowRandomAvatar: false
            });
        } else {
            // 降级处理
            const avatarUrl = this.formatAvatarUrl(avatarPath, userInfo);
            document.querySelectorAll('.avatar, .user-avatar, .profile-avatar').forEach(img => {
                img.src = avatarUrl;
                img.onerror = () => this.handleAvatarError(img, userInfo);
            });
        }
    }

    /**
     * 验证上传文件
     * @param {File} file - 要验证的文件
     * @returns {Object} 验证结果
     */
    validateFile(file) {
        if (!file) {
            return { valid: false, message: '请选择文件' };
        }

        if (!this.allowedTypes.includes(file.type)) {
            return { valid: false, message: '只支持 JPG、PNG、GIF 格式的图片' };
        }

        if (file.size > this.maxFileSize) {
            return { valid: false, message: '图片大小不能超过5MB' };
        }

        return { valid: true };
    }

    /**
     * 上传头像
     * @param {File} file - 头像文件
     * @param {string} token - 认证token
     * @returns {Promise} 上传结果
     */
    async uploadAvatar(file, token) {
        // 验证文件
        const validation = this.validateFile(file);
        if (!validation.valid) {
            throw new Error(validation.message);
        }

        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch('/api/user/avatar', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
            },
            body: formData
        });

        const result = await response.json();

        if (!response.ok || !result.success) {
            throw new Error(result.message || '头像上传失败');
        }

        return result.data;
    }

    /**
     * 初始化头像上传功能
     * @param {string} inputId - 文件输入框ID
     * @param {string} buttonId - 上传按钮ID
     */
    initUpload(inputId, buttonId) {
        const input = document.getElementById(inputId);
        const button = document.getElementById(buttonId);

        if (!input || !button) {
            console.warn('头像上传元素未找到');
            return;
        }

        // 点击按钮触发文件选择
        button.addEventListener('click', () => {
            input.click();
        });

        // 文件选择后处理上传
        input.addEventListener('change', async (e) => {
            const file = e.target.files[0];
            if (!file) return;

            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    throw new Error('请先登录');
                }

                // 显示上传进度
                if (window.showToast) {
                    window.showToast('正在上传头像...', 'info');
                }

                const avatarPath = await this.uploadAvatar(file, token);
                
                // 更新所有头像显示
                this.updateAllAvatars(avatarPath);

                if (window.showToast) {
                    window.showToast('头像上传成功', 'success');
                }

            } catch (error) {
                console.error('头像上传失败:', error);
                if (window.showToast) {
                    window.showToast(error.message, 'error');
                }
            } finally {
                // 清空文件输入框
                input.value = '';
            }
        });
    }

    /**
     * 预览头像文件
     * @param {File} file - 要预览的文件
     * @param {string} previewSelector - 预览元素选择器
     */
    previewAvatar(file, previewSelector = '.avatar') {
        if (!file || !file.type.startsWith('image/')) {
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            const previewElements = document.querySelectorAll(previewSelector);
            previewElements.forEach(img => {
                img.src = e.target.result;
            });
        };
        reader.readAsDataURL(file);
    }
}

// 创建全局实例
window.avatarManager = new AvatarManager();

// 全局头像错误处理函数
window.handleAvatarError = function(img) {
    window.avatarManager.handleAvatarError(img);
};

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AvatarManager;
} 