document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const showRegister = document.getElementById('showRegister');
    const showLogin = document.getElementById('showLogin');
    const formTitle = document.getElementById('formTitle');
    const forgotPassword = document.getElementById('forgotPassword');
    const forgotPasswordModal = document.getElementById('forgotPasswordModal');
    const closeForgotModal = document.getElementById('closeForgotModal');
    const sendResetEmail = document.getElementById('sendResetEmail');
    const resendEmail = document.getElementById('resendEmail');
    
    // 验证码相关变量
    let captchaCode = '';
    
    // 初始化验证码
    generateCaptcha();
    
    // 绑定验证码事件
    document.getElementById('refreshCaptcha').addEventListener('click', generateCaptcha);
    document.getElementById('captchaCanvas').addEventListener('click', generateCaptcha);
    
    // 绑定密码显示/隐藏事件
    initPasswordToggle();

    // 生成验证码函数
    function generateCaptcha() {
        const canvas = document.getElementById('captchaCanvas');
        const ctx = canvas.getContext('2d');
        
        // 清除画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 生成随机验证码
        const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
        captchaCode = '';
        for (let i = 0; i < 4; i++) {
            captchaCode += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        
        // 设置背景
        ctx.fillStyle = '#f8f9fa';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // 添加干扰线
        for (let i = 0; i < 3; i++) {
            ctx.strokeStyle = getRandomColor();
            ctx.beginPath();
            ctx.moveTo(Math.random() * canvas.width, Math.random() * canvas.height);
            ctx.lineTo(Math.random() * canvas.width, Math.random() * canvas.height);
            ctx.stroke();
        }
        
        // 绘制验证码
        ctx.font = '18px Arial';
        ctx.textBaseline = 'middle';
        
        for (let i = 0; i < captchaCode.length; i++) {
            ctx.fillStyle = getRandomColor();
            const x = 15 + i * 18;
            const y = canvas.height / 2;
            const angle = (Math.random() - 0.5) * 0.5;
            
            ctx.save();
            ctx.translate(x, y);
            ctx.rotate(angle);
            ctx.fillText(captchaCode.charAt(i), 0, 0);
            ctx.restore();
        }
        
        // 添加干扰点
        for (let i = 0; i < 20; i++) {
            ctx.fillStyle = getRandomColor();
            ctx.beginPath();
            ctx.arc(Math.random() * canvas.width, Math.random() * canvas.height, 1, 0, 2 * Math.PI);
            ctx.fill();
        }
    }
    
    // 获取随机颜色
    function getRandomColor() {
        const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe'];
        return colors[Math.floor(Math.random() * colors.length)];
    }
    
    // 验证验证码
    function validateCaptcha(inputCaptcha) {
        return inputCaptcha.toLowerCase() === captchaCode.toLowerCase();
    }
    
    // 初始化密码显示/隐藏功能
    function initPasswordToggle() {
        // 登录表单密码切换
        const togglePassword = document.getElementById('togglePassword');
        const passwordInput = document.getElementById('password');
        
        if (togglePassword && passwordInput) {
            togglePassword.addEventListener('click', function() {
                togglePasswordVisibility(passwordInput, togglePassword);
            });
        }
        
        // 注册表单密码切换
        const toggleRegPassword = document.getElementById('toggleRegPassword');
        const regPasswordInput = document.getElementById('reg-password');
        
        if (toggleRegPassword && regPasswordInput) {
            toggleRegPassword.addEventListener('click', function() {
                togglePasswordVisibility(regPasswordInput, toggleRegPassword);
            });
        }
    }
    
    // 切换密码可见性
    function togglePasswordVisibility(passwordInput, toggleIcon) {
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
            toggleIcon.title = '隐藏密码';
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
            toggleIcon.title = '显示密码';
        }
    }

    // 找回密码相关功能
    forgotPassword.addEventListener('click', (e) => {
        e.preventDefault();
        forgotPasswordModal.style.display = 'block';
        resetForgotPasswordModal();
    });

    closeForgotModal.addEventListener('click', () => {
        forgotPasswordModal.style.display = 'none';
    });

    // 点击模态框外部关闭
    window.addEventListener('click', (e) => {
        if (e.target === forgotPasswordModal) {
            forgotPasswordModal.style.display = 'none';
        }
    });

    // 发送重置邮件
    sendResetEmail.addEventListener('click', async () => {
        const email = document.getElementById('forgotEmail').value.trim();
        if (!email) {
            showToast('请输入邮箱地址', 'error');
            return;
        }
        
        if (!validateEmail(email)) {
            showToast('请输入有效的邮箱地址', 'error');
            return;
        }

        sendResetEmail.disabled = true;
        sendResetEmail.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发送中...';

        try {
            const response = await fetch('/api/user/forgot-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email: email })
            });

            const data = await response.json();
            if (data.code === 200) {
                showForgotStep(2);
            } else {
                showToast(data.message || '发送失败，请稍后重试', 'error');
            }
        } catch (error) {
            console.error('发送重置邮件失败:', error);
            showToast('发送失败，请检查网络连接', 'error');
        } finally {
            sendResetEmail.disabled = false;
            sendResetEmail.innerHTML = '发送重置链接';
        }
    });

    // 重新发送邮件
    resendEmail.addEventListener('click', () => {
        showForgotStep(1);
    });

    // 显示找回密码步骤
    function showForgotStep(step) {
        document.querySelectorAll('.forgot-step').forEach(el => {
            el.classList.remove('active');
        });
        document.getElementById(`forgotStep${step}`).classList.add('active');
    }

    // 重置找回密码模态框
    function resetForgotPasswordModal() {
        showForgotStep(1);
        document.getElementById('forgotEmail').value = '';
    }

    // 邮箱验证函数
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    // 切换表单显示
    function toggleForms(showRegisterForm) {
        loginForm.style.display = showRegisterForm ? 'none' : 'block';
        registerForm.style.display = showRegisterForm ? 'block' : 'none';
        formTitle.textContent = showRegisterForm ? '创建账号' : '登录账号';
        document.querySelector('.subtitle').textContent = showRegisterForm ? 
            '填写以下信息创建您的账号' : '欢迎回来，请输入您的账号信息';
        
        // 切换到登录表单时刷新验证码
        if (!showRegisterForm) {
            generateCaptcha();
            document.getElementById('captcha').value = '';
        }
    }

    showRegister.addEventListener('click', (e) => {
        e.preventDefault();
        toggleForms(true);
    });

    showLogin.addEventListener('click', (e) => {
        e.preventDefault();
        toggleForms(false);
    });

    // 登录表单提交
    loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        // 验证验证码
        const captchaInput = document.getElementById('captcha').value.trim();
        if (!captchaInput) {
            showToast('请输入验证码', 'error');
            return;
        }
        
        if (!validateCaptcha(captchaInput)) {
            showToast('验证码错误，请重新输入', 'error');
            generateCaptcha(); // 刷新验证码
            document.getElementById('captcha').value = '';
            document.getElementById('captcha').focus();
            return;
        }
        
        const submitBtn = loginForm.querySelector('.submit-btn');
        submitBtn.disabled = true;
        submitBtn.textContent = '登录中...';

        try {
            const response = await fetch('/api/user/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: document.getElementById('username').value,
                    password: document.getElementById('password').value,
                    captcha: captchaInput,
                    ip: await getClientIP()
                })
            });

            const data = await response.json();
            if (data.code === 200) {
                localStorage.setItem('token', data.data.token);
                
                // 设置JWT_TOKEN到cookie中，有效期1天
                document.cookie = `JWT_TOKEN=${data.data.token}; path=/; max-age=86400; SameSite=Strict;`;
                
                showToast('登录成功', 'success');
                setTimeout(() => window.location.href = '/index.html', 1000);
            } else {
                showToast(data.message, 'error');
                // 登录失败时刷新验证码
                generateCaptcha();
                document.getElementById('captcha').value = '';
            }
        } catch (error) {
            console.error('登录失败:', error);
            showToast('登录失败，请重试', 'error');
            // 网络错误时也刷新验证码
            generateCaptcha();
            document.getElementById('captcha').value = '';
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = '登录';
        }
    });

    // 注册表单提交
    registerForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const submitBtn = registerForm.querySelector('.submit-btn');
        submitBtn.disabled = true;
        submitBtn.textContent = '注册中...';

        try {
            const response = await fetch('/api/user/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: document.getElementById('reg-username').value,
                    password: document.getElementById('reg-password').value,
                    email: document.getElementById('reg-email').value
                })
            });

            const data = await response.json();
            if (data.code === 200) {
                showToast('注册成功', 'success');
                setTimeout(() => toggleForms(false), 1000);
            } else {
                showToast(data.message, 'error');
            }
        } catch (error) {
            console.error('注册失败:', error);
            showToast('注册失败，请重试', 'error');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = '注册';
        }
    });

    // 获取客户端IP
    async function getClientIP() {
        try {
            const response = await fetch('https://api.ipify.org?format=json');
            const data = await response.json();
            return data.ip;
        } catch (error) {
            console.error('获取IP失败:', error);
            return '';
        }
    }

    // 提示消息
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.classList.add('show');
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }, 100);
    }
});