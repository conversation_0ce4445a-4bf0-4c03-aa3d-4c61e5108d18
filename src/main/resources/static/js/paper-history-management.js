/**
 * 历史试卷管理JavaScript
 * 提供完整的试卷管理功能，包括CRUD操作、批量操作、搜索筛选等
 */

// 全局变量
let currentPage = 1;
let pageSize = 12;
let totalPages = 0;
let selectedPapers = new Set();
let allPapers = [];
let filteredPapers = [];

// 初始化历史试卷管理
function initPaperHistoryManagement() {
    console.log('初始化历史试卷管理...');

    // 绑定事件
    bindEvents();

    // 初始化搜索
    initSearch();

    // 初始化批量操作
    initBatchOperations();
}

// 绑定事件
function bindEvents() {
    // 搜索输入框
    $('#searchInput').on('input', debounce(searchPapers, 300));

    // 排序和筛选
    $('#sortSelect, #dateFilter, #scoreFilter').on('change', searchPapers);

    // 全选复选框
    $('#selectAll').on('change', function () {
        const isChecked = $(this).is(':checked');
        $('.paper-checkbox').prop('checked', isChecked);
        updateSelectedPapers();
    });

    // 批量操作按钮
    $('#batchDownloadBtn').on('click', showBatchDownloadModal);
    $('#batchEditBtn').on('click', showBatchEditModal);
    $('#batchDeleteBtn').on('click', confirmBatchDelete);
    $('#batchExportBtn').on('click', batchExportData);
    $('#cancelSelectionBtn').on('click', cancelSelection);

    // 模态框确认按钮
    $('#confirmDownloadBtn').on('click', executeBatchDownload);
    $('#confirmEditBtn').on('click', executeBatchEdit);
}

// 初始化搜索
function initSearch() {
    // 搜索框回车事件
    $('#searchInput').on('keypress', function (e) {
        if (e.which === 13) {
            searchPapers();
        }
    });
}

// 初始化批量操作
function initBatchOperations() {
    // 初始状态隐藏批量操作工具栏
    $('#batchToolbar').hide();
}

// 加载试卷列表
function loadPapers(page = 1) {
    currentPage = page;
    showLoading();

    const params = {
        page: page - 1, // 后端从0开始
        size: pageSize,
        search: $('#searchInput').val(),
        sort: $('#sortSelect').val(),
        dateFilter: $('#dateFilter').val(),
        scoreFilter: $('#scoreFilter').val()
    };

    // 获取token
    const token = localStorage.getItem('token');

    $.ajax({
        url: '/paper-history/papers/history',
        method: 'GET',
        data: params,
        headers: {
            'Authorization': token ? (token.startsWith('Bearer ') ? token : `Bearer ${token}`) : ''
        },
        success: function (response) {
            console.log('试卷列表加载成功:', response);
            if (response.success && response.data) {
                allPapers = response.data.content || [];
                filteredPapers = [...allPapers];
                totalPages = response.data.totalPages || 0;

                hideLoading();
                renderPapers(allPapers);
                renderPagination();
                updateStatistics();
            } else {
                showError(response.message || '获取试卷列表失败');
                hideLoading();
            }
        },
        error: function (xhr, status, error) {
            console.error('加载试卷列表失败:', error);
            if (xhr.status === 401) {
                showError('登录已过期，请重新登录');
                // 可以选择重定向到登录页面
                // window.location.href = '/login';
            } else {
                showError('加载试卷列表失败，请稍后重试');
            }
            hideLoading();
        }
    });
}

// 渲染试卷列表
function renderPapers(papers) {
    const container = $('#papersList');
    container.empty();

    if (!papers || papers.length === 0) {
        container.html(`
            <div class="col-12 text-center py-5">
                <i class="fas fa-file-alt fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">暂无试卷</h4>
                <p class="text-muted">您还没有创建任何试卷，<a href="/paper/generate" class="text-decoration-none">立即创建</a></p>
            </div>
        `);
        $('#batchToolbar').hide();
        return;
    }

    papers.forEach(paper => {
        const paperCard = createPaperCard(paper);
        container.append(paperCard);
    });

    // 显示批量操作工具栏
    $('#batchToolbar').show();

    // 绑定复选框事件
    $('.paper-checkbox').on('change', updateSelectedPapers);
}

// 创建试卷卡片
function createPaperCard(paper) {
    const createTime = new Date(paper.createTime).toLocaleString('zh-CN');
    const difficultyBadge = getDifficultyBadge(paper.difficulty);
    const statusBadge = getStatusBadge(paper.fileFormat);

    return `
        <div class="col-md-6 col-lg-4">
            <div class="card paper-card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="form-check">
                        <input class="form-check-input paper-checkbox" type="checkbox" 
                               value="${paper.id}" id="paper_${paper.id}">
                        <label class="form-check-label" for="paper_${paper.id}"></label>
                    </div>
                    <div class="d-flex gap-1">
                        ${difficultyBadge}
                        ${statusBadge}
                    </div>
                </div>
                <div class="card-body">
                    <h6 class="card-title">
                        <a href="#" class="paper-title" onclick="showPaperDetail(${paper.id})">
                            ${escapeHtml(paper.title)}
                        </a>
                    </h6>
                    <div class="paper-meta mb-3">
                        <div class="d-flex justify-content-between">
                            <span><i class="fas fa-calendar me-1"></i>${createTime}</span>
                            <span><i class="fas fa-star me-1"></i>${paper.totalScore}分</span>
                        </div>
                        <div class="mt-1">
                            <span><i class="fas fa-question-circle me-1"></i>${paper.questionCount || 0}题</span>
                            <span class="ms-2"><i class="fas fa-download me-1"></i>${paper.downloadCount || 0}次</span>
                        </div>
                    </div>
                    
                    <!-- 难度分布 -->
                    <div class="mb-3">
                        <small class="text-muted">难度分布:</small>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-success" style="width: ${paper.easyPercent || 0}%" title="简单: ${paper.easyPercent || 0}%"></div>
                            <div class="progress-bar bg-warning" style="width: ${paper.mediumPercent || 0}%" title="中等: ${paper.mediumPercent || 0}%"></div>
                            <div class="progress-bar bg-danger" style="width: ${paper.hardPercent || 0}%" title="困难: ${paper.hardPercent || 0}%"></div>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <div class="btn-group w-100" role="group">
                        <button class="btn btn-outline-primary action-btn" onclick="previewPaper(${paper.id})" title="预览">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success action-btn" onclick="downloadPaper(${paper.id}, 'pdf')" title="下载PDF">
                            <i class="fas fa-file-pdf"></i>
                        </button>
                        <button class="btn btn-outline-info action-btn" onclick="downloadPaper(${paper.id}, 'word')" title="下载Word">
                            <i class="fas fa-file-word"></i>
                        </button>
                        <button class="btn btn-outline-warning action-btn" onclick="editPaper(${paper.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-secondary action-btn" onclick="clonePaper(${paper.id})" title="克隆">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button class="btn btn-outline-danger action-btn" onclick="deletePaper(${paper.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// 获取难度徽章
function getDifficultyBadge(difficulty) {
    const badges = {
        1: '<span class="badge bg-success">简单</span>',
        2: '<span class="badge bg-warning">中等</span>',
        3: '<span class="badge bg-danger">困难</span>'
    };
    return badges[difficulty] || '<span class="badge bg-secondary">未知</span>';
}

// 获取状态徽章
function getStatusBadge(fileFormat) {
    const badges = {
        'pdf': '<span class="badge bg-primary">PDF</span>',
        'docx': '<span class="badge bg-info">DOCX</span>',
        'doc': '<span class="badge bg-secondary">DOC</span>'
    };
    return badges[fileFormat] || '<span class="badge bg-light">未知格式</span>';
}

// 搜索试卷
function searchPapers() {
    currentPage = 1;
    loadPapers(currentPage);
}

// 更新选中的试卷
function updateSelectedPapers() {
    selectedPapers.clear();
    $('.paper-checkbox:checked').each(function () {
        selectedPapers.add(parseInt($(this).val()));
    });

    const count = selectedPapers.size;
    $('#selectedCount').text(count);
    $('#selectedCountText').text(count);

    // 更新批量操作按钮状态
    const hasSelection = count > 0;
    $('#batchDownloadBtn, #batchEditBtn, #batchDeleteBtn, #batchExportBtn').prop('disabled', !hasSelection);

    // 更新全选复选框状态
    const totalCheckboxes = $('.paper-checkbox').length;
    if (count === 0) {
        $('#selectAll').prop('indeterminate', false).prop('checked', false);
    } else if (count === totalCheckboxes) {
        $('#selectAll').prop('indeterminate', false).prop('checked', true);
    } else {
        $('#selectAll').prop('indeterminate', true).prop('checked', false);
    }
}

// 显示批量下载模态框
function showBatchDownloadModal() {
    if (selectedPapers.size === 0) {
        showAlert('请先选择要下载的试卷', 'warning');
        return;
    }

    $('#downloadCountText').text(selectedPapers.size);
    $('#batchDownloadModal').modal('show');
}

// 执行批量下载
function executeBatchDownload() {
    const format = $('input[name="downloadFormat"]:checked').val();
    const version = $('#downloadVersion').val();
    const paperIds = Array.from(selectedPapers);
    const token = localStorage.getItem('token');

    // 显示进度条
    $('#downloadProgress').show();
    $('#confirmDownloadBtn').prop('disabled', true);

    // 构建URL参数
    const params = new URLSearchParams();
    paperIds.forEach(id => params.append('paperIds', id));
    params.append('format', format);
    params.append('version', version);

    $.ajax({
        url: '/paper-history/batch-download',
        method: 'POST',
        headers: {
            'Authorization': token ? (token.startsWith('Bearer ') ? token : `Bearer ${token}`) : ''
        },
        data: params.toString(),
        contentType: 'application/x-www-form-urlencoded',
        xhrFields: {
            responseType: 'blob'
        },
        success: function (data, status, xhr) {
            // 创建下载链接
            const blob = new Blob([data]);
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `批量试卷_${new Date().getTime()}.zip`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            $('#batchDownloadModal').modal('hide');
            showAlert('批量下载成功！', 'success');

            // 更新下载统计
            updateDownloadStats();
        },
        error: function (xhr, status, error) {
            console.error('批量下载失败:', error);
            if (xhr.status === 401) {
                showAlert('请先登录后再进行批量下载', 'warning');
            } else {
                showAlert('批量下载失败，请稍后重试', 'danger');
            }
        },
        complete: function () {
            $('#downloadProgress').hide();
            $('#confirmDownloadBtn').prop('disabled', false);
        }
    });
}

// 显示批量编辑模态框
function showBatchEditModal() {
    if (selectedPapers.size === 0) {
        showAlert('请先选择要编辑的试卷', 'warning');
        return;
    }

    $('#editCountText').text(selectedPapers.size);
    $('#batchEditModal').modal('show');
}

// 执行批量编辑
function executeBatchEdit() {
    const paperIds = Array.from(selectedPapers);
    const token = localStorage.getItem('token');
    const editData = {
        paperIds: paperIds,
        titlePrefix: $('#titlePrefix').val(),
        titleSuffix: $('#titleSuffix').val(),
        categoryTag: $('#categoryTag').val(),
        difficultyLevel: $('#difficultyLevel').val()
    };

    // 过滤空值
    Object.keys(editData).forEach(key => {
        if (!editData[key] && key !== 'paperIds') {
            delete editData[key];
        }
    });

    if (Object.keys(editData).length === 1) {
        showAlert('请至少填写一个编辑字段', 'warning');
        return;
    }

    $('#confirmEditBtn').prop('disabled', true);

    $.ajax({
        url: '/paper-history/batch-edit',
        method: 'POST',
        headers: {
            'Authorization': token ? (token.startsWith('Bearer ') ? token : `Bearer ${token}`) : ''
        },
        contentType: 'application/json',
        data: JSON.stringify(editData),
        success: function (response) {
            if (response.success) {
                $('#batchEditModal').modal('hide');
                showAlert(response.message || '批量编辑成功！', 'success');

                // 重新加载试卷列表
                loadPapers(currentPage);

                // 清空表单
                $('#batchEditForm')[0].reset();
            } else {
                showAlert(response.message || '批量编辑失败', 'danger');
            }
        },
        error: function (xhr, status, error) {
            console.error('批量编辑失败:', error);
            if (xhr.status === 401) {
                showAlert('请先登录后再进行批量编辑', 'warning');
            } else {
                showAlert('批量编辑失败，请稍后重试', 'danger');
            }
        },
        complete: function () {
            $('#confirmEditBtn').prop('disabled', false);
        }
    });
}

// 确认批量删除
function confirmBatchDelete() {
    if (selectedPapers.size === 0) {
        showAlert('请先选择要删除的试卷', 'warning');
        return;
    }

    if (confirm(`确定要删除选中的 ${selectedPapers.size} 个试卷吗？此操作不可恢复！`)) {
        executeBatchDelete();
    }
}

// 执行批量删除
function executeBatchDelete() {
    const paperIds = Array.from(selectedPapers);
    const token = localStorage.getItem('token');

    $('#batchDeleteBtn').prop('disabled', true);

    $.ajax({
        url: '/paper-history/batch-delete',
        method: 'POST',
        headers: {
            'Authorization': token ? (token.startsWith('Bearer ') ? token : `Bearer ${token}`) : ''
        },
        contentType: 'application/json',
        data: JSON.stringify(paperIds),
        success: function (response) {
            if (response.success) {
                showAlert(response.message || `成功删除 ${paperIds.length} 个试卷！`, 'success');

                // 清空选择
                cancelSelection();

                // 重新加载试卷列表
                loadPapers(currentPage);
            } else {
                showAlert(response.message || '批量删除失败', 'danger');
            }
        },
        error: function (xhr, status, error) {
            console.error('批量删除失败:', error);
            if (xhr.status === 401) {
                showAlert('请先登录后再进行批量删除', 'warning');
            } else {
                showAlert('批量删除失败，请稍后重试', 'danger');
            }
        },
        complete: function () {
            $('#batchDeleteBtn').prop('disabled', false);
        }
    });
}

// 批量导出数据
function batchExportData() {
    if (selectedPapers.size === 0) {
        showAlert('请先选择要导出的试卷', 'warning');
        return;
    }

    const paperIds = Array.from(selectedPapers);
    const token = localStorage.getItem('token');

    $('#batchExportBtn').prop('disabled', true);

    $.ajax({
        url: '/paper-history/batch-export',
        method: 'POST',
        headers: {
            'Authorization': token ? (token.startsWith('Bearer ') ? token : `Bearer ${token}`) : ''
        },
        data: { paperIds: paperIds },
        xhrFields: {
            responseType: 'blob'
        },
        success: function (data) {
            // 创建下载链接
            const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `试卷数据_${new Date().getTime()}.xlsx`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            showAlert('数据导出成功！', 'success');
        },
        error: function (xhr, status, error) {
            console.error('数据导出失败:', error);
            if (xhr.status === 401) {
                showAlert('请先登录后再进行数据导出', 'warning');
            } else {
                showAlert('数据导出失败，请稍后重试', 'danger');
            }
        },
        complete: function () {
            $('#batchExportBtn').prop('disabled', false);
        }
    });
}

// 取消选择
function cancelSelection() {
    selectedPapers.clear();
    $('.paper-checkbox').prop('checked', false);
    $('#selectAll').prop('checked', false).prop('indeterminate', false);
    updateSelectedPapers();
}

// 单个试卷操作
function previewPaper(paperId) {
    window.open(`/paper-history/preview/${paperId}`, '_blank');
}

function downloadPaper(paperId, format) {
    const token = localStorage.getItem('token');
    const tokenParam = token ? `&token=${encodeURIComponent(token)}` : '';
    window.location.href = `/api/papers/download/${paperId}?format=${format}${tokenParam}`;
    updateDownloadStats();
}

function editPaper(paperId) {
    window.location.href = `/papers/generate?paperId=${paperId}&mode=edit`;
}

function clonePaper(paperId) {
    if (confirm('确定要克隆这个试卷吗？')) {
        const token = localStorage.getItem('token');
        $.ajax({
            url: `/paper-history/${paperId}/clone`,
            method: 'POST',
            headers: {
                'Authorization': token ? (token.startsWith('Bearer ') ? token : `Bearer ${token}`) : ''
            },
            success: function (response) {
                if (response.success) {
                    showAlert(response.message || '试卷克隆成功！', 'success');
                    loadPapers(currentPage);
                } else {
                    showAlert(response.message || '试卷克隆失败', 'danger');
                }
            },
            error: function (xhr, status, error) {
                console.error('试卷克隆失败:', error);
                if (xhr.status === 401) {
                    showAlert('登录已过期，请重新登录', 'danger');
                } else {
                    showAlert('试卷克隆失败，请稍后重试', 'danger');
                }
            }
        });
    }
}

function deletePaper(paperId) {
    if (confirm('确定要删除这个试卷吗？此操作不可恢复！')) {
        const token = localStorage.getItem('token');
        $.ajax({
            url: `/paper-history/${paperId}`,
            method: 'DELETE',
            headers: {
                'Authorization': token ? (token.startsWith('Bearer ') ? token : `Bearer ${token}`) : ''
            },
            success: function (response) {
                if (response.success) {
                    showAlert(response.message || '试卷删除成功！', 'success');
                    loadPapers(currentPage);
                } else {
                    showAlert(response.message || '试卷删除失败', 'danger');
                }
            },
            error: function (xhr, status, error) {
                console.error('试卷删除失败:', error);
                if (xhr.status === 401) {
                    showAlert('登录已过期，请重新登录', 'danger');
                } else {
                    showAlert('试卷删除失败，请稍后重试', 'danger');
                }
            }
        });
    }
}

// 显示试卷详情
function showPaperDetail(paperId) {
    const token = localStorage.getItem('token');
    $.ajax({
        url: `/paper-history/${paperId}`,
        method: 'GET',
        headers: {
            'Authorization': token ? (token.startsWith('Bearer ') ? token : `Bearer ${token}`) : ''
        },
        success: function (response) {
            if (response.success && response.data) {
                renderPaperDetail(response.data);
                $('#paperDetailModal').modal('show');
            } else {
                showAlert(response.message || '获取试卷详情失败', 'danger');
            }
        },
        error: function (xhr, status, error) {
            console.error('获取试卷详情失败:', error);
            if (xhr.status === 401) {
                showAlert('登录已过期，请重新登录', 'danger');
            } else {
                showAlert('获取试卷详情失败，请稍后重试', 'danger');
            }
        }
    });
}

// 渲染试卷详情
function renderPaperDetail(paper) {
    const createTime = new Date(paper.createTime).toLocaleString('zh-CN');
    const updateTime = paper.updateTime ? new Date(paper.updateTime).toLocaleString('zh-CN') : '未更新';

    const content = `
        <div class="row">
            <div class="col-md-8">
                <h4>${escapeHtml(paper.title)}</h4>
                <div class="mb-3">
                    <span class="badge bg-primary me-2">总分: ${paper.totalScore}分</span>
                    <span class="badge bg-success me-2">题目: ${paper.questionCount || 0}题</span>
                    <span class="badge bg-info me-2">下载: ${paper.downloadCount || 0}次</span>
                    ${getDifficultyBadge(paper.difficulty)}
                </div>
                
                <div class="mb-3">
                    <strong>创建时间:</strong> ${createTime}<br>
                    <strong>更新时间:</strong> ${updateTime}<br>
                    <strong>试卷描述:</strong> ${escapeHtml(paper.description || '暂无描述')}
                </div>
                
                <!-- 难度分布图表 -->
                <div class="mb-3">
                    <strong>难度分布:</strong>
                    <div class="progress mt-2" style="height: 20px;">
                        <div class="progress-bar bg-success" style="width: ${paper.easyPercent || 0}%">
                            简单 ${paper.easyPercent || 0}%
                        </div>
                        <div class="progress-bar bg-warning" style="width: ${paper.mediumPercent || 0}%">
                            中等 ${paper.mediumPercent || 0}%
                        </div>
                        <div class="progress-bar bg-danger" style="width: ${paper.hardPercent || 0}%">
                            困难 ${paper.hardPercent || 0}%
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">快速操作</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="previewPaper(${paper.id})">
                                <i class="fas fa-eye me-1"></i>预览试卷
                            </button>
                            <button class="btn btn-outline-success" onclick="downloadPaper(${paper.id}, 'pdf')">
                                <i class="fas fa-file-pdf me-1"></i>下载PDF
                            </button>
                            <button class="btn btn-outline-info" onclick="downloadPaper(${paper.id}, 'word')">
                                <i class="fas fa-file-word me-1"></i>下载Word
                            </button>
                            <button class="btn btn-outline-warning" onclick="editPaper(${paper.id})">
                                <i class="fas fa-edit me-1"></i>编辑试卷
                            </button>
                            <button class="btn btn-outline-secondary" onclick="clonePaper(${paper.id})">
                                <i class="fas fa-copy me-1"></i>克隆试卷
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('#paperDetailContent').html(content);
    $('#editPaperBtn').off('click').on('click', () => editPaper(paper.id));
}

// 渲染分页
function renderPagination() {
    const pagination = $('#pagination');
    pagination.empty();

    if (totalPages <= 1) {
        return;
    }

    // 上一页
    pagination.append(`
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadPapers(${currentPage - 1})">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `);

    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    if (startPage > 1) {
        pagination.append(`<li class="page-item"><a class="page-link" href="#" onclick="loadPapers(1)">1</a></li>`);
        if (startPage > 2) {
            pagination.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        pagination.append(`
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadPapers(${i})">${i}</a>
            </li>
        `);
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            pagination.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
        }
        pagination.append(`<li class="page-item"><a class="page-link" href="#" onclick="loadPapers(${totalPages})">${totalPages}</a></li>`);
    }

    // 下一页
    pagination.append(`
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadPapers(${currentPage + 1})">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `);
}

// 更新统计信息
function updateStatistics() {
    // 获取统计数据
    const token = localStorage.getItem('token');
    $.ajax({
        url: '/paper-history/statistics',
        method: 'GET',
        headers: {
            'Authorization': token ? (token.startsWith('Bearer ') ? token : `Bearer ${token}`) : ''
        },
        success: function (response) {
            if (response.success && response.data) {
                const stats = response.data;
                $('#totalPapers').text(stats.totalPapers || 0);
                $('#todayPapers').text(stats.todayPapers || 0);
                $('#downloadCount').text(stats.totalDownloads || 0);
            }
        },
        error: function (xhr, status, error) {
            console.error('获取统计信息失败:', error);
            if (xhr.status === 401) {
                console.warn('统计信息获取需要登录');
            }
        }
    });
}

// 更新下载统计
function updateDownloadStats() {
    const token = localStorage.getItem('token');
    $.ajax({
        url: '/paper-history/statistics',
        method: 'GET',
        headers: {
            'Authorization': token ? (token.startsWith('Bearer ') ? token : `Bearer ${token}`) : ''
        },
        success: function (response) {
            if (response.success && response.data) {
                const stats = response.data;
                $('#downloadCount').text(stats.totalDownloads || 0);
            }
        },
        error: function (xhr, status, error) {
            console.error('获取下载统计失败:', error);
            if (xhr.status === 401) {
                console.warn('下载统计获取需要登录');
            }
        }
    });
}

// 工具函数
function showLoading() {
    $('#loadingState').show();
    $('#papersList').hide();
}

function hideLoading() {
    $('#loadingState').hide();
    $('#papersList').show();
}

function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 移除现有的alert
    $('.alert').remove();

    // 添加新的alert到页面顶部
    $('body').prepend(alertHtml);

    // 3秒后自动消失
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 3000);
}

function showError(message) {
    showAlert(message, 'danger');
}

function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 导出函数供全局使用
window.initPaperHistoryManagement = initPaperHistoryManagement;
window.loadPapers = loadPapers;
window.searchPapers = searchPapers;
window.previewPaper = previewPaper;
window.downloadPaper = downloadPaper;
window.editPaper = editPaper;
window.clonePaper = clonePaper;
window.deletePaper = deletePaper;
window.showPaperDetail = showPaperDetail;