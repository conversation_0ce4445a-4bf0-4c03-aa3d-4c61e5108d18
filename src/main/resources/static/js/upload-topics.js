// Hoist utility functions to the top to ensure they are defined before use.
function renderMarkdown(text) {
    if (!text) return '';
    if (typeof marked === 'undefined') {
        console.error('marked.js library is not loaded.');
        return text; // Fallback to plain text
    }
    const unescapedText = String(text).replace(/\\/g, '\\');
    return marked.parse(unescapedText, { breaks: true, gfm: true });
}

// Global variables
let editor;
let previewTopics = [];

document.addEventListener('DOMContentLoaded', function() {
    // Initialize CodeMirror editor
    editor = CodeMirror.fromTextArea(document.getElementById('jsonEditor'), {
        mode: {name: "javascript", json: true},
        theme: "material-darker",
        lineNumbers: true,
        matchBrackets: true,
        autoCloseBrackets: true,
        foldGutter: true,
        gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
        lineWrapping: true,
        extraKeys: {"Ctrl-Space": "autocomplete"}
    });

    // Get DOM elements
    const insertExampleBtn = document.getElementById('insertExampleBtn');
    const submitTopicsBtn = document.getElementById('submitTopicsBtn');
    const defaultSubmitBtnHtml = '<i class="bi bi-cloud-upload"></i> 提交题目';
    submitTopicsBtn.innerHTML = defaultSubmitBtnHtml;
    const validateAndPreviewBtn = document.getElementById('validateAndPreviewBtn');
    const previewContainer = document.getElementById('previewContainer');
    const errorMessageDiv = document.getElementById('errorMessage');
    const successMessageDiv = document.getElementById('successMessage');
    const previewTopicCountSpan = document.getElementById('previewTopicCount');

    // Event Listeners
    insertExampleBtn.addEventListener('click', function () {
        editor.setValue(JSON.stringify(exampleTopicData, null, 2));
        showToast('示例JSON已插入编辑器', 'info');
        validateAndPreview();
    });

    validateAndPreviewBtn.addEventListener('click', validateAndPreview);

    submitTopicsBtn.addEventListener('click', async function () {
        if (previewTopics.length === 0) {
            showError('没有可提交的题目。');
            return;
        }
        this.disabled = true;
        this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 提交中...';

        let submissionSuccess = false;

        try {
            const token = getAuthHeader();
            if (!token) {
                showError('用户未登录或会话已过期，请重新登录。');
                return; // Finally will handle button reset
            }

            const response = await fetch('/api/topics/upload', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(previewTopics)
            });

            const result = await response.json();

            if (response.ok && result.code === 200) {
                showSuccess(result.message || `成功提交 ${previewTopics.length} 道题目！`);
                editor.setValue('');
                previewTopics = [];
                renderPreview();
                submissionSuccess = true;

                if (typeof window.refreshStatsChart === 'function') {
                    window.refreshStatsChart();
                }
            } else {
                throw new Error(result.message || `服务器错误: ${response.status}`);
            }
        } catch (error) {
            showError('提交失败: ' + error.message);
        } finally {
            // 提交结束后总是恢复按钮为可用，并显示默认文字
            this.disabled = false;
            this.innerHTML = defaultSubmitBtnHtml;
        }
    });

    // Core Functions
    function validateAndPreview() {
        const jsonString = editor.getValue();
        const autoFixBtn = document.getElementById('autoFixTagsBtn');
        const tagAnalysisSection = document.getElementById('tagAnalysisSection');

        previewTopics = [];
        previewTopicCountSpan.textContent = '0';
        submitTopicsBtn.disabled = true;
        // 确保按钮文本复位，避免上一轮提交中的 loading 状态残留
        submitTopicsBtn.innerHTML = defaultSubmitBtnHtml;
        previewContainer.classList.add('preview-container-empty');
        previewContainer.innerHTML = '<p class="text-muted text-center p-5"><i class="bi bi-card-text fs-1"></i><br>请在编辑器中输入或粘贴题目的JSON数据。</p>';

        if (!jsonString.trim()) {
            showError('JSON数据不能为空。');
            // 隐藏智能修复按钮和标签分析区域
            if (autoFixBtn) autoFixBtn.style.display = 'none';
            if (tagAnalysisSection) tagAnalysisSection.classList.add('d-none');
            return;
        }

        try {
            const topics = JSON.parse(jsonString);
            if (!Array.isArray(topics)) throw new Error('JSON数据必须是一个数组。');

            for (let i = 0; i < topics.length; i++) {
                const topic = topics[i];
                const errors = [];
                const topicTitle = topic.title ? `"${topic.title.substring(0, 30)}${topic.title.length > 30 ? '...' : ''}"` : '(无标题)';
                
                // 检查必需字段是否存在
                if (!topic.know_id) errors.push('缺少 know_id 字段');
                if (!topic.type) errors.push('缺少 type 字段');
                if (!topic.title) errors.push('缺少 title 字段');
                if (topic.difficulty === undefined || topic.difficulty === null) errors.push('缺少 difficulty 字段');
                
                // 检查字段类型和值的有效性
                if (topic.know_id !== undefined && (!Number.isInteger(topic.know_id) || topic.know_id <= 0)) {
                    errors.push('know_id 必须是正整数');
                }
                
                if (topic.type !== undefined) {
                    const validTypes = ['choice', 'multiple', 'judge', 'fill', 'short', 'subjective'];
                    if (!validTypes.includes(topic.type)) {
                        errors.push(`type 必须是以下值之一: ${validTypes.join(', ')}`);
                    }
                }
                
                if (topic.title !== undefined && (typeof topic.title !== 'string' || topic.title.trim() === '')) {
                    errors.push('title 必须是非空字符串');
                }
                
                if (topic.difficulty !== undefined && (typeof topic.difficulty !== 'number' || topic.difficulty < 0 || topic.difficulty > 1)) {
                    errors.push('difficulty 必须是 0.0 到 1.0 之间的数字');
                }
                
                // 检查选择题和多选题的选项
                if (topic.type === 'choice' || topic.type === 'multiple') {
                    if (!topic.options || !Array.isArray(topic.options) || topic.options.length === 0) {
                        errors.push('选择题和多选题必须包含 options 数组');
                    } else {
                        for (let j = 0; j < topic.options.length; j++) {
                            const option = topic.options[j];
                            if (!option.key || !option.name) {
                                errors.push(`选项 ${j + 1} 缺少 key 或 name 字段`);
                            }
                        }
                    }
                }
                
                // 检查答案字段
                if (topic.type === 'choice' || topic.type === 'multiple' || topic.type === 'judge') {
                    if (!topic.answer) {
                        errors.push('选择题、多选题和判断题必须包含 answer 字段');
                    }
                }
                
                if (errors.length > 0) {
                    throw new Error(`第 ${i + 1} 道题目 ${topicTitle} 存在以下问题:\n${errors.map(err => '• ' + err).join('\n')}`);
                }
            }

            if (topics.length === 0) {
                showSuccess('JSON数组为空，没有可预览的题目。');
                previewContainer.innerHTML = '<p class="text-muted text-center p-5"><i class="bi bi-card-text fs-1"></i><br>JSON数组为空。</p>';
                // 确保隐藏智能修复按钮和标签分析区域
                if (autoFixBtn) autoFixBtn.style.display = 'none';
                if (tagAnalysisSection) tagAnalysisSection.classList.add('d-none');
                return;
            }

            previewTopics = topics;
            showSuccess(`JSON格式正确，共 ${previewTopics.length} 道题目，预览已生成。`);
            errorMessageDiv.classList.add('d-none');
            submitTopicsBtn.disabled = false;

            // 分析标签并显示统计信息
            analyzeAndDisplayTags(topics);
            renderPreview();

        } catch (e) {
            showError('JSON解析或验证失败: ' + e.message);
            previewContainer.innerHTML = `<p class="text-danger text-center p-5"><i class="bi bi-x-octagon-fill fs-1"></i><br>${e.message}</p>`;
            // 确保在解析失败时隐藏智能修复按钮和标签分析区域
            if (autoFixBtn) autoFixBtn.style.display = 'none';
            if (tagAnalysisSection) tagAnalysisSection.classList.add('d-none');
        }
    }

    function renderPreview() {
        const previewContainer = document.getElementById('previewContainer');
        const previewTopicCountSpan = document.getElementById('previewTopicCount');

        previewTopicCountSpan.textContent = previewTopics.length;

        if (previewTopics.length === 0) {
            previewContainer.innerHTML = '<p class="text-muted">没有预览内容。</p>';
            previewContainer.classList.add('preview-container-empty');
            return;
        }

        previewContainer.classList.remove('preview-container-empty');

        let allTopicsHtml = '';
        previewTopics.forEach((topic, index) => {
            let optionsHtml = '';
            if (topic.options && Array.isArray(topic.options)) {
                optionsHtml = topic.options.map(opt => {
                    const isCorrect = topic.answer && topic.answer.includes(opt.key);
                    const correctClass = isCorrect ? 'correct-answer' : '';
                    return `<li class="list-group-item ${correctClass}"><strong>${opt.key}:</strong> ${renderMarkdown(opt.name)}</li>`;
                }).join('');
            }

            let tagsHtml = '';
            if (topic.tags && typeof topic.tags === 'string' && topic.tags.trim() !== '') {
                tagsHtml = topic.tags.split(',').map(tag => `<span class="badge bg-secondary me-1">${tag.trim()}</span>`).join('');
            }

            allTopicsHtml += `
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>题目 ${index + 1}</span>
                        <div>${tagsHtml}</div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">${renderMarkdown(topic.title)}</h5>
                        <ul class="list-group list-group-flush">
                        ${optionsHtml}
                        </ul>
                        <div class="mt-3">
                            <p><strong>答案:</strong> <span class="text-success fw-bold">${topic.answer}</span></p>
                            <p><strong>解析:</strong> ${renderMarkdown(topic.parse || '暂无解析')}</p>
                            <p class="small text-muted"><strong>来源:</strong> ${topic.source || '暂无来源'}</p>
                            <p class="small text-muted"><strong>难度:</strong> ${topic.difficulty || '暂无难度'}</p>
                        </div>
                    </div>
                </div>
            `;
        });

        previewContainer.innerHTML = allTopicsHtml;

        if (window.renderMathInElement) {
            renderMathInElement(previewContainer, {
                delimiters: [
                    {left: "$$", right: "$$", display: true},
                    {left: "$", right: "$", display: false}
                ]
            });
        }
    }

    function showError(message) {
        errorMessageDiv.textContent = message;
        errorMessageDiv.classList.remove('d-none');
        successMessageDiv.classList.add('d-none');
        showToast(message, 'error');
    }

    function showSuccess(message) {
        successMessageDiv.textContent = message;
        successMessageDiv.classList.remove('d-none');
        errorMessageDiv.classList.add('d-none');
        showToast(message, 'success');
    }


});
const exampleTopicData = [
    {
        "know_id": 252,
        "type": "choice",
        "title": "党的十八大以来，党中央对地方党委工作条例进行了首次修订，其主要目的是（    ）",
        "tags": "社会主义发展简史-第8章-中国特色社会主义进入新时代",
        "options": [
            {"key": "A", "name": "扩大地方党委的自主权"},
            {"key": "B", "name": "加强党中央的集中统一领导"},
            {"key": "C", "name": "减轻地方党委的工作负担"},
            {"key": "D", "name": "简化地方党委的议事程序"}
        ],
        "answer": "B",
        "source": "《社会主义发展简史》第302页",
        "parse": "新时代加强党内法规制度建设，一个核心目标就是确保政令畅通，维护党中央权威和集中统一领导。",
        "difficulty": 0.6
    },
    {
        "know_id": 247,
        "type": "multiple",
        "title": "20世纪20年代，在中国的城市里，居民区和商业区的分布开始出现一种新的趋势，即沿着（    ）发展。",
        "tags": "历史选择性必修2-经济与社会生活-现代交通运输的新变化",
        "options": [
            { "key": "A", "name": "城墙" },
            { "key": "B", "name": "河流" },
            { "key": "C", "name": "铁道和电车道" },
            { "key": "D", "name": "山脉" }
        ],
        "answer": "C",
        "source": "《历史选择性必修2-经济与社会生活》第13课",
        "parse": "课文引用的史料描述了天津市的发展趋势：“复次则沿铁道线，自有电气事业，则沿电车道而发展。”",
        "difficulty": 0.4
    }
];

function insertMathExample() {
    const mathExample = [
        {
            "know_id": 1,
            "type": "choice",
            "title": "以下哪个是正确的平方和公式？$\\sum_{i=1}^{n} i^2 = ?$",
            "tags": "数学,公式",
            "options": [
                {"key": "A", "name": "$\\frac{n(n+1)}{2}$"},
                {"key": "B", "name": "$\\frac{n(n+1)(2n+1)}{6}$"},
                {"key": "C", "name": "$\\frac{n^2(n+1)^2}{4}$"},
                {"key": "D", "name": "$n^3$"}
            ],
            "answer": "B",
            "source": "数学公式例题",
            "parse": "正确的平方和公式是：$$\\sum_{i=1}^{n} i^2 = \\frac{n(n+1)(2n+1)}{6}$$",
            "difficulty": 0.6
        }
    ];
    editor.setValue(JSON.stringify(mathExample, null, 2));
    showToast('已插入数学公式示例', 'info');
    validateAndPreview();
}

// 标签分析和统计功能
function analyzeAndDisplayTags(topics) {
    const tagAnalysisSection = document.getElementById('tagAnalysisSection');
    const tagStatsContainer = document.getElementById('tagStatsContainer');
    const autoFixBtn = document.getElementById('autoFixTagsBtn');

    // 收集所有标签
    const allTags = [];
    const tagCounts = new Map();
    const tagPattern = /^.+-第\d+章-.+(?:-.+)?$/;

    topics.forEach((topic, topicIndex) => {
        if (topic.tags && typeof topic.tags === 'string' && topic.tags.trim() !== '') {
            const tags = topic.tags.split(',').map(tag => tag.trim()).filter(tag => tag !== '');
            tags.forEach(tag => {
                allTags.push({ tag, topicIndex });
                tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
            });
        }
    });

    if (allTags.length === 0) {
        tagAnalysisSection.classList.add('d-none');
        autoFixBtn.style.display = 'none';
        return;
    }

    // 分析标签格式和相似性
    const tagAnalysis = analyzeTagsFormat(allTags, tagPattern);
    const similarGroups = findSimilarTags(Array.from(tagCounts.keys()));

    // 显示分析结果
    displayTagAnalysis(tagStatsContainer, tagAnalysis, similarGroups, tagCounts);
    tagAnalysisSection.classList.remove('d-none');

    // 控制智能修复按钮显示：当有相似标签组或格式错误标签时显示
    if (similarGroups.length > 0 || tagAnalysis.invalidTags.length > 0) {
        autoFixBtn.style.display = 'inline-block';
        autoFixBtn.onclick = () => autoFixTags(similarGroups, topics);
    } else {
        autoFixBtn.style.display = 'none';
    }
}

function analyzeTagsFormat(allTags, tagPattern) {
    const validTags = [];
    const invalidTags = [];

    allTags.forEach(({ tag, topicIndex }) => {
        if (tagPattern.test(tag)) {
            validTags.push({ tag, topicIndex });
        } else {
            invalidTags.push({ tag, topicIndex });
        }
    });

    return { validTags, invalidTags };
}

// 高效相似标签聚类算法
function findSimilarTags(tags) {
    if (tags.length < 2) return [];

    const similarGroups = [];
    const processed = new Set();
    const tagFrequency = new Map();

    // 统计标签频率
    tags.forEach(tag => {
        tagFrequency.set(tag, (tagFrequency.get(tag) || 0) + 1);
    });

    // 按频率排序，优先处理高频标签
    const sortedTags = [...new Set(tags)].sort((a, b) =>
        (tagFrequency.get(b) || 0) - (tagFrequency.get(a) || 0)
    );

    for (let i = 0; i < sortedTags.length; i++) {
        const currentTag = sortedTags[i];
        if (processed.has(currentTag)) continue;

        const group = [currentTag];
        processed.add(currentTag);

        // 使用更智能的相似度阈值
        const baseThreshold = 0.85;
        const lengthFactor = Math.min(currentTag.length / 20, 1); // 长标签降低阈值
        const threshold = baseThreshold - (lengthFactor * 0.1);

        for (let j = i + 1; j < sortedTags.length; j++) {
            const compareTag = sortedTags[j];
            if (processed.has(compareTag)) continue;

            // 章节号检查：确保只有相同章节的标签才能合并
            const currentParts = currentTag.split('-');
            const compareParts = compareTag.split('-');
            // 只有当两个标签都包含章节信息时才进行章节检查
            if (currentParts.length >= 2 && compareParts.length >= 2) {
                // 检查第二部分是否包含"第"和"章"字样（章节标识）
                const currentHasChapter = currentParts[1] && currentParts[1].includes('第') && currentParts[1].includes('章');
                const compareHasChapter = compareParts[1] && compareParts[1].includes('第') && compareParts[1].includes('章');

                // 如果两个标签都有章节信息，则必须章节号相同
                if (currentHasChapter && compareHasChapter) {
                    const currentChapter = currentParts[1];
                    const compareChapter = compareParts[1];
                    // 如果章节号不同，跳过
                    if (currentChapter !== compareChapter) continue;
                }
            }

            // 快速预筛选：长度差异检查
            const lengthRatio = Math.min(currentTag.length, compareTag.length) /
                Math.max(currentTag.length, compareTag.length);
            if (lengthRatio < 0.7) continue;

            const similarity = calculateStringSimilarity(currentTag, compareTag);

            // 动态阈值：考虑标签结构相似性
            let finalThreshold = threshold;
            if (hasSimilarStructure(currentTag, compareTag)) {
                finalThreshold -= 0.05; // 结构相似时降低阈值
            }

            if (similarity > finalThreshold) {
                group.push(compareTag);
                processed.add(compareTag);
            }
        }

        if (group.length > 1) {
            // 按频率排序组内标签，最高频的作为推荐标准
            group.sort((a, b) => (tagFrequency.get(b) || 0) - (tagFrequency.get(a) || 0));
            similarGroups.push(group);
        }
    }

    // 按组大小和总频率排序
    return similarGroups.sort((a, b) => {
        const aWeight = a.length * a.reduce((sum, tag) => sum + (tagFrequency.get(tag) || 0), 0);
        const bWeight = b.length * b.reduce((sum, tag) => sum + (tagFrequency.get(tag) || 0), 0);
        return bWeight - aWeight;
    });
}

// 检查标签结构相似性
function hasSimilarStructure(tag1, tag2) {
    const parts1 = tag1.split('-');
    const parts2 = tag2.split('-');

    // 基本结构检查
    if (parts1.length < 2 || parts2.length < 2) return false;

    // 章节号检查：只有当两个标签都有章节信息时才要求章节相同
    const hasChapter1 = parts1[1] && parts1[1].includes('第') && parts1[1].includes('章');
    const hasChapter2 = parts2[1] && parts2[1].includes('第') && parts2[1].includes('章');

    // 如果两个标签都有章节信息，则必须章节号相同
    if (hasChapter1 && hasChapter2) {
        const chapter1 = parts1[1];
        const chapter2 = parts2[1];
        if (chapter1 !== chapter2) return false; // 不同章节不能合并
    }

    // 检查课程名是否相似
    if (parts1[0] && parts2[0]) {
        const courseSimilarity = calculateStringSimilarity(parts1[0], parts2[0]);
        return courseSimilarity > 0.8;
    }

    return false;
}

// 高效字符串相似度计算（结合多种算法）
function calculateStringSimilarity(str1, str2) {
    // 预处理：去除空格和标准化
    const s1 = str1.trim().toLowerCase();
    const s2 = str2.trim().toLowerCase();

    // 完全相同
    if (s1 === s2) return 1.0;

    // 长度差异过大，直接返回低相似度
    const lengthDiff = Math.abs(s1.length - s2.length);
    const maxLength = Math.max(s1.length, s2.length);
    if (lengthDiff / maxLength > 0.5) return 0.0;

    // 使用Jaro-Winkler距离（对前缀相似性更敏感）
    const jaroSimilarity = calculateJaroSimilarity(s1, s2);
    if (jaroSimilarity < 0.7) return jaroSimilarity;

    // 计算公共前缀长度（最多4个字符）
    let prefixLength = 0;
    const minLength = Math.min(s1.length, s2.length, 4);
    for (let i = 0; i < minLength; i++) {
        if (s1[i] === s2[i]) {
            prefixLength++;
        } else {
            break;
        }
    }

    // Jaro-Winkler = Jaro + (0.1 * prefixLength * (1 - Jaro))
    return jaroSimilarity + (0.1 * prefixLength * (1 - jaroSimilarity));
}

// Jaro相似度算法
function calculateJaroSimilarity(s1, s2) {
    if (s1.length === 0 && s2.length === 0) return 1.0;
    if (s1.length === 0 || s2.length === 0) return 0.0;

    const matchWindow = Math.floor(Math.max(s1.length, s2.length) / 2) - 1;
    if (matchWindow < 0) return 0.0;

    const s1Matches = new Array(s1.length).fill(false);
    const s2Matches = new Array(s2.length).fill(false);

    let matches = 0;
    let transpositions = 0;

    // 找到匹配字符
    for (let i = 0; i < s1.length; i++) {
        const start = Math.max(0, i - matchWindow);
        const end = Math.min(i + matchWindow + 1, s2.length);

        for (let j = start; j < end; j++) {
            if (s2Matches[j] || s1[i] !== s2[j]) continue;
            s1Matches[i] = true;
            s2Matches[j] = true;
            matches++;
            break;
        }
    }

    if (matches === 0) return 0.0;

    // 计算转置
    let k = 0;
    for (let i = 0; i < s1.length; i++) {
        if (!s1Matches[i]) continue;
        while (!s2Matches[k]) k++;
        if (s1[i] !== s2[k]) transpositions++;
        k++;
    }

    return (matches / s1.length + matches / s2.length + (matches - transpositions / 2) / matches) / 3.0;
}

// 优化的编辑距离（仅在需要时使用）
function levenshteinDistance(str1, str2) {
    if (str1.length === 0) return str2.length;
    if (str2.length === 0) return str1.length;

    // 使用两行数组优化空间复杂度
    let previousRow = new Array(str2.length + 1);
    let currentRow = new Array(str2.length + 1);

    // 初始化第一行
    for (let j = 0; j <= str2.length; j++) {
        previousRow[j] = j;
    }

    for (let i = 1; i <= str1.length; i++) {
        currentRow[0] = i;

        for (let j = 1; j <= str2.length; j++) {
            const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
            currentRow[j] = Math.min(
                previousRow[j] + 1,      // 删除
                currentRow[j - 1] + 1,   // 插入
                previousRow[j - 1] + cost // 替换
            );
        }

        // 交换行
        [previousRow, currentRow] = [currentRow, previousRow];
    }

    return previousRow[str2.length];
}

function displayTagAnalysis(container, tagAnalysis, similarGroups, tagCounts) {
    let html = '';

    // 极简统计（单行显示）
    const totalTags = tagAnalysis.validTags.length + tagAnalysis.invalidTags.length;
    const uniqueTagsCount = tagCounts.size;

    html += `
        <div class="d-flex justify-content-between align-items-center mb-1 px-2 py-1 bg-light rounded" style="font-size: 0.75em;">
            <span class="text-primary"><strong>${totalTags}</strong> 总数</span>
            <span class="text-info"><strong>${uniqueTagsCount}</strong> 种类</span>
            <span class="${tagAnalysis.invalidTags.length > 0 ? 'text-danger' : 'text-success'}"><strong>${tagAnalysis.invalidTags.length}</strong> 错误</span>
        </div>
    `;

    // 标签列表（美化版）
    const sortedTags = Array.from(tagCounts.entries()).sort((a, b) => b[1] - a[1]);
    if (sortedTags.length > 0) {
        html += `
             <div class="mb-2">
                 <div class="d-flex flex-wrap" style="gap: 4px; max-height: 50px; overflow-y: auto; font-size: 0.8em;">
         `;

        // 只显示前15个标签
        sortedTags.slice(0, 15).forEach(([tag, count]) => {
            const isValid = /^[^-]+-第\d+章-[^-]+(-[^-]+)*$/.test(tag.trim());
            const badgeClass = isValid ? 'bg-success' : 'bg-danger';
            const title = `${tag} - 使用${count}次`;

            html += `<span class="badge ${badgeClass} px-2 py-1 rounded-pill" title="${title}" style="font-weight: 500;">${tag} <small>(${count})</small></span>`;
        });

        if (sortedTags.length > 15) {
            html += `<span class="badge bg-secondary px-2 py-1 rounded-pill" style="font-weight: 500;">+${sortedTags.length - 15}个</span>`;
        }

        html += `
                 </div>
             </div>
         `;
    }

    // 错误提示（美化版）
    if (tagAnalysis.invalidTags.length > 0) {
        const uniqueInvalidTags = [...new Set(tagAnalysis.invalidTags.map(item => item.tag))];
        html += `
             <div class="alert alert-warning py-2 px-3 mb-2 border-0 shadow-sm" style="font-size: 0.8em; border-radius: 8px;">
                 <div class="d-flex align-items-center mb-2">
                     <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                     <span class="fw-bold">格式错误标签</span>
                 </div>
                 <div class="d-flex flex-wrap" style="gap: 4px;">
         `;

        uniqueInvalidTags.slice(0, 8).forEach(tag => {
            const count = tagCounts.get(tag) || 1;
            html += `<span class="badge bg-danger px-2 py-1 rounded-pill" style="font-weight: 500;">${tag} <small>(${count})</small></span>`;
        });

        if (uniqueInvalidTags.length > 8) {
            html += `<span class="badge bg-secondary px-2 py-1 rounded-pill" style="font-weight: 500;">+${uniqueInvalidTags.length - 8}个</span>`;
        }

        html += `
                 </div>
             </div>
         `;
    }

    // 相似标签（美化版）
    if (similarGroups.length > 0) {
        html += `
             <div class="alert alert-info py-2 px-3 mb-2 border-0 shadow-sm" style="font-size: 0.8em; border-radius: 8px;">
                 <div class="d-flex align-items-center justify-content-between">
                     <div class="d-flex align-items-center">
                         <i class="bi bi-collection text-info me-2"></i>
                         <span class="fw-bold">相似标签 (${similarGroups.length}组)</span>
                     </div>
                     <a class="btn btn-sm btn-outline-info" data-bs-toggle="collapse" href="#similarTagsCollapse" style="font-size: 0.7em; padding: 2px 8px;">查看详情</a>
                 </div>
                 <div class="collapse mt-2" id="similarTagsCollapse">
         `;

        similarGroups.slice(0, 2).forEach((group, index) => {
            html += `<div class="mb-2">`;
            html += `<small class="text-muted d-block mb-1">组 ${index + 1}:</small>`;
            html += `<div class="d-flex flex-wrap" style="gap: 4px;">`;
            group.forEach(tag => {
                const count = tagCounts.get(tag);
                html += `<span class="badge bg-secondary px-2 py-1 rounded-pill" style="font-weight: 500;">${tag} <small>(${count})</small></span>`;
            });
            html += `</div></div>`;
        });

        html += `
                 </div>
             </div>
         `;
    }

    container.innerHTML = html;
}

// 智能自动修复标签
function autoFixTags(similarGroups, topics) {
    if (!similarGroups || similarGroups.length === 0) {
        showFixResult('没有发现需要修复的相似标签', 'info');
        return;
    }

    let fixStats = {
        groupsProcessed: 0,
        topicsModified: 0,
        tagsReplaced: 0,
        details: []
    };

    similarGroups.forEach((group, groupIndex) => {
        if (group.length < 2) return;

        fixStats.groupsProcessed++;

        // 智能选择标准标签
        const standardTag = selectBestTag(group, topics);
        const tagsToReplace = group.filter(tag => tag !== standardTag);

        let groupFixCount = 0;
        let groupTopicCount = 0;

        topics.forEach((topic, topicIndex) => {
            if (!topic.tags || typeof topic.tags !== 'string') return;

            let topicModified = false;
            const originalTags = topic.tags;

            tagsToReplace.forEach(oldTag => {
                if (topic.tags.includes(oldTag)) {
                    topic.tags = topic.tags.replace(new RegExp(escapeRegExp(oldTag), 'g'), standardTag);
                    topicModified = true;
                    groupFixCount++;
                }
            });

            if (topicModified) {
                groupTopicCount++;
                fixStats.topicsModified++;
            }
        });

        if (groupFixCount > 0) {
            fixStats.tagsReplaced += groupFixCount;
            fixStats.details.push({
                group: groupIndex + 1,
                standardTag: standardTag,
                replacedTags: tagsToReplace,
                affectedTopics: groupTopicCount,
                replacedCount: groupFixCount
            });
        }
    });

    if (fixStats.topicsModified > 0) {
        // 更新编辑器内容
        editor.setValue(JSON.stringify(topics, null, 2));
        showFixResult(fixStats);

        // 自动重新验证
        setTimeout(() => {
            validateAndPreview();
        }, 500);
    } else {
        showFixResult('没有找到需要修复的标签', 'info');
    }
}

// 智能选择最佳标签
function selectBestTag(group, topics) {
    const tagScores = new Map();

    group.forEach(tag => {
        let score = 0;

        // 1. 频率得分（40%权重）
        const frequency = topics.reduce((count, topic) => {
            return count + (topic.tags && topic.tags.includes(tag) ? 1 : 0);
        }, 0);
        score += frequency * 0.4;

        // 2. 格式正确性得分（30%权重）
        const isValidFormat = /^[^-]+-第\d+章-[^-]+(-[^-]+)*$/.test(tag.trim());
        if (isValidFormat) score += 30;

        // 3. 长度适中得分（20%权重）
        const idealLength = 15; // 理想长度
        const lengthScore = Math.max(0, 20 - Math.abs(tag.length - idealLength));
        score += lengthScore * 0.2;

        // 4. 结构完整性得分（10%权重）
        const parts = tag.split('-');
        if (parts.length >= 3) {
            score += 10; // 有课程名、章节、知识点
            if (parts.length >= 4) score += 5; // 有细分知识点
        }

        tagScores.set(tag, score);
    });

    // 返回得分最高的标签
    return [...tagScores.entries()].reduce((best, current) =>
        current[1] > best[1] ? current : best
    )[0];
}

// 显示修复结果
function showFixResult(fixStats, type = 'success') {
    if (typeof fixStats === 'string') {
        showToast(fixStats, type);
        return;
    }

    if (fixStats.topicsModified === 0) {
        showToast('没有找到需要修复的标签', 'info');
        return;
    }

    let message = `🎉 标签修复完成！\n\n`;
    message += `📊 修复统计：\n`;
    message += `• 处理相似组: ${fixStats.groupsProcessed} 组\n`;
    message += `• 修改题目: ${fixStats.topicsModified} 个\n`;
    message += `• 替换标签: ${fixStats.tagsReplaced} 次\n\n`;

    if (fixStats.details.length > 0) {
        message += `📝 修复详情：\n`;
        fixStats.details.slice(0, 3).forEach(detail => {
            message += `\n组${detail.group}: ${detail.replacedTags.join(', ')}\n`;
            message += `→ 统一为: ${detail.standardTag}\n`;
            message += `影响题目: ${detail.affectedTopics} 个\n`;
        });

        if (fixStats.details.length > 3) {
            message += `\n... 还有 ${fixStats.details.length - 3} 组修复\n`;
        }
    }

    showToast(message, 'success');
}

// 转义正则表达式特殊字符
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// 绑定智能修复按钮事件
$(document).on('click', '#autoFixTagsBtn', function() {
    const topics = getTopicsFromEditor();
    if (topics && topics.length > 0) {
        // 收集所有标签
        const allTags = [];
        const tagCounts = new Map();

        topics.forEach((topic, topicIndex) => {
            if (topic.tags && typeof topic.tags === 'string' && topic.tags.trim() !== '') {
                const tags = topic.tags.split(',').map(tag => tag.trim()).filter(tag => tag !== '');
                tags.forEach(tag => {
                    allTags.push({ tag, topicIndex });
                    tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
                });
            }
        });

        // 获取相似标签组
        const similarGroups = findSimilarTags(Array.from(tagCounts.keys()));
        autoFixTags(similarGroups, topics);
    }
});

function getTopicsFromEditor() {
    try {
        const jsonText = editor.getValue();
        return JSON.parse(jsonText);
    } catch (e) {
        return null;
    }
}