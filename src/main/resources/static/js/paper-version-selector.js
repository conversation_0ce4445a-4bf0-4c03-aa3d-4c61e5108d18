/**
 * 试卷版本选择功能
 * 支持学生版、教师版、标准版的选择和说明
 */

// 版本描述信息
const PAPER_VERSION_DESCRIPTIONS = {
    regular: {
        title: '学生版',
        description: '只包含题目和选项，不显示答案和解析，适合考试使用',
        icon: 'fas fa-user-graduate',
        color: 'primary'
    },
    teacher: {
        title: '教师版',
        description: '只显示答案和解析（按题号对应），不显示题目内容，适合教师批改使用',
        icon: 'fas fa-chalkboard-teacher',
        color: 'success'
    },
    standard: {
        title: '标准版',
        description: '包含完整的题目、答案和解析，答案解析显示在每题后，适合学习使用',
        icon: 'fas fa-book-open',
        color: 'info'
    }
};

// 默认版本设置
const DEFAULT_PAPER_VERSION = 'regular';

/**
 * 初始化版本选择功能
 */
function initPaperVersionSelector() {
    // 为所有版本选择器添加事件监听
    $(document).on('change', '#paperType, #customPaperType', function() {
        const selectedVersion = $(this).val();
        const descriptionElement = $(this).closest('.form-group').find('[id$="Description"]');

        updateVersionDescription(selectedVersion, descriptionElement);
    });

    // 初始化默认描述
    setTimeout(() => {
        $('#paperType, #customPaperType').each(function() {
            const selectedVersion = $(this).val() || DEFAULT_PAPER_VERSION;
            const descriptionElement = $(this).closest('.form-group').find('[id$="Description"]');
            updateVersionDescription(selectedVersion, descriptionElement);
        });
    }, 100);
}

/**
 * 更新版本描述
 */
function updateVersionDescription(version, descriptionElement) {
    const versionInfo = PAPER_VERSION_DESCRIPTIONS[version];
    if (versionInfo && descriptionElement.length > 0) {
        descriptionElement.html(versionInfo.description);

        // 更新图标颜色
        const iconElement = descriptionElement.siblings().find('.fas');
        if (iconElement.length > 0) {
            iconElement.removeClass('text-primary text-success text-info text-warning text-danger')
                     .addClass(`text-${versionInfo.color}`);
        }
    }
}

/**
 * 获取当前选择的版本
 */
function getCurrentPaperVersion(formId = 'generatePaperForm') {
    const selector = formId === 'customPaperForm' ? '#customPaperType' : '#paperType';
    return $(selector).val() || DEFAULT_PAPER_VERSION;
}

/**
 * 设置版本选择
 */
function setPaperVersion(version, formId = 'generatePaperForm') {
    const selector = formId === 'customPaperForm' ? '#customPaperType' : '#paperType';
    $(selector).val(version).trigger('change');
}

/**
 * 创建版本选择下拉菜单（用于历史试卷）
 */
function createVersionSelector(paperId, currentVersion = DEFAULT_PAPER_VERSION) {
    const selectorId = `paperVersionSelector_${paperId}`;

    let html = `
        <div class="version-selector-container">
            <select class="form-control form-control-sm" id="${selectorId}" data-paper-id="${paperId}">
    `;

    Object.keys(PAPER_VERSION_DESCRIPTIONS).forEach(version => {
        const versionInfo = PAPER_VERSION_DESCRIPTIONS[version];
        const selected = version === currentVersion ? 'selected' : '';
        html += `<option value="${version}" ${selected}>${versionInfo.title}</option>`;
    });

    html += `
            </select>
            <small class="form-text text-muted version-description" id="${selectorId}_desc">
                ${PAPER_VERSION_DESCRIPTIONS[currentVersion].description}
            </small>
        </div>
    `;

    return html;
}

/**
 * 为历史试卷添加版本选择功能
 */
function enhanceHistoryPaperDownloads() {
    // 查找所有下载按钮并替换为版本选择器
    $('.paper-actions').each(function() {
        const $actions = $(this);
        const paperId = extractPaperIdFromActions($actions);

        if (paperId) {
            enhanceSinglePaperDownload($actions, paperId);
        }
    });
}

/**
 * 从操作按钮中提取试卷ID
 */
function extractPaperIdFromActions($actions) {
    // 尝试从下载链接中提取ID
    const downloadLink = $actions.find('a[href*="/download/"]').attr('href');
    if (downloadLink) {
        const match = downloadLink.match(/\/download\/(\d+)/);
        return match ? match[1] : null;
    }

    // 尝试从其他按钮的data属性中提取
    const dataId = $actions.find('[data-id]').attr('data-id');
    return dataId || null;
}

/**
 * 增强单个试卷的下载功能
 */
function enhanceSinglePaperDownload($actions, paperId) {
    // 查找现有的下载按钮
    const $downloadDropdown = $actions.find('.dropdown-menu');

    if ($downloadDropdown.length > 0) {
        // 替换下载菜单内容
        const newContent = createEnhancedDownloadMenu(paperId);
        $downloadDropdown.html(newContent);

        // 添加版本选择事件监听
        $downloadDropdown.on('change', '.version-selector', function() {
            const selectedVersion = $(this).val();
            const descElement = $(this).siblings('.version-description');
            updateVersionDescription(selectedVersion, descElement);
        });
    }
}

/**
 * 创建增强的下载菜单
 */
function createEnhancedDownloadMenu(paperId) {
    return `
        <div class="px-3 py-2">
            <h6 class="dropdown-header">选择试卷版本</h6>
            <select class="form-control form-control-sm version-selector mb-2" data-paper-id="${paperId}">
                <option value="regular" selected>学生版</option>
                <option value="teacher">教师版</option>
                <option value="standard">标准版</option>
            </select>
            <small class="text-muted version-description">
                ${PAPER_VERSION_DESCRIPTIONS.regular.description}
            </small>
            <div class="dropdown-divider my-2"></div>
            <h6 class="dropdown-header">下载格式</h6>
            <button class="dropdown-item download-with-version" data-paper-id="${paperId}" data-format="pdf">
                <i class="fas fa-file-pdf text-danger"></i> PDF格式
            </button>
            <button class="dropdown-item download-with-version" data-paper-id="${paperId}" data-format="word">
                <i class="fas fa-file-word text-primary"></i> Word格式
            </button>
        </div>
    `;
}

/**
 * 更新版本描述
 */
function updateVersionDescription(version, descriptionElement) {
    const versionInfo = PAPER_VERSION_DESCRIPTIONS[version];
    if (versionInfo && descriptionElement.length > 0) {
        descriptionElement.html(versionInfo.description);

        // 更新图标颜色
        const iconElement = descriptionElement.siblings().find('.fas');
        if (iconElement.length > 0) {
            iconElement.removeClass('text-primary text-success text-info text-warning text-danger')
                     .addClass(`text-${versionInfo.color}`);
        }
    }
}

/**
 * 带版本的下载函数
 */
function downloadPaperWithVersion(paperId, format, version) {
    console.log(`下载试卷 ID: ${paperId}, 格式: ${format}, 版本: ${version}`);
    
    // 在下载前准备干净的token环境
    const validToken = prepareDownloadEnvironment();
    if (!validToken) {
        Swal.fire({
            title: '下载失败',
            text: '认证失效，请重新登录后再试',
            icon: 'error',
            confirmButtonText: '确定'
        });
        return;
    }
    
    // 添加token参数到下载URL
    const url = `/api/papers/download/${paperId}?format=${format}&paperType=${version}&token=${encodeURIComponent(validToken)}`;
    
    // 显示下载提示
    Swal.fire({
        title: '下载开始',
        text: `正在下载${format.toUpperCase()}格式的${getVersionDisplayName(version)}`,
        icon: 'success',
        timer: 2000,
        showConfirmButton: false,
        toast: true,
        position: 'top-end'
    });
    
    // 创建下载链接
    const downloadLink = document.createElement('a');
    downloadLink.href = url;
    downloadLink.style.display = 'none';
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}

/**
 * 初始化下载事件监听
 */
function initDownloadEventListeners() {
    // 版本选择器变化事件 - 阻止事件冒泡
    $(document).on('change', '.version-selector', function(e) {
        e.stopPropagation(); // 阻止事件冒泡，防止关闭dropdown
        const selectedVersion = $(this).val();
        const descElement = $(this).siblings('.version-description');
        updateVersionDescription(selectedVersion, descElement);
    });

    // 版本选择器点击事件 - 阻止事件冒泡
    $(document).on('click', '.version-selector', function(e) {
        e.stopPropagation(); // 阻止点击时关闭dropdown
    });

    // 版本选择器容器点击事件 - 阻止事件冒泡
    $(document).on('click', '.enhanced-download-menu', function(e) {
        e.stopPropagation(); // 阻止点击菜单内容时关闭dropdown
    });

    // 带版本的下载按钮点击事件
    $(document).on('click', '.download-with-version', function(e) {
        e.preventDefault();
        e.stopPropagation(); // 阻止事件冒泡
        const paperId = $(this).data('paper-id');
        const format = $(this).data('format');
        downloadPaperWithVersion(paperId, format);

        // 下载后关闭dropdown
        $(this).closest('.dropdown-menu').prev('.dropdown-toggle').dropdown('toggle');
    });
}

/**
 * 显示版本选择帮助信息
 */
function showVersionHelp() {
    let helpContent = '<div class="version-help-content">';

    Object.keys(PAPER_VERSION_DESCRIPTIONS).forEach(version => {
        const info = PAPER_VERSION_DESCRIPTIONS[version];
        helpContent += `
            <div class="version-help-item mb-3">
                <h6><i class="${info.icon} text-${info.color}"></i> ${info.title}</h6>
                <p class="text-muted mb-0">${info.description}</p>
            </div>
        `;
    });

    helpContent += '</div>';

    Swal.fire({
        title: '试卷版本说明',
        html: helpContent,
        icon: 'info',
        confirmButtonText: '我知道了',
        width: '600px'
    });
}

// 页面加载完成后初始化
$(document).ready(function() {
    initPaperVersionSelector();
    initDownloadEventListeners();

    // 如果是历史试卷页面，增强下载功能
    if (window.location.pathname.includes('/papers') || $('#papersTable').length > 0) {
        setTimeout(enhanceHistoryPaperDownloads, 500);
    }
});

// 导出函数供其他脚本使用
window.PaperVersionSelector = {
    getCurrentPaperVersion,
    setPaperVersion,
    downloadPaperWithVersion,
    showVersionHelp,
    PAPER_VERSION_DESCRIPTIONS,
    DEFAULT_PAPER_VERSION
};
