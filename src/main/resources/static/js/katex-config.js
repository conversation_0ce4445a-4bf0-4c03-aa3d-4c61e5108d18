// KaTeX 增强配置 - 支持完整的LaTeX数学符号
document.addEventListener('DOMContentLoaded', function() {
    // 自动渲染页面上的所有公式
    renderMathInElement(document.body, {
        // 自定义分隔符
        delimiters: [
            {left: '$$', right: '$$', display: true},
            {left: '$', right: '$', display: false},
            {left: '\\(', right: '\\)', display: false},
            {left: '\\[', right: '\\]', display: true},
            {left: '\\{', right: '\\}', display: false},
            // 化学公式分隔符
            {left: '\\ce{', right: '}', display: false},
            {left: '\\cee{', right: '}', display: true}
        ],
        // 错误处理
        throwOnError: false,
        // 显示错误信息
        errorCallback: function(msg, err) {
            console.warn('KaTeX 渲染错误:', msg, err);
        },
        // 其他选项
        fleqn: false,
        leqno: false,
        strict: false,
        trust: true, // 启用信任模式以支持更多命令
        macros: getExtendedMacros()
    });
});

/**
 * 获取扩展的LaTeX宏定义
 * 包含完整的数学符号支持
 */
function getExtendedMacros() {
    return {
        // 希腊字母扩展
        "\\Alpha": "\\mathrm{A}",
        "\\Beta": "\\mathrm{B}",
        "\\Epsilon": "\\mathrm{E}",
        "\\Zeta": "\\mathrm{Z}",
        "\\Eta": "\\mathrm{H}",
        "\\Iota": "\\mathrm{I}",
        "\\Kappa": "\\mathrm{K}",
        "\\Mu": "\\mathrm{M}",
        "\\Nu": "\\mathrm{N}",
        "\\Omicron": "\\mathrm{O}",
        "\\Rho": "\\mathrm{P}",
        "\\Tau": "\\mathrm{T}",
        "\\Chi": "\\mathrm{X}",

        // 希伯来字母
        "\\aleph": "\\aleph",
        "\\beth": "\\beth",
        "\\gimel": "\\gimel",
        "\\daleth": "\\daleth",

        // 二元运算符扩展
        "\\ast": "*",
        "\\star": "\\star",
        "\\cdot": "\\cdot",
        "\\circ": "\\circ",
        "\\bullet": "\\bullet",
        "\\bigcirc": "\\bigcirc",
        "\\diamond": "\\diamond",
        "\\times": "\\times",
        "\\div": "\\div",
        "\\centerdot": "\\centerdot",

        // 关系符号扩展
        "\\equiv": "\\equiv",
        "\\cong": "\\cong",
        "\\neq": "\\neq",
        "\\sim": "\\sim",
        "\\simeq": "\\simeq",
        "\\approx": "\\approx",
        "\\asymp": "\\asymp",
        "\\doteq": "\\doteq",
        "\\propto": "\\propto",
        "\\models": "\\models",
        "\\perp": "\\perp",
        "\\mid": "\\mid",
        "\\parallel": "\\parallel",
        "\\bowtie": "\\bowtie",
        "\\Join": "\\Join",
        "\\ltimes": "\\ltimes",
        "\\rtimes": "\\rtimes",
        "\\smile": "\\smile",
        "\\frown": "\\frown",

        // 箭头符号扩展
        "\\leftarrow": "\\leftarrow",
        "\\Leftarrow": "\\Leftarrow",
        "\\rightarrow": "\\rightarrow",
        "\\Rightarrow": "\\Rightarrow",
        "\\leftrightarrow": "\\leftrightarrow",
        "\\Leftrightarrow": "\\Leftrightarrow",
        "\\mapsto": "\\mapsto",
        "\\hookleftarrow": "\\hookleftarrow",
        "\\hookrightarrow": "\\hookrightarrow",
        "\\leftharpoonup": "\\leftharpoonup",
        "\\leftharpoondown": "\\leftharpoondown",
        "\\rightharpoonup": "\\rightharpoonup",
        "\\rightharpoondown": "\\rightharpoondown",
        "\\rightleftharpoons": "\\rightleftharpoons",
        "\\leadsto": "\\leadsto",

        // 大型运算符
        "\\sum": "\\sum",
        "\\prod": "\\prod",
        "\\coprod": "\\coprod",
        "\\int": "\\int",
        "\\oint": "\\oint",
        "\\iint": "\\iint",
        "\\iiint": "\\iiint",
        "\\iiiint": "\\iiiint",
        "\\bigcap": "\\bigcap",
        "\\bigcup": "\\bigcup",
        "\\biguplus": "\\biguplus",
        "\\bigsqcup": "\\bigsqcup",
        "\\bigvee": "\\bigvee",
        "\\bigwedge": "\\bigwedge",
        "\\bigoplus": "\\bigoplus",
        "\\bigotimes": "\\bigotimes",
        "\\bigodot": "\\bigodot",

        // 杂项符号
        "\\infty": "\\infty",
        "\\forall": "\\forall",
        "\\exists": "\\exists",
        "\\nexists": "\\nexists",
        "\\emptyset": "\\emptyset",
        "\\varnothing": "\\varnothing",
        "\\nabla": "\\nabla",
        "\\partial": "\\partial",
        "\\eth": "\\eth",
        "\\hbar": "\\hbar",
        "\\imath": "\\imath",
        "\\jmath": "\\jmath",
        "\\ell": "\\ell",
        "\\wp": "\\wp",
        "\\Re": "\\Re",
        "\\Im": "\\Im",
        "\\mho": "\\mho",
        "\\prime": "\\prime",
        "\\backprime": "\\backprime",
        "\\surd": "\\surd",
        "\\triangle": "\\triangle",
        "\\square": "\\square",
        "\\blacksquare": "\\blacksquare",
        "\\triangledown": "\\triangledown",
        "\\blacktriangle": "\\blacktriangle",
        "\\blacktriangledown": "\\blacktriangledown",
        "\\lozenge": "\\lozenge",
        "\\blacklozenge": "\\blacklozenge",
        "\\star": "\\star",
        "\\bigstar": "\\bigstar",
        "\\angle": "\\angle",
        "\\measuredangle": "\\measuredangle",
        "\\sphericalangle": "\\sphericalangle",
        "\\complement": "\\complement",

        // 数学字体
        "\\mathcal": "\\mathcal",
        "\\mathbb": "\\mathbb",
        "\\mathfrak": "\\mathfrak",
        "\\mathsf": "\\mathsf",
        "\\mathbf": "\\mathbf",
        "\\mathrm": "\\mathrm",
        "\\mathit": "\\mathit",
        "\\mathtt": "\\mathtt",

        // 重音符号
        "\\acute": "\\acute",
        "\\grave": "\\grave",
        "\\ddot": "\\ddot",
        "\\tilde": "\\tilde",
        "\\bar": "\\bar",
        "\\breve": "\\breve",
        "\\check": "\\check",
        "\\hat": "\\hat",
        "\\vec": "\\vec",
        "\\dot": "\\dot",

        // 分隔符
        "\\lfloor": "\\lfloor",
        "\\rfloor": "\\rfloor",
        "\\lceil": "\\lceil",
        "\\rceil": "\\rceil",
        "\\langle": "\\langle",
        "\\rangle": "\\rangle",
        "\\llcorner": "\\llcorner",
        "\\lrcorner": "\\lrcorner",
        "\\ulcorner": "\\ulcorner",
        "\\urcorner": "\\urcorner",

        // 函数名
        "\\arccos": "\\arccos",
        "\\arcsin": "\\arcsin",
        "\\arctan": "\\arctan",
        "\\arg": "\\arg",
        "\\cos": "\\cos",
        "\\cosh": "\\cosh",
        "\\cot": "\\cot",
        "\\coth": "\\coth",
        "\\csc": "\\csc",
        "\\deg": "\\deg",
        "\\det": "\\det",
        "\\dim": "\\dim",
        "\\exp": "\\exp",
        "\\gcd": "\\gcd",
        "\\hom": "\\hom",
        "\\inf": "\\inf",
        "\\ker": "\\ker",
        "\\lg": "\\lg",
        "\\lim": "\\lim",
        "\\liminf": "\\liminf",
        "\\limsup": "\\limsup",
        "\\ln": "\\ln",
        "\\log": "\\log",
        "\\max": "\\max",
        "\\min": "\\min",
        "\\Pr": "\\Pr",
        "\\sec": "\\sec",
        "\\sin": "\\sin",
        "\\sinh": "\\sinh",
        "\\sup": "\\sup",
        "\\tan": "\\tan",
        "\\tanh": "\\tanh",

        // 扩展的数学符号和变体
        // 无穷大的变体
        "-\\infty": "-\\infty",
        "+\\infty": "+\\infty",
        "\\pm\\infty": "\\pm\\infty",
        "\\mp\\infty": "\\mp\\infty",

        // 更多希腊字母变体
        "\\digamma": "\\digamma",
        "\\varkappa": "\\varkappa",
        "\\varpi": "\\varpi",
        "\\varrho": "\\varrho",
        "\\varsigma": "\\varsigma",
        "\\vartheta": "\\vartheta",
        "\\varepsilon": "\\varepsilon",
        "\\varphi": "\\varphi",

        // 更多二元运算符
        "\\amalg": "\\amalg",
        "\\barwedge": "\\barwedge",
        "\\veebar": "\\veebar",
        "\\curlywedge": "\\curlywedge",
        "\\curlyvee": "\\curlyvee",
        "\\Cap": "\\Cap",
        "\\Cup": "\\Cup",
        "\\rightthreetimes": "\\rightthreetimes",
        "\\leftthreetimes": "\\leftthreetimes",
        "\\divideontimes": "\\divideontimes",
        "\\dotplus": "\\dotplus",
        "\\intercal": "\\intercal",
        "\\doublebarwedge": "\\doublebarwedge",

        // 更多关系符号
        "\\approxeq": "\\approxeq",
        "\\thicksim": "\\thicksim",
        "\\backsim": "\\backsim",
        "\\backsimeq": "\\backsimeq",
        "\\triangleq": "\\triangleq",
        "\\circeq": "\\circeq",
        "\\bumpeq": "\\bumpeq",
        "\\Bumpeq": "\\Bumpeq",
        "\\doteqdot": "\\doteqdot",
        "\\thickapprox": "\\thickapprox",
        "\\fallingdotseq": "\\fallingdotseq",
        "\\risingdotseq": "\\risingdotseq",
        "\\varpropto": "\\varpropto",
        "\\therefore": "\\therefore",
        "\\because": "\\because",
        "\\eqcirc": "\\eqcirc",

        // 否定关系符号
        "\\ncong": "\\ncong",
        "\\nmid": "\\nmid",
        "\\nparallel": "\\nparallel",
        "\\nleq": "\\nleq",
        "\\ngeq": "\\ngeq",
        "\\nless": "\\nless",
        "\\ngtr": "\\ngtr",
        "\\nprec": "\\nprec",
        "\\nsucc": "\\nsucc",
        "\\npreceq": "\\npreceq",
        "\\nsucceq": "\\nsucceq",
        "\\precnapprox": "\\precnapprox",
        "\\succnapprox": "\\succnapprox",
        "\\precnsim": "\\precnsim",
        "\\succnsim": "\\succnsim",
        "\\lnapprox": "\\lnapprox",
        "\\gnapprox": "\\gnapprox",
        "\\lneq": "\\lneq",
        "\\gneq": "\\gneq",
        "\\lneqq": "\\lneqq",
        "\\gneqq": "\\gneqq",
        "\\lnsim": "\\lnsim",
        "\\gnsim": "\\gnsim",
        "\\lvertneqq": "\\lvertneqq",
        "\\gvertneqq": "\\gvertneqq",
        "\\nsubseteq": "\\nsubseteq",
        "\\nsupseteq": "\\nsupseteq",
        "\\subsetneq": "\\subsetneq",
        "\\supsetneq": "\\supsetneq",
        "\\varsubsetneq": "\\varsubsetneq",
        "\\varsupsetneq": "\\varsupsetneq",
        "\\subsetneqq": "\\subsetneqq",
        "\\supsetneqq": "\\supsetneqq",
        "\\varsubsetneqq": "\\varsubsetneqq",
        "\\varsupsetneqq": "\\varsupsetneqq",

        // 更多箭头符号
        "\\dashrightarrow": "\\dashrightarrow",
        "\\dashleftarrow": "\\dashleftarrow",
        "\\leftleftarrows": "\\leftleftarrows",
        "\\leftrightarrows": "\\leftrightarrows",
        "\\Lleftarrow": "\\Lleftarrow",
        "\\twoheadleftarrow": "\\twoheadleftarrow",
        "\\leftrightharpoons": "\\leftrightharpoons",
        "\\leftarrowtail": "\\leftarrowtail",
        "\\looparrowleft": "\\looparrowleft",
        "\\curvearrowleft": "\\curvearrowleft",
        "\\circlearrowleft": "\\circlearrowleft",
        "\\Lsh": "\\Lsh",
        "\\upuparrows": "\\upuparrows",
        "\\upharpoonleft": "\\upharpoonleft",
        "\\downharpoonleft": "\\downharpoonleft",
        "\\multimap": "\\multimap",
        "\\leftrightsquigarrow": "\\leftrightsquigarrow",
        "\\rightrightarrows": "\\rightrightarrows",
        "\\rightleftarrows": "\\rightleftarrows",
        "\\twoheadrightarrow": "\\twoheadrightarrow",
        "\\rightarrowtail": "\\rightarrowtail",
        "\\looparrowright": "\\looparrowright",
        "\\curvearrowright": "\\curvearrowright",
        "\\circlearrowright": "\\circlearrowright",
        "\\Rsh": "\\Rsh",
        "\\downdownarrows": "\\downdownarrows",
        "\\upharpoonright": "\\upharpoonright",
        "\\downharpoonright": "\\downharpoonright",
        "\\rightsquigarrow": "\\rightsquigarrow",
        "\\nleftarrow": "\\nleftarrow",
        "\\nrightarrow": "\\nrightarrow",
        "\\nLeftarrow": "\\nLeftarrow",
        "\\nRightarrow": "\\nRightarrow",
        "\\nleftrightarrow": "\\nleftrightarrow",
        "\\nLeftrightarrow": "\\nLeftrightarrow",

        // 更多杂项符号
        "\\Bbbk": "\\Bbbk",
        "\\diagdown": "\\diagdown",
        "\\diagup": "\\diagup",
        "\\Finv": "\\Finv",
        "\\Game": "\\Game",
        "\\vartriangle": "\\vartriangle",
        "\\hslash": "\\hslash"
    };
}

// 全局渲染函数，用于动态添加的内容
function renderKaTeX(element) {
    if (!element) {
        element = document.body;
    }

    try {
        renderMathInElement(element, {
            delimiters: [
                {left: '$$', right: '$$', display: true},
                {left: '$', right: '$', display: false},
                {left: '\\(', right: '\\)', display: false},
                {left: '\\[', right: '\\]', display: true}
            ],
            throwOnError: false,
            errorCallback: function(msg, err) {
                console.warn('KaTeX 渲染错误:', msg, err);
            },
            strict: false,
            trust: true,
            macros: getExtendedMacros()
        });
        console.log('KaTeX 渲染完成');
        return true;
    } catch (e) {
        console.error('KaTeX 渲染异常:', e);
        return false;
    }
}
