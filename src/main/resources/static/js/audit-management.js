/**
 * 审核管理页面JavaScript
 */

let currentAuditData = null;
let currentPage = {
    pending: 1,
    myAudits: 1,
    auditorRecords: 1
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查用户权限
    checkAuth().then(userInfo => {
        if (!userInfo || (userInfo.role !== 2 && userInfo.role !== 3)) {
            alert('您没有权限访问此页面');
            window.location.href = '/';
            return;
        }
        
        // 初始化页面
        loadAuditStatistics();
        loadPendingAudits();
        loadUnreadMessages();
        
        // 设置选项卡切换事件
        setupTabEvents();
    });
});

/**
 * 检查用户认证
 */
async function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = '/auth/login';
        return null;
    }
    
    try {
        const response = await fetch('/api/user/info', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            return data.success ? data.data : null;
        } else {
            localStorage.removeItem('token');
            window.location.href = '/auth/login';
            return null;
        }
    } catch (error) {
        console.error('Auth check failed:', error);
        return null;
    }
}

/**
 * 设置选项卡切换事件
 */
function setupTabEvents() {
    document.getElementById('my-audits-tab').addEventListener('shown.bs.tab', function() {
        loadMyAudits();
    });
    
    document.getElementById('auditor-records-tab').addEventListener('shown.bs.tab', function() {
        loadAuditorRecords();
    });
}

/**
 * 加载审核统计信息
 */
async function loadAuditStatistics() {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch('/api/audit/statistics', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                const stats = data.data;
                document.getElementById('pendingCount').textContent = stats.pendingCount || 0;
                document.getElementById('approvedCount').textContent = stats.approvedCount || 0;
                document.getElementById('rejectedCount').textContent = stats.rejectedCount || 0;
                document.getElementById('totalCount').textContent = stats.totalAudits || 0;
            }
        }
    } catch (error) {
        console.error('Failed to load audit statistics:', error);
    }
}

/**
 * 加载待审核题目列表
 */
async function loadPendingAudits(page = 1) {
    const container = document.getElementById('pendingAuditsContainer');
    showLoading(container);
    
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/audit/pending?pageNum=${page}&pageSize=10`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                renderPendingAudits(data.data);
                currentPage.pending = page;
            } else {
                showError(container, data.message || '加载失败');
            }
        } else {
            showError(container, '网络请求失败');
        }
    } catch (error) {
        console.error('Failed to load pending audits:', error);
        showError(container, '加载失败');
    }
}

/**
 * 渲染待审核题目列表
 */
function renderPendingAudits(pageData) {
    const container = document.getElementById('pendingAuditsContainer');
    const pagination = document.getElementById('pendingPagination');
    
    if (!pageData.records || pageData.records.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4 text-muted">
                <i class="fas fa-inbox fa-2x"></i>
                <p class="mt-2">暂无待审核题目</p>
            </div>
        `;
        pagination.style.display = 'none';
        return;
    }
    
    let html = '';
    pageData.records.forEach(audit => {
        html += `
            <div class="card mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">${escapeHtml(audit.title)}</h6>
                        <small class="text-muted">
                            提交者: ${escapeHtml(audit.username)} | 
                            提交时间: ${formatDate(audit.submitTime)}
                        </small>
                    </div>
                    <button class="btn btn-primary btn-sm" onclick="openAuditModal(${audit.id})">
                        <i class="fas fa-gavel"></i> 审核
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>题目类型:</strong> ${getTopicTypeText(audit.type)}</p>
                            <p><strong>知识点ID:</strong> ${audit.knowId}</p>
                            <p><strong>难度:</strong> ${audit.difficulty || '未设置'}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>分值:</strong> ${audit.score}分</p>
                            <p><strong>来源:</strong> ${escapeHtml(audit.source || '未设置')}</p>
                            <p><strong>标签:</strong> ${escapeHtml(audit.tags || '无')}</p>
                        </div>
                    </div>
                    ${audit.answer ? `<p><strong>答案:</strong> ${escapeHtml(audit.answer)}</p>` : ''}
                    ${audit.parse ? `<div class="topic-content"><strong>解析:</strong><br>${escapeHtml(audit.parse)}</div>` : ''}
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
    
    // 渲染分页
    if (pageData.pages > 1) {
        renderPagination('pendingPagination', pageData, loadPendingAudits);
    } else {
        pagination.style.display = 'none';
    }
}

/**
 * 加载我的审核提交
 */
async function loadMyAudits(page = 1) {
    const container = document.getElementById('myAuditsContainer');
    showLoading(container);
    
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/audit/my-audits?pageNum=${page}&pageSize=10`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                renderMyAudits(data.data);
                currentPage.myAudits = page;
            } else {
                showError(container, data.message || '加载失败');
            }
        } else {
            showError(container, '网络请求失败');
        }
    } catch (error) {
        console.error('Failed to load my audits:', error);
        showError(container, '加载失败');
    }
}

/**
 * 渲染我的审核提交
 */
function renderMyAudits(pageData) {
    const container = document.getElementById('myAuditsContainer');
    const pagination = document.getElementById('myAuditsPagination');
    
    if (!pageData.records || pageData.records.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4 text-muted">
                <i class="fas fa-inbox fa-2x"></i>
                <p class="mt-2">暂无审核提交记录</p>
            </div>
        `;
        pagination.style.display = 'none';
        return;
    }
    
    let html = '';
    pageData.records.forEach(audit => {
        const statusClass = getAuditStatusClass(audit.auditStatus);
        html += `
            <div class="card mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">${escapeHtml(audit.title)}</h6>
                        <small class="text-muted">提交时间: ${formatDate(audit.submitTime)}</small>
                    </div>
                    <span class="badge ${statusClass}">${audit.auditStatusText}</span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <p><strong>题目类型:</strong> ${getTopicTypeText(audit.type)}</p>
                            <p><strong>知识点ID:</strong> ${audit.knowId}</p>
                        </div>
                        <div class="col-md-4">
                            ${audit.auditorName ? `<p><strong>审核员:</strong> ${escapeHtml(audit.auditorName)}</p>` : ''}
                            ${audit.auditTime ? `<p><strong>审核时间:</strong> ${formatDate(audit.auditTime)}</p>` : ''}
                        </div>
                    </div>
                    ${audit.auditComment ? `<div class="alert alert-info"><strong>审核意见:</strong> ${escapeHtml(audit.auditComment)}</div>` : ''}
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
    
    // 渲染分页
    if (pageData.pages > 1) {
        renderPagination('myAuditsPagination', pageData, loadMyAudits);
    } else {
        pagination.style.display = 'none';
    }
}

/**
 * 加载审核员记录
 */
async function loadAuditorRecords(page = 1) {
    const container = document.getElementById('auditorRecordsContainer');
    showLoading(container);
    
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/audit/auditor-records?pageNum=${page}&pageSize=10`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                renderAuditorRecords(data.data);
                currentPage.auditorRecords = page;
            } else {
                showError(container, data.message || '加载失败');
            }
        } else {
            showError(container, '网络请求失败');
        }
    } catch (error) {
        console.error('Failed to load auditor records:', error);
        showError(container, '加载失败');
    }
}

/**
 * 渲染审核员记录
 */
function renderAuditorRecords(pageData) {
    const container = document.getElementById('auditorRecordsContainer');
    const pagination = document.getElementById('auditorRecordsPagination');
    
    if (!pageData.records || pageData.records.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4 text-muted">
                <i class="fas fa-inbox fa-2x"></i>
                <p class="mt-2">暂无审核记录</p>
            </div>
        `;
        pagination.style.display = 'none';
        return;
    }
    
    let html = '';
    pageData.records.forEach(audit => {
        if (audit.auditStatus === 0) return; // 跳过待审核的
        
        const statusClass = getAuditStatusClass(audit.auditStatus);
        html += `
            <div class="card mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">${escapeHtml(audit.title)}</h6>
                        <small class="text-muted">
                            提交者: ${escapeHtml(audit.username)} | 
                            审核时间: ${formatDate(audit.auditTime)}
                        </small>
                    </div>
                    <span class="badge ${statusClass}">${audit.auditStatusText}</span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>题目类型:</strong> ${getTopicTypeText(audit.type)}</p>
                            <p><strong>知识点ID:</strong> ${audit.knowId}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>提交时间:</strong> ${formatDate(audit.submitTime)}</p>
                        </div>
                    </div>
                    ${audit.auditComment ? `<div class="alert alert-secondary"><strong>审核意见:</strong> ${escapeHtml(audit.auditComment)}</div>` : ''}
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
    
    // 渲染分页
    if (pageData.pages > 1) {
        renderPagination('auditorRecordsPagination', pageData, loadAuditorRecords);
    } else {
        pagination.style.display = 'none';
    }
}

/**
 * 打开审核模态框
 */
async function openAuditModal(auditId) {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/audit/pending?pageNum=1&pageSize=1000`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                const audit = data.data.records.find(a => a.id === auditId);
                if (audit) {
                    currentAuditData = audit;
                    renderAuditModal(audit);
                    const modal = new bootstrap.Modal(document.getElementById('auditModal'));
                    modal.show();
                }
            }
        }
    } catch (error) {
        console.error('Failed to load audit data:', error);
        alert('加载审核数据失败');
    }
}

/**
 * 渲染审核模态框内容
 */
function renderAuditModal(audit) {
    const content = document.getElementById('auditTopicContent');
    
    let optionsHtml = '';
    if (audit.options) {
        try {
            const options = JSON.parse(audit.options);
            if (options.length > 0) {
                optionsHtml = '<div class="mb-3"><strong>选项:</strong><ul>';
                options.forEach((option, index) => {
                    const label = String.fromCharCode(65 + index); // A, B, C, D...
                    optionsHtml += `<li>${label}. ${escapeHtml(option.text || option)}</li>`;
                });
                optionsHtml += '</ul></div>';
            }
        } catch (e) {
            console.error('Failed to parse options:', e);
        }
    }
    
    content.innerHTML = `
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">${escapeHtml(audit.title)}</h6>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>题目类型:</strong> ${getTopicTypeText(audit.type)}</p>
                        <p><strong>知识点ID:</strong> ${audit.knowId}</p>
                        <p><strong>难度:</strong> ${audit.difficulty || '未设置'}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>分值:</strong> ${audit.score}分</p>
                        <p><strong>来源:</strong> ${escapeHtml(audit.source || '未设置')}</p>
                        <p><strong>标签:</strong> ${escapeHtml(audit.tags || '无')}</p>
                    </div>
                </div>
                ${optionsHtml}
                ${audit.answer ? `<div class="mb-3"><strong>答案:</strong> ${escapeHtml(audit.answer)}</div>` : ''}
                ${audit.parse ? `<div class="mb-3"><strong>解析:</strong><div class="bg-light p-2 rounded">${escapeHtml(audit.parse)}</div></div>` : ''}
                <div class="text-muted">
                    <small>提交者: ${escapeHtml(audit.username)} | 提交时间: ${formatDate(audit.submitTime)}</small>
                </div>
            </div>
        </div>
    `;
    
    // 重置表单
    document.getElementById('auditForm').reset();
    document.getElementById('auditId').value = audit.id;
}

/**
 * 提交审核
 */
async function submitAudit() {
    const form = document.getElementById('auditForm');
    const formData = new FormData(form);
    const auditResult = formData.get('auditResult');
    const auditComment = formData.get('auditComment');
    
    if (!auditResult) {
        alert('请选择审核结果');
        return;
    }
    
    if (auditResult === '2' && !auditComment.trim()) {
        alert('拒绝时必须填写审核意见');
        return;
    }
    
    try {
        const token = localStorage.getItem('token');
        const response = await fetch('/api/audit/review', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                auditId: parseInt(formData.get('auditId')),
                auditResult: parseInt(auditResult),
                auditComment: auditComment
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                alert('审核成功');
                const modal = bootstrap.Modal.getInstance(document.getElementById('auditModal'));
                modal.hide();
                
                // 刷新数据
                loadAuditStatistics();
                loadPendingAudits(currentPage.pending);
            } else {
                alert(data.message || '审核失败');
            }
        } else {
            alert('网络请求失败');
        }
    } catch (error) {
        console.error('Failed to submit audit:', error);
        alert('审核失败');
    }
}

/**
 * 加载未读消息
 */
async function loadUnreadMessages() {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch('/api/audit/messages/unread-count', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                const count = data.data;
                const badge = document.getElementById('unreadBadge');
                if (count > 0) {
                    badge.textContent = count;
                    badge.style.display = 'inline';
                } else {
                    badge.style.display = 'none';
                }
            }
        }
    } catch (error) {
        console.error('Failed to load unread messages:', error);
    }
}

// 工具函数
function showLoading(container) {
    container.innerHTML = `
        <div class="text-center py-4">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">加载中...</p>
        </div>
    `;
}

function showError(container, message) {
    container.innerHTML = `
        <div class="text-center py-4 text-danger">
            <i class="fas fa-exclamation-triangle fa-2x"></i>
            <p class="mt-2">${escapeHtml(message)}</p>
        </div>
    `;
}

function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

function getTopicTypeText(type) {
    if (!type) {
        return '未设置';
    }
    
    const typeMap = {
        // 数据库标准格式（与后端DB_*常量对应）
        'choice': '单选题',
        'multiple': '多选题',
        'judge': '判断题',
        'fill': '填空题',
        'short': '简答题',
        'subjective': '主观题',
        'group': '组合题',
        
        // 前端格式（与后端FRONTEND_*常量对应）
        'SINGLE_CHOICE': '单选题',
        'MULTIPLE_CHOICE': '多选题',
        'JUDGE': '判断题',
        'FILL': '填空题',
        'SHORT': '简答题',
        'SUBJECTIVE': '主观题',
        'GROUP': '组合题',
        
        // 驼峰格式（与后端CAMEL_*常量对应）
        'singleChoice': '单选题',
        'multipleChoice': '多选题',
        'judgment': '判断题',
        'fillBlank': '填空题',
        'shortAnswer': '简答题',
        'groupQuestion': '组合题',
        
        // 兼容旧格式和别名
        'single_choice': '单选题',
        'multiple_choice': '多选题',
        'fill_in_blank': '填空题',
        'essay': '简答题',
        'composite': '组合题',
        'programming': '编程题'
    };
    
    // 先尝试直接匹配
    if (typeMap[type]) {
        return typeMap[type];
    }
    
    // 尝试大写匹配
    const upperType = type.toUpperCase();
    if (typeMap[upperType]) {
        return typeMap[upperType];
    }
    
    // 尝试小写匹配
    const lowerType = type.toLowerCase();
    if (typeMap[lowerType]) {
        return typeMap[lowerType];
    }
    
    // 如果都没匹配到，返回原始类型
    return type;
}

function getAuditStatusClass(status) {
    switch (status) {
        case 0: return 'bg-warning text-dark';
        case 1: return 'bg-success';
        case 2: return 'bg-danger';
        default: return 'bg-secondary';
    }
}

function renderPagination(containerId, pageData, loadFunction) {
    const container = document.getElementById(containerId);
    const pagination = container.querySelector('.pagination');
    
    if (pageData.pages <= 1) {
        container.style.display = 'none';
        return;
    }
    
    let html = '';
    
    // 上一页
    if (pageData.current > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="${loadFunction.name}(${pageData.current - 1}); return false;">上一页</a></li>`;
    }
    
    // 页码
    const start = Math.max(1, pageData.current - 2);
    const end = Math.min(pageData.pages, pageData.current + 2);
    
    for (let i = start; i <= end; i++) {
        const activeClass = i === pageData.current ? 'active' : '';
        html += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="${loadFunction.name}(${i}); return false;">${i}</a></li>`;
    }
    
    // 下一页
    if (pageData.current < pageData.pages) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="${loadFunction.name}(${pageData.current + 1}); return false;">下一页</a></li>`;
    }
    
    pagination.innerHTML = html;
    container.style.display = 'block';
}