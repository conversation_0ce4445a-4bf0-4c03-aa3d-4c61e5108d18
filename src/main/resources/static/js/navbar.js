/**
 *  通用导航栏功能
 * 包含用户下拉菜单、用户信息加载、编辑、退出登录等功能
 */

/**
 * 初始化导航栏功能
 */
function initNavbar() {
    try {
        initUserDropdown();
        initUserProfileModal();
        loadNavbarUserInfo();

    } catch (error) {
    }
}

/**
 * 初始化用户下拉菜单
 */
function initUserDropdown() {

    // 用户信息点击事件
    $('.nav-user .user-info').on('click', function(e) {
        e.stopPropagation();
        const $userInfo = $(this);
        const $dropdown = $('.nav-user .dropdown-menu');

        // 切换下拉菜单显示状态
        $dropdown.toggleClass('show');
        $userInfo.toggleClass('active');

        console.log('🔧 用户下拉菜单状态:', $dropdown.hasClass('show') ? '显示' : '隐藏');
    });

    // 点击其他地方关闭下拉菜单
    $(document).on('click', function() {
        $('.nav-user .dropdown-menu').removeClass('show');
        $('.nav-user .user-info').removeClass('active');
    });

    // 阻止下拉菜单内部点击事件冒泡
    $('.nav-user .dropdown-menu').on('click', function(e) {
        e.stopPropagation();
    });

    // 个人信息编辑事件
    $('.dropdown-item[href="/user/profile"]').on('click', function(e) {
        e.preventDefault();
        console.log('🔧 用户点击编辑个人信息');
        openUserProfileModal();
    });

    // 退出登录事件
    $('#logout').on('click', function(e) {
        e.preventDefault();
        console.log('🔧 用户点击退出登录');
        handleNavbarLogout();
    });
}

/**
 * 加载导航栏用户信息
 */
function loadNavbarUserInfo() {

    $.ajax({
        url: '/api/user/current',
        method: 'GET',
        beforeSend: function(xhr) {
            const token = localStorage.getItem('token');
            if (token) {
                xhr.setRequestHeader('Authorization', 'Bearer ' + token);
            }
        },
        success: function(response) {
            if (response.code === 200 && response.data) {
                updateNavbarUserInfo(response.data);
            } else {
                setNavbarUserInfoDefault();
            }
        },
        error: function(xhr) {
            console.error('❌ 导航栏用户信息加载失败:', xhr.status, xhr.responseText);
            setNavbarUserInfoDefault();

            if (xhr.status === 401) {
                console.warn('⚠️ 用户未认证，可能需要登录');
            }
        }
    });
}

/**
 * 更新导航栏用户信息
 * @param {Object} user - 用户信息对象
 */
function updateNavbarUserInfo(user) {
    // 更新导航栏用户名
    $('.username').text(user.username || '用户');

    // 更新下拉菜单中的用户信息
    $('.dropdown-username').text(user.username || '用户');
    $('.dropdown-email').text(user.email || '未设置邮箱');

    // 使用新的AvatarUtils处理头像显示
    if (window.AvatarUtils) {
        // 获取所有导航栏头像元素
        const avatarElements = document.querySelectorAll('.avatar, .dropdown-avatar, #userAvatar');
        
        avatarElements.forEach(img => {
            // 使用新的头像处理逻辑
            window.AvatarUtils.setAvatarElement(img, user.avatar, {
                id: user.id,
                username: user.username,
                email: user.email
            }, {
                showInitials: true,        // 显示首字母头像
                allowRandomAvatar: false,  // 不允许随机头像
                showPlaceholder: false,    // 不显示占位符
                hideIfNoAvatar: false      // 不隐藏元素
            });
        });
    } else {
        // 降级处理：如果AvatarUtils不可用，使用简单逻辑
        console.warn('AvatarUtils不可用，使用降级处理');
        
        let avatarUrl;
        if (user.avatar && user.avatar.trim() !== '') {
            // 用户有头像
            avatarUrl = user.avatar.startsWith('/') ? user.avatar : `/uploads/avatars/${user.avatar}`;
        } else {
            // 用户没有头像，生成首字母头像
            avatarUrl = generateSimpleInitialsAvatar(user.username || 'U');
        }

        // 更新所有头像元素
        $('.avatar, .dropdown-avatar, #userAvatar').attr('src', avatarUrl).on('error', function() {
            // 如果加载失败，生成首字母头像
            $(this).attr('src', generateSimpleInitialsAvatar(user.username || 'U'));
        });
    }
}

/**
 * 简单的首字母头像生成（降级方案）
 * @param {string} username - 用户名
 * @returns {string} Data URL
 */
function generateSimpleInitialsAvatar(username) {
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const ctx = canvas.getContext('2d');
    
    // 生成颜色
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
    const colorIndex = Math.abs(hashString(username)) % colors.length;
    const bgColor = colors[colorIndex];
    
    // 获取首字母
    let initials = 'U';
    if (username && username.length > 0) {
        if (/[\u4e00-\u9fa5]/.test(username)) {
            // 中文用户名取第一个字
            initials = username.charAt(0);
        } else {
            // 英文用户名取前两个字母
            initials = username.substring(0, 2).toUpperCase();
        }
    }
    
    // 绘制背景
    ctx.fillStyle = bgColor;
    ctx.fillRect(0, 0, 64, 64);
    
    // 绘制字母
    ctx.fillStyle = '#FFFFFF';
    ctx.font = 'bold 24px Arial, sans-serif';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(initials, 32, 32);
    
    return canvas.toDataURL();
}

/**
 * 简单哈希函数
 * @param {string} str 
 * @returns {number}
 */
function hashString(str) {
    let hash = 0;
    if (!str || str.length === 0) return hash;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
    }
    return Math.abs(hash);
}

/**
 * 设置导航栏用户信息为默认状态
 */
function setNavbarUserInfoDefault() {
    $('.username').text('未登录');
    $('.dropdown-username').text('未登录');
    $('.dropdown-email').text('请先登录');
    
    // 为未登录状态显示通用头像
    const defaultAvatarUrl = generateSimpleInitialsAvatar('未登录');
    $('.avatar, .dropdown-avatar, #userAvatar').attr('src', defaultAvatarUrl);
}

/**
 * 处理导航栏退出登录
 */
function handleNavbarLogout() {
    Swal.fire({
        title: '确认退出登录',
        text: '您确定要退出当前账户吗？',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: '确定退出',
        cancelButtonText: '取消',
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        customClass: {
            popup: 'logout-popup'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            performLogout();
        }
    });
}

/**
 * 执行退出登录操作
 */
function performLogout() {
    // 显示退出中的提示
    Swal.fire({
        title: '正在退出...',
        text: '请稍候',
        icon: 'info',
        allowOutsideClick: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    $.ajax({
        url: '/api/user/logout',
        method: 'POST',
        beforeSend: function(xhr) {
            const token = localStorage.getItem('token');
            if (token) {
                xhr.setRequestHeader('Authorization', 'Bearer ' + token);
            }
        },
        success: function(response) {
            console.log('✅ 退出登录成功:', response);
            clearUserData();

            Swal.fire({
                title: '退出成功',
                text: '您已成功退出登录',
                icon: 'success',
                timer: 1500,
                showConfirmButton: false
            }).then(() => {
                window.location.href = '/auth/login';
            });
        },
        error: function(xhr) {
            console.error('❌ 退出登录失败:', xhr);
            clearUserData();

            Swal.fire({
                title: '已清除登录信息',
                text: '本地登录信息已清除，即将跳转到登录页面',
                icon: 'info',
                timer: 2000,
                showConfirmButton: false
            }).then(() => {
                window.location.href = '/auth/login';
            });
        }
    });
}

/**
 * 清除用户数据
 */
function clearUserData() {
    localStorage.removeItem('token');
    localStorage.removeItem('currentUser');
    console.log('🔧 用户数据已清除');
}

/**
 * 获取当前用户信息（同步方式）
 */
function getCurrentUserInfo() {
    const userStr = localStorage.getItem('currentUser');
    if (userStr) {
        try {
            return JSON.parse(userStr);
        } catch (error) {
            console.error('❌ 解析用户信息失败:', error);
            return null;
        }
    }
    return null;
}

/**
 * 检查用户是否已登录
 */
function isUserLoggedIn() {
    const token = localStorage.getItem('token');
    const user = getCurrentUserInfo();
    return !!(token && user);
}

/**
 * 初始化用户信息编辑模态框
 */
function initUserProfileModal() {
    // 头像预览点击事件
    $('.avatar-preview').on('click', function() {
        $('#avatarInput').click();
    });

    // 头像文件选择事件
    $('#avatarInput').on('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            handleAvatarUpload(file);
        }
    });

    // 保存用户信息事件
    $('#saveUserProfile').on('click', function() {
        saveUserProfile();
    });
}

/**
 * 打开用户信息编辑模态框
 */
function openUserProfileModal() {
    // 关闭下拉菜单
    $('.nav-user .dropdown-menu').removeClass('show');
    $('.nav-user .user-info').removeClass('active');

    // 加载当前用户信息到表单
    loadUserInfoToModal();

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('userProfileModal'));
    modal.show();
}

/**
 * 加载用户信息到模态框
 */
function loadUserInfoToModal() {
    console.log('🔧 加载用户信息到模态框...');

    $.ajax({
        url: '/api/user/current',
        method: 'GET',
        beforeSend: function(xhr) {
            const token = localStorage.getItem('token');
            if (token) {
                xhr.setRequestHeader('Authorization', 'Bearer ' + token);
            }
        },
        success: function(response) {
            if (response.code === 200 && response.data) {
                const user = response.data;

                // 填充表单数据
                $('#editUsername').val(user.username || '');
                $('#editEmail').val(user.email || '');
                $('#editPhone').val(user.phone || '');
                $('#editRealName').val(user.realName || '');
                $('#editBio').val(user.bio || '');

                // 设置头像预览
                const avatarUrl = user.avatar ?
                    (user.avatar.startsWith('/') ? user.avatar : '/static/uploads/' + user.avatar) :
                    '/static/images/default-avatar.png';
                $('#previewAvatar').attr('src', avatarUrl);

                console.log('✅ 用户信息加载到模态框完成');
            }
        },
        error: function(xhr) {
            console.error('❌ 加载用户信息到模态框失败:', xhr);
            Swal.fire('错误', '加载用户信息失败', 'error');
        }
    });
}

/**
 * 处理头像上传
 */
function handleAvatarUpload(file) {
    console.log('🔧 处理头像上传:', file.name);

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
        Swal.fire('错误', '请选择图片文件', 'error');
        return;
    }

    // 验证文件大小 (2MB)
    if (file.size > 2 * 1024 * 1024) {
        Swal.fire('错误', '图片大小不能超过2MB', 'error');
        return;
    }

    // 预览图片
    const reader = new FileReader();
    reader.onload = function(e) {
        $('#previewAvatar').attr('src', e.target.result);
    };
    reader.readAsDataURL(file);

    console.log('✅ 头像预览更新完成');
}

/**
 * 保存用户信息
 */
function saveUserProfile() {
    console.log('🔧 保存用户信息...');

    // 显示保存中状态
    const saveBtn = $('#saveUserProfile');
    const originalText = saveBtn.html();
    saveBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>保存中...');

    // 准备表单数据
    const formData = new FormData();
    formData.append('username', $('#editUsername').val());
    formData.append('email', $('#editEmail').val());
    formData.append('phone', $('#editPhone').val());
    formData.append('realName', $('#editRealName').val());
    formData.append('bio', $('#editBio').val());

    // 如果有新头像文件
    const avatarFile = $('#avatarInput')[0].files[0];
    if (avatarFile) {
        formData.append('avatar', avatarFile);
    }

    $.ajax({
        url: '/api/user/update-profile',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        beforeSend: function(xhr) {
            const token = localStorage.getItem('token');
            if (token) {
                xhr.setRequestHeader('Authorization', 'Bearer ' + token);
            }
        },
        success: function(response) {
            console.log('✅ 用户信息保存成功:', response);

            if (response.code === 200) {
                Swal.fire({
                    title: '保存成功',
                    text: '个人信息已更新',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });

                // 关闭模态框
                bootstrap.Modal.getInstance(document.getElementById('userProfileModal')).hide();

                // 重新加载导航栏用户信息
                loadNavbarUserInfo();
            } else {
                Swal.fire('保存失败', response.message || '未知错误', 'error');
            }
        },
        error: function(xhr) {
            console.error('❌ 用户信息保存失败:', xhr);
            let errorMsg = '保存失败，请稍后重试';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMsg = xhr.responseJSON.message;
            }

            Swal.fire('保存失败', errorMsg, 'error');
        },
        complete: function() {
            // 恢复按钮状态
            saveBtn.prop('disabled', false).html(originalText);
        }
    });
}

// 页面加载完成后自动初始化导航栏
$(document).ready(function() {
    // 检查是否存在导航栏
    if ($('.nav-user').length > 0) {
        initNavbar();
    }
});
