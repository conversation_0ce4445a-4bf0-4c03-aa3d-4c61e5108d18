// 通用功能
function escapeHtml(unsafe) {
    if (typeof unsafe !== 'string') {
        console.warn('escapeHtml called with non-string value:', unsafe);
        return String(unsafe); // Coerce to string if not already
    }
    return unsafe
         .replace(/&/g, "&amp;")
         .replace(/</g, "&lt;")
         .replace(/>/g, "&gt;")
         .replace(/"/g, "&quot;")
         .replace(/'/g, "&#039;");
}

const API_BASE_URL = '/api';

// Token处理函数 - 确保格式一致
function normalizeToken(token) {
    if (!token) return null;
    // 移除可能存在的Bearer前缀
    if (token.toLowerCase().startsWith('bearer ')) {
        return token.substring(7);
    }
    return token;
}

// 获取标准化的认证头
function getAuthHeader() {
    // 优先获取普通用户token
    let token = localStorage.getItem('token');
    
    // 如果没有普通用户token，检查管理员token
    if (!token || token === 'undefined' || token === 'null') {
        token = localStorage.getItem('adminToken');
    }
    
    // 最后检查sessionStorage中的管理员token
    if (!token || token === 'undefined' || token === 'null') {
        token = sessionStorage.getItem('adminToken');
    }
    
    if (!token || token === 'undefined' || token === 'null') return null;
    // 返回标准化的token，不添加Bearer前缀
    return normalizeToken(token);
}

// 增强的Token有效性预检
async function validateCurrentToken() {
    const token = localStorage.getItem('token');
    if (!token) return false;

    // 尝试次数追踪
    let attempts = 0;
    const maxAttempts = 2;

    while (attempts < maxAttempts) {
        attempts++;

        try {
            console.log(`正在验证当前token有效性...(尝试 ${attempts}/${maxAttempts})`);

            // 使用标准化的token
            const normalizedToken = normalizeToken(token);
            if (!normalizedToken) {
                console.error("Token标准化失败，可能格式有误");
                return false;
            }

            // 调用专门的token验证接口
            const response = await fetch(`${API_BASE_URL}/user/validate`, {
                headers: {
                    'Authorization': normalizedToken
                },
                // 设置较短的超时时间
                signal: AbortSignal.timeout(5000)
            });

            if (!response.ok) {
                console.warn(`Token验证失败，HTTP状态码: ${response.status}`);

                if (response.status === 401) {
                    // 清除无效token
                    localStorage.removeItem('token');
                    return false;
                }

                // 如果是其他错误且未达到最大尝试次数，则重试
                if (attempts < maxAttempts) {
                    console.log(`将在1秒后重试验证 (${attempts}/${maxAttempts})...`);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    continue;
                }

                return false;
            }

            const data = await response.json();
            console.log("Token验证响应:", data);

            if (data.code === 200) {
                console.log("Token验证成功");
                return true;
            } else {
                console.warn("Token验证返回非成功状态:", data);
                return false;
            }
        } catch (error) {
            console.error(`验证token时出错 (尝试 ${attempts}/${maxAttempts}):`, error);

            // 如果是网络错误且未达到最大尝试次数，则重试
            if (error.name === 'TypeError' && error.message.includes('fetch') && attempts < maxAttempts) {
                console.log(`网络错误，将在1秒后重试 (${attempts}/${maxAttempts})...`);
                await new Promise(resolve => setTimeout(resolve, 1000));
                continue;
            }

            return false;
        }
    }

    // 如果所有尝试都失败
    return false;
}

// 检查登录状态
async function checkAuth() {
    try {
        // 先验证本地是否有token（支持多种token类型）
        let token = localStorage.getItem('token');
        if (!token || token === 'undefined' || token === 'null') {
            token = localStorage.getItem('adminToken');
        }
        if (!token || token === 'undefined' || token === 'null') {
            token = sessionStorage.getItem('adminToken');
        }
        
        // 检查当前页面是否需要强制认证
        const currentPath = window.location.pathname;
        const optionalAuthPages = ['/topics/upload-topics', '/topics/bank', '/paper/generate'];
        const isOptionalAuthPage = optionalAuthPages.some(page => currentPath.includes(page));
        
        if (!token) {
            console.warn("本地无token，需要登录");
            // 对于可选认证的页面，不强制跳转
            if (isOptionalAuthPage) {
                console.info(`页面 ${currentPath} 允许无认证访问`);
                return null;
            }
            // 不在登录页时才跳转
            if (!window.location.pathname.includes('/auth/login') && !window.location.pathname.includes('/admin/login')) {
                window.location.href = '/auth/login';
            }
            return null;
        }

        // 使用标准化的token
        const normalizedToken = normalizeToken(token);
        if (!normalizedToken) {
            console.error("Token标准化失败，可能格式有误");
            // 清除无效的token
            localStorage.removeItem('token');
            localStorage.removeItem('adminToken');
            sessionStorage.removeItem('adminToken');
            // 对于可选认证的页面，不强制跳转
            if (isOptionalAuthPage) {
                console.info(`页面 ${currentPath} 允许无认证访问`);
                return null;
            }
            if (!window.location.pathname.includes('/auth/login') && !window.location.pathname.includes('/admin/login')) {
                window.location.href = '/auth/login';
            }
            return null;
        }
        // 设置请求超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 8000);

    try {
        const response = await fetch(`${API_BASE_URL}/user/current`, {
            headers: {
                    'Authorization': normalizedToken
                },
                signal: controller.signal
        });

            // 清除超时
            clearTimeout(timeoutId);

        if (!response.ok) {
            if (response.status === 401) {
                console.warn("Token无效或已过期 (状态码: 401)");
                // 清除所有无效的token
                localStorage.removeItem('token');
                localStorage.removeItem('adminToken');
                sessionStorage.removeItem('adminToken');
                // 对于可选认证的页面，不强制跳转
                if (isOptionalAuthPage) {
                    console.info(`页面 ${currentPath} 允许无认证访问`);
                    return null;
                }
                if (!window.location.pathname.includes('/auth/login') && !window.location.pathname.includes('/admin/login')) {
                    window.location.href = '/auth/login';
                }
                return null;
            }
                throw new Error(`服务器返回错误状态: ${response.status}`);
        }

        const data = await response.json();
        if (data.code === 200) {
            updateNavbarUserInfo(data.data);
            return data.data;
        } else {
            console.warn("checkAuth: Backend validation response error code:", data.code, data.message);
            throw new Error(data.message || "验证失败");
        }
    } catch (fetchError) {
        console.error("checkAuth: Error during fetch or JSON parsing:", fetchError);
        if (fetchError.name === 'AbortError') {
            console.error('验证请求超时');
            throw new Error('验证请求超时，请检查网络连接');
        }
        throw fetchError;
    }
} catch (error) {
    console.error('验证登录状态失败:', error);

    if (error.message.includes('无效') ||
        error.message.includes('过期') ||
        error.message.includes('401')) {
        localStorage.removeItem('token');
        localStorage.removeItem('adminToken');
        sessionStorage.removeItem('adminToken');
    }

    // 检查当前页面是否需要强制认证
    const currentPath = window.location.pathname;
    const optionalAuthPages = ['/topics/upload-topics', '/topics/bank', '/paper/generate'];
    const isOptionalAuthPage = optionalAuthPages.some(page => currentPath.includes(page));
    
    // 对于可选认证的页面，不强制跳转
    if (!isOptionalAuthPage && !window.location.pathname.includes('/auth/login') && !window.location.pathname.includes('/admin/login')) {
        window.location.href = '/auth/login';
    }
    return null;
}
}

// 获取客户端IP
async function getClientIP() {
    try {
        const response = await fetch('https://api.ipify.org?format=json');
        const data = await response.json();
        return data.ip;
    } catch (error) {
        console.error('获取IP失败:', error);
        return '';
    }
}

// 添加更详细的错误处理
class ApiError extends Error {
    constructor(message, code) {
        super(message);
        this.code = code;
        this.name = 'ApiError';
    }
}

async function handleApiResponse(response) {
    const data = await response.json();
    if (data.code !== 200) {
        throw new ApiError(data.message, data.code);
    }
    return data.data;
}

function handleError(error) {
    if (error instanceof ApiError) {
        showToast(error.message, 'error');
    } else if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        showToast('网络连接失败，请检查网络设置', 'error');
    } else {
        showToast('操作失败，请稍后重试', 'error');
    }
    console.error(error);
}

// 添加通用请求函数
async function apiRequest(url, options = {}) {
    const token = getAuthHeader(); // 使用统一获取认证头的函数
    if (!token && !url.includes('/login') && !url.includes('/register')) {
        window.location.href = '/auth/login';
        return;
    }

    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': token
        }
    };

    try {
        const response = await fetch(url, { ...defaultOptions, ...options });

        if (response.status === 401) {
            console.warn("API请求返回401未授权");
            localStorage.removeItem('token');
            window.location.href = '/auth/login';
            return;
        }

        const data = await response.json();
        if (data.code === 200) {
            return data.data;
        } else {
            throw new Error(data.message);
        }
    } catch (error) {
        handleError(error);
        throw error;
    }
}

// Toast 通知功能
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container') || createToastContainer();

    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <span class="toast-message">${message}</span>
        </div>
        <button class="toast-close">&times;</button>
    `;

    const closeButton = toast.querySelector('.toast-close');
    closeButton.addEventListener('click', () => {
        toast.classList.add('fade-out');
        setTimeout(() => toast.remove(), 300);
    });

    toastContainer.appendChild(toast);

    // 自动消失
    setTimeout(() => {
        toast.classList.add('fade-out');
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    document.body.appendChild(container);
    return container;
}

// 在页面加载完成后初始化导航栏和通用功能
document.addEventListener('DOMContentLoaded', async function() {
    // 插入导航栏（如果页面没有）
    insertNavbar();

    // 设置导航项高亮
    setActiveNavItem();

    // 登录状态检查
    await checkAuth();

    // 设置用户下拉菜单
    setupUserDropdown();

    // AFTER insertNavbar() and setupUserDropdown(), re-bind or ensure logout listener is correctly setup
    // It's usually better to setup the listener inside insertNavbar or right after it.
    // For this example, assuming 'logout' element is available after insertNavbar.
    const logoutLink = document.getElementById('logout');
    if (logoutLink) {
        // Remove any existing listener to avoid duplicates if this runs multiple times
        // A more robust solution would be to ensure this setup runs only once.
        // For now, this is a simple way to try and ensure it's freshly bound.
        // logoutLink.replaceWith(logoutLink.cloneNode(true)); // Re-cloning can also remove listeners
        // const freshLogoutLink = document.getElementById('logout');

        // It's better to have the original event listener attachment point (likely in insertNavbar) modified.
        // However, if insertNavbar is not in the visible scope, we are modifying it here.
        // If the original listener is still there, this might lead to issues.
        // The ideal place for this logic is where the 'logout' element's listener is first defined.
        // We will assume this is the main point or that insertNavbar calls this.

        // Try to remove any pre-existing listener if this code might run multiple times.
        // A cleaner way is to ensure event listeners are set up once.
        // However, given the context, this is an attempt to make it work.
        // Create a new function for the listener to ensure it's the one we control
        const handleLogout = async function(e) {
            e.preventDefault();
            console.log("Logout link clicked. Attempting logout.");

            const token = getAuthHeader(); // Get the current token

            if (token) {
                try {
                    console.log("Calling backend /api/user/logout");
                    const response = await fetch(`${API_BASE_URL}/user/logout`, {
                        method: 'POST',
                        headers: {
                            // The getAuthHeader() should return the raw token.
                            // If your backend expects "Bearer <token>", and getAuthHeader() doesn't add it,
                            // and your $.ajaxSetup doesn't cover 'fetch', you might need 'Authorization': 'Bearer ' + token
                            'Authorization': token, // Assuming getAuthHeader() provides the correct format
                            'Content-Type': 'application/json'
                        }
                        // No body needed for this specific logout if backend only checks token
                    });

                    if (response.ok) {
                        const responseData = await response.json(); // Try to parse JSON
                        if (responseData.code === 200) {
                            console.log("Successfully logged out from backend:", responseData.message || "Success");
                        } else {
                            console.warn("Backend logout returned an error code:", responseData.code, responseData.message || "No message");
                        }
                    } else {
                        console.warn(`Backend logout call failed with status: ${response.status} - ${response.statusText}`);
                        // Try to get error message from body if any
                        try {
                            const errorData = await response.json();
                            console.warn("Backend logout error details:", errorData.message || "No error details");
                        } catch (parseError) {
                            // Silent if no JSON body
                        }
                    }
                } catch (error) {
                    console.error("Error during backend logout API call:", error);
                }
            } else {
                console.warn("No token found in localStorage for backend logout call.");
            }

            // Always perform client-side logout actions
            localStorage.removeItem('token');
            console.log("Token removed from localStorage.");
            showToast('您已成功退出。正在跳转到登录页面...', 'success');
            setTimeout(() => {
                window.location.href = '/auth/login';
                console.log("Redirecting to login page.");
            }, 1500); // Delay for toast visibility
        };

        // To avoid multiple listeners if this code runs more than once.
        // This is a common issue if DOMContentLoaded handlers are complex or re-run.
        // A simple flag or checking for an existing custom attribute can help.
        if (!logoutLink.dataset.logoutListenerAttached) {
            logoutLink.addEventListener('click', handleLogout);
            logoutLink.dataset.logoutListenerAttached = 'true';
            console.log("Logout event listener attached.");
        }

    } else {
        console.warn("Logout link with id 'logout' not found after DOMContentLoaded. Navbar might not be inserted yet or ID is incorrect.");
    }
});

// 插入统一导航栏
function insertNavbar() {
    // Temporarily comment out the condition to force insertion for diagnostics
    // if (!existingNavbar) {
        const navbarHTML = `
        <nav class="navbar">
            <div class="nav-brand">
                <a href="/">Maizi EDU</a>
            </div>
            <div class="nav-menu">
                <a href="/main/chat" class="nav-item">出题</a>
                <a href="/topics/upload-topics" class="nav-item">上传</a>
                <a href="/paper/generate" class="nav-item">组卷</a>
                <a href="/papers/duplicate-check" class="nav-item">查重</a>
                <a href="/topics/bank" class="nav-item">题库</a>
            </div>
            <div class="nav-user"> <!-- User section restored for testing -->
                <div class="user-info">
                    <img src="/static/images/default-avatar.png" alt="avatar" class="avatar">
                    <span class="username">加载中...</span>
                </div>
                <div class="dropdown-menu">
                    <a href="/user/profile">个人信息</a>
                    <a href="#" id="logout">退出登录</a>
                </div>
            </div>
        </nav>
        `;

        // 插入到body开始
        try {
            document.body.insertAdjacentHTML('afterbegin', navbarHTML);
        } catch (e) {
            console.error("insertNavbar: Error during insertAdjacentHTML:", e); // Diagnostic log for error
        }

        const logoutLink = document.getElementById('logout');
        if (logoutLink && !logoutLink.dataset.logoutListenerAttached) {
            logoutLink.addEventListener('click', async function(e) {
                e.preventDefault();
                console.log("退出登录按钮被点击");
                await logout();
            });
            logoutLink.dataset.logoutListenerAttached = 'true';
        }
    // } else { // End of temporarily commented out condition
    //     console.log("insertNavbar: Navbar already exists (document.querySelector('.navbar') found an element). Navbar not inserted by common.js."); // Diagnostic log
    // }
}

// 设置导航项高亮
function setActiveNavItem() {
    const currentPath = window.location.pathname;
    const navItems = document.querySelectorAll('.nav-menu .nav-item');

    navItems.forEach(item => {
        item.classList.remove('active');
        const href = item.getAttribute('href');

        if (currentPath === '/' && href === '/') {
            item.classList.add('active');
        } else if (currentPath.includes('chat') && href.includes('chat')) {
            item.classList.add('active');
        } else if (currentPath.includes('upload') && href.includes('upload')) {
            item.classList.add('active');
        } else if (currentPath.includes('generate') && href.includes('generate')) {
            item.classList.add('active');
        } else if (currentPath.includes('check') && href.includes('check')) {
            item.classList.add('active');
        } else if (currentPath.includes('bank') && href.includes('bank')) {
            item.classList.add('active');
        } else if (currentPath.includes('profile') && href.includes('profile')) {
            item.classList.add('active');
        }
    });
}

// 设置用户下拉菜单
function setupUserDropdown() {
    const userInfo = document.querySelector('.user-info');
    const dropdownMenu = document.querySelector('.dropdown-menu');

    if (userInfo && dropdownMenu) {
        userInfo.addEventListener('click', function() {
            dropdownMenu.style.display = dropdownMenu.style.display === 'block' ? 'none' : 'block';
        });

        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(event) {
            if (!userInfo.contains(event.target) && !dropdownMenu.contains(event.target)) {
                dropdownMenu.style.display = 'none';
            }
        });
    }
}

// 设置全局AJAX头
$(document).ready(function() {
    // 为所有jQuery AJAX请求设置全局处理
    $.ajaxSetup({
        beforeSend: function(xhr) {
            const token = getAuthHeader();
            if (token) {
                xhr.setRequestHeader('Authorization', 'Bearer ' + token);
            }
        }
    });

    // 全局AJAX错误处理
    $(document).ajaxError(function(event, jqXHR, ajaxSettings, thrownError) {
        console.error("AJAX错误:", thrownError, "状态:", jqXHR.status);

        if (jqXHR.status === 401) {
            console.warn("认证失败（401）- 可能需要重新登录");
            // 仅在非登录页跳转
            if (!window.location.pathname.includes('/auth/login')) {
                localStorage.removeItem('token');
                window.location.href = '/auth/login';
            }
        }
    });
});

// 更新导航栏用户信息
function updateNavbarUserInfo(userData) {
    const usernameElements = document.querySelectorAll('.username');
    const avatarElements = document.querySelectorAll('.avatar');

    let newUsername = '用户'; // Default username
    if (userData && (userData.nickname || userData.username)) {
        newUsername = userData.nickname || userData.username;
    } else {
        console.warn("updateNavbarUserInfo: userData is missing or lacks nickname/username. Defaulting to '用户'.");
    }

    usernameElements.forEach(element => {
        element.textContent = newUsername;
    });

    if (userData && userData.avatar) {
        try {
            // 正确处理头像路径
            let avatarPath = userData.avatar.trim();

            // 确保是完整的URL或正确的路径
            if (avatarPath.startsWith('//')) {
                // 相对协议URL，添加协议
                avatarPath = window.location.protocol + avatarPath;
            } else if (!avatarPath.startsWith('http') && !avatarPath.startsWith('/')) {
                // 不是完整URL且不以/开头，添加/
                avatarPath = '/' + avatarPath;
            }

            // 修复常见路径问题
            if (avatarPath.includes('avatars/') && !avatarPath.includes('/uploads/')) {
                avatarPath = avatarPath.replace('avatars/', '/uploads/avatars/');
            }

            // 使用当前域名构建绝对URL（如果是相对路径）
            if (!avatarPath.startsWith('http')) {
                const baseUrl = window.location.origin; // 动态获取当前域名和端口

                // 确保没有双斜杠问题
                if (avatarPath.startsWith('/')) {
                    avatarPath = baseUrl + avatarPath;
                } else {
                    avatarPath = baseUrl + '/' + avatarPath;
                }
            }

            // 修复双斜杠问题
            avatarPath = avatarPath.replace(/([^:])\/\//g, '$1/');

            // 使用fixAvatarUrl辅助函数（如果存在）
        if (window.fixAvatarUrl) {
            avatarPath = window.fixAvatarUrl(avatarPath);
        }

        avatarElements.forEach(element => {
                // 直接使用内联SVG作为默认头像
                useFallbackSvgAvatar(element);

                // 然后尝试加载用户头像
                const img = new Image();
                img.onload = function() {
                    // 只有成功加载后才设置头像
                    element.src = `${avatarPath}?t=${new Date().getTime()}`;
                };
                img.onerror = function() {
                    // 如果加载失败，保留已设置的默认SVG头像
                    console.warn('Failed to preload avatar:', avatarPath);
                };
                img.src = avatarPath;
            });
        } catch (e) {
            console.error('处理头像路径时出错:', e);
            // 出错时使用默认头像
            avatarElements.forEach(element => {
                useFallbackSvgAvatar(element);
            });
        }
    } else {
        avatarElements.forEach(element => {
            useFallbackSvgAvatar(element); // Ensure this is robust
        });
    }
}

// 获取题目类型的显示信息（文本和图标）- 与后端TopicTypeMapper保持一致
function getTopicTypeInfo(typeKey) {
    if (!typeKey) {
        return { text: '未设置', icon: 'bi-question-lg' };
    }
    
    const typeMap = {
        // 数据库标准格式（与后端DB_*常量对应）
        'choice': { text: '单选题', icon: 'bi-check-circle' },
        'multiple': { text: '多选题', icon: 'bi-list-check' },
        'judge': { text: '判断题', icon: 'bi-question-octagon' },
        'fill': { text: '填空题', icon: 'bi-input-cursor-text' },
        'short': { text: '简答题', icon: 'bi-pencil-square' },
        'subjective': { text: '主观题', icon: 'bi-pencil-square' },
        'group': { text: '组合题', icon: 'bi-collection' },
        
        // 前端格式（与后端FRONTEND_*常量对应）
        'SINGLE_CHOICE': { text: '单选题', icon: 'bi-check-circle' },
        'MULTIPLE_CHOICE': { text: '多选题', icon: 'bi-list-check' },
        'JUDGE': { text: '判断题', icon: 'bi-question-octagon' },
        'FILL': { text: '填空题', icon: 'bi-input-cursor-text' },
        'SHORT': { text: '简答题', icon: 'bi-pencil-square' },
        'SUBJECTIVE': { text: '主观题', icon: 'bi-pencil-square' },
        'GROUP': { text: '组合题', icon: 'bi-collection' },
        
        // 驼峰格式（与后端CAMEL_*常量对应）
        'singleChoice': { text: '单选题', icon: 'bi-check-circle' },
        'multipleChoice': { text: '多选题', icon: 'bi-list-check' },
        'judgment': { text: '判断题', icon: 'bi-question-octagon' },
        'fillBlank': { text: '填空题', icon: 'bi-input-cursor-text' },
        'shortAnswer': { text: '简答题', icon: 'bi-pencil-square' },
        'groupQuestion': { text: '组合题', icon: 'bi-collection' },
        
        // 兼容旧格式和别名
        'single_choice': { text: '单选题', icon: 'bi-check-circle' },
        'multiple_choice': { text: '多选题', icon: 'bi-list-check' },
        'fill_in_blank': { text: '填空题', icon: 'bi-input-cursor-text' },
        'essay': { text: '简答题', icon: 'bi-pencil-square' },
        'composite': { text: '组合题', icon: 'bi-grid-3x3-gap' },
        'programming': { text: '编程题', icon: 'bi-code-slash' },
        'default': { text: '未知类型', icon: 'bi-question-lg' }
    };
    
    // 先尝试直接匹配
    if (typeMap[typeKey]) {
        return typeMap[typeKey];
    }
    
    // 尝试大写匹配
    const upperType = typeKey.toUpperCase();
    if (typeMap[upperType]) {
        return typeMap[upperType];
    }
    
    // 尝试小写匹配
    const lowerType = typeKey.toLowerCase();
    if (typeMap[lowerType]) {
        return typeMap[lowerType];
    }
    
    // 如果都没匹配到，返回默认值
    return typeMap['default'];
}

// 统一的退出登录函数
async function logout() {
    try {
        const token = getAuthHeader();
        if (token) {
            try {
                console.log("调用后端退出登录API");
                const response = await fetch(`${API_BASE_URL}/user/logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': token,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const responseData = await response.json();
                    console.log("后端退出登录成功:", responseData.message || "Success");
                } else {
                    console.warn(`后端退出登录失败，状态码: ${response.status}`);
                }
            } catch (error) {
                console.error("调用后端退出登录API时出错:", error);
            }
        }

        // 清除本地存储
        localStorage.removeItem('token');
        localStorage.removeItem('currentUser');

        // 清除Cookie
        document.cookie = 'JWT_TOKEN=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
        document.cookie = 'Authorization=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';

        // 显示提示并跳转
        if (typeof showToast === 'function') {
            showToast('您已成功退出登录', 'success');
        }

        setTimeout(() => {
            window.location.href = '/auth/login';
        }, 1500);

    } catch (error) {
        console.error("退出登录过程中出错:", error);
        // 即使出错也要清除本地数据并跳转
        localStorage.removeItem('token');
        localStorage.removeItem('currentUser');
        window.location.href = '/auth/login';
    }
}

// 调试辅助函数
window.debugAuth = async function() {
    const token = localStorage.getItem('token');
    console.log("当前token:", token);
    console.log("标准化后token:", normalizeToken(token));

    try {
        const response = await fetch(`${API_BASE_URL}/user/validate`, {
            headers: {
                'Authorization': normalizeToken(token)
            }
        });

        console.log("验证状态:", response.status);
        const data = await response.json();
        console.log("验证响应:", data);

        return {
            token,
            normalizedToken: normalizeToken(token),
            isValid: data.code === 200,
            response: data
        };
    } catch (error) {
        console.error("调试验证失败:", error);
        return {
            token,
            normalizedToken: normalizeToken(token),
            isValid: false,
            error: error.message
        };
    }
};

// 修复头像加载失败的处理逻辑
function handleAvatarError(element, userInfo) {
    // 记录错误
    console.error('Failed to load avatar from:', element.src);

    try {
        // 如果传入了用户信息，使用用户信息生成随机头像
        if (userInfo && (userInfo.username || userInfo.id)) {
            generateRandomAvatar(element, userInfo);
            return;
        }
        
        // 尝试从元素属性获取用户信息
        const username = element.getAttribute('data-username');
        const userId = element.getAttribute('data-user-id');
        
        if (username || userId) {
            generateRandomAvatar(element, {
                username: username || 'User',
                id: userId || username || 'unknown'
            });
            return;
        }
        
        // 尝试使用完整URL路径作为备用方案
        const baseUrl = window.location.origin;

        // 只有当当前src不是默认头像时才替换为默认头像
        if (!element.src.includes('default-avatar.png') && !element.src.includes('data:image/svg+xml')) {
            console.log('Trying to load default avatar...');

            // 使用完整路径
            const defaultAvatarUrl = baseUrl + '/static/images/default-avatar.png';
            element.src = defaultAvatarUrl;

            // 如果再次失败，使用内联SVG
            element.onerror = function() {
                console.warn('默认头像也加载失败，使用随机SVG头像');
                generateRandomAvatar(this, {username: 'User', id: 'default'});
            };
        } else {
            // 直接使用随机SVG头像作为最后的备用方案
            generateRandomAvatar(element, {username: 'User', id: 'default'});
        }
    } catch (e) {
        console.error('Avatar error handler failed:', e);
        // 确保在任何情况下都有一个可用的头像
        generateRandomAvatar(element, {username: 'User', id: 'fallback'});
    }
}

// 生成随机头像（与index.html中的函数保持一致）
function generateRandomAvatar(element, userData) {
    if (!element || !userData) return;
    
    try {
        // 使用用户名生成稳定的颜色
        const username = userData.username || 'User';
        const userId = userData.id || username;
        
        // 预定义的颜色组合
        const colors = [
            { bg: '#e3f2fd', fg: '#2196f3' }, // 蓝色
            { bg: '#e8f5e9', fg: '#4caf50' }, // 绿色
            { bg: '#fff3e0', fg: '#ff9800' }, // 橙色
            { bg: '#f3e5f5', fg: '#9c27b0' }, // 紫色
            { bg: '#e8eaf6', fg: '#3f51b5' }, // 靛蓝色
            { bg: '#fce4ec', fg: '#e91e63' }, // 粉色
            { bg: '#e0f2f1', fg: '#009688' }, // 青色
            { bg: '#fff8e1', fg: '#ffc107' }  // 黄色
        ];

        // 基于用户ID生成稳定的颜色索引
        let hash = 0;
        for (let i = 0; i < userId.toString().length; i++) {
            const char = userId.toString().charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        const colorIndex = Math.abs(hash) % colors.length;
        const color = colors[colorIndex];

        // 生成用户名首字母（最多2个字符）
        let initials = '';
        if (username && username.length > 0) {
            if (username.length === 1) {
                initials = username.toUpperCase();
            } else if (username.length >= 2) {
                // 如果是中文用户名，取前两个字符
                // 如果是英文用户名，取首字母和第二个词的首字母（如果有）
                if (/[\u4e00-\u9fa5]/.test(username)) {
                    initials = username.substring(0, 2);
                } else {
                    const words = username.split(/[\s_-]+/);
                    if (words.length >= 2) {
                        initials = words[0].charAt(0) + words[1].charAt(0);
                    } else {
                        initials = username.substring(0, 2);
                    }
                    initials = initials.toUpperCase();
                }
            }
        } else {
            initials = 'U';
        }

        // 创建SVG头像
        const svgContent = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="64" height="64">
            <rect width="64" height="64" fill="${color.bg}" rx="32" ry="32" />
            <text x="32" y="40" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="${color.fg}" text-anchor="middle">${initials}</text>
        </svg>`;
        
        const svgData = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgContent)));
        element.src = svgData;
        element.onerror = null; // 防止无限循环
        
        console.log(`为用户 ${username} 生成随机头像，使用颜色索引 ${colorIndex}`);
        
    } catch (e) {
        console.error('生成随机头像失败:', e);
        // 最后的备用方案
        useFallbackSvgAvatar(element);
    }
}

// 为所有头像添加错误处理
document.addEventListener('DOMContentLoaded', function() {
    const avatars = document.querySelectorAll('.avatar');
    avatars.forEach(avatar => {
        avatar.onerror = function() {
            handleAvatarError(this, {username: 'User', id: 'default'});
        };
    });
});

// 全局图片错误处理
window.addEventListener('error', function(e) {
    if (e.target.tagName === 'IMG') {
        const imgSrc = e.target.src || '';

        // 过滤无效的URL，如当前页面URL或空值
        if (!imgSrc || imgSrc === 'about:blank' || imgSrc.includes('#') ||
            imgSrc === window.location.href || imgSrc === window.location.origin + window.location.pathname) {
            // 对于无效的URL，直接删除src属性或设置为空
            e.target.removeAttribute('src');
            return;
        }

        console.warn('Image failed to load:', imgSrc);

        // 防止重复请求不存在的资源
        if (e.target.dataset.errorHandled !== 'true') {
            e.target.dataset.errorHandled = 'true';

            // 对于头像特殊处理
            if (e.target.classList.contains('avatar') && !imgSrc.includes('default-avatar.png')) {
                e.target.src = '/static/images/default-avatar.png';
            }
        }
    }
}, true);

// 立即执行清理过期cookies（不等待DOM加载）
(function() {

    
    try {
        // 强制清理所有token相关的cookies
        const cookiesToClear = ['Authorization', 'JWT_TOKEN', 'token', 'userToken', 'adminToken'];
        cookiesToClear.forEach(cookieName => {
            document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
            document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname};`;
            if (window.location.hostname.includes('.')) {
                document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname};`;
            }
        });
    } catch (error) {
        console.warn('清理cookies时出错:', error);
    }
})();

// 页面加载时立即执行
$(document).ready(function() {
    try {
        // 插入导航栏
        insertNavbar();
        
        // 设置当前页面高亮
        setActiveNavItem();
        
        // 初始化导航栏功能（如果navbar.js已加载）
        if (typeof initNavbar === 'function') {
            initNavbar();
        } else {
            setupUserDropdown();
        }
        
        // 清理过期的token和cookies
        cleanExpiredTokens();
    } catch (error) {
    }
});

/**
 * 清理过期的token和cookies
 */
function cleanExpiredTokens() {
    try {
        // 检查localStorage中的token
        const token = localStorage.getItem('token');
        if (token && isTokenExpired(token)) {
            console.log('检测到过期的token，正在清理...');
            localStorage.removeItem('token');
        }
        
        // 检查其他可能的token存储
        const userToken = localStorage.getItem('userToken');
        if (userToken && isTokenExpired(userToken)) {
            console.log('检测到过期的userToken，正在清理...');
            localStorage.removeItem('userToken');
        }
        
        const adminToken = localStorage.getItem('adminToken');
        if (adminToken && isTokenExpired(adminToken)) {
            console.log('检测到过期的adminToken，正在清理...');
            localStorage.removeItem('adminToken');
            sessionStorage.removeItem('adminToken');
        }
        
        // 强制清理所有可能的过期cookies
        forceCleanAllTokenCookies();
        
    } catch (error) {
        console.warn('清理过期token时出错:', error);
    }
}

/**
 * 强制清理所有token相关的cookies
 */
function forceCleanAllTokenCookies() {
    try {
        const cookiesToClear = ['Authorization', 'JWT_TOKEN', 'token', 'userToken', 'adminToken'];
        
        cookiesToClear.forEach(cookieName => {
            // 清理当前路径的cookie
            document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
            // 清理根路径的cookie  
            document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname};`;
            // 清理可能的子域名cookie
            document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname};`;
        });

    } catch (error) {
        console.warn('清理token cookies时出错:', error);
    }
}

/**
 * 检查token是否过期
 */
function isTokenExpired(token) {
    try {
        if (!token) return true;
        
        // 移除Bearer前缀
        if (token.startsWith('Bearer ')) {
            token = token.substring(7);
        }
        
        // 解析JWT token
        const payload = JSON.parse(atob(token.split('.')[1]));
        const currentTime = Math.floor(Date.now() / 1000);
        
        // 检查是否过期（添加5分钟缓冲）
        return payload.exp < (currentTime + 300);
        
    } catch (error) {
        console.warn('解析token时出错:', error);
        return true; // 解析失败时认为token过期
    }
}

/**
 * 统一获取有效的token
 * 按优先级获取：localStorage.token > localStorage.userToken > sessionStorage.adminToken > localStorage.adminToken
 */
function getValidToken() {
    // 优先获取用户token
    let token = localStorage.getItem('token');
    if (token && !isTokenExpired(token)) {
        return token;
    }
    
    // 检查userToken
    token = localStorage.getItem('userToken');
    if (token && !isTokenExpired(token)) {
        return token;
    }
    
    // 检查管理员token（优先sessionStorage）
    token = sessionStorage.getItem('adminToken');
    if (token && !isTokenExpired(token)) {
        return token;
    }
    
    token = localStorage.getItem('adminToken');
    if (token && !isTokenExpired(token)) {
        return token;
    }
    
    return null;
}

/**
 * 创建带有Authorization header的AJAX请求配置
 */
function createAuthenticatedRequest(url, options = {}) {
    const token = getValidToken();
    
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    };
    
    // 添加Authorization header
    if (token) {
        defaultOptions.headers['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
    }
    
    // 合并选项
    const mergedOptions = { ...defaultOptions, ...options };
    
    // 合并headers
    if (options.headers) {
        mergedOptions.headers = { ...defaultOptions.headers, ...options.headers };
    }
    
    return { url, options: mergedOptions };
}

/**
 * 创建带有token的下载URL
 */
function createDownloadUrl(baseUrl, additionalParams = {}) {
    const token = getValidToken();
    const url = new URL(baseUrl, window.location.origin);
    
    // 添加额外参数
    Object.keys(additionalParams).forEach(key => {
        url.searchParams.set(key, additionalParams[key]);
    });
    
    // 添加token参数
    if (token && !url.searchParams.has('token')) {
        url.searchParams.set('token', token);
    }
    
    return url.toString();
}

/**
 * 为下载功能准备干净的token环境
 * 清理所有过期token和cookies，确保只使用有效token
 */
function prepareDownloadEnvironment() {
    console.log('🔧 准备下载环境，清理过期token...');
    
    // 强制清理所有过期的token和cookies
    forceCleanAllTokenCookies();
    cleanExpiredTokens();
    
    // 获取当前有效的token
    const validToken = getValidToken();
    
    if (!validToken) {
        console.warn('⚠️ 没有找到有效的token，可能需要重新登录');
        return null;
    }
    
    console.log('✅ 下载环境准备完成，token有效');
    return validToken;
}