-- ========================
-- MineRU 文档解析服务数据库表结构
-- ========================

-- 创建文档解析任务表
CREATE TABLE IF NOT EXISTS `document_parse_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(128) NOT NULL COMMENT '任务ID (MineRU返回的task_id)',
  `batch_id` varchar(128) DEFAULT NULL COMMENT '批次ID (用于批量处理)',
  `data_id` varchar(128) DEFAULT NULL COMMENT '数据ID (业务标识)',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `file_url` text NOT NULL COMMENT '文件URL',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小 (字节)',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型',
  `status` varchar(50) NOT NULL DEFAULT 'pending' COMMENT '任务状态: pending(排队中), running(解析中), done(完成), failed(失败), converting(转换中)',
  `extracted_pages` int(11) DEFAULT NULL COMMENT '解析进度 - 已解析页数',
  `total_pages` int(11) DEFAULT NULL COMMENT '解析进度 - 总页数',
  `start_time` datetime DEFAULT NULL COMMENT '解析开始时间',
  `complete_time` datetime DEFAULT NULL COMMENT '解析完成时间',
  `result_zip_url` text DEFAULT NULL COMMENT '结果文件压缩包URL',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `parse_config` text DEFAULT NULL COMMENT '解析配置 (JSON格式)',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 (0:未删除, 1:已删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_data_id` (`data_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档解析任务表';

-- 创建文档解析结果表
CREATE TABLE IF NOT EXISTS `document_parse_result` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` bigint(20) NOT NULL COMMENT '关联的解析任务ID',
  `result_type` varchar(50) NOT NULL COMMENT '解析结果类型: markdown, json, docx, html, latex',
  `content` longtext DEFAULT NULL COMMENT '解析结果内容 (小文件直接存储)',
  `file_url` text DEFAULT NULL COMMENT '结果文件URL (大文件存储路径)',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小 (字节)',
  `quality_score` int(11) DEFAULT NULL COMMENT '解析质量评分 (0-100)',
  `statistics` text DEFAULT NULL COMMENT '解析统计信息 (JSON格式)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 (0:未删除, 1:已删除)',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_result_type` (`result_type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_parse_result_task` FOREIGN KEY (`task_id`) REFERENCES `document_parse_task` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档解析结果表';

-- 创建索引优化查询性能
CREATE INDEX `idx_task_status_user` ON `document_parse_task` (`user_id`, `status`, `created_at`);
CREATE INDEX `idx_task_batch_status` ON `document_parse_task` (`batch_id`, `status`);
CREATE INDEX `idx_result_task_type` ON `document_parse_result` (`task_id`, `result_type`);

-- 插入示例数据 (可选)
-- INSERT INTO `document_parse_task` (`task_id`, `data_id`, `file_name`, `file_url`, `status`, `user_id`) 
-- VALUES ('test-task-001', 'sample-doc-001', 'sample.pdf', 'https://example.com/sample.pdf', 'pending', 1);
