-- MineRU API使用情况跟踪表
CREATE TABLE IF NOT EXISTS `mineru_api_usage` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `token_name` VARCHAR(50) NOT NULL COMMENT 'API密钥名称',
    `usage_date` DATE NOT NULL COMMENT '使用日期',
    `parsed_pages` INT DEFAULT 0 COMMENT '当日已解析页数',
    `request_count` INT DEFAULT 0 COMMENT '当日请求次数',
    `success_count` INT DEFAULT 0 COMMENT '当日成功次数',
    `failure_count` INT DEFAULT 0 COMMENT '当日失败次数',
    `exceeded_priority_limit` BOOLEAN DEFAULT FALSE COMMENT '是否超出高优先级限制',
    `last_used_at` DATETIME COMMENT '最后使用时间',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `remarks` TEXT COMMENT '备注信息',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_token_date` (`token_name`, `usage_date`),
    INDEX `idx_usage_date` (`usage_date`),
    INDEX `idx_token_name` (`token_name`),
    INDEX `idx_last_used` (`last_used_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='MineRU API使用情况跟踪表';

-- 插入初始数据（可选）
INSERT IGNORE INTO `mineru_api_usage` (`token_name`, `usage_date`, `parsed_pages`, `request_count`, `success_count`, `failure_count`) 
VALUES 
('primary', CURDATE(), 0, 0, 0, 0),
('backup1', CURDATE(), 0, 0, 0, 0),
('backup2', CURDATE(), 0, 0, 0, 0);
