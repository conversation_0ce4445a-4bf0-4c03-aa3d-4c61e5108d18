/*
 Navicat Premium Data Transfer

 Source Server         : *************_8082
 Source Server Type    : MySQL
 Source Server Version : 80042
 Source Host           : *************:8082
 Source Schema         : maizipapergendb

 Target Server Type    : MySQL
 Target Server Version : 80042
 File Encoding         : 65001

 Date: 18/07/2025 19:18:07
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for algorithm_event_log
-- ----------------------------
DROP TABLE IF EXISTS `algorithm_event_log`;
CREATE TABLE `algorithm_event_log`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `execution_id` bigint(0) NOT NULL COMMENT '执行记录ID',
  `event_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '事件类型',
  `event_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '事件消息',
  `event_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '事件数据(JSON格式)',
  `generation` int(0) NULL DEFAULT NULL COMMENT '发生代数',
  `event_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_execution_id`(`execution_id`) USING BTREE,
  INDEX `idx_event_type`(`event_type`) USING BTREE,
  INDEX `idx_event_time`(`event_time`) USING BTREE,
  CONSTRAINT `algorithm_event_log_ibfk_1` FOREIGN KEY (`execution_id`) REFERENCES `algorithm_execution` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '算法事件日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for algorithm_execution
-- ----------------------------
DROP TABLE IF EXISTS `algorithm_execution`;
CREATE TABLE `algorithm_execution`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `paper_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '试卷ID',
  `paper_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '试卷名称',
  `algorithm_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'genetic' COMMENT '算法类型',
  `status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '运行状态: running, paused, completed, failed, stopped',
  `start_time` datetime(0) NOT NULL COMMENT '开始时间',
  `end_time` datetime(0) NULL DEFAULT NULL COMMENT '结束时间',
  `current_generation` int(0) NULL DEFAULT 0 COMMENT '当前代数',
  `max_generations` int(0) NOT NULL COMMENT '最大代数',
  `target_fitness` double NULL DEFAULT 0.95 COMMENT '目标适应度',
  `population_size` int(0) NULL DEFAULT 100 COMMENT '种群大小',
  `mutation_rate` double NULL DEFAULT 0.1 COMMENT '变异率',
  `crossover_rate` double NULL DEFAULT 0.8 COMMENT '交叉率',
  `selection_method` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'tournament' COMMENT '选择方法',
  `best_fitness` double NULL DEFAULT 0 COMMENT '最佳适应度',
  `avg_fitness` double NULL DEFAULT 0 COMMENT '平均适应度',
  `convergence_speed` double NULL DEFAULT 0 COMMENT '收敛速度',
  `diversity_index` double NULL DEFAULT 1 COMMENT '多样性指数',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '错误信息',
  `created_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  `updated_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_paper_id`(`paper_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_start_time`(`start_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '算法执行记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for algorithm_generation_data
-- ----------------------------
DROP TABLE IF EXISTS `algorithm_generation_data`;
CREATE TABLE `algorithm_generation_data`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `execution_id` bigint(0) NOT NULL COMMENT '执行记录ID',
  `generation` int(0) NOT NULL COMMENT '代数',
  `best_fitness` double NOT NULL COMMENT '最佳适应度',
  `avg_fitness` double NOT NULL COMMENT '平均适应度',
  `worst_fitness` double NOT NULL COMMENT '最差适应度',
  `diversity_index` double NOT NULL COMMENT '多样性指数',
  `constraint_violations` int(0) NULL DEFAULT 0 COMMENT '约束违反次数',
  `mutation_count` int(0) NULL DEFAULT 0 COMMENT '变异次数',
  `crossover_count` int(0) NULL DEFAULT 0 COMMENT '交叉次数',
  `selection_pressure` double NULL DEFAULT 1 COMMENT '选择压力',
  `created_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  `convergence_speed` double NOT NULL,
  `fitness_std_dev` double NOT NULL,
  `recorded_at` datetime(6) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_execution_id`(`execution_id`) USING BTREE,
  INDEX `idx_generation`(`generation`) USING BTREE,
  CONSTRAINT `algorithm_generation_data_ibfk_1` FOREIGN KEY (`execution_id`) REFERENCES `algorithm_execution` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '算法代数进化数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for algorithm_parameters
-- ----------------------------
DROP TABLE IF EXISTS `algorithm_parameters`;
CREATE TABLE `algorithm_parameters`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) NULL DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `parameter_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `parameter_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `parameter_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `execution_id` bigint(0) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FKoots8hi78wvajflvxlg3mda4k`(`execution_id`) USING BTREE,
  CONSTRAINT `FKoots8hi78wvajflvxlg3mda4k` FOREIGN KEY (`execution_id`) REFERENCES `algorithm_execution` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for algorithm_performance_metrics
-- ----------------------------
DROP TABLE IF EXISTS `algorithm_performance_metrics`;
CREATE TABLE `algorithm_performance_metrics`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `cpu_usage` double NOT NULL,
  `crossover_time` bigint(0) NOT NULL,
  `evaluation_time` bigint(0) NOT NULL,
  `generation` int(0) NOT NULL,
  `generation_time` bigint(0) NOT NULL,
  `memory_usage` double NOT NULL,
  `mutation_time` bigint(0) NOT NULL,
  `recorded_at` datetime(6) NULL DEFAULT NULL,
  `selection_time` bigint(0) NOT NULL,
  `execution_id` bigint(0) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FK2uogoqlrvd0emadjq0d3y1gf4`(`execution_id`) USING BTREE,
  CONSTRAINT `FK2uogoqlrvd0emadjq0d3y1gf4` FOREIGN KEY (`execution_id`) REFERENCES `algorithm_execution` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for books
-- ----------------------------
DROP TABLE IF EXISTS `books`;
CREATE TABLE `books`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `know_id` int(0) NOT NULL COMMENT '知识点id（请在麦子教育后台登录后查看【题库管理】->【知识点列表】->【知识点分类】获取知识点 ID）',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '书籍类型',
  `url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '书籍地址',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '书籍描述，属于那一章节的书',
  `user_id` bigint(0) UNSIGNED NULL DEFAULT NULL COMMENT '上传用户ID,需要从当前session中获取                                                                                   ',
  `created_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  `updated_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  CONSTRAINT `books_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for chat_messages
-- ----------------------------
DROP TABLE IF EXISTS `chat_messages`;
CREATE TABLE `chat_messages`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `session_id` bigint(0) NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `is_user` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_chat_messages_session_id`(`session_id`) USING BTREE,
  CONSTRAINT `chat_messages_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `chat_sessions` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 62 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for chat_sessions
-- ----------------------------
DROP TABLE IF EXISTS `chat_sessions`;
CREATE TABLE `chat_sessions`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(0) UNSIGNED NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `know_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `book_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  `deleted` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_chat_sessions_user_id`(`user_id`) USING BTREE,
  INDEX `idx_chat_sessions_updated_at`(`updated_at`) USING BTREE,
  CONSTRAINT `chat_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 51 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for constraint_statistics
-- ----------------------------
DROP TABLE IF EXISTS `constraint_statistics`;
CREATE TABLE `constraint_statistics`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `constraint_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `generation` int(0) NOT NULL,
  `penalty_value` double NULL DEFAULT NULL,
  `recorded_at` datetime(6) NULL DEFAULT NULL,
  `repair_failure_count` int(0) NULL DEFAULT NULL,
  `repair_success_count` int(0) NULL DEFAULT NULL,
  `violation_count` int(0) NULL DEFAULT NULL,
  `execution_id` bigint(0) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FKel58afba2wbkv4n4e4mpmavcu`(`execution_id`) USING BTREE,
  CONSTRAINT `FKel58afba2wbkv4n4e4mpmavcu` FOREIGN KEY (`execution_id`) REFERENCES `algorithm_execution` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for correction_approval
-- ----------------------------
DROP TABLE IF EXISTS `correction_approval`;
CREATE TABLE `correction_approval`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `correction_date` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '修正日期(YYYY-MM-DD)',
  `correction_json` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '修正结果JSON',
  `statistics` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '修正统计信息JSON',
  `status` tinyint(0) NOT NULL DEFAULT 0 COMMENT '审核状态: 0-待审核, 1-已通过, 2-已拒绝, 3-已过期',
  `approver` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核人',
  `approval_comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核意见',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `approved_at` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `expires_at` datetime(0) NOT NULL COMMENT '过期时间',
  `batch_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '批次ID，用于关联题目',
  `topic_ids` json NULL COMMENT '涉及的题目ID列表',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_correction_date`(`correction_date`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_expires_at`(`expires_at`) USING BTREE,
  INDEX `idx_correction_approval_status_created`(`status`, `created_at`) USING BTREE,
  INDEX `idx_correction_approval_expires`(`expires_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1840 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'AI修正审核表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fitness_dimension_data
-- ----------------------------
DROP TABLE IF EXISTS `fitness_dimension_data`;
CREATE TABLE `fitness_dimension_data`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `execution_id` bigint(0) NOT NULL COMMENT '执行记录ID',
  `dimension_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '维度名称',
  `dimension_weight` double NULL DEFAULT 1 COMMENT '维度权重',
  `fitness_value` double NOT NULL COMMENT '适应度值',
  `target_value` double NULL DEFAULT 1 COMMENT '目标值',
  `created_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  `dimension_value` double NOT NULL,
  `generation` int(0) NOT NULL,
  `recorded_at` datetime(6) NULL DEFAULT NULL,
  `weight` double NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_execution_id`(`execution_id`) USING BTREE,
  INDEX `idx_dimension_name`(`dimension_name`) USING BTREE,
  CONSTRAINT `fitness_dimension_data_ibfk_1` FOREIGN KEY (`execution_id`) REFERENCES `algorithm_execution` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 64 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '适应度维度数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for paper_configs
-- ----------------------------
DROP TABLE IF EXISTS `paper_configs`;
CREATE TABLE `paper_configs`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置名称',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '配置描述',
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `title_template` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '试卷标题模板',
  `paper_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'standard' COMMENT '试卷类型',
  `paper_count` int(0) NULL DEFAULT 1 COMMENT '生成套数',
  `single_choice_count` int(0) NULL DEFAULT 0 COMMENT '单选题数量',
  `single_choice_score` int(0) NULL DEFAULT 0 COMMENT '单选题分值',
  `multiple_choice_count` int(0) NULL DEFAULT 0 COMMENT '多选题数量',
  `multiple_choice_score` int(0) NULL DEFAULT 0 COMMENT '多选题分值',
  `judgment_count` int(0) NULL DEFAULT 0 COMMENT '判断题数量',
  `judgment_score` int(0) NULL DEFAULT 0 COMMENT '判断题分值',
  `fill_count` int(0) NULL DEFAULT 0 COMMENT '填空题数量',
  `fill_score` int(0) NULL DEFAULT 0 COMMENT '填空题分值',
  `short_answer_count` int(0) NULL DEFAULT 0 COMMENT '简答题数量',
  `short_answer_score` int(0) NULL DEFAULT 0 COMMENT '简答题分值',
  `difficulty_distribution` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '难度分布配置（JSON）',
  `knowledge_point_configs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '知识点配置（JSON）',
  `is_default` tinyint(1) NULL DEFAULT 0 COMMENT '是否为默认配置',
  `is_public` tinyint(1) NULL DEFAULT 0 COMMENT '是否为公共配置',
  `usage_count` int(0) NULL DEFAULT 0 COMMENT '使用次数',
  `created_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `last_used_at` timestamp(0) NULL DEFAULT NULL COMMENT '最后使用时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_config_name`(`user_id`, `config_name`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_config_name`(`config_name`) USING BTREE,
  INDEX `idx_is_public`(`is_public`) USING BTREE,
  INDEX `idx_is_default`(`is_default`) USING BTREE,
  INDEX `idx_usage_count`(`usage_count`) USING BTREE,
  INDEX `idx_last_used_at`(`last_used_at`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_user_config_name`(`user_id`, `config_name`) USING BTREE,
  INDEX `idx_user_default`(`user_id`, `is_default`) USING BTREE,
  INDEX `idx_user_usage`(`user_id`, `usage_count`) USING BTREE,
  INDEX `idx_user_recent`(`user_id`, `last_used_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '试卷配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for paper_downloads
-- ----------------------------
DROP TABLE IF EXISTS `paper_downloads`;
CREATE TABLE `paper_downloads`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(0) UNSIGNED NOT NULL COMMENT '用户ID',
  `paper_id` bigint(0) NOT NULL COMMENT '试卷ID',
  `download_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '下载时间',
  `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '下载IP地址',
  `file_format` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '下载格式：pdf, docx',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_paper_id`(`paper_id`) USING BTREE,
  INDEX `idx_download_time`(`download_time`) USING BTREE,
  CONSTRAINT `fk_downloads_paper` FOREIGN KEY (`paper_id`) REFERENCES `papers` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_downloads_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 506 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户试卷下载记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for papers
-- ----------------------------
DROP TABLE IF EXISTS `papers`;
CREATE TABLE `papers`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '试卷标题',
  `user_id` bigint(0) UNSIGNED NULL DEFAULT NULL COMMENT '创建用户ID',
  `knowledge_id` int(0) NULL DEFAULT NULL COMMENT '知识点ID',
  `knowledge_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '知识点名称',
  `total_score` int(0) NOT NULL COMMENT '总分（目标分数）',
  `actual_total_score` int(0) NULL DEFAULT NULL COMMENT '实际总分',
  `difficulty` double NULL DEFAULT NULL COMMENT '试卷难度',
  `difficulty_distribution` json NULL COMMENT '难度分布JSON，格式：[{\"1\": 0.3, \"2\": 0.5, \"3\": 0.2}] 表示简单30%，中等50%，困难20%',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '试卷内容JSON',
  `config` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '生成配置JSON',
  `paper_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'regular' COMMENT '试卷类型：regular-普通试卷, teacher-教师试卷, standard-标准试卷',
  `file_format` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'pdf' COMMENT '文件格式：pdf, docx',
  `download_count` int(0) NOT NULL DEFAULT 0 COMMENT '下载次数',
  `last_download_time` datetime(0) NULL DEFAULT NULL COMMENT '最后下载时间',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0=未删除，1=已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_knowledge_id`(`knowledge_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  CONSTRAINT `fk_papers_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 302 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '试卷表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for population_individual_data
-- ----------------------------
DROP TABLE IF EXISTS `population_individual_data`;
CREATE TABLE `population_individual_data`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `constraint_violations` int(0) NULL DEFAULT NULL,
  `fitness_value` double NOT NULL,
  `gene_sequence` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `generation` int(0) NOT NULL,
  `individual_id` int(0) NOT NULL,
  `is_elite` bit(1) NULL DEFAULT NULL,
  `recorded_at` datetime(6) NULL DEFAULT NULL,
  `execution_id` bigint(0) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FK7ll4780due828v2ef5phmb35n`(`execution_id`) USING BTREE,
  CONSTRAINT `FK7ll4780due828v2ef5phmb35n` FOREIGN KEY (`execution_id`) REFERENCES `algorithm_execution` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for role_permissions
-- ----------------------------
DROP TABLE IF EXISTS `role_permissions`;
CREATE TABLE `role_permissions`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '角色权限ID',
  `role_id` tinyint(0) NOT NULL COMMENT '角色ID：1-普通用户，2-管理员，3-超级管理员',
  `permission_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限代码',
  `permission_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限名称',
  `resource_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '资源类型：MENU-菜单，API-接口，BUTTON-按钮',
  `resource_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '资源路径',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '权限描述',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活：0-禁用，1-启用',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_role_permission`(`role_id`, `permission_code`) USING BTREE,
  INDEX `idx_role_id`(`role_id`) USING BTREE,
  INDEX `idx_permission_code`(`permission_code`) USING BTREE,
  INDEX `idx_resource_type`(`resource_type`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '角色权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_config
-- ----------------------------
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '配置值',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '配置描述',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `config_key`(`config_key`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 65 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_messages
-- ----------------------------
DROP TABLE IF EXISTS `system_messages`;
CREATE TABLE `system_messages`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `user_id` bigint(0) UNSIGNED NOT NULL COMMENT '接收用户ID',
  `sender_id` bigint(0) UNSIGNED NULL DEFAULT NULL COMMENT '发送者ID（系统消息为NULL）',
  `message_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息类型：AUDIT_APPROVED-审核通过，AUDIT_REJECTED-审核拒绝，SYSTEM_NOTICE-系统通知',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息内容',
  `related_id` bigint(0) NULL DEFAULT NULL COMMENT '关联ID（如审核记录ID）',
  `related_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联类型：TOPIC_AUDIT-题目审核',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读：0-未读，1-已读',
  `read_time` datetime(0) NULL DEFAULT NULL COMMENT '阅读时间',
  `priority` tinyint(0) NOT NULL DEFAULT 1 COMMENT '优先级：1-普通，2-重要，3-紧急',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_sender_id`(`sender_id`) USING BTREE,
  INDEX `idx_message_type`(`message_type`) USING BTREE,
  INDEX `idx_is_read`(`is_read`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_priority`(`priority`) USING BTREE,
  CONSTRAINT `fk_system_messages_sender` FOREIGN KEY (`sender_id`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_system_messages_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 87 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统消息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for topic_audit
-- ----------------------------
DROP TABLE IF EXISTS `topic_audit`;
CREATE TABLE `topic_audit`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '审核记录ID',
  `user_id` bigint(0) UNSIGNED NOT NULL COMMENT '提交用户ID',
  `know_id` int(0) UNSIGNED NOT NULL COMMENT '知识点ID',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目类型',
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目标题',
  `options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '选项数据(json)',
  `subs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '组合题数据(json)',
  `answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案',
  `parse` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '答案解析',
  `score` int(0) UNSIGNED NOT NULL DEFAULT 3 COMMENT '分值',
  `source` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '来源',
  `difficulty` double NULL DEFAULT NULL COMMENT '题目难度',
  `tags` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '细分知识点标签',
  `audit_status` tinyint(0) NOT NULL DEFAULT 0 COMMENT '审核状态：0-待审核，1-审核通过，2-审核拒绝',
  `auditor_id` bigint(0) UNSIGNED NULL DEFAULT NULL COMMENT '审核员ID',
  `audit_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `audit_comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '审核意见',
  `auto_approved` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否自动审核通过：0-否，1-是',
  `submit_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '提交时间',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `title_hash` varchar(64) CHARACTER SET ascii COLLATE ascii_general_ci GENERATED ALWAYS AS (sha2(`title`,256)) STORED COMMENT '用于题目去重的字段' NULL,
  `answer_hash` varchar(64) CHARACTER SET ascii COLLATE ascii_general_ci GENERATED ALWAYS AS (sha2(`answer`,256)) STORED COMMENT '用于题目去重的字段' NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_audit_status`(`audit_status`) USING BTREE,
  INDEX `idx_submit_time`(`submit_time`) USING BTREE,
  INDEX `idx_auditor_id`(`auditor_id`) USING BTREE,
  INDEX `idx_know_id`(`know_id`) USING BTREE,
  INDEX `idx_status_created`(`audit_status`, `submit_time`) USING BTREE,
  INDEX `idx_user_status_time`(`user_id`, `audit_status`, `submit_time`) USING BTREE,
  INDEX `idx_topic_type`(`type`) USING BTREE,
  INDEX `idx_audit_complex`(`audit_status`, `submit_time`, `user_id`) USING BTREE,
  INDEX `idx_duplicates`(`user_id`, `know_id`, `type`, `title_hash`, `answer_hash`, `submit_time`) USING BTREE,
  CONSTRAINT `fk_topic_audit_auditor` FOREIGN KEY (`auditor_id`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_topic_audit_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 89507 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '题目审核表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for topic_bak
-- ----------------------------
DROP TABLE IF EXISTS `topic_bak`;
CREATE TABLE `topic_bak`  (
  `id` int(0) UNSIGNED NOT NULL AUTO_INCREMENT,
  `know_id` int(0) UNSIGNED NOT NULL DEFAULT 0 COMMENT '知识点id',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '题目类型',
  `tags` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '细分知识点标签，用逗号分隔',
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目标题',
  `options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目数据(json)',
  `subs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '组合题数据(json，仅组合题使用)',
  `answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案',
  `parse` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案解析',
  `score` int(0) UNSIGNED NOT NULL DEFAULT 3 COMMENT '分值',
  `source` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '来源',
  `sort` tinyint(0) UNSIGNED NOT NULL DEFAULT 1 COMMENT '排序值(0-255)',
  `difficulty` double NOT NULL COMMENT '题目难度',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '题目创建时间',
  `corrected` tinyint(0) NULL DEFAULT 0 COMMENT '题目是否被校正',
  `update_at` timestamp(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_know_id`(`know_id`) USING BTREE,
  INDEX `idx_know_type_difficulty`(`know_id`, `type`, `difficulty`) USING BTREE COMMENT ' 核心筛选条件索引 ',
  INDEX `idx_know_type_score_id`(`know_id`, `type`, `score`, `id`) USING BTREE COMMENT ' 覆盖索引，用于快速获取候选 ID 和分数 ',
  INDEX `idx_topic_created_id`(`created_at`, `id`) USING BTREE,
  INDEX `idx_corrected_created_id`(`corrected`, `created_at`, `id`) USING BTREE,
  INDEX `idx_corrected_id`(`corrected`, `id`) USING BTREE,
  INDEX `idx_corrected_only`(`corrected`) USING BTREE,
  FULLTEXT INDEX `ft_idx_title`(`title`) COMMENT ' 题目文本全文索引，用于语义去重辅助 '
) ENGINE = InnoDB AUTO_INCREMENT = 381527 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '题目表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for topic_correction_suggestion
-- ----------------------------
DROP TABLE IF EXISTS `topic_correction_suggestion`;
CREATE TABLE `topic_correction_suggestion`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `topic_id` bigint(0) NOT NULL COMMENT '题目ID',
  `correction_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '修正原因',
  `suggested_updates` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '建议的更新内容(JSON格式)',
  `ai_response` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'AI返回的完整响应',
  `batch_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '批次ID',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'PENDING' COMMENT '状态: PENDING-待处理, APPLIED-已应用, REJECTED-已拒绝',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_topic_id`(`topic_id`) USING BTREE,
  INDEX `idx_batch_id`(`batch_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2831 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '题目修正建议表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for topic_enhancement_data
-- ----------------------------
DROP TABLE IF EXISTS `topic_enhancement_data`;
CREATE TABLE `topic_enhancement_data`  (
  `id` int(0) NOT NULL AUTO_INCREMENT,
  `topic_id` int(0) NOT NULL COMMENT '题目ID',
  `cognitive_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '认知层次',
  `usage_count` int(0) NULL DEFAULT 0 COMMENT '使用次数',
  `last_used_time` datetime(0) NULL DEFAULT NULL COMMENT '最后使用时间',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_topic_id`(`topic_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1308770308 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '题目增强数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for topic_rejected
-- ----------------------------
DROP TABLE IF EXISTS `topic_rejected`;
CREATE TABLE `topic_rejected`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '拒绝记录ID',
  `audit_id` bigint(0) NOT NULL COMMENT '审核记录ID',
  `user_id` bigint(0) UNSIGNED NOT NULL COMMENT '提交用户ID',
  `know_id` int(0) UNSIGNED NOT NULL COMMENT '知识点ID',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目类型',
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目标题',
  `options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '选项数据(json)',
  `subs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '组合题数据(json)',
  `answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案',
  `parse` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '答案解析',
  `score` int(0) UNSIGNED NOT NULL DEFAULT 3 COMMENT '分值',
  `source` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '来源',
  `difficulty` double NULL DEFAULT NULL COMMENT '题目难度',
  `tags` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '细分知识点标签',
  `auditor_id` bigint(0) UNSIGNED NOT NULL COMMENT '审核员ID',
  `reject_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '拒绝原因',
  `reject_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '拒绝时间',
  `submit_time` datetime(0) NOT NULL COMMENT '原始提交时间',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_audit_id`(`audit_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_auditor_id`(`auditor_id`) USING BTREE,
  INDEX `idx_reject_time`(`reject_time`) USING BTREE,
  CONSTRAINT `fk_topic_rejected_audit` FOREIGN KEY (`audit_id`) REFERENCES `topic_audit` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_topic_rejected_auditor` FOREIGN KEY (`auditor_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_topic_rejected_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '被拒绝题目表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` bigint(0) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID，主键',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码（加密存储）',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `role` tinyint(0) NOT NULL DEFAULT 2 COMMENT '角色：1-普通用户，2-管理员，3-超级管理员',
  `status` tinyint(0) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
  `last_login_time` datetime(0) NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后登录IP',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `deleted` tinyint(0) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像路径',
  `bio` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '个人简介',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_username`(`username`) USING BTREE,
  INDEX `idx_email`(`email`) USING BTREE,
  INDEX `idx_phone`(`phone`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1920280447393230872 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_activity_logs
-- ----------------------------
DROP TABLE IF EXISTS `user_activity_logs`;
CREATE TABLE `user_activity_logs`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '用户ID',
  `username` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `activity_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '活动类型',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '活动描述',
  `module` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模块名称',
  `target_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作对象ID',
  `target_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作对象类型',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户代理',
  `request_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '请求路径',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '请求方法',
  `result` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'SUCCESS' COMMENT '操作结果',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `duration` bigint(0) NULL DEFAULT NULL COMMENT '操作耗时(毫秒)',
  `extra_data` json NULL COMMENT '额外数据',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_username`(`username`) USING BTREE,
  INDEX `idx_activity_type`(`activity_type`) USING BTREE,
  INDEX `idx_module`(`module`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_result`(`result`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户行为日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_permissions
-- ----------------------------
DROP TABLE IF EXISTS `user_permissions`;
CREATE TABLE `user_permissions`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `user_id` bigint(0) UNSIGNED NOT NULL COMMENT '用户ID',
  `permission_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限代码',
  `permission_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限名称',
  `resource_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '资源类型：MENU-菜单，API-接口，BUTTON-按钮',
  `resource_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '资源路径',
  `granted_by` bigint(0) UNSIGNED NULL DEFAULT NULL COMMENT '授权人ID',
  `granted_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '授权时间',
  `expires_at` datetime(0) NULL DEFAULT NULL COMMENT '过期时间（NULL表示永不过期）',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活：0-禁用，1-启用',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_permission`(`user_id`, `permission_code`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_permission_code`(`permission_code`) USING BTREE,
  INDEX `idx_resource_type`(`resource_type`) USING BTREE,
  INDEX `idx_granted_by`(`granted_by`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE,
  CONSTRAINT `fk_user_permissions_granter` FOREIGN KEY (`granted_by`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_user_permissions_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wm_knowledge
-- ----------------------------
DROP TABLE IF EXISTS `wm_knowledge`;
CREATE TABLE `wm_knowledge`  (
  `id` int(0) UNSIGNED NOT NULL AUTO_INCREMENT,
  `knowledge_id` int(0) UNSIGNED NOT NULL COMMENT '知识点id',
  `knowledge_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '知识点名',
  `group_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类名（如语文、数学）',
  `is_free` tinyint(0) NOT NULL COMMENT '是否免费：0不免费，1免费',
  `sort` tinyint(0) UNSIGNED NOT NULL COMMENT '排序字段',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `is_deleted` tinyint(0) NOT NULL DEFAULT 0 COMMENT '是否删除：0=未删除，1=已删除',
  PRIMARY KEY (`id`, `knowledge_id`) USING BTREE,
  UNIQUE INDEX `uk_knowledge_id`(`knowledge_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 515 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '知识点表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wm_topic
-- ----------------------------
DROP TABLE IF EXISTS `wm_topic`;
CREATE TABLE `wm_topic`  (
  `id` int(0) UNSIGNED NOT NULL AUTO_INCREMENT,
  `know_id` int(0) UNSIGNED NOT NULL DEFAULT 0 COMMENT '知识点id',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '题目类型',
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目标题',
  `options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目数据(json)',
  `subs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '组合题数据(json，仅组合题使用)',
  `answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案',
  `parse` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案解析',
  `star` double NULL DEFAULT NULL,
  `sort` tinyint(0) UNSIGNED NOT NULL DEFAULT 1 COMMENT '排序值(0-255)',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_know_id`(`know_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '题目表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wm_topic_bak
-- ----------------------------
DROP TABLE IF EXISTS `wm_topic_bak`;
CREATE TABLE `wm_topic_bak`  (
  `id` int(0) UNSIGNED NOT NULL AUTO_INCREMENT,
  `know_id` int(0) UNSIGNED NOT NULL DEFAULT 0 COMMENT '知识点id',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '题目类型',
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目标题',
  `options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目数据(json)',
  `subs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '组合题数据(json，仅组合题使用)',
  `answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案',
  `parse` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案解析',
  `star` double NULL DEFAULT NULL,
  `sort` tinyint(0) UNSIGNED NOT NULL DEFAULT 1 COMMENT '排序值(0-255)',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_know_id`(`know_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '题目表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Triggers structure for table papers
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_papers_difficulty_insert`;
delimiter ;;
CREATE TRIGGER `tr_papers_difficulty_insert` BEFORE INSERT ON `papers` FOR EACH ROW BEGIN
    /* -----------------------------
       1. 校验 difficulty 范围 0~1
       ----------------------------- */
    IF NEW.difficulty IS NOT NULL
       AND (NEW.difficulty < 0 OR NEW.difficulty > 1) THEN
        SIGNAL SQLSTATE '45000'
            SET MESSAGE_TEXT = 'difficulty must be between 0 and 1';
    END IF;

    /* -----------------------------
       2. 自动生成 difficulty_distribution
       ----------------------------- */
    IF NEW.difficulty IS NOT NULL
       AND NEW.difficulty_distribution IS NULL THEN

        IF NEW.difficulty <= 0.4 THEN
            SET NEW.difficulty_distribution =
                JSON_ARRAY(
                    JSON_OBJECT('easy', 0.8, 'medium', 0.2, 'hard', 0)
                );
        ELSEIF NEW.difficulty <= 0.7 THEN
            SET NEW.difficulty_distribution =
                JSON_ARRAY(
                    JSON_OBJECT('easy', 0.2, 'medium', 0.7, 'hard', 0.1)
                );
        ELSE
            SET NEW.difficulty_distribution =
                JSON_ARRAY(
                    JSON_OBJECT('easy', 0, 'medium', 0.3, 'hard', 0.7)
                );
        END IF;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table papers
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_papers_difficulty_update`;
delimiter ;;
CREATE TRIGGER `tr_papers_difficulty_update` BEFORE UPDATE ON `papers` FOR EACH ROW BEGIN
    /* -----------------------------
       1. 校验 difficulty 范围 0~1
       ----------------------------- */
    IF NEW.difficulty IS NOT NULL
       AND (NEW.difficulty < 0 OR NEW.difficulty > 1) THEN
        SIGNAL SQLSTATE '45000'
            SET MESSAGE_TEXT = 'difficulty must be between 0 and 1';
    END IF;

    /* -----------------------------
       2. 难度变更或分布被清空时重新计算
       ----------------------------- */
    IF NEW.difficulty IS NOT NULL
       AND (OLD.difficulty <> NEW.difficulty
            OR NEW.difficulty_distribution IS NULL) THEN

        IF NEW.difficulty <= 0.4 THEN
            SET NEW.difficulty_distribution =
                JSON_ARRAY(
                    JSON_OBJECT('easy', 0.8, 'medium', 0.2, 'hard', 0)
                );
        ELSEIF NEW.difficulty <= 0.7 THEN
            SET NEW.difficulty_distribution =
                JSON_ARRAY(
                    JSON_OBJECT('easy', 0.2, 'medium', 0.7, 'hard', 0.1)
                );
        ELSE
            SET NEW.difficulty_distribution =
                JSON_ARRAY(
                    JSON_OBJECT('easy', 0, 'medium', 0.3, 'hard', 0.7)
                );
        END IF;
    END IF;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
