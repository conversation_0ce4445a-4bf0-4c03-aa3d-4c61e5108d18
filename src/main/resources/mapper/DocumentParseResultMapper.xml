<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.maizi_edu_sys.mapper.DocumentParseResultMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.edu.maizi_edu_sys.entity.DocumentParseResult">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="result_type" property="resultType" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="LONGVARCHAR"/>
        <result column="file_url" property="fileUrl" jdbcType="VARCHAR"/>
        <result column="file_size" property="fileSize" jdbcType="BIGINT"/>
        <result column="quality_score" property="qualityScore" jdbcType="INTEGER"/>
        <result column="statistics" property="statistics" jdbcType="VARCHAR"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, task_id, result_type, content, file_url, file_size, quality_score, statistics,
        created_at, updated_at, deleted
    </sql>

    <!-- 根据任务ID查询所有结果 -->
    <select id="selectByTaskId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM document_parse_result
        WHERE task_id = #{taskId,jdbcType=BIGINT} AND deleted = 0
        ORDER BY result_type ASC
    </select>

    <!-- 根据任务ID和结果类型查询 -->
    <select id="selectByTaskIdAndType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM document_parse_result
        WHERE task_id = #{taskId,jdbcType=BIGINT} 
        AND result_type = #{resultType,jdbcType=VARCHAR} 
        AND deleted = 0
        LIMIT 1
    </select>

    <!-- 批量根据任务ID查询结果 -->
    <select id="selectByTaskIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM document_parse_result
        WHERE task_id IN
        <foreach collection="taskIds" item="taskId" open="(" close=")" separator=",">
            #{taskId,jdbcType=BIGINT}
        </foreach>
        AND deleted = 0
        ORDER BY task_id ASC, result_type ASC
    </select>

    <!-- 插入解析结果 -->
    <insert id="insert" parameterType="com.edu.maizi_edu_sys.entity.DocumentParseResult" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO document_parse_result (
            task_id, result_type, content, file_url, file_size, quality_score, statistics,
            created_at, updated_at, deleted
        ) VALUES (
            #{taskId,jdbcType=BIGINT}, #{resultType,jdbcType=VARCHAR}, #{content,jdbcType=LONGVARCHAR},
            #{fileUrl,jdbcType=VARCHAR}, #{fileSize,jdbcType=BIGINT}, #{qualityScore,jdbcType=INTEGER},
            #{statistics,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP},
            #{deleted,jdbcType=TINYINT}
        )
    </insert>

    <!-- 批量插入解析结果 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO document_parse_result (
            task_id, result_type, content, file_url, file_size, quality_score, statistics,
            created_at, updated_at, deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.taskId,jdbcType=BIGINT}, #{item.resultType,jdbcType=VARCHAR}, 
                #{item.content,jdbcType=LONGVARCHAR}, #{item.fileUrl,jdbcType=VARCHAR}, 
                #{item.fileSize,jdbcType=BIGINT}, #{item.qualityScore,jdbcType=INTEGER},
                #{item.statistics,jdbcType=VARCHAR}, #{item.createdAt,jdbcType=TIMESTAMP}, 
                #{item.updatedAt,jdbcType=TIMESTAMP}, #{item.deleted,jdbcType=TINYINT}
            )
        </foreach>
    </insert>

    <!-- 更新解析结果 -->
    <update id="updateById" parameterType="com.edu.maizi_edu_sys.entity.DocumentParseResult">
        UPDATE document_parse_result
        SET result_type = #{resultType,jdbcType=VARCHAR},
            content = #{content,jdbcType=LONGVARCHAR},
            file_url = #{fileUrl,jdbcType=VARCHAR},
            file_size = #{fileSize,jdbcType=BIGINT},
            quality_score = #{qualityScore,jdbcType=INTEGER},
            statistics = #{statistics,jdbcType=VARCHAR},
            updated_at = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据任务ID软删除结果 -->
    <update id="deleteByTaskId" parameterType="java.lang.Long">
        UPDATE document_parse_result
        SET deleted = 1, updated_at = NOW()
        WHERE task_id = #{taskId,jdbcType=BIGINT}
    </update>

    <!-- 根据ID软删除结果 -->
    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE document_parse_result
        SET deleted = 1, updated_at = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 统计任务结果数量 -->
    <select id="countByTaskId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM document_parse_result
        WHERE task_id = #{taskId,jdbcType=BIGINT} AND deleted = 0
    </select>

    <!-- 根据结果类型统计 -->
    <select id="countByResultType" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM document_parse_result
        WHERE result_type = #{resultType,jdbcType=VARCHAR} AND deleted = 0
    </select>

    <!-- 清理过期结果 -->
    <update id="cleanupExpiredResults">
        UPDATE document_parse_result
        SET deleted = 1, updated_at = NOW()
        WHERE task_id IN (
            SELECT id FROM document_parse_task
            WHERE status IN ('done', 'failed')
            AND complete_time &lt; DATE_SUB(NOW(), INTERVAL #{expireDays,jdbcType=INTEGER} DAY)
            AND deleted = 0
        )
        AND deleted = 0
    </update>

</mapper>
