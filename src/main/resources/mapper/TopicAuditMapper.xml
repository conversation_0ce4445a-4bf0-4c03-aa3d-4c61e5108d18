<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.maizi_edu_sys.repository.TopicAuditMapper">

    <!-- 查询待审核题目列表（分页，支持搜索和筛选） -->
    <select id="selectPendingAuditsWithFilters" resultType="com.edu.maizi_edu_sys.entity.TopicAudit">
        SELECT ta.*
        FROM topic_audit ta
        LEFT JOIN user u ON ta.user_id = u.id
        <where>
            ta.audit_status = 0
            <if test="keyword != null and keyword != ''">
                AND (ta.title LIKE CONCAT('%', #{keyword}, '%') 
                     OR ta.answer LIKE CONCAT('%', #{keyword}, '%')
                     OR ta.parse LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="submitter != null and submitter != ''">
                AND (u.username LIKE CONCAT('%', #{submitter}, '%') 
                     OR ta.user_id = #{submitter})
            </if>
            <if test="knowledge != null and knowledge != ''">
                AND (ta.know_id = #{knowledge}
                     OR ta.tags LIKE CONCAT('%', #{knowledge}, '%'))
            </if>
        </where>
        ORDER BY ta.submit_time ASC
    </select>

    <!-- 查询按用户和日期分组的审核数据 -->
    <select id="selectUserDailyAuditGroups" resultType="com.edu.maizi_edu_sys.dto.UserDailyAuditGroupDTO">
        SELECT 
            CAST(ta.user_id AS CHAR) AS userId,
            u.username AS username,
            DATE(ta.submit_time) AS submitDate,
            COUNT(*) AS topicCount,
            SUM(CASE WHEN ta.audit_status = 0 THEN 1 ELSE 0 END) AS pendingCount,
            SUM(CASE WHEN ta.audit_status = 1 THEN 1 ELSE 0 END) AS approvedCount,
            SUM(CASE WHEN ta.audit_status = 2 THEN 1 ELSE 0 END) AS rejectedCount,
            
            <!-- 题目类型分布统计 -->
            SUM(CASE WHEN ta.type = '单选题' OR ta.type = 'singleChoice' THEN 1 ELSE 0 END) AS choiceCount,
            SUM(CASE WHEN ta.type = '多选题' OR ta.type = 'multipleChoice' THEN 1 ELSE 0 END) AS multipleCount,
            SUM(CASE WHEN ta.type = '判断题' OR ta.type = 'judgment' THEN 1 ELSE 0 END) AS judgeCount,
            SUM(CASE WHEN ta.type = '填空题' OR ta.type = 'fillBlank' THEN 1 ELSE 0 END) AS fillCount,
            SUM(CASE WHEN ta.type = '简答题' OR ta.type = 'shortAnswer' THEN 1 ELSE 0 END) AS shortCount,
            SUM(CASE WHEN ta.type = '论述题' OR ta.type = 'essay' OR ta.type = 'subjective' THEN 1 ELSE 0 END) AS essayCount,
            
            <!-- 提交时间范围 -->
            DATE_FORMAT(MIN(ta.submit_time), '%H:%i') AS earliestSubmitTime,
            DATE_FORMAT(MAX(ta.submit_time), '%H:%i') AS latestSubmitTime,
            
            <!-- 批次信息判断：如果最早和最晚提交时间相差超过1小时，标记为多批次 -->
            CASE 
                WHEN TIMESTAMPDIFF(HOUR, MIN(ta.submit_time), MAX(ta.submit_time)) > 1 THEN '多批次'
                ELSE '单批次'
            END AS batchInfo,
            
            <!-- 获取该组的审核ID列表（用于批量操作） -->
            GROUP_CONCAT(ta.id) AS auditIds
            
        FROM topic_audit ta
        LEFT JOIN user u ON ta.user_id = u.id
        <where>
            <if test="keyword != null and keyword != ''">
                AND (u.username LIKE CONCAT('%', #{keyword}, '%') 
                     OR ta.title LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
        GROUP BY ta.user_id, DATE(ta.submit_time), u.username
        HAVING pendingCount > 0
        ORDER BY submitDate DESC, pendingCount DESC, topicCount DESC
    </select>

    <!-- 批量更新审核状态 -->
    <update id="batchUpdateAuditStatus">
        UPDATE topic_audit 
        SET audit_status = #{auditStatus},
            auditor_id = #{auditorId},
            audit_time = #{auditTime},
            audit_comment = #{auditComment}
        WHERE id IN
        <foreach collection="auditIds" item="auditId" open="(" separator="," close=")">
            #{auditId}
        </foreach>
        AND audit_status = 0
    </update>

</mapper>