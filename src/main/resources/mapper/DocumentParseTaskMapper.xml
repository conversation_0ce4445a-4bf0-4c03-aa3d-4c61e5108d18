<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.maizi_edu_sys.mapper.DocumentParseTaskMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.edu.maizi_edu_sys.entity.DocumentParseTask">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="VARCHAR"/>
        <result column="batch_id" property="batchId" jdbcType="VARCHAR"/>
        <result column="data_id" property="dataId" jdbcType="VARCHAR"/>
        <result column="file_name" property="fileName" jdbcType="VARCHAR"/>
        <result column="file_url" property="fileUrl" jdbcType="VARCHAR"/>
        <result column="file_size" property="fileSize" jdbcType="BIGINT"/>
        <result column="file_type" property="fileType" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="extracted_pages" property="extractedPages" jdbcType="INTEGER"/>
        <result column="total_pages" property="totalPages" jdbcType="INTEGER"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="complete_time" property="completeTime" jdbcType="TIMESTAMP"/>
        <result column="result_zip_url" property="resultZipUrl" jdbcType="VARCHAR"/>
        <result column="error_message" property="errorMessage" jdbcType="VARCHAR"/>
        <result column="parse_config" property="parseConfig" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, task_id, batch_id, data_id, file_name, file_url, file_size, file_type, status,
        extracted_pages, total_pages, start_time, complete_time, result_zip_url, error_message,
        parse_config, user_id, created_at, updated_at, deleted
    </sql>

    <!-- 根据任务ID查询 -->
    <select id="selectByTaskId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM document_parse_task
        WHERE task_id = #{taskId,jdbcType=VARCHAR} AND deleted = 0
    </select>

    <!-- 根据批次ID查询 -->
    <select id="selectByBatchId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM document_parse_task
        WHERE batch_id = #{batchId,jdbcType=VARCHAR} AND deleted = 0
        ORDER BY created_at ASC
    </select>

    <!-- 根据用户ID和状态查询 -->
    <select id="selectByUserIdAndStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM document_parse_task
        WHERE user_id = #{userId,jdbcType=BIGINT} AND deleted = 0
        <if test="status != null and status != ''">
            AND status = #{status,jdbcType=VARCHAR}
        </if>
        ORDER BY created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询需要同步状态的任务 -->
    <select id="selectTasksForSync" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM document_parse_task
        WHERE status IN ('pending', 'running', 'converting') 
        AND deleted = 0
        AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY created_at ASC
        LIMIT #{limit,jdbcType=INTEGER}
    </select>

    <!-- 插入任务 -->
    <insert id="insert" parameterType="com.edu.maizi_edu_sys.entity.DocumentParseTask" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO document_parse_task (
            task_id, batch_id, data_id, file_name, file_url, file_size, file_type, status,
            extracted_pages, total_pages, start_time, complete_time, result_zip_url, error_message,
            parse_config, user_id, created_at, updated_at, deleted
        ) VALUES (
            #{taskId,jdbcType=VARCHAR}, #{batchId,jdbcType=VARCHAR}, #{dataId,jdbcType=VARCHAR},
            #{fileName,jdbcType=VARCHAR}, #{fileUrl,jdbcType=VARCHAR}, #{fileSize,jdbcType=BIGINT},
            #{fileType,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{extractedPages,jdbcType=INTEGER},
            #{totalPages,jdbcType=INTEGER}, #{startTime,jdbcType=TIMESTAMP}, #{completeTime,jdbcType=TIMESTAMP},
            #{resultZipUrl,jdbcType=VARCHAR}, #{errorMessage,jdbcType=VARCHAR}, #{parseConfig,jdbcType=VARCHAR},
            #{userId,jdbcType=BIGINT}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP},
            #{deleted,jdbcType=TINYINT}
        )
    </insert>

    <!-- 更新任务状态 -->
    <update id="updateStatus">
        UPDATE document_parse_task
        SET status = #{status,jdbcType=VARCHAR},
            updated_at = NOW()
            <if test="startTime != null">
                , start_time = #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="completeTime != null">
                , complete_time = #{completeTime,jdbcType=TIMESTAMP}
            </if>
            <if test="errorMessage != null">
                , error_message = #{errorMessage,jdbcType=VARCHAR}
            </if>
        WHERE task_id = #{taskId,jdbcType=VARCHAR}
    </update>

    <!-- 更新任务进度 -->
    <update id="updateProgress">
        UPDATE document_parse_task
        SET extracted_pages = #{extractedPages,jdbcType=INTEGER},
            total_pages = #{totalPages,jdbcType=INTEGER},
            updated_at = NOW()
        WHERE task_id = #{taskId,jdbcType=VARCHAR}
    </update>

    <!-- 更新任务结果 -->
    <update id="updateResult">
        UPDATE document_parse_task
        SET result_zip_url = #{resultZipUrl,jdbcType=VARCHAR},
            status = #{status,jdbcType=VARCHAR},
            complete_time = #{completeTime,jdbcType=TIMESTAMP},
            updated_at = NOW()
        WHERE task_id = #{taskId,jdbcType=VARCHAR}
    </update>

    <!-- 批量更新任务状态 -->
    <update id="batchUpdateStatus">
        UPDATE document_parse_task
        SET status = #{status,jdbcType=VARCHAR},
            updated_at = NOW()
            <if test="errorMessage != null">
                , error_message = #{errorMessage,jdbcType=VARCHAR}
            </if>
        WHERE batch_id = #{batchId,jdbcType=VARCHAR}
    </update>

    <!-- 软删除任务 -->
    <update id="deleteByTaskId" parameterType="java.lang.String">
        UPDATE document_parse_task
        SET deleted = 1, updated_at = NOW()
        WHERE task_id = #{taskId,jdbcType=VARCHAR}
    </update>

    <!-- 统计用户任务数量 -->
    <select id="countByUserIdAndStatus" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM document_parse_task
        WHERE user_id = #{userId,jdbcType=BIGINT} AND deleted = 0
        <if test="status != null and status != ''">
            AND status = #{status,jdbcType=VARCHAR}
        </if>
    </select>

    <!-- 清理过期任务 -->
    <update id="cleanupExpiredTasks">
        UPDATE document_parse_task
        SET deleted = 1, updated_at = NOW()
        WHERE status IN ('done', 'failed')
        AND deleted = 0
        AND complete_time &lt; DATE_SUB(NOW(), INTERVAL #{expireDays,jdbcType=INTEGER} DAY)
    </update>

</mapper>
