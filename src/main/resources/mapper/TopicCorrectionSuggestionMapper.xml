<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.maizi_edu_sys.mapper.TopicCorrectionSuggestionMapper">

    <!-- 批量插入修正建议 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO topic_correction_suggestion 
        (topic_id, correction_reason, suggested_updates, ai_response, batch_id, status, created_at, updated_at)
        VALUES
        <foreach collection="suggestions" item="item" separator=",">
            (
                #{item.topicId},
                #{item.correctionReason},
                #{item.suggestedUpdates},
                #{item.aiResponse},
                #{item.batchId},
                #{item.status},
                #{item.createdAt},
                #{item.updatedAt}
            )
        </foreach>
    </insert>

    <!-- 根据题目ID列表更新状态 -->
    <update id="updateStatusByTopicIds">
        UPDATE topic_correction_suggestion 
        SET status = #{status}, updated_at = NOW() 
        WHERE status = 'PENDING'
        <if test="topicIds != null and topicIds.size() > 0">
            AND topic_id IN
            <foreach collection="topicIds" item="topicId" open="(" separator="," close=")">
                #{topicId}
            </foreach>
        </if>
    </update>

    <!-- 根据批次ID更新状态 -->
    <update id="updateStatusByBatchId">
        UPDATE topic_correction_suggestion 
        SET status = #{status}, updated_at = NOW() 
        WHERE batch_id = #{batchId} AND status = 'PENDING'
    </update>

</mapper>