<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.maizi_edu_sys.mapper.CorrectionApprovalMapper">

    <select id="getPendingApprovals" resultType="com.edu.maizi_edu_sys.entity.CorrectionApproval">
        SELECT *
        FROM correction_approval
        WHERE status = 0
          AND expires_at > NOW()
        ORDER BY created_at DESC
    </select>

    <select id="countPendingApprovals" resultType="int">
        SELECT count(*)
        FROM correction_approval
        <where>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="status == null or status == ''">
                AND status = 0
            </if>
            <if test="search != null and search != ''">
                AND (correction_json LIKE CONCAT('%', #{search}, '%') OR statistics LIKE CONCAT('%', #{search}, '%'))
            </if>
        </where>
    </select>

    <select id="getPendingApprovalsWithPagination" resultType="com.edu.maizi_edu_sys.entity.CorrectionApproval">
        SELECT *
        FROM correction_approval
        <where>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="status == null or status == ''">
                AND status = 0
            </if>
            <if test="search != null and search != ''">
                AND (correction_json LIKE CONCAT('%', #{search}, '%') OR statistics LIKE CONCAT('%', #{search}, '%'))
            </if>
        </where>
        ORDER BY created_at DESC
        LIMIT #{offset}, #{size}
    </select>

    <update id="approveCorrection">
        UPDATE correction_approval
        SET status     = 1,
            approver   = #{approver},
            approval_comment = #{comment},
            approved_at = #{approvedAt}
        WHERE id = #{approvalId} AND status = 0
    </update>

    <update id="rejectCorrection">
        UPDATE correction_approval
        SET status     = 2,
            approver   = #{approver},
            approval_comment = #{comment},
            approved_at = #{rejectedAt}
        WHERE id = #{approvalId} AND status = 0
    </update>

    <update id="expirePendingApprovals">
        UPDATE correction_approval
        SET status = 3
        WHERE status = 0 AND expires_at > NOW()
    </update>

</mapper>