<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题目审核管理 - 麦子教育系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .audit-status-pending { color: #ffc107; }
        .audit-status-approved { color: #198754; }
        .audit-status-rejected { color: #dc3545; }
        .topic-content { 
            max-height: 200px; 
            overflow-y: auto; 
            background-color: #f8f9fa; 
            padding: 10px; 
            border-radius: 5px; 
        }
        .message-unread { background-color: #fff3cd; }
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-graduation-cap"></i> 麦子教育系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/"><i class="fas fa-home"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/topics/upload"><i class="fas fa-upload"></i> 上传题目</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/audit/management"><i class="fas fa-check-circle"></i> 审核管理</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="messagesDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i> 消息 <span id="unreadBadge" class="badge bg-danger" style="display: none;">0</span>
                        </a>
                        <ul class="dropdown-menu" id="messagesMenu">
                            <li><h6 class="dropdown-header">系统消息</h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li id="noMessages" class="dropdown-item-text">暂无消息</li>
                        </ul>
                    </li>
                    <li class="nav-item">
                                                        <a class="nav-link" href="javascript:void(0)" onclick="logout()"><i class="fas fa-sign-out-alt"></i> 退出</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 页面标题 -->
    <div class="page-header">
        <div class="container">
            <h1><i class="fas fa-check-circle"></i> 题目审核管理</h1>
            <p class="lead">管理和审核用户提交的题目</p>
        </div>
    </div>

    <div class="container">
        <!-- 审核统计 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                            <div>
                                <h5 class="card-title">待审核</h5>
                                <h3 id="pendingCount">0</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-check fa-2x"></i>
                            </div>
                            <div>
                                <h5 class="card-title">已通过</h5>
                                <h3 id="approvedCount">0</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-times fa-2x"></i>
                            </div>
                            <div>
                                <h5 class="card-title">已拒绝</h5>
                                <h3 id="rejectedCount">0</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-list fa-2x"></i>
                            </div>
                            <div>
                                <h5 class="card-title">总计</h5>
                                <h3 id="totalCount">0</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 审核选项卡 -->
        <ul class="nav nav-tabs" id="auditTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab">
                    <i class="fas fa-clock"></i> 待审核题目
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="my-audits-tab" data-bs-toggle="tab" data-bs-target="#my-audits" type="button" role="tab">
                    <i class="fas fa-user"></i> 我的提交
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="auditor-records-tab" data-bs-toggle="tab" data-bs-target="#auditor-records" type="button" role="tab">
                    <i class="fas fa-history"></i> 审核记录
                </button>
            </li>
        </ul>

        <div class="tab-content" id="auditTabContent">
            <!-- 待审核题目 -->
            <div class="tab-pane fade show active" id="pending" role="tabpanel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">待审核题目列表</h5>
                        <button class="btn btn-outline-primary btn-sm" onclick="loadPendingAudits()">
                            <i class="fas fa-refresh"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="pendingAuditsContainer">
                            <div class="text-center py-4">
                                <i class="fas fa-spinner fa-spin fa-2x"></i>
                                <p class="mt-2">加载中...</p>
                            </div>
                        </div>
                        <!-- 分页 -->
                        <nav id="pendingPagination" style="display: none;">
                            <ul class="pagination justify-content-center">
                                <!-- 分页链接将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- 我的提交 -->
            <div class="tab-pane fade" id="my-audits" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">我的审核提交</h5>
                    </div>
                    <div class="card-body">
                        <div id="myAuditsContainer">
                            <!-- 内容将通过JavaScript加载 -->
                        </div>
                        <nav id="myAuditsPagination" style="display: none;">
                            <ul class="pagination justify-content-center">
                                <!-- 分页链接将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- 审核记录 -->
            <div class="tab-pane fade" id="auditor-records" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">我的审核记录</h5>
                    </div>
                    <div class="card-body">
                        <div id="auditorRecordsContainer">
                            <!-- 内容将通过JavaScript加载 -->
                        </div>
                        <nav id="auditorRecordsPagination" style="display: none;">
                            <ul class="pagination justify-content-center">
                                <!-- 分页链接将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 审核模态框 -->
    <div class="modal fade" id="auditModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">审核题目</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="auditTopicContent">
                        <!-- 题目内容将在这里显示 -->
                    </div>
                    <hr>
                    <form id="auditForm">
                        <input type="hidden" id="auditId" name="auditId">
                        <div class="mb-3">
                            <label class="form-label">审核结果 <span class="text-danger">*</span></label>
                            <div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="auditResult" id="approve" value="1" required>
                                    <label class="form-check-label text-success" for="approve">
                                        <i class="fas fa-check"></i> 通过
                                    </label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="auditResult" id="reject" value="2" required>
                                    <label class="form-check-label text-danger" for="reject">
                                        <i class="fas fa-times"></i> 拒绝
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="auditComment" class="form-label">审核意见</label>
                            <textarea class="form-control" id="auditComment" name="auditComment" rows="3" 
                                    placeholder="请输入审核意见（拒绝时必填）"></textarea>
                            <div class="form-text">通过时可选，拒绝时请说明原因</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitAudit()">提交审核</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/audit-management.js"></script>
</body>
</html> 