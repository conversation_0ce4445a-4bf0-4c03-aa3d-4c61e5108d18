<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MineRU 文档解析测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .drop-zone {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .drop-zone:hover, .drop-zone.dragover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
        .api-status {
            font-size: 0.9em;
        }
        .token-card {
            border-left: 4px solid #28a745;
            margin-bottom: 10px;
        }
        .token-card.warning {
            border-left-color: #ffc107;
        }
        .token-card.danger {
            border-left-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="bi bi-file-earmark-text"></i>
                    MineRU 文档解析测试
                </h1>
            </div>
        </div>

        <!-- API状态面板 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-server"></i>
                            API 状态监控
                            <button class="btn btn-sm btn-outline-primary float-end" onclick="refreshApiStatus()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="apiStatus">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在获取API状态...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文件上传区域 -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-cloud-upload"></i>
                            文档上传解析
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- 拖拽上传区域 -->
                        <div class="drop-zone" id="dropZone">
                            <i class="bi bi-cloud-upload fs-1 text-muted"></i>
                            <h4 class="mt-3">拖拽文件到此处或点击选择</h4>
                            <p class="text-muted">支持 PDF, Word, PowerPoint, Excel, 图片等格式</p>
                            <p class="text-muted">最大文件大小: 200MB</p>
                            <input type="file" id="fileInput" accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.rtf,.jpg,.jpeg,.png,.gif,.bmp,.tiff" style="display: none;">
                        </div>

                        <!-- 解析选项 -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h6>解析选项</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableOcr" checked>
                                    <label class="form-check-label" for="enableOcr">启用OCR识别</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableFormula" checked>
                                    <label class="form-check-label" for="enableFormula">启用公式识别</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableTable" checked>
                                    <label class="form-check-label" for="enableTable">启用表格识别</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>输出格式</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="outputMarkdown" checked>
                                    <label class="form-check-label" for="outputMarkdown">Markdown</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="outputJson">
                                    <label class="form-check-label" for="outputJson">JSON</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="outputHtml">
                                    <label class="form-check-label" for="outputHtml">HTML</label>
                                </div>
                            </div>
                        </div>

                        <!-- 语言选择 -->
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label for="languageSelect" class="form-label">识别语言</label>
                                <select class="form-select" id="languageSelect">
                                    <option value="ch" selected>中文</option>
                                    <option value="en">英文</option>
                                    <option value="auto">自动检测</option>
                                </select>
                            </div>
                        </div>

                        <!-- 进度显示 -->
                        <div id="progressSection" class="mt-4" style="display: none;">
                            <h6>解析进度</h6>
                            <div class="progress">
                                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%"></div>
                            </div>
                            <p id="progressText" class="mt-2 text-muted">准备中...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 侧边栏 - Token详情 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-key"></i>
                            API Token 详情
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="tokenDetails">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在获取Token信息...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 解析结果 -->
        <div class="row mt-4" id="resultsSection" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-check-circle"></i>
                            解析结果
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="parseResults"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupDropZone();
            refreshApiStatus();
            refreshTokenDetails();
        });

        // 设置拖拽上传
        function setupDropZone() {
            const dropZone = document.getElementById('dropZone');
            const fileInput = document.getElementById('fileInput');

            dropZone.addEventListener('click', () => fileInput.click());
            dropZone.addEventListener('dragover', handleDragOver);
            dropZone.addEventListener('drop', handleDrop);
            dropZone.addEventListener('dragleave', handleDragLeave);
            fileInput.addEventListener('change', handleFileSelect);
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            if (files.length > 0) {
                uploadFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            if (files.length > 0) {
                uploadFile(files[0]);
            }
        }

        // 上传文件
        function uploadFile(file) {
            if (!validateFile(file)) {
                return;
            }

            const formData = new FormData();
            formData.append('file', file);
            formData.append('ocr', document.getElementById('enableOcr').checked);
            formData.append('formula', document.getElementById('enableFormula').checked);
            formData.append('table', document.getElementById('enableTable').checked);
            formData.append('language', document.getElementById('languageSelect').value);

            const outputFormats = [];
            if (document.getElementById('outputMarkdown').checked) outputFormats.push('markdown');
            if (document.getElementById('outputJson').checked) outputFormats.push('json');
            if (document.getElementById('outputHtml').checked) outputFormats.push('html');
            formData.append('outputFormats', outputFormats.join(','));

            showProgress();

            fetch('/mineru/api/parse', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                hideProgress();
                if (data.success) {
                    showResult(data);
                } else {
                    showError(data.error || '解析失败');
                }
            })
            .catch(error => {
                hideProgress();
                showError('网络错误: ' + error.message);
            });
        }

        // 文件验证
        function validateFile(file) {
            const maxSize = 200 * 1024 * 1024; // 200MB
            if (file.size > maxSize) {
                alert(`文件 ${file.name} 超过最大大小限制 (200MB)`);
                return false;
            }

            const allowedExts = ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'txt', 'rtf', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff'];
            const fileExt = file.name.toLowerCase().split('.').pop();
            
            if (!allowedExts.includes(fileExt)) {
                alert(`不支持的文件类型: ${file.name}`);
                return false;
            }

            return true;
        }

        // 显示进度
        function showProgress() {
            document.getElementById('progressSection').style.display = 'block';
            document.getElementById('resultsSection').style.display = 'none';
            
            let progress = 0;
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            
            const interval = setInterval(() => {
                progress += Math.random() * 10;
                if (progress > 90) progress = 90;
                
                progressBar.style.width = progress + '%';
                progressText.textContent = `解析中... ${Math.round(progress)}%`;
            }, 500);
            
            // 存储interval以便后续清除
            window.currentProgressInterval = interval;
        }

        // 隐藏进度
        function hideProgress() {
            if (window.currentProgressInterval) {
                clearInterval(window.currentProgressInterval);
            }
            
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            
            progressBar.style.width = '100%';
            progressText.textContent = '解析完成';
            
            setTimeout(() => {
                document.getElementById('progressSection').style.display = 'none';
            }, 1000);
        }

        // 显示结果
        function showResult(data) {
            const resultsSection = document.getElementById('resultsSection');
            const parseResults = document.getElementById('parseResults');
            
            parseResults.innerHTML = `
                <div class="alert alert-success">
                    <h5><i class="bi bi-check-circle"></i> 解析成功</h5>
                    <p><strong>任务ID:</strong> ${data.taskId}</p>
                    <p><strong>页数:</strong> ${data.pages}</p>
                    <p><strong>处理时间:</strong> ${data.processingTime}ms</p>
                    <p><strong>使用Token:</strong> ${data.tokenUsed}</p>
                </div>
                <div class="mt-3">
                    <a href="${data.downloadUrl}" class="btn btn-primary me-2">
                        <i class="bi bi-download"></i> 下载结果
                    </a>
                    <a href="${data.previewUrl}" class="btn btn-outline-primary" target="_blank">
                        <i class="bi bi-eye"></i> 预览结果
                    </a>
                </div>
            `;
            
            resultsSection.style.display = 'block';
        }

        // 显示错误
        function showError(message) {
            const resultsSection = document.getElementById('resultsSection');
            const parseResults = document.getElementById('parseResults');
            
            parseResults.innerHTML = `
                <div class="alert alert-danger">
                    <h5><i class="bi bi-exclamation-triangle"></i> 解析失败</h5>
                    <p>${message}</p>
                </div>
            `;
            
            resultsSection.style.display = 'block';
        }

        // 刷新API状态
        function refreshApiStatus() {
            fetch('/mineru/api/status')
                .then(response => response.json())
                .then(data => {
                    const apiStatus = document.getElementById('apiStatus');
                    apiStatus.innerHTML = `
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>可用Token</h6>
                                    <span class="badge bg-success fs-6">${data.availableTokens}</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>总Token</h6>
                                    <span class="badge bg-info fs-6">${data.totalTokens}</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>今日使用</h6>
                                    <span class="badge bg-warning fs-6">${data.todayUsage}</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>系统状态</h6>
                                    <span class="badge bg-${data.systemStatus === 'healthy' ? 'success' : 'danger'} fs-6">
                                        ${data.systemStatus === 'healthy' ? '正常' : '异常'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    `;
                })
                .catch(error => {
                    document.getElementById('apiStatus').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i> 
                            无法获取API状态: ${error.message}
                        </div>
                    `;
                });
        }

        // 刷新Token详情
        function refreshTokenDetails() {
            fetch('/mineru/api/tokens')
                .then(response => response.json())
                .then(data => {
                    const tokenDetails = document.getElementById('tokenDetails');
                    let html = '';
                    
                    for (const [tokenName, tokenInfo] of Object.entries(data)) {
                        const statusClass = tokenInfo.available ? 'success' : 
                                          tokenInfo.quota > 0 ? 'warning' : 'danger';
                        
                        html += `
                            <div class="card token-card ${statusClass} mb-2">
                                <div class="card-body p-2">
                                    <h6 class="card-title mb-1">${tokenName}</h6>
                                    <small class="text-muted">
                                        剩余: ${tokenInfo.remaining}/${tokenInfo.quota}<br>
                                        优先级: ${tokenInfo.priority}<br>
                                        状态: ${tokenInfo.available ? '可用' : '不可用'}
                                    </small>
                                </div>
                            </div>
                        `;
                    }
                    
                    tokenDetails.innerHTML = html || '<p class="text-muted">暂无Token信息</p>';
                })
                .catch(error => {
                    document.getElementById('tokenDetails').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i> 
                            无法获取Token信息: ${error.message}
                        </div>
                    `;
                });
        }

        // 定时刷新状态
        setInterval(refreshApiStatus, 30000); // 每30秒刷新一次
        setInterval(refreshTokenDetails, 60000); // 每60秒刷新一次
    </script>
</body>
</html>
