<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史试卷管理 - 麦子教育系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link rel="stylesheet" th:href="@{/static/css/common.css}">
    <link rel="stylesheet" th:href="@{/static/css/navbar.css}">
    <link rel="stylesheet" th:href="@{/static/css/paper-history.css}">
    
    <style>
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .stats-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .batch-toolbar {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            border: 1px solid #e9ecef;
        }
        
        .paper-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }
        
        .paper-card:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        
        .paper-title {
            font-weight: 600;
            color: #2c3e50;
            text-decoration: none;
        }
        
        .paper-title:hover {
            color: #3498db;
        }
        
        .paper-meta {
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .action-btn {
            border-radius: 8px;
            padding: 0.4rem 0.8rem;
            font-size: 0.85rem;
            margin: 0.1rem;
        }
        
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1.5rem;
        }
        
        .search-box:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .filter-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <div th:replace="fragments/navbar :: navbar('history')"></div>
    
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-history me-3"></i>历史试卷管理
                    </h1>
                    <p class="mb-0 opacity-75">管理您的所有试卷，支持批量操作和高级搜索</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-lg" onclick="window.location.href='/paper/generate'">
                        <i class="fas fa-plus me-2"></i>创建新试卷
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                        <h4 class="mb-1" id="totalPapers">0</h4>
                        <small class="text-muted">总试卷数</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <i class="fas fa-calendar-day fa-2x text-success mb-2"></i>
                        <h4 class="mb-1" id="todayPapers">0</h4>
                        <small class="text-muted">今日创建</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <i class="fas fa-download fa-2x text-info mb-2"></i>
                        <h4 class="mb-1" id="downloadCount">0</h4>
                        <small class="text-muted">下载次数</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <i class="fas fa-check-square fa-2x text-warning mb-2"></i>
                        <h4 class="mb-1" id="selectedCount">0</h4>
                        <small class="text-muted">已选择</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 搜索和筛选 -->
        <div class="filter-card">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text bg-white border-end-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" class="form-control border-start-0 search-box" 
                               id="searchInput" placeholder="搜索试卷标题...">
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="sortSelect">
                        <option value="createTime-desc">创建时间↓</option>
                        <option value="createTime-asc">创建时间↑</option>
                        <option value="title-asc">标题A-Z</option>
                        <option value="title-desc">标题Z-A</option>
                        <option value="totalScore-desc">总分↓</option>
                        <option value="totalScore-asc">总分↑</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="dateFilter">
                        <option value="">全部时间</option>
                        <option value="today">今天</option>
                        <option value="week">本周</option>
                        <option value="month">本月</option>
                        <option value="quarter">本季度</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="scoreFilter">
                        <option value="">全部分数</option>
                        <option value="0-50">0-50分</option>
                        <option value="51-80">51-80分</option>
                        <option value="81-100">81-100分</option>
                        <option value="100+">100分以上</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-primary w-100" onclick="searchPapers()">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 批量操作工具栏 -->
        <div class="batch-toolbar" id="batchToolbar" style="display: none;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <div class="form-check me-3">
                            <input class="form-check-input" type="checkbox" id="selectAll">
                            <label class="form-check-label" for="selectAll">
                                全选
                            </label>
                        </div>
                        <span class="text-muted">已选择 <strong id="selectedCountText">0</strong> 个试卷</span>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="btn-group">
                        <button class="btn btn-outline-primary" id="batchDownloadBtn" disabled>
                            <i class="fas fa-download me-1"></i>批量下载
                        </button>
                        <button class="btn btn-outline-success" id="batchExportBtn" disabled>
                            <i class="fas fa-file-export me-1"></i>导出数据
                        </button>
                        <button class="btn btn-outline-warning" id="batchEditBtn" disabled>
                            <i class="fas fa-edit me-1"></i>批量编辑
                        </button>
                        <button class="btn btn-outline-danger" id="batchDeleteBtn" disabled>
                            <i class="fas fa-trash-alt me-1"></i>批量删除
                        </button>
                        <button class="btn btn-outline-secondary" id="cancelSelectionBtn">
                            <i class="fas fa-times me-1"></i>取消选择
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 试卷列表 -->
        <div class="row" id="papersList">
            <!-- 加载中状态 -->
            <div class="col-12 text-center py-5" id="loadingState">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-3 text-muted">正在加载试卷列表...</p>
            </div>
        </div>
        
        <!-- 分页 -->
        <nav aria-label="试卷分页" class="mt-4">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页内容将通过JavaScript生成 -->
            </ul>
        </nav>
    </div>
    
    <!-- 批量下载模态框 -->
    <div class="modal fade" id="batchDownloadModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-download me-2"></i>批量下载试卷
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        即将下载 <strong id="downloadCountText">0</strong> 个试卷
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">文件格式</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="downloadFormat" id="formatPdf" value="pdf" checked>
                            <label class="form-check-label" for="formatPdf">
                                <i class="fas fa-file-pdf text-danger me-1"></i>PDF 格式
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="downloadFormat" id="formatWord" value="word">
                            <label class="form-check-label" for="formatWord">
                                <i class="fas fa-file-word text-primary me-1"></i>Word 格式
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="downloadVersion" class="form-label">试卷版本</label>
                        <select class="form-select" id="downloadVersion">
                            <option value="standard">标准版 - 题目+答案+解析</option>
                            <option value="regular">学生版 - 只有题目</option>
                            <option value="teacher">教师版 - 只有答案和解析</option>
                        </select>
                    </div>
                    
                    <div class="progress" id="downloadProgress" style="display: none;">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmDownloadBtn">
                        <i class="fas fa-download me-1"></i>开始下载
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 批量编辑模态框 -->
    <div class="modal fade" id="batchEditModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>批量编辑试卷
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        即将编辑 <strong id="editCountText">0</strong> 个试卷
                    </div>
                    
                    <form id="batchEditForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">标题前缀</label>
                                <input type="text" class="form-control" id="titlePrefix" placeholder="为选中试卷添加前缀">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">标题后缀</label>
                                <input type="text" class="form-control" id="titleSuffix" placeholder="为选中试卷添加后缀">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">分类标签</label>
                                <input type="text" class="form-control" id="categoryTag" placeholder="设置分类标签">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">难度等级</label>
                                <select class="form-select" id="difficultyLevel">
                                    <option value="">不修改</option>
                                    <option value="1">简单</option>
                                    <option value="2">中等</option>
                                    <option value="3">困难</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning" id="confirmEditBtn">
                        <i class="fas fa-save me-1"></i>保存修改
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 试卷详情模态框 -->
    <div class="modal fade" id="paperDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-eye me-2"></i>试卷详情
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="paperDetailContent">
                    <!-- 试卷详情内容将通过JavaScript加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="editPaperBtn">
                        <i class="fas fa-edit me-1"></i>编辑试卷
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script th:src="@{/static/js/navbar.js}"></script>
    <script th:src="@{/static/js/paper-history-management.js}"></script>
    
    <script>
        $(document).ready(function() {
            // 初始化导航栏
            initNavbar();
            
            // 初始化历史试卷管理
            initPaperHistoryManagement();
            
            // 加载试卷列表
            loadPapers();
        });
    </script>
</body>
</html>