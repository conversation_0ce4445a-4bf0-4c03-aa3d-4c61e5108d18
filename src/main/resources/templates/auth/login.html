<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - <PERSON>zi EDU</title>
    <link rel="stylesheet" type="text/css" href="/static/css/login.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="page-container">
        <div class="left-section">
            <div class="brand">
                <h1>Maizi EDU</h1>
                <p class="slogan">智慧教育，未来可期</p>
            </div>
        </div>
        
        <div class="right-section">
            <div class="form-container">
                <div class="login-card">
                    <div class="form-header">
                        <h2 id="formTitle">登录账号</h2>
                        <p class="subtitle">欢迎回来，请输入您的账号信息</p>
                    </div>

                    <form id="loginForm" class="active">
                        <div class="input-group">
                            <label for="username">用户名</label>
                            <div class="input-with-icon">
                                <i class="fas fa-user"></i>
                                <input type="text" id="username" required>
                            </div>
                        </div>
                        <div class="input-group">
                            <label for="password">密码</label>
                            <div class="input-with-icon password-input">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="password" required>
                                <i class="fas fa-eye password-toggle" id="togglePassword" title="显示/隐藏密码"></i>
                            </div>
                            <span class="forgot-password" id="forgotPassword">忘记密码？</span>
                        </div>
                        <div class="input-group">
                            <label for="captcha">验证码</label>
                            <div class="captcha-container">
                                <div class="input-with-icon">
                                    <i class="fas fa-shield-alt"></i>
                                    <input type="text" id="captcha" placeholder="请输入验证码" required>
                                </div>
                                <div class="captcha-display">
                                    <canvas id="captchaCanvas" width="100" height="40"></canvas>
                                    <button type="button" id="refreshCaptcha" class="refresh-btn" title="点击刷新验证码">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="submit-btn">登录</button>
                        <p class="switch-form">
                            还没有账号？<a href="#" id="showRegister">立即注册</a>
                        </p>
                    </form>

                    <form id="registerForm" style="display: none;">
                        <div class="input-group">
                            <label for="reg-username">用户名</label>
                            <div class="input-with-icon">
                                <i class="fas fa-user"></i>
                                <input type="text" id="reg-username" required>
                            </div>
                        </div>
                        <div class="input-group">
                            <label for="reg-password">密码</label>
                            <div class="input-with-icon password-input">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="reg-password" required>
                                <i class="fas fa-eye password-toggle" id="toggleRegPassword" title="显示/隐藏密码"></i>
                            </div>
                        </div>
                        <div class="input-group">
                            <label for="reg-email">邮箱</label>
                            <div class="input-with-icon">
                                <i class="fas fa-envelope"></i>
                                <input type="email" id="reg-email" required>
                            </div>
                        </div>
                        <button type="submit" class="submit-btn">注册</button>
                        <p class="switch-form">
                            已有账号？<a href="#" id="showLogin">返回登录</a>
                        </p>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 找回密码模态框 -->
    <div id="forgotPasswordModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>找回密码</h3>
                <span class="close" id="closeForgotModal">&times;</span>
            </div>
            <div class="modal-body">
                <div id="forgotStep1" class="forgot-step active">
                    <p>请输入您的邮箱地址，我们将发送重置密码的链接到您的邮箱。</p>
                    <div class="input-group">
                        <label for="forgotEmail">邮箱地址</label>
                        <div class="input-with-icon">
                            <i class="fas fa-envelope"></i>
                            <input type="email" id="forgotEmail" placeholder="请输入您的邮箱" required>
                        </div>
                    </div>
                    <button type="button" id="sendResetEmail" class="submit-btn">发送重置链接</button>
                </div>
                
                <div id="forgotStep2" class="forgot-step">
                    <div class="success-message">
                        <i class="fas fa-check-circle"></i>
                        <h4>邮件已发送</h4>
                        <p>我们已经向您的邮箱发送了重置密码的链接，请查收邮件并点击链接重置密码。</p>
                        <p class="note">如果您没有收到邮件，请检查垃圾邮件文件夹，或等待1分钟后重新发送。</p>
                    </div>
                    <button type="button" id="resendEmail" class="submit-btn secondary">重新发送</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/login.js"></script>
</body>
</html>