<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle} ?: 'Maizi EDU - 智慧教育平台'"><PERSON>zi EDU</title>
    
    <!-- Favicon -->
    <link rel="icon" href="/favicon.svg" type="image/svg+xml">

    <!--  基础CSS依赖 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!--  通用导航栏样式 -->
    <link rel="stylesheet" href="/static/css/navbar.css">
    
    <!--  页面特定样式 -->
    <th:block th:fragment="page-styles">
        <!-- 子页面可以在这里添加特定的CSS -->
    </th:block>
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-content {
            min-height: calc(100vh - 70px);
            padding-top: 2rem;
        }
        
        .page-header {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-title {
            color: #333;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .page-description {
            color: #6c757d;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <!--  通用导航栏 -->
    <div th:replace="fragments/navbar :: navbar(${activePage})"></div>
    
    <!--  主要内容区域 -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- 页面头部 -->
            <div class="page-header" th:if="${showPageHeader} != false">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="page-title" th:text="${pageTitle} ?: '页面标题'">页面标题</h1>
                        <p class="page-description" th:text="${pageDescription} ?: '页面描述'" th:if="${pageDescription}">页面描述</p>
                    </div>
                    <div class="col-auto" th:if="${pageActions}">
                        <!-- 页面操作按钮区域 -->
                        <th:block th:fragment="page-actions">
                            <!-- 子页面可以在这里添加操作按钮 -->
                        </th:block>
                    </div>
                </div>
            </div>
            
            <!-- 页面内容 -->
            <th:block th:fragment="page-content">
                <!-- 子页面的主要内容 -->
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    这是基础页面模板，请在子页面中替换此内容。
                </div>
            </th:block>
        </div>
    </div>
    
    <!--  基础JavaScript依赖 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!--  通用功能 -->
    <script src="/static/js/avatar-manager.js"></script>
    <script src="/static/js/navbar.js"></script>
    
    <!--  页面特定脚本 -->
    <th:block th:fragment="page-scripts">
        <!-- 子页面可以在这里添加特定的JavaScript -->
    </th:block>
</body>
</html>
