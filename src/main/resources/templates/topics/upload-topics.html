<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题目上传 - Maizi EDU</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/codemirror@5.65.2/lib/codemirror.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/codemirror@5.65.2/theme/material-darker.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/upload-topics.css">

    <!-- ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.3.3/dist/echarts.min.js"></script>

    <!-- KaTeX Support -->
    <div th:replace="fragments/katex-support :: katex"></div>


</head>
<body>
<!-- 导航栏将由 common.js 动态插入 -->

<div class="container-fluid mt-4">
    <header class="page-header mb-4">
        <h2><i class="bi bi-cloud-arrow-up-fill"></i> 题目批量上传</h2>
        <p class="text-muted">通过JSON格式批量导入题目，支持Markdown和LaTeX公式。</p>
    </header>

    <!-- 个人上传统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-start border-primary border-4 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-file-earmark-text text-primary fs-2"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small">我的总上传量</div>
                            <div class="fs-4 fw-bold text-dark" id="userTotalUploads">0</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-start border-success border-4 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-check-circle text-success fs-2"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small">已通过审核</div>
                            <div class="fs-4 fw-bold text-dark" id="userApprovedUploads">0</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-start border-warning border-4 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-clock text-warning fs-2"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small">待审核</div>
                            <div class="fs-4 fw-bold text-dark" id="userPendingUploads">0</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-start border-info border-4 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-calendar-week text-info fs-2"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small">本周上传</div>
                            <div class="fs-4 fw-bold text-dark" id="userWeeklyUploads">0</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-3">
        <!-- JSON 编辑器区域 -->
        <div class="col-lg-6">
            <div class="card editor-card shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-filetype-json"></i> JSON 数据编辑器</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-secondary me-2" onclick="insertMathExample()" title="插入数学公式示例">
                            <i class="bi bi-calculator"></i> 数学示例
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" id="insertExampleBtn" title="插入示例JSON">
                            <i class="bi bi-file-earmark-code"></i> 插入示例
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <textarea id="jsonEditor"></textarea>
                </div>
                <div class="card-footer editor-actions text-end">
                    <button class="btn btn-primary" id="validateAndPreviewBtn">
                        <i class="bi bi-check-circle"></i> 验证并预览
                    </button>
                    <button class="btn btn-success ms-2" id="submitTopicsBtn" disabled>
                        <i class="bi bi-cloud-upload"></i> 提交题目
                    </button>
                </div>
            </div>
            <div class="alert alert-danger mt-3 d-none" id="errorMessage"></div>
            <div class="alert alert-success mt-3 d-none" id="successMessage"></div>
        </div>

        <!-- 预览区域 -->
        <div class="col-lg-6">
            <div class="card preview-card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0 d-flex align-items-center">
                        <i class="bi bi-eye-fill"></i>
                        <span class="ms-2">题目预览</span>
                        <span class="badge bg-primary rounded-pill ms-2" id="previewTopicCount">0</span>
                    </h5>
                    <!-- 标签统计区域 -->
                    <div id="tagAnalysisSection" class="mt-3 d-none">
                        <div class="card-header bg-light py-2">
                            <div class="d-flex align-items-center justify-content-between">
                                <small class="text-muted mb-0"><i class="bi bi-tags me-1"></i>标签分析</small>
                                <button id="autoFixTagsBtn" class="btn btn-sm btn-outline-primary" style="display: none; font-size: 0.7em; padding: 2px 8px;">
                                    <i class="bi bi-magic"></i> 智能修复
                                </button>
                            </div>
                        </div>
                        <div id="tagStatsContainer"></div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="previewContainer" class="preview-container-empty">
                        <p class="text-muted text-center p-5">
                            <i class="bi bi-card-text fs-1"></i><br>
                            在左侧编辑器中输入或粘贴JSON数据，然后点击"验证并预览"按钮。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 我的上传统计趋势 -->
    <div class="row mb-4" id="uploadStatsSection">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center flex-wrap">
                    <div class="d-flex align-items-center">
                        <h5 class="mb-0 me-3">
                            <i class="bi bi-person-badge"></i> 我的题目上传趋势
                        </h5>
                        <small class="text-muted">仅显示您个人的上传数据</small>
                    </div>
                    <div class="d-flex align-items-center mt-2 mt-md-0">
                        <div class="me-3">
                            <span class="badge bg-secondary">累计: <span id="totalCount">0</span>题</span>
                            <span class="badge bg-success">通过率: <span id="approvalRate">0%</span></span>
                        </div>
                        <select class="form-select form-select-sm me-2" id="statType" style="width: auto;">
                            <option value="day">按日统计</option>
                            <option value="week">按周统计</option>
                            <option value="month">按月统计</option>
                        </select>
                        <input type="date" class="form-control form-control-sm me-1" id="startDate" style="width: auto;">
                        <span class="me-1">-</span>
                        <input type="date" class="form-control form-control-sm me-2" id="endDate" style="width: auto;">
                        <button class="btn btn-sm btn-primary" id="searchBtn"><i class="bi bi-search"></i></button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="uploadStatsChart" style="height: 400px;"></div>

                    <!-- 个人成就和排名信息 -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3"><i class="bi bi-trophy"></i> 个人成就</h6>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>连续上传天数</span>
                                <span class="badge bg-primary" id="consecutiveDays">0</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>最高单日上传</span>
                                <span class="badge bg-success" id="maxDailyUploads">0</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>平均审核时长</span>
                                <span class="badge bg-info" id="avgAuditTime">-</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3"><i class="bi bi-award"></i> 排名情况</h6>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>总上传量排名</span>
                                <span class="badge bg-warning" id="totalUploadRank">-</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>本月排名</span>
                                <span class="badge bg-danger" id="monthlyRank">-</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>质量评分</span>
                                <span class="badge bg-purple" id="qualityScore">-</span>
                            </div>
                        </div>
                    </div>

                    <!-- 上传类型分布 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="text-muted mb-3"><i class="bi bi-pie-chart"></i> 我的题目类型分布</h6>
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="text-center p-2 border rounded">
                                        <div class="text-primary fw-bold" id="choiceCount">0</div>
                                        <small class="text-muted">单选题</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center p-2 border rounded">
                                        <div class="text-success fw-bold" id="multipleCount">0</div>
                                        <small class="text-muted">多选题</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center p-2 border rounded">
                                        <div class="text-warning fw-bold" id="judgeCount">0</div>
                                        <small class="text-muted">判断题</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center p-2 border rounded">
                                        <div class="text-info fw-bold" id="fillCount">0</div>
                                        <small class="text-muted">填空题</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center p-2 border rounded">
                                        <div class="text-secondary fw-bold" id="shortCount">0</div>
                                        <small class="text-muted">简答题</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center p-2 border rounded">
                                        <div class="text-dark fw-bold" id="essayCount">0</div>
                                        <small class="text-muted">论述题</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 格式说明 -->
    <div class="card mt-4 shadow-sm">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-info-circle"></i> JSON 格式说明</h5>
        </div>
        <div class="card-body">
            <p>请确保您的JSON数据符合以下结构。所有文本字段（如 `title`, `options.name`, `parse`）均支持Markdown和LaTeX数学公式。</p>
            <pre class="bg-dark text-light p-3 rounded-2 small"><code>
[
  {
    "know_id": 218, // 知识点ID (必需, 整数)
    "type": "choice", // 题型 (必需, choice/multiple/judge/fill/short/subjective)
    "title": "新民主主义革命的开端是（    ）", // 题目标题 (必需)
    "tags": "近代史,革命", // 细分知识点标签 (可选, 逗号分隔)
    "options": [ // 选项 (选择题/多选题必需)
      {"key":"A","name":"中国共产党成立（1921年）"},
      {"key":"B","name":"五四运动（1919年）"}
      // ...更多选项
    ],
    "answer": "B", // 答案 (单选/多选/判断题必需, 填空题为数组或字符串)
    "parse": "五四运动中无产阶级首次以独立姿态登上政治舞台...", // 解析 (可选)
    "source": "《思想政治必修1中国特色社会主义》...", // 题目来源 (可选)
    "difficulty": 0.3 // 难度 (必需, 0.0 ~ 1.0)
  }
  // ...更多题目
]
                </code></pre>
            <h6>LaTeX 数学公式示例:</h6>
            <ul>
                <li>行内公式: <code>$\sum_{i=1}^{n} i^2 = \frac{n(n+1)(2n+1)}{6}$</code> 将渲染为: <span class="tex2jax_process">$\sum_{i=1}^{n} i^2 = \frac{n(n+1)(2n+1)}{6}$</span></li>
                <li>块级公式: <code>$$\int_0^\infty e^{-x^2} dx = \frac{\sqrt{\pi}}{2}$$</code> 将渲染为: <div class="tex2jax_process">$$\int_0^\infty e^{-x^2} dx = \frac{\sqrt{\pi}}{2}$$</div></li>
            </ul>
            <p class="mt-2"><strong>注意:</strong> JSON字符串中的反斜杠 <code>\</code> 需要转义为 <code>\\</code>。例如, LaTeX命令 <code>\frac</code> 在JSON字符串中应写作 <code>\\frac</code>。</p>
        </div>
    </div>
</div>

<!-- 消息提示框容器 -->
<div id="toast-container" class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 1100"></div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.2/lib/codemirror.js"></script>
<script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.2/mode/javascript/javascript.js"></script>
<script src="https://cdn.jsdelivr.net/npm/marked@4.0.0/marked.min.js"></script>
<script src="/static/js/common.js"></script>
<script src="/static/js/toast.js"></script>
<script src="/static/js/upload-topics.js"></script>
<script src="/static/js/user-upload-statistics.js"></script>
</body>
</html>