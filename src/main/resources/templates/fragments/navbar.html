<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <!-- 这个文件只包含片段，不会被直接访问 -->
</head>
<body>
    <!--  通用导航栏片段 -->
    <nav class="navbar" th:fragment="navbar(activePage)">
        <div class="nav-brand">
            <a href="/">Maizi EDU</a>
        </div>
        <div class="nav-menu">
            <a href="/main/chat" class="nav-item" th:classappend="${activePage == 'chat'} ? 'active' : ''">出题</a>
            <a href="/topics/upload-topics" class="nav-item" th:classappend="${activePage == 'upload'} ? 'active' : ''">上传</a>
            <a href="/paper/generate" class="nav-item" th:classappend="${activePage == 'generate'} ? 'active' : ''">组卷</a>
            <a href="/paper-history" class="nav-item" th:classappend="${activePage == 'history'} ? 'active' : ''">历史试卷</a>
            <a href="/main/books" class="nav-item" th:classappend="${activePage == 'books'} ? 'active' : ''">教材资源</a>
            <a href="/paper-configs" class="nav-item" th:classappend="${activePage == 'configs'} ? 'active' : ''">配置管理</a>
        </div>
        <div class="nav-user">
            <div class="user-info">
                <img src="/static/images/default-avatar.png" alt="avatar" class="avatar">
                <span class="username">加载中...</span>
                <i class="fas fa-chevron-down dropdown-arrow"></i>
            </div>
            <div class="dropdown-menu">
                <div class="dropdown-header">
                    <img src="/static/images/default-avatar.png" alt="avatar" class="dropdown-avatar">
                    <div class="dropdown-user-info">
                        <div class="dropdown-username">加载中...</div>
                        <div class="dropdown-email">加载中...</div>
                    </div>
                </div>
                <div class="dropdown-divider"></div>
                <a href="/user/profile" class="dropdown-item">
                    <i class="fas fa-user me-2"></i>个人信息
                </a>
                <a href="/paper-configs" class="dropdown-item">
                    <i class="fas fa-cog me-2"></i>配置管理
                </a>
                <a href="/main/books" class="dropdown-item">
                    <i class="fas fa-book me-2"></i>教材资源
                </a>
                <a href="/paper-history" class="dropdown-item">
                    <i class="fas fa-history me-2"></i>历史试卷
                </a>
                <div class="dropdown-divider"></div>
                <a href="#" id="logout" class="dropdown-item text-danger">
                    <i class="fas fa-sign-out-alt me-2"></i>退出登录
                </a>
            </div>
        </div>
    </nav>

    <!--  用户信息编辑模态框 -->
    <div class="modal fade" id="userProfileModal" tabindex="-1" aria-labelledby="userProfileModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userProfileModalLabel">
                        <i class="fas fa-user-edit me-2"></i>编辑个人信息
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="userProfileForm">
                        <div class="row">
                            <!-- 头像上传区域 -->
                            <div class="col-md-4 text-center">
                                <div class="avatar-upload-section">
                                    <div class="avatar-preview">
                                        <img id="previewAvatar" src="/static/images/default-avatar.png" alt="头像预览" class="preview-img">
                                        <div class="avatar-overlay">
                                            <i class="fas fa-camera"></i>
                                            <span>点击更换</span>
                                        </div>
                                    </div>
                                    <input type="file" id="avatarInput" accept="image/*" style="display: none;">
                                    <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="$('#avatarInput').click()">
                                        <i class="fas fa-upload me-1"></i>上传头像
                                    </button>
                                    <small class="text-muted d-block mt-1">支持 JPG、PNG 格式，大小不超过 2MB</small>
                                </div>
                            </div>
                            
                            <!-- 用户信息表单 -->
                            <div class="col-md-8">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="editUsername" class="form-label">用户名</label>
                                        <input type="text" class="form-control" id="editUsername" name="username" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="editEmail" class="form-label">邮箱</label>
                                        <input type="email" class="form-control" id="editEmail" name="email">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="editPhone" class="form-label">手机号</label>
                                        <input type="tel" class="form-control" id="editPhone" name="phone">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="editRealName" class="form-label">真实姓名</label>
                                        <input type="text" class="form-control" id="editRealName" name="realName">
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label for="editBio" class="form-label">个人简介</label>
                                        <textarea class="form-control" id="editBio" name="bio" rows="3" placeholder="介绍一下自己..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" id="saveUserProfile">
                        <i class="fas fa-save me-1"></i>保存更改
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
