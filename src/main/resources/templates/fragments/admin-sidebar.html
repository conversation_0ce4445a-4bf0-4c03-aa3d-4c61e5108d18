<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <style>
        .sidebar {
            background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
            border-right: 1px solid #dee2e6;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            width: 250px;
        }
        
        .sidebar.collapsed {
            width: 60px;
        }
        
        .sidebar.collapsed .nav-link-text,
        .sidebar.collapsed .sidebar-divider,
        .sidebar.collapsed .sidebar-heading {
            opacity: 0;
            visibility: hidden;
        }
        
        .sidebar.collapsed .nav-link {
            padding: 0.75rem 0.5rem;
            text-align: center;
        }
        
        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            margin: 0.125rem 0.5rem;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            white-space: nowrap;
        }
        
        .sidebar .nav-link:hover {
            background-color: #e9ecef;
            color: #0d6efd;
            border-left-color: #0d6efd;
            transform: translateX(2px);
        }
        
        .sidebar .nav-link.active {
            background-color: #0d6efd;
            color: white;
            border-left-color: #0a58ca;
            box-shadow: 0 2px 4px rgba(13, 110, 253, 0.3);
        }
        
        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
        }
        
        .nav-link-text {
            transition: all 0.3s ease;
        }
        
        .sidebar-divider {
            border-color: #dee2e6;
            margin: 1rem 0;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link small {
            font-size: 0.8rem;
            color: #6c757d;
        }
        
        .sidebar .nav-link:hover small {
            color: #0d6efd;
        }
        
        .sidebar-toggle {
            position: absolute;
            top: 10px;
            right: -15px;
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .sidebar-toggle:hover {
            background: #f8f9fa;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .sidebar-toggle i {
            color: #495057;
            font-size: 12px;
            transition: transform 0.3s ease;
        }
        
        .sidebar.collapsed .sidebar-toggle i {
            transform: rotate(180deg);
        }

        /* 响应式调整 */
        @media (max-width: 767.98px) {
            .sidebar {
                width: 100%;
                position: fixed;
                height: 100vh;
                z-index: 1040;
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .sidebar-toggle {
                display: none;
            }
        }

        /* Tooltip样式 */
        .sidebar.collapsed .nav-item {
            position: relative;
        }
        
        .sidebar.collapsed .nav-link::after {
            content: attr(data-tooltip);
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            margin-left: 10px;
            z-index: 1050;
        }
        
        .sidebar.collapsed .nav-link:hover::after {
            opacity: 1;
            visibility: visible;
        }
    </style>
</head>
<body>

<!-- 管理员侧边导航栏 Fragment -->
<nav th:fragment="admin-sidebar" id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
    <!-- 折叠/展开按钮 -->
    <div class="sidebar-toggle" onclick="toggleSidebar()" title="折叠/展开侧边栏">
        <i class="fas fa-chevron-left"></i>
    </div>
    
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="/admin/dashboard" 
                   th:classappend="${activeTab == 'dashboard'} ? 'active' : ''"
                   data-tooltip="仪表板">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    <span class="nav-link-text">仪表板</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/users" 
                   th:classappend="${activeTab == 'users'} ? 'active' : ''"
                   data-tooltip="人员管理">
                    <i class="fas fa-users me-2"></i>
                    <span class="nav-link-text">人员管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/topics" 
                   th:classappend="${activeTab == 'topics'} ? 'active' : ''"
                   data-tooltip="题目管理">
                    <i class="fas fa-book me-2"></i>
                    <span class="nav-link-text">题目管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/audit" 
                   th:classappend="${activeTab == 'audit'} ? 'active' : ''"
                   data-tooltip="审核管理">
                    <i class="fas fa-clipboard-check me-2"></i>
                    <span class="nav-link-text">审核管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/correction/approval" 
                   th:classappend="${activeTab == 'correction'} ? 'active' : ''"
                   data-tooltip="AI修正审核">
                    <i class="fas fa-check-circle me-2"></i>
                    <span class="nav-link-text">AI修正审核</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/user-upload-stats" 
                   th:classappend="${activeTab == 'user-upload-stats'} ? 'active' : ''"
                   data-tooltip="用户上传统计">
                    <i class="fas fa-chart-line me-2"></i>
                    <span class="nav-link-text">用户上传统计</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/knowledge" 
                   th:classappend="${activeTab == 'knowledge'} ? 'active' : ''"
                   data-tooltip="知识点管理">
                    <i class="fas fa-sitemap me-2"></i>
                    <span class="nav-link-text">知识点管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/exams" 
                   th:classappend="${activeTab == 'exams'} ? 'active' : ''"
                   data-tooltip="考试管理">
                    <i class="fas fa-clipboard-list me-2"></i>
                    <span class="nav-link-text">考试管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/reports" 
                   th:classappend="${activeTab == 'reports'} ? 'active' : ''"
                   data-tooltip="数据报表">
                    <i class="fas fa-chart-bar me-2"></i>
                    <span class="nav-link-text">数据报表</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/system" 
                   th:classappend="${activeTab == 'system'} ? 'active' : ''"
                   data-tooltip="系统监控">
                    <i class="fas fa-server me-2"></i>
                    <span class="nav-link-text">系统监控</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/algorithm-monitor" 
                   th:classappend="${activeTab == 'algorithm-monitor'} ? 'active' : ''"
                   data-tooltip="算法监控">
                    <i class="fas fa-dna me-2"></i>
                    <span class="nav-link-text">算法监控</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/logs" 
                   th:classappend="${activeTab == 'logs'} ? 'active' : ''"
                   data-tooltip="日志管理">
                    <i class="fas fa-file-alt me-2"></i>
                    <span class="nav-link-text">日志管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/settings" 
                   th:classappend="${activeTab == 'settings'} ? 'active' : ''"
                   data-tooltip="系统设置">
                    <i class="fas fa-cog me-2"></i>
                    <span class="nav-link-text">系统设置</span>
                </a>
            </li>
            
            <!-- 分隔线 -->
            <hr class="sidebar-divider my-3">
            
            <!-- 调试工具 (可选) -->
            <li class="nav-item">
                <a class="nav-link" href="/admin/user-debug" 
                   th:classappend="${activeTab == 'debug'} ? 'active' : ''"
                   data-tooltip="用户调试">
                    <i class="fas fa-bug me-2"></i>
                    <span class="nav-link-text"><small>用户调试</small></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/audit-debug" 
                   th:classappend="${activeTab == 'audit-debug'} ? 'active' : ''"
                   data-tooltip="审核调试">
                    <i class="fas fa-search me-2"></i>
                    <span class="nav-link-text"><small>审核调试</small></span>
                </a>
            </li>
        </ul>
    </div>
</nav>



</body>
</html>