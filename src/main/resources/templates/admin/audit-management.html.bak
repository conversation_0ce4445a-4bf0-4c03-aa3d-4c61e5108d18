<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能审核管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .audit-stats .card {
            transition: transform 0.2s;
        }
        .audit-stats .card:hover {
            transform: translateY(-2px);
        }
        .audit-toolbar {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .view-mode-toggle {
            border-radius: 20px;
        }
        .user-card {
            transition: all 0.3s ease;
        }
        .user-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        .audit-item {
            border-left: 4px solid #dee2e6;
            transition: border-color 0.2s;
        }
        .audit-item:hover {
            border-left-color: #007bff;
        }
        .smart-audit-indicator {
            position: relative;
            overflow: hidden;
        }
        .smart-audit-indicator::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shimmer 2s infinite;
        }
        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        /* 新增：固定分页容器样式 */
        .audit-tab-container {
            display: flex;
            flex-direction: column;
            height: 70vh; /* 固定高度，约70%视口高度 */
            min-height: 500px;
        }
        
        .audit-content-area {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 10px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: white;
        }
        
        .audit-content-area::-webkit-scrollbar {
            width: 8px;
        }
        
        .audit-content-area::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        
        .audit-content-area::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }
        
        .audit-content-area::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        .audit-pagination-fixed {
            flex-shrink: 0;
            padding: 10px 0;
            background: white;
            border-top: 1px solid #dee2e6;
            border-radius: 0 0 8px 8px;
            position: sticky;
            bottom: 0;
            z-index: 10;
        }
        
        .audit-stats-container {
            flex-shrink: 0;
            margin-bottom: 10px;
        }
        
        /* 表格优化 */
        .audit-content-area .table {
            margin-bottom: 0;
        }
        
        .audit-content-area .table thead th {
            background-color: #f8f9fa;
            border-top: none;
            position: sticky;
            top: 0;
            z-index: 5;
        }
        
        .audit-content-area .table tbody tr:hover {
            background-color: rgba(0,123,255,0.1);
        }
        
        /* 优化加载状态 */
        .audit-content-area .loading-overlay {
            border-radius: 8px;
        }
        
        /* 空状态优化 */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #dee2e6;
        }
        
        /* 分页组件优化 */
        .audit-pagination-fixed .pagination {
            margin-bottom: 0;
            display: flex;
            align-items: center;
        }
        
        .audit-pagination-fixed .page-link {
            border-radius: 6px;
            margin: 0 2px;
            border: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .audit-pagination-fixed .page-item.active .page-link {
            background-color: #007bff;
            border-color: #007bff;
        }
        
        .audit-pagination-fixed .page-item.disabled .page-link {
            background-color: #f8f9fa;
            border-color: #dee2e6;
            color: #6c757d;
        }
        
        .audit-pagination-fixed .page-item:last-child {
            margin-left: auto;
        }
        
        /* 响应式优化 */
        @media (max-height: 768px) {
            .audit-tab-container {
                height: 60vh;
                min-height: 400px;
            }
        }
        
        @media (max-width: 768px) {
            .audit-tab-container {
                height: 50vh;
                min-height: 350px;
            }
            
            .audit-content-area {
                border-radius: 4px;
            }
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-gavel text-primary"></i>
                        智能审核管理系统
                    </h1>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" onclick="adminAudit.refreshAuditData()" title="刷新数据">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="exportAuditData()" title="导出数据">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="showGroupAuditHelp()" title="帮助">
                            <i class="fas fa-question-circle"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 审核统计 -->
        <div class="row mb-4 audit-stats" id="auditStatsContainer">
            <!-- 统计卡片将通过JavaScript动态生成 -->
        </div>

        <!-- 工具栏 -->
        <div class="audit-toolbar">
            <div class="row g-3 align-items-center">
                <!-- 搜索框 -->
                <div class="col-md-3">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="auditSearchInput" 
                               placeholder="搜索题目标题..." onkeyup="adminAudit.searchAudits()">
                    </div>
                </div>
                
                <!-- 提交者筛选 -->
                <div class="col-md-2">
                    <select class="form-select" id="submitterFilter" onchange="adminAudit.loadPendingAudits()">
                        <option value="">所有提交者</option>
                        <!-- 动态填充提交者列表 -->
                    </select>
                </div>
                
                <!-- 知识点筛选 -->
                <div class="col-md-2">
                    <select class="form-select" id="knowledgeFilter" onchange="adminAudit.loadPendingAudits()">
                        <option value="">所有知识点</option>
                        <!-- 动态填充知识点列表 -->
                    </select>
                </div>
                
                <!-- 视图模式切换 -->
                <div class="col-md-2">
                    <select class="form-select view-mode-toggle" id="viewModeSelect" onchange="adminAudit.loadPendingAudits()">
                        <option value="list">列表视图</option>
                        <option value="group">分组视图</option>
                    </select>
                </div>
                
                <!-- 智能工具 -->
                <div class="col-md-3">
                    <div class="btn-group w-100" role="group">
                        <button class="btn btn-outline-warning smart-audit-indicator" onclick="adminAudit.smartAudit()" title="AI智能审核">
                            <i class="fas fa-robot"></i> 智能审核
                        </button>
                        <button class="btn btn-outline-info" onclick="adminAudit.showBulkOperations()" title="批量操作">
                            <i class="fas fa-tasks"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="row">
            <div class="col-12">
                <!-- 导航标签 -->
                <ul class="nav nav-tabs mb-4" id="auditTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="pending-tab" data-bs-toggle="tab" 
                                data-bs-target="#pending" type="button" role="tab" onclick="adminAudit.switchTab('pending')">
                            <i class="fas fa-clock text-warning"></i>
                            待审核 <span class="badge bg-warning ms-1" id="pendingCount">0</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="approved-tab" data-bs-toggle="tab" 
                                data-bs-target="#approved" type="button" role="tab" onclick="adminAudit.switchTab('approved')">
                            <i class="fas fa-check text-success"></i>
                            已通过 <span class="badge bg-success ms-1" id="approvedCount">0</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="rejected-tab" data-bs-toggle="tab" 
                                data-bs-target="#rejected" type="button" role="tab" onclick="adminAudit.switchTab('rejected')">
                            <i class="fas fa-times text-danger"></i>
                            已拒绝 <span class="badge bg-danger ms-1" id="rejectedCount">0</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="records-tab" data-bs-toggle="tab" 
                                data-bs-target="#records" type="button" role="tab" onclick="adminAudit.switchTab('records')">
                            <i class="fas fa-history text-info"></i>
                            我的记录 <span class="badge bg-info ms-1" id="recordsCount">0</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="group-audit-tab" data-bs-toggle="tab" 
                                data-bs-target="#group-audit" type="button" role="tab" onclick="adminAudit.switchTab('group-audit')">
                            <i class="fas fa-users text-primary"></i>
                            分组审核
                        </button>
                    </li>
                </ul>

                <!-- 标签页内容 -->
                <div class="tab-content" id="auditTabContent">
                    <!-- 待审核标签页 -->
                    <div class="tab-pane fade show active" id="pending" role="tabpanel">
                        <div class="audit-tab-container">
                            <!-- 待审核统计 -->
                            <div id="pendingStatsContainer" class="audit-stats-container">
                                <!-- 统计信息将通过JavaScript动态生成 -->
                            </div>
                            
                            <!-- 待审核内容容器 -->
                            <div id="pendingAuditsContainer" class="audit-content-area position-relative">
                                <div class="loading-overlay d-none" id="pendingLoading">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                                <!-- 内容将通过JavaScript动态生成 -->
                            </div>
                            
                            <!-- 分页 -->
                            <nav aria-label="待审核分页" class="audit-pagination-fixed">
                                <ul class="pagination justify-content-center mb-0" id="pendingPagination">
                                    <!-- 分页将通过JavaScript动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>

                    <!-- 已通过标签页 -->
                    <div class="tab-pane fade" id="approved" role="tabpanel">
                        <div class="audit-tab-container">
                            <div id="approvedAuditsContainer" class="audit-content-area position-relative">
                                <div class="loading-overlay d-none" id="approvedLoading">
                                    <div class="spinner-border text-success" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                            <nav aria-label="已通过分页" class="audit-pagination-fixed">
                                <ul class="pagination justify-content-center mb-0" id="approvedPagination"></ul>
                            </nav>
                        </div>
                    </div>

                    <!-- 已拒绝标签页 -->
                    <div class="tab-pane fade" id="rejected" role="tabpanel">
                        <div class="audit-tab-container">
                            <div id="rejectedAuditsContainer" class="audit-content-area position-relative">
                                <div class="loading-overlay d-none" id="rejectedLoading">
                                    <div class="spinner-border text-danger" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                            <nav aria-label="已拒绝分页" class="audit-pagination-fixed">
                                <ul class="pagination justify-content-center mb-0" id="rejectedPagination"></ul>
                            </nav>
                        </div>
                    </div>

                    <!-- 审核记录标签页 -->
                    <div class="tab-pane fade" id="records" role="tabpanel">
                        <div class="audit-tab-container">
                            <div id="auditorRecordsContainer" class="audit-content-area position-relative">
                                <div class="loading-overlay d-none" id="recordsLoading">
                                    <div class="spinner-border text-info" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                            <nav aria-label="审核记录分页" class="audit-pagination-fixed">
                                <ul class="pagination justify-content-center mb-0" id="auditorRecordsPagination"></ul>
                            </nav>
                        </div>
                    </div>

                    <!-- 分组审核标签页 -->
                    <div class="tab-pane fade" id="group-audit" role="tabpanel">
                        <div class="audit-tab-container">
                            <div id="groupAuditContainer" class="audit-content-area position-relative">
                                <div class="loading-overlay d-none" id="groupAuditLoading">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                            <nav aria-label="分组审核分页" class="audit-pagination-fixed">
                                <ul class="pagination justify-content-center mb-0" id="groupAuditPagination"></ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 审核模态框 -->
    <div class="modal fade" id="auditModal" tabindex="-1" aria-labelledby="auditModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="auditModalLabel">题目审核</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="auditForm">
                    <div class="modal-body">
                        <input type="hidden" id="auditId" name="auditId">
                        
                        <!-- 题目内容显示区域 -->
                        <div id="auditTopicContent" class="mb-4">
                            <!-- 内容将通过JavaScript动态填充 -->
                        </div>
                        
                        <!-- 审核选项 -->
                        <div class="mb-3">
                            <label class="form-label">审核结果 <span class="text-danger">*</span></label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="auditResult" id="approve" value="1" required>
                                <label class="form-check-label text-success" for="approve">
                                    <i class="fas fa-check"></i> 通过
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="auditResult" id="reject" value="2" required>
                                <label class="form-check-label text-danger" for="reject">
                                    <i class="fas fa-times"></i> 拒绝
                                </label>
                            </div>
                        </div>
                        
                        <!-- 审核意见 -->
                        <div class="mb-3">
                            <label for="auditComment" class="form-label">审核意见</label>
                            <textarea class="form-control" id="auditComment" name="auditComment" rows="3" 
                                      placeholder="请填写审核意见，拒绝时必填..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="submitAudit()">提交审核</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 批量审核模态框 -->
    <div class="modal fade" id="batchAuditModal" tabindex="-1" aria-labelledby="batchAuditModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="batchAuditModalLabel">批量审核</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="batchAuditForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">批量操作 <span class="text-danger">*</span></label>
                            <select class="form-select" name="batchAction" required>
                                <option value="">请选择操作</option>
                                <option value="approve">批量通过</option>
                                <option value="reject">批量拒绝</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="batchComment" class="form-label">批量意见</label>
                            <textarea class="form-control" id="batchComment" name="batchComment" rows="3" 
                                      placeholder="批量操作的统一意见..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="submitBatchAudit()">执行批量操作</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 脚本引入 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script th:src="@{/assets/js/admin-audit-management.js}"></script>
</body>
</html> 