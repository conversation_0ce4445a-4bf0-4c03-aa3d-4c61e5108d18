<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员仪表板 - 麦子教育系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/admin/assets/css/base.css">
    <link rel="stylesheet" href="/admin/assets/css/admin-dashboard.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin/dashboard">
                <i class="fas fa-graduation-cap me-2"></i>
                <span class="brand-text">麦子教育管理系统</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="notificationDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <span class="badge bg-danger">3</span>
                        </a>
                        <ul class="dropdown-menu notification-dropdown">
                            <li><h6 class="dropdown-header">通知</h6></li>
                            <li><a class="dropdown-item notification-item" href="#">
                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                系统将于今晚维护
                            </a></li>
                            <li><a class="dropdown-item notification-item" href="#">
                                <i class="fas fa-user-plus text-info me-2"></i>
                                新用户注册
                            </a></li>
                            <li><a class="dropdown-item notification-item" href="#">
                                <i class="fas fa-file text-success me-2"></i>
                                新的修正记录待审核
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i>
                            管理员
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                                                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 侧边栏 -->
    <div th:replace="fragments/admin-sidebar :: admin-sidebar"></div>

    <!-- 主要内容区域 -->
    <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">仪表板</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-download"></i> 导出报告
                    </button>
                </div>
                <button type="button" class="btn btn-sm btn-primary" onclick="refreshDashboard()">
                    <i class="fas fa-sync"></i> 刷新
                </button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">总用户数</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalUsers">1,257</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">今日活跃用户</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="activeUsers">342</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-user-check fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">待审核项目</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="pendingApprovals">18</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">系统负载</div>
                                <div class="row no-gutters align-items-center">
                                    <div class="col-auto">
                                        <div class="h5 mb-0 mr-3 font-weight-bold text-gray-800">68%</div>
                                    </div>
                                    <div class="col">
                                        <div class="progress progress-sm mr-2">
                                            <div class="progress-bar bg-info" role="progressbar" style="width: 68%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-server fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表和活动 -->
        <div class="row">
            <!-- 用户增长图表 -->
            <div class="col-xl-8 col-lg-7">
                <div class="card">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">用户增长趋势</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="userGrowthChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近活动 -->
            <div class="col-xl-4 col-lg-5">
                <div class="card">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">最近活动</h6>
                    </div>
                    <div class="card-body">
                        <div class="activity-list">
                            <div class="activity-item">
                                <div class="activity-icon bg-success">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">新用户注册</div>
                                    <div class="activity-description">用户 zhangsan 完成注册</div>
                                    <div class="activity-time">2分钟前</div>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon bg-info">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">审核通过</div>
                                    <div class="activity-description">修正记录 #1234 审核通过</div>
                                    <div class="activity-time">5分钟前</div>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon bg-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">系统警告</div>
                                    <div class="activity-description">磁盘使用率达到85%</div>
                                    <div class="activity-time">10分钟前</div>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon bg-primary">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">系统配置</div>
                                    <div class="activity-description">修改了审核配置</div>
                                    <div class="activity-time">15分钟前</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作和系统状态 -->
        <div class="row mt-4">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">快速操作</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <a href="/admin/users" class="btn btn-outline-primary btn-block">
                                    <i class="fas fa-users me-2"></i>用户管理
                                </a>
                            </div>
                            <div class="col-md-6 mb-3">
                                <a href="/admin/topics" class="btn btn-outline-success btn-block">
                                    <i class="fas fa-book me-2"></i>题目管理
                                </a>
                            </div>
                            <div class="col-md-6 mb-3">
                                <a href="/admin/audit" class="btn btn-outline-info btn-block">
                                    <i class="fas fa-shield-check me-2"></i>审核管理
                                </a>
                            </div>
                            <div class="col-md-6 mb-3">
                                <a href="/admin/settings" class="btn btn-outline-warning btn-block">
                                    <i class="fas fa-cog me-2"></i>系统设置
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">系统状态</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-sm-6 mb-3">
                                <div class="status-indicator online"></div>
                                <div>数据库</div>
                                <small class="text-success">运行正常</small>
                            </div>
                            <div class="col-sm-6 mb-3">
                                <div class="status-indicator online"></div>
                                <div>Redis缓存</div>
                                <small class="text-success">运行正常</small>
                            </div>
                            <div class="col-sm-6 mb-3">
                                <div class="status-indicator busy"></div>
                                <div>API服务</div>
                                <small class="text-warning">高负载</small>
                            </div>
                            <div class="col-sm-6 mb-3">
                                <div class="status-indicator online"></div>
                                <div>文件系统</div>
                                <small class="text-success">运行正常</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/admin/assets/libs/jquery-1.12.4/jquery.min.js"></script>
    <script>
        // 初始化图表
        function initCharts() {
            const ctx = document.getElementById('userGrowthChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '用户增长',
                        data: [120, 190, 300, 500, 700, 1257],
                        borderColor: 'rgb(102, 126, 234)',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 刷新仪表板数据
        function refreshDashboard() {
            // 这里可以添加AJAX请求来获取最新数据
            console.log('刷新仪表板数据...');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
        });
    </script>
</body>
</html> 