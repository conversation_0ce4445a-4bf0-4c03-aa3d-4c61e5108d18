<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题目管理 - 麦子教育系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link rel="stylesheet" href="/static/css/admin-layout.css">
    <link rel="stylesheet" href="/static/assets/css/topic-management.css">
    
    <!-- KaTeX CSS for math formula rendering -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css" integrity="sha384-GvrOXuhMATgEsSwCs4smul74iXGOixntILdUW9XmUC6+HX0sLNAK3q71HotJqlAn" crossorigin="anonymous">
    
    <!-- 题目表格独立滚动样式 -->
    <style>
        .topic-table-container {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            overflow: hidden;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            position: relative; /* 为状态指示器提供定位基准 */
        }
        
        /* 题目管理卡片布局 */
        .topics-management-card {
            height: calc(100vh - 420px); /* 根据页面其他元素动态计算高度 */
            min-height: 600px;
            display: flex;
            flex-direction: column;
        }
        
        .topics-management-card .card-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .search-filters-fixed {
            flex-shrink: 0; /* 筛选栏不缩放 */
        }
        
        .table-scrollable {
            flex: 1; /* 表格区域占满剩余空间 */
            overflow-y: auto !important;
            overflow-x: auto !important;
            min-height: 300px;
            max-height: 100%;
            position: relative;
            -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
        }
        
        /* 确保表格内容可以滚动 */
        .table-scrollable table {
            min-width: 1200px;
            width: 100%;
        }
        
        /* 滚动状态微妙提示 - 仅在滚动条不明显时显示淡淡的阴影 */
        .table-scrollable.has-vertical-scroll {
            box-shadow: inset 0 -8px 8px -8px rgba(0, 0, 0, 0.1);
        }
        
        .table-scrollable.has-horizontal-scroll {
            box-shadow: inset -8px 0 8px -8px rgba(0, 0, 0, 0.1);
        }
        
        .table-scrollable.has-vertical-scroll.has-horizontal-scroll {
            box-shadow: inset 0 -8px 8px -8px rgba(0, 0, 0, 0.1),
                        inset -8px 0 8px -8px rgba(0, 0, 0, 0.1);
        }
        
        /* 表格状态指示器样式 - 更加低调 */
        .table-status-indicator {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(248, 249, 250, 0.95);
            color: #6c757d;
            padding: 4px 8px;
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            z-index: 100;
            transition: all 0.3s ease;
            font-size: 0.75rem;
            opacity: 0.8;
        }
        
        .table-status-indicator:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        /* 题目详情模态框样式 */
        .topic-detail-view {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .topic-detail-view .options-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .topic-detail-view .option-item {
            transition: background-color 0.2s ease;
        }
        
        .topic-detail-view .option-item:hover {
            background-color: #f8f9fa !important;
        }
        
        .topic-detail-view .option-label {
            min-width: 30px;
            text-align: center;
        }
        
        .topic-detail-view .card {
            transition: box-shadow 0.2s ease;
        }
        
        .topic-detail-view .card:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        /* 题目表单样式优化 */
        .topic-form .option-input {
            border-radius: 0 !important;
        }
        
        .topic-form .input-group:first-child .option-input {
            border-top-left-radius: 0.375rem !important;
            border-top-right-radius: 0.375rem !important;
        }
        
        .topic-form .input-group:last-child .option-input {
            border-bottom-left-radius: 0.375rem !important;
            border-bottom-right-radius: 0.375rem !important;
        }
        
        /* 打印样式 */
        @media print {
            .modal-header, .modal-footer {
                display: none !important;
            }
            
            .modal-dialog {
                max-width: 100% !important;
                margin: 0 !important;
            }
            
            .modal-content {
                border: none !important;
                box-shadow: none !important;
            }
            
            .topic-detail-view {
                padding: 0 !important;
            }
            
            .card {
                border: 1px solid #000 !important;
                page-break-inside: avoid;
            }
        }
        
        /* Toast 通知样式 */
        .custom-toast {
            cursor: pointer;
        }
        
        .custom-toast .toast-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .custom-toast .toast-content i {
            font-size: 16px;
            flex-shrink: 0;
        }
        
        .custom-toast .toast-message {
            flex: 1;
            word-break: break-word;
        }
        
        .custom-toast:hover {
            opacity: 0.9;
            transform: translateX(-5px) !important;
        }
        
        .pagination-footer-fixed {
            flex-shrink: 0; /* 分页区域不缩放 */
            border-top: 1px solid #dee2e6;
        }
        
        .table-header-sticky th {
            position: sticky;
            top: 0;
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            z-index: 10;
            padding: 0.75rem;
            font-weight: 600;
            white-space: nowrap;
            box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.1);
        }
        
        /* 自定义滚动条样式 */
        .table-scrollable::-webkit-scrollbar {
            width: 12px;
            height: 12px;
        }
        
        .table-scrollable::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
        }
        
        .table-scrollable::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, #c1c1c1, #a1a1a1);
            border-radius: 6px;
            border: 1px solid #999;
            transition: all 0.3s ease;
        }
        
        .table-scrollable::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, #a1a1a1, #888);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .table-scrollable::-webkit-scrollbar-corner {
            background: #f1f1f1;
            border: 1px solid #e0e0e0;
        }
        
        /* Firefox滚动条样式 */
        .table-scrollable {
            scrollbar-width: thin;
            scrollbar-color: #c1c1c1 #f1f1f1;
        }
        
        /* 表格列宽度设置 */
        .topic-table-container th:nth-child(1),
        .topic-table-container td:nth-child(1) {
            width: 60px;
            text-align: center;
            min-width: 60px;
        }
        
        .topic-table-container th:nth-child(2),
        .topic-table-container td:nth-child(2) {
            width: 80px;
            text-align: center;
            min-width: 80px;
        }
        
        .topic-table-container th:nth-child(3),
        .topic-table-container td:nth-child(3) {
            width: 320px;
            min-width: 250px;
        }
        
        .topic-table-container th:nth-child(4),
        .topic-table-container td:nth-child(4) {
            width: 100px;
            text-align: center;
            min-width: 100px;
        }
        
        .topic-table-container th:nth-child(5),
        .topic-table-container td:nth-child(5) {
            width: 180px;
            min-width: 150px;
        }
        
        .topic-table-container th:nth-child(6),
        .topic-table-container td:nth-child(6) {
            width: 80px;
            text-align: center;
            min-width: 80px;
        }
        
        .topic-table-container th:nth-child(7),
        .topic-table-container td:nth-child(7) {
            width: 80px;
            text-align: center;
            min-width: 80px;
        }
        
        .topic-table-container th:nth-child(8),
        .topic-table-container td:nth-child(8) {
            width: 120px;
            min-width: 120px;
        }
        
        .topic-table-container th:nth-child(9),
        .topic-table-container td:nth-child(9) {
            width: 150px;
            min-width: 150px;
        }
        
        .topic-table-container th:nth-child(10),
        .topic-table-container td:nth-child(10) {
            width: 200px;
            text-align: center;
            min-width: 180px;
        }
        
        /* 表格内容优化 */
        .topic-title {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: block;
        }
        
        .action-buttons {
            white-space: nowrap;
        }
        
        .action-buttons .btn {
            margin-right: 4px;
            margin-bottom: 2px;
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        
        /* 表格行悬停效果 */
        .topic-table-container tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }
        
        /* 批量操作区域样式 */
        .batch-action-info {
            font-size: 0.9rem;
        }
        
        #batchActions .btn-group .btn {
            font-size: 0.85rem;
            padding: 0.375rem 0.75rem;
        }
        
        /* 分页区域优化 */
        .pagination-footer-fixed {
            background-color: #f8f9fa !important;
        }
        
        .pagination-footer-fixed .pagination .page-link {
            font-size: 0.9rem;
            padding: 0.5rem 0.75rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .topics-management-card {
                height: calc(100vh - 380px);
                min-height: 550px;
            }
            
            .knowledge-filter-container {
                max-height: 380px;
            }
            
            .knowledge-filter-card.expanded .knowledge-filter-container {
                max-height: 550px;
            }
        }
        
        @media (max-width: 992px) {
            .topics-management-card {
                height: calc(100vh - 350px);
                min-height: 500px;
            }
            
            .topic-table-container th:nth-child(3),
            .topic-table-container td:nth-child(3) {
                width: 250px;
                min-width: 200px;
            }
            
            .search-filters-fixed .col-md-2 {
                margin-bottom: 0.5rem;
            }
            
            #batchActions .d-flex {
                flex-direction: column;
                gap: 1rem;
            }
            
            #batchActions .btn-group {
                justify-content: center;
            }
            
            /* 中等屏幕上的知识点筛选面板 */
            .knowledge-filter-container {
                max-height: 350px;
            }
            
            .knowledge-filter-card.expanded .knowledge-filter-container {
                max-height: 500px;
            }
        }
        
        @media (max-width: 768px) {
            .topics-management-card {
                height: calc(100vh - 320px);
                min-height: 450px;
            }
            
            .topic-table-container th:nth-child(3),
            .topic-table-container td:nth-child(3) {
                width: 200px;
                min-width: 150px;
            }
            
            .action-buttons .btn {
                font-size: 0.7rem;
                padding: 0.2rem 0.4rem;
            }
            
            .pagination-footer-fixed .pagination .page-link {
                font-size: 0.8rem;
                padding: 0.375rem 0.5rem;
            }
            
            /* 平板设备上的知识点筛选 */
            .knowledge-filter-container {
                max-height: 300px;
            }
            
            .knowledge-filter-card.expanded .knowledge-filter-container {
                max-height: 450px;
            }
            
            /* 平板设备上重新排列布局 */
            .row.mb-3 .col-md-4,
            .row.mb-3 .col-md-8 {
                width: 100% !important;
                margin-bottom: 1rem;
            }
            
            .knowledge-filter-card.expanded .col-md-4,
            .knowledge-filter-card.expanded .col-md-6 {
                width: 100% !important;
            }
        }
        
        @media (max-width: 576px) {
            .topics-management-card {
                height: calc(100vh - 280px);
                min-height: 400px;
            }
            
            .search-filters-fixed {
                margin-bottom: 1rem !important;
            }
            
            .search-filters-fixed .col-md-2 {
                margin-bottom: 0.75rem;
            }
            
            .batch-action-info {
                text-align: center;
                margin-bottom: 0.5rem;
            }
            
            #batchActions .btn-group .btn {
                font-size: 0.8rem;
                padding: 0.25rem 0.5rem;
            }
            
            /* 手机设备上的知识点筛选 */
            .knowledge-filter-container {
                max-height: 250px;
            }
            
            .knowledge-filter-card.expanded .knowledge-filter-container {
                max-height: 400px;
            }
            
            .knowledge-filter-card.fullscreen {
                top: 60px;
                left: 10px;
                right: 10px;
                bottom: 10px;
            }
            
            /* 手机上优化按钮组 */
            .knowledge-filter-card .btn-group-sm .btn {
                font-size: 0.7rem;
                padding: 0.2rem 0.4rem;
            }
            
            /* 手机上的知识点列表优化 */
            .knowledge-sublist .nav-link {
                font-size: 0.8rem;
                padding: 0.2rem 0.4rem;
            }
            
            .knowledge-group-header {
                padding: 0.5rem;
            }
        }
        
        /* 加载状态优化 */
        .topic-table-container .spinner-border {
            width: 2rem;
            height: 2rem;
        }
        
        /* 空状态样式 */
        .topic-table-container .empty-state {
            color: #6c757d;
            font-style: italic;
        }
        
        .topic-table-container .empty-state i {
            font-size: 3rem;
            color: #dee2e6;
        }
        
        /* 滚动到底部提示 */
        .table-scrollable::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 10px;
            background: linear-gradient(to top, rgba(0,0,0,0.05), transparent);
            pointer-events: none;
            display: none;
        }
        
        .table-scrollable.has-more-content::after {
            display: block;
        }
        
        /* 优化全选复选框样式 */
        #selectAll {
            transform: scale(1.1);
        }
        
        /* 表格加载状态 */
        .table-loading {
            position: relative;
        }
        
        .table-loading::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            z-index: 999;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 知识点筛选容器样式 */
        .knowledge-filter-container {
            max-height: 420px; /* 增加默认高度 */
            overflow-y: auto;
            overflow-x: hidden;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .knowledge-filter-container:hover {
            box-shadow: inset 0 0 5px rgba(0,0,0,0.1);
        }
        
        /* 展开状态的筛选容器 */
        .knowledge-filter-card.expanded .knowledge-filter-container {
            max-height: 600px; /* 展开时的最大高度 */
        }
        
        /* 全屏展开状态 */
        .knowledge-filter-card.fullscreen {
            position: fixed;
            top: 80px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            z-index: 1050;
            margin: 0;
        }
        
        .knowledge-filter-card.fullscreen .knowledge-filter-container {
            max-height: calc(100vh - 200px);
        }
        
        /* 滚动提示 */
        .knowledge-filter-container::before {
            content: "滚动查看更多知识点";
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(to top, rgba(248,249,250,0.95), transparent);
            text-align: center;
            font-size: 0.7rem;
            color: #6c757d;
            padding: 8px 4px 4px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 10;
        }
        
        /* 当容器内容可滚动时显示提示 */
        .knowledge-filter-container.has-scroll::before {
            opacity: 0.8;
        }
        
        .knowledge-filter-container:hover::before {
            opacity: 0;
        }
        
        /* 知识点分组样式 */
        .knowledge-group-header {
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            margin-bottom: 0.25rem;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid #e9ecef;
        }
        
        .knowledge-group-header:hover {
            background-color: #e9ecef;
            border-color: #dee2e6;
        }
        
        .knowledge-group-header.active {
            background-color: #e7f3ff;
            border-color: #007bff;
            color: #007bff;
        }
        
        .knowledge-group-header .group-toggle-icon {
            transition: transform 0.2s ease;
            font-size: 0.8rem;
            width: 16px;
            text-align: center;
        }
        
        .knowledge-group-header.collapsed .group-toggle-icon {
            transform: rotate(-90deg);
        }
        
        .knowledge-sublist {
            margin-left: 1rem;
            border-left: 2px solid #e9ecef;
            padding-left: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .knowledge-sublist .nav-link {
            font-size: 0.85rem;
            padding: 0.25rem 0.5rem;
            color: #6c757d;
            border-radius: 0.25rem;
            margin-bottom: 0.1rem;
        }
        
        .knowledge-sublist .nav-link:hover {
            background-color: #f8f9fa;
            color: #495057;
        }
        
        .knowledge-sublist .nav-link.active {
            background-color: #007bff;
            color: white;
        }
        
        .knowledge-item-badge {
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
            border-radius: 0.75rem;
            background-color: #6c757d;
            color: white;
            margin-left: 0.25rem;
        }
        
        .knowledge-group-badge {
            font-size: 0.75rem;
            padding: 0.2rem 0.5rem;
            border-radius: 0.75rem;
            background-color: #007bff;
            color: white;
            margin-left: auto;
        }
        
        /* 滚动条样式 */
        .knowledge-filter-container::-webkit-scrollbar {
            width: 6px;
        }
        
        .knowledge-filter-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        
        .knowledge-filter-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        
        .knowledge-filter-container::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }
        
        /* 展开/折叠动画 */
        .knowledge-sublist {
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .knowledge-sublist.collapsed {
            max-height: 0;
            margin-bottom: 0;
            opacity: 0;
            padding-left: 0;
        }
        
        .knowledge-sublist:not(.collapsed) {
            max-height: 500px;
            opacity: 1;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <!-- 移动端菜单按钮 -->
            <button class="btn btn-outline-light d-md-none me-2" type="button" onclick="toggleMobileSidebar()" title="显示菜单">
                <i class="fas fa-bars"></i>
            </button>
            
            <a class="navbar-brand d-flex align-items-center" href="/admin/dashboard">
                <i class="fas fa-graduation-cap me-2"></i>
                <span class="brand-text">麦子教育管理系统</span>
            </a>
            
            <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <img src="/static/images/default-avatar.png" alt="Avatar" class="rounded-circle me-2" width="32" height="32">
                        <span id="currentUserName">管理员</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#" onclick="showUserProfile()"><i class="fas fa-user me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item" href="#" onclick="showSettings()"><i class="fas fa-cog me-2"></i>系统设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div th:replace="fragments/admin-sidebar :: admin-sidebar"></div>

            <!-- 主要内容区 -->
            <main class="col-md-10 ms-sm-auto col-lg-10 px-md-4">
                <!-- 知识点筛选面板 -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="card shadow-sm knowledge-filter-card">
                            <div class="card-header py-2">
                                <h6 class="card-title mb-0 d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-filter me-2"></i>按知识点筛选</span>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-info" onclick="toggleAllKnowledgeGroups()" title="展开/折叠所有分组">
                                            <i class="fas fa-expand-arrows-alt"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="clearKnowledgeFilter()" title="清除筛选">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        <button class="btn btn-outline-primary" onclick="toggleKnowledgeFilterExpand()" title="展开/收起筛选面板">
                                            <i class="fas fa-expand" id="expandIcon"></i>
                                        </button>
                                    </div>
                                </h6>
                            </div>
                            <div class="card-body py-2 knowledge-filter-container">
                                <ul class="nav flex-column mb-0" id="knowledgeGroupsList">
                                    <li class="nav-item">
                                        <a class="nav-link active py-1" href="#" onclick="filterByKnowledge('')" data-knowledge-id="">
                                            <i class="fas fa-list me-2"></i>全部题目
                                        </a>
                                    </li>
                                    <!-- 知识点分组将通过JavaScript动态加载 -->
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-8 main-content-col">
                        <!-- 页面标题 -->
                        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                            <h1 class="h2">
                                <i class="fas fa-file-alt me-2"></i>
                                题目管理
                            </h1>
                            <div class="btn-toolbar mb-2 mb-md-0">
                                <div class="btn-group me-2">
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportTopics()">
                                        <i class="fas fa-download"></i> 导出
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="showImportModal()">
                                        <i class="fas fa-upload"></i> 批量导入
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="downloadTemplate()">
                                        <i class="fas fa-file-excel"></i> 模板下载
                                    </button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-primary" onclick="showAddTopicModal()">
                                        <i class="fas fa-plus"></i> 添加题目
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-xl-2 col-md-4 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            总题目数
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalTopicsCount">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-2 col-md-4 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            单选题
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="choiceCount">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-dot-circle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-2 col-md-4 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            多选题
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="multipleCount">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-check-square fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-2 col-md-4 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            判断题
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="judgeCount">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-question-circle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-2 col-md-4 mb-4">
                        <div class="card border-left-secondary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                            填空题
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="fillCount">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-edit fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-2 col-md-4 mb-4">
                        <div class="card border-left-dark shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-dark text-uppercase mb-1">
                                            简答题
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="shortCount">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-align-left fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 题目列表 -->
                <div class="card shadow mb-4 topics-management-card">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">题目列表</h6>
                    </div>
                    <div class="card-body pb-2">
                        <!-- 搜索筛选栏 -->
                        <div class="row mb-3 search-filters-fixed">
                            <div class="col-md-2">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="searchInput" placeholder="搜索题目标题...">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                                    <input type="text" class="form-control" id="idsInput" placeholder="题目ID(多个用逗号分割)">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-control" id="typeFilter">
                                    <option value="">所有类型</option>
                                    <option value="choice">单选题</option>
                                    <option value="multiple">多选题</option>
                                    <option value="judge">判断题</option>
                                    <option value="fill">填空题</option>
                                    <option value="short">简答题</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-control" id="knowledgeFilter">
                                    <option value="">所有知识点</option>
                                    <!-- 知识点选项将通过JavaScript动态加载 -->
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-control" id="difficultyFilter">
                                    <option value="">所有难度</option>
                                    <option value="1">简单</option>
                                    <option value="2">中等</option>
                                    <option value="3">困难</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-primary me-1" onclick="searchTopics()">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                                    <i class="fas fa-undo"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 题目表格 -->
                        <div class="topic-table-container">
                            <div class="table-scrollable">
                                <table class="table table-hover mb-0">
                                    <thead class="table-header-sticky">
                                        <tr>
                                            <th>
                                                <input type="checkbox" id="selectAll">
                                            </th>
                                            <th>ID</th>
                                            <th>题目标题</th>
                                            <th>类型</th>
                                            <th>知识点</th>
                                            <th>难度</th>
                                            <th>分值</th>
                                            <th>标签</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="topicTableBody">
                                        <tr>
                                            <td colspan="10" class="text-center">
                                                <div class="py-3">
                                                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                                                    <p class="mt-2">加载中...</p>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- 数据状态指示器 - 仅在必要时显示 -->
                            <div class="table-status-indicator d-none" id="tableStatusIndicator">
                                <span id="tableStatusText"></span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 分页和批量操作固定在底部 -->
                    <div class="card-footer bg-light py-3 pagination-footer-fixed">
                        <!-- 批量操作 -->
                        <div id="batchActions" class="mb-3" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="batch-action-info">
                                    <span class="text-muted">已选择 <span id="selectedCount">0</span> 个题目</span>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="batchExportTopics()">
                                        <i class="fas fa-download"></i> 批量导出
                                    </button>
                                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="batchMoveTopics()">
                                        <i class="fas fa-exchange-alt"></i> 批量移动
                                    </button>
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="batchDeleteTopics()">
                                        <i class="fas fa-trash"></i> 批量删除
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 分页 -->
                        <nav id="pagination" style="display: none;">
                            <ul class="pagination justify-content-center mb-0">
                                <!-- 分页链接将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 添加/编辑题目模态框 -->
    <div class="modal fade" id="topicModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="topicModalTitle">添加题目</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="topicForm" class="topic-form">
                        <input type="hidden" id="topicId" name="topicId">
                        
                        <!-- 基本信息 -->
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">题目标题 <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="title" name="title" rows="2" required placeholder="请输入题目标题"></textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">题目类型 <span class="text-danger">*</span></label>
                                            <select class="form-control" id="type" name="type" required onchange="handleTypeChange()">
                                                <option value="">请选择</option>
                                                <option value="choice">单选题</option>
                                                <option value="multiple">多选题</option>
                                                <option value="judge">判断题</option>
                                                <option value="fill">填空题</option>
                                                <option value="short">简答题</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">知识点 <span class="text-danger">*</span></label>
                                            <select class="form-control" id="knowId" name="knowId" required>
                                                <option value="">请选择</option>
                                                <!-- 知识点选项将通过JavaScript动态加载 -->
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 选项区域 (仅选择题显示) -->
                        <div id="optionsSection" style="display: none;">
                            <div class="mb-3">
                                <label class="form-label">选项 <span class="text-danger">*</span></label>
                                <div id="optionsContainer">
                                    <!-- 选项将通过JavaScript动态生成 -->
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="addOption()">
                                    <i class="fas fa-plus"></i> 添加选项
                                </button>
                            </div>
                        </div>

                        <!-- 答案 -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">正确答案 <span class="text-danger">*</span></label>
                                    <div id="answerContainer">
                                        <input type="text" class="form-control" id="answer" name="answer" required>
                                    </div>
                                    <div class="form-text" id="answerHint">请输入正确答案</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">题目解析</label>
                                    <textarea class="form-control" id="parse" name="parse" rows="3" placeholder="请输入题目解析（可选）"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- 其他信息 -->
                        <div class="row">
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label class="form-label">分值</label>
                                    <input type="number" class="form-control" id="score" name="score" value="3" min="1" max="100">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label class="form-label">难度</label>
                                    <select class="form-control" id="difficulty" name="difficulty">
                                        <option value="1">简单</option>
                                        <option value="2" selected>中等</option>
                                        <option value="3">困难</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">标签</label>
                                    <input type="text" class="form-control" id="tags" name="tags" placeholder="多个标签用逗号分隔">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">题目来源</label>
                                    <input type="text" class="form-control" id="source" name="source" placeholder="如：教材、试卷名称等">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveTopic()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量导入模态框 -->
    <div class="modal fade" id="importModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量导入题目</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">选择文件 <span class="text-danger">*</span></label>
                                <input type="file" class="form-control" id="importFile" accept=".xlsx,.xls,.csv" required>
                                <div class="form-text">支持Excel和CSV格式，文件大小不超过10MB</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">默认知识点</label>
                                <select class="form-control" id="defaultKnowledgePoint">
                                    <option value="">请选择默认知识点</option>
                                    <!-- 知识点选项将通过JavaScript动态加载 -->
                                </select>
                                <div class="form-text">文件中未指定知识点的题目将使用此默认值</div>
                            </div>
                        </div>
                    </div>

                    <!-- 导入进度 -->
                    <div id="importProgress" style="display: none;">
                        <div class="mb-3">
                            <label class="form-label">导入进度</label>
                            <div class="progress">
                                <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="progressText" class="form-text">准备导入...</div>
                        </div>
                    </div>

                    <!-- 导入结果 -->
                    <div id="importResult" style="display: none;">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> 导入结果</h6>
                            <div id="importSummary"></div>
                        </div>
                        <div id="importErrors" style="display: none;">
                            <h6>错误详情：</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>行号</th>
                                            <th>错误信息</th>
                                        </tr>
                                    </thead>
                                    <tbody id="errorTableBody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 使用说明 -->
                    <div class="mt-3">
                        <h6>导入格式说明：</h6>
                        <ul class="small text-muted">
                            <li>Excel/CSV文件应包含以下列：题目标题、题目类型、知识点ID、选项、答案、解析、分值、难度、标签、来源</li>
                            <li>题目类型：choice(单选)、multiple(多选)、judge(判断)、fill(填空)、short(简答)</li>
                            <li>选项格式：A.选项1|B.选项2|C.选项3|D.选项4（选择题必填）</li>
                            <li>答案格式：单选填字母(如A)，多选填多个字母(如ABC)，判断填是/否</li>
                            <li>难度：1(简单)、2(中等)、3(困难)</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="startImport()" id="importBtn">开始导入</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 题目详情模态框 -->
    <div class="modal fade" id="topicDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">题目详情</h5>
                    <div class="ms-auto">
                        <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="printTopic()">
                            <i class="fas fa-print"></i> 打印
                        </button>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                </div>
                <div class="modal-body" id="topicDetailContent">
                    <!-- 题目详情内容将通过JavaScript动态生成 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-warning" onclick="editCurrentTopic()">
                        <i class="fas fa-edit"></i> 编辑题目
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/libs/jquery-1.12.4/jquery.min.js"></script>
    <script src="/static/js/admin-layout.js"></script>
    <script src="/static/assets/js/topic-management.js"></script>
    
    <!-- KaTeX JavaScript for math formula rendering -->
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js" integrity="sha384-cpW21h6RZv/phavutF+AuVYrr+dA8xD9zs6FwLpaCct6O9ctzYFfFr4dgmgccOTx" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/auto-render.min.js" integrity="sha384-+VBxd3r6XgURycqtZ117nYw44OOcIax56Z4dCRWbxyPt0Koah1uHoK0o4+/RRE05" crossorigin="anonymous"></script>
    <!-- KaTeX mhchem extension for chemistry formulas -->
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/mhchem.min.js" crossorigin="anonymous"></script>
    
    <!-- KaTeX initialization script -->
    <script>
        // 确保KaTeX在页面加载完成后可用
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟执行以确保所有内容都已加载
            setTimeout(function() {
                if (typeof renderMathInElement !== 'undefined') {
                    try {
                        // 为整个页面渲染数学公式
                        renderMathInElement(document.body, {
                            delimiters: [
                            {left: '$$', right: '$$', display: true},
                            {left: '$', right: '$', display: false},
                            {left: '\\(', right: '\\)', display: false},
                            {left: '\\[', right: '\\]', display: true},
                            // 集合类数学公式分隔符
                            {left: '\\{', right: '\\}', display: false},
                            // 化学公式分隔符
                            {left: '\\ce{', right: '}', display: false},
                            {left: '\\cee{', right: '}', display: true}
                        ],
                            throwOnError: false,
                            errorColor: '#cc0000',
                            strict: false,
                            // 启用 mhchem 扩展
                            trust: true,
                            macros: {
                                "\\ce": "\\ce",
                                "\\cee": "\\cee"
                            }
                        });
                        console.log('✅ KaTeX数学公式渲染完成');
                    } catch (e) {
                        console.warn('❌ KaTeX渲染失败:', e);
                    }
                } else {
                    console.warn('⚠️ KaTeX未加载，跳过数学公式渲染');
                }
            }, 500); // 延迟500ms确保DOM更新完成
        });
        
        // 全局函数：重新渲染数学公式（用于动态内容更新后）
        function renderMathFormulas(container) {
            if (typeof renderMathInElement !== 'undefined') {
                try {
                    const targetContainer = container || document.body;
                    renderMathInElement(targetContainer, {
                        delimiters: [
                            {left: '$$', right: '$$', display: true},
                            {left: '$', right: '$', display: false},
                            {left: '\\(', right: '\\)', display: false},
                            {left: '\\[', right: '\\]', display: true},
                            // 集合类数学公式分隔符
                            {left: '\\{', right: '\\}', display: false},
                            // 化学公式分隔符
                            {left: '\\ce{', right: '}', display: false},
                            {left: '\\cee{', right: '}', display: true}
                        ],
                        throwOnError: false,
                        errorColor: '#cc0000',
                        strict: false,
                        // 启用 mhchem 扩展
                        trust: true,
                        macros: {
                            "\\ce": "\\ce",
                            "\\cee": "\\cee"
                        }
                    });

                } catch (e) {
                    console.warn('动态内容KaTeX渲染失败:', e);
                }
            }
        }
    </script>
</body>
</html>