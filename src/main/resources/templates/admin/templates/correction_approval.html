<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI修正审核管理 - 麦子教育系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css" rel="stylesheet">
    <!-- KaTeX CSS for math formula rendering -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css" integrity="sha384-GvrOXuhMATgEsSwCs4smul74iXGOixntILdUW9XmUC6+HX0sLNAK3q71HotJqlAn" crossorigin="anonymous">
    <link rel="stylesheet" href="/assets/css/admin-dashboard.css">
    <style>
        /* 全局样式优化 */
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --shadow-light: 0 2px 10px rgba(0,0,0,0.08);
            --shadow-medium: 0 4px 20px rgba(0,0,0,0.12);
            --shadow-heavy: 0 8px 30px rgba(0,0,0,0.15);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        /* 审核项目卡片样式 */
        .approval-item { 
            border: none;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            transition: var(--transition);
            box-shadow: var(--shadow-light);
            overflow: hidden;
            position: relative;
        }
        
        .approval-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--primary-gradient);
            transition: var(--transition);
        }
        
        .approval-item:hover {
            box-shadow: var(--shadow-heavy);
            transform: translateY(-4px) scale(1.01);
        }
        
        .approval-item:hover::before {
            width: 6px;
        }
        
        /* 状态样式 */
        .status-pending::before { background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%); }
        .status-approved::before { background: var(--success-gradient); }
        .status-rejected::before { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }
        .status-expired::before { background: linear-gradient(135deg, #6c757d 0%, #495057 100%); }
        .status-withdrawn::before { background: var(--info-gradient); }
        
        /* 卡片头部美化 */
        .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 1rem 1.5rem;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        /* 修正详情样式 */
        .correction-detail { 
            max-height: 350px; 
            overflow-y: auto; 
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding: 1.5rem; 
            border-radius: var(--border-radius);
            border: 1px solid rgba(0,0,0,0.05);
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.06);
        }
        
        /* 配置信息美化 */
        .config-info {
            background: var(--primary-gradient);
            color: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            box-shadow: var(--shadow-medium);
            position: relative;
            overflow: hidden;
        }
        
        .config-info::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: shimmer 3s ease-in-out infinite;
        }
        
        @keyframes shimmer {
            0%, 100% { transform: rotate(0deg); }
            50% { transform: rotate(180deg); }
        }
        
        /* 对比展示样式优化 */
        .comparison-container {
            border: none;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-light);
            background: white;
        }
        
        .comparison-header {
            background: var(--primary-gradient);
            color: white;
            padding: 1rem 1.5rem;
            font-weight: 600;
            border: none;
        }
        
        .comparison-content {
            padding: 2rem;
        }
        
        .field-comparison {
            margin-bottom: 1.5rem;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            background: white;
            border: 1px solid rgba(0,0,0,0.05);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        
        .field-comparison::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            transition: var(--transition);
        }
        
        .field-comparison.changed {
            background: linear-gradient(135deg, #f8fff9 0%, #ffffff 100%);
            box-shadow: var(--shadow-light);
        }
        
        .field-comparison.changed::before {
            background: var(--success-gradient);
        }
        
        .field-comparison.unchanged {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }
        
        .field-comparison.unchanged::before {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }
        
        .field-comparison:hover {
            transform: translateX(4px);
            box-shadow: var(--shadow-medium);
        }
        
        .field-label {
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .field-label::before {
            content: '📝';
            font-size: 1.2rem;
        }
        
        .value-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }
        
        .original-value, .corrected-value {
            padding: 1.5rem;
            border-radius: var(--border-radius);
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.95em;
            line-height: 1.6;
            position: relative;
            transition: var(--transition);
        }
        
        .original-value {
            background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
            border: 2px solid #feb2b2;
            box-shadow: inset 0 2px 4px rgba(220, 53, 69, 0.1);
        }
        
        .original-value::before {
            content: '原始 📄';
            position: absolute;
            top: -10px;
            left: 15px;
            background: #dc3545;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .corrected-value {
            background: linear-gradient(135deg, #f0fff4 0%, #9ae6b4 100%);
            border: 2px solid #68d391;
            box-shadow: inset 0 2px 4px rgba(40, 167, 69, 0.1);
        }
        
        .corrected-value::before {
            content: '修正 ✨';
            position: absolute;
            top: -10px;
            left: 15px;
            background: #28a745;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .original-value:hover, .corrected-value:hover {
            transform: scale(1.02);
            box-shadow: var(--shadow-medium);
        }
        
        .diff-highlight {
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(253, 203, 110, 0.3);
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        /* 题目预览美化 */
        .topic-preview {
            max-height: 250px;
            overflow-y: auto;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 2px solid rgba(0,0,0,0.05);
            box-shadow: var(--shadow-light);
            transition: var(--transition);
        }
        
        .topic-preview:hover {
            box-shadow: var(--shadow-medium);
            border-color: rgba(102, 126, 234, 0.3);
        }
        
        /* 批量操作美化 */
        .batch-actions {
            background: var(--info-gradient);
            color: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            box-shadow: var(--shadow-medium);
            position: relative;
            overflow: hidden;
        }
        
        .batch-actions::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: slide 3s ease-in-out infinite;
        }
        
        @keyframes slide {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: 100%; }
        }
        
        /* 统计卡片美化 */
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: var(--transition);
            border: 1px solid rgba(0,0,0,0.05);
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }
        
        .stat-card:hover {
            transform: translateY(-8px) scale(1.03);
            box-shadow: var(--shadow-heavy);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-number.system-status {
            background: none;
            -webkit-background-clip: initial;
            -webkit-text-fill-color: initial;
            background-clip: initial;
            color: inherit;
        }
        
        .stat-number.system-status .badge {
            font-size: 1.2rem;
            padding: 0.6rem 1.2rem;
            border-radius: 25px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        /* 按钮组美化 */
        .action-buttons {
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;
        }
        
        .btn {
            border-radius: 8px;
            font-weight: 600;
            transition: var(--transition);
            border: none;
            box-shadow: var(--shadow-light);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }
        
        .btn-group-vertical .btn {
            margin-bottom: 0.5rem;
        }
        
        /* 搜索过滤器美化 */
        .search-filters {
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            margin-bottom: 2rem;
            border: 1px solid rgba(0,0,0,0.05);
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid rgba(0,0,0,0.1);
            transition: var(--transition);
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        /* 表格美化 */
        .table-responsive {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-light);
        }
        
        .table th {
            background: var(--primary-gradient);
            color: white;
            border: none;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .table td {
            border-color: rgba(0,0,0,0.05);
            vertical-align: middle;
        }
        
        .table tbody tr {
            transition: var(--transition);
        }
        
        .table tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.05);
            transform: scale(1.01);
        }
        
        /* 模态框美化 */
        .modal-xl {
            max-width: 95%;
        }
        
        .modal-content {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--shadow-heavy);
        }
        
        .modal-header {
            background: var(--primary-gradient);
            color: white;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            border: none;
        }
        
        /* 加载覆盖层 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.95);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }
        
        .spinner-border {
            width: 4rem;
            height: 4rem;
            border-width: 0.4rem;
            border-color: #667eea;
            border-right-color: transparent;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 页面加载动画 */
        .fade-in {
            animation: fadeIn 0.6s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 状态徽章美化 */
        .badge {
            border-radius: 20px;
            padding: 0.5rem 1rem;
            font-weight: 600;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: var(--shadow-light);
        }
        
        .badge.bg-warning {
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%) !important;
            color: #212529;
        }
        
        .badge.bg-success {
            background: var(--success-gradient) !important;
            color: white;
        }
        
        .badge.bg-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
            color: white;
        }
        
        .badge.bg-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
            color: white;
        }
        
        .badge.bg-info {
            background: var(--info-gradient) !important;
            color: white;
        }
        
        /* 导航栏美化 */
        .navbar {
            box-shadow: var(--shadow-medium);
            backdrop-filter: blur(10px);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .brand-text {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .dropdown-menu {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-heavy);
            backdrop-filter: blur(10px);
        }
        
        .dropdown-item {
            border-radius: 8px;
            margin: 0.25rem 0.5rem;
            transition: var(--transition);
        }
        
        .dropdown-item:hover {
            background: var(--primary-gradient);
            color: white;
            transform: translateX(4px);
        }
        
        /* 页面标题美化 */
        .border-bottom {
            border-bottom: 2px solid rgba(102, 126, 234, 0.2) !important;
        }
        
        .h2 {
            color: #2c3e50;
            font-weight: 700;
        }
        
        /* 工具栏按钮美化 */
        .btn-toolbar .btn {
            border-radius: 8px;
            font-weight: 600;
            transition: var(--transition);
            box-shadow: var(--shadow-light);
        }
        
        .btn-outline-secondary:hover {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border-color: transparent;
            transform: translateY(-2px);
        }
        
        .btn-outline-info:hover {
            background: var(--info-gradient);
            border-color: transparent;
            transform: translateY(-2px);
        }
        
        .btn-outline-success:hover {
            background: var(--success-gradient);
            border-color: transparent;
            transform: translateY(-2px);
        }
        
        /* 搜索框美化 */
        .search-filters .form-control,
        .search-filters .form-select {
            border: 2px solid rgba(0,0,0,0.1);
            border-radius: 10px;
            transition: var(--transition);
            background: white;
        }
        
        .search-filters .form-control:focus,
        .search-filters .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            transform: scale(1.02);
        }
        
        /* 表单标签美化 */
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        /* 响应式设计优化 */
        @media (max-width: 1200px) {
            .value-comparison {
                gap: 1rem;
            }
            
            .original-value, .corrected-value {
                padding: 1rem;
            }
        }
        
        @media (max-width: 768px) {
            :root {
                --border-radius: 8px;
            }
            
            .value-comparison {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .quick-stats {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .action-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .action-buttons .btn {
                width: 100%;
            }
            
            .config-info, .batch-actions, .search-filters {
                padding: 1.5rem;
            }
            
            .stat-card {
                padding: 1.5rem;
            }
            
            .stat-number {
                font-size: 2rem;
            }
            
            .comparison-content {
                padding: 1rem;
            }
            
            .field-comparison {
                padding: 1rem;
            }
            
            .original-value::before, .corrected-value::before {
                position: static;
                display: block;
                margin-bottom: 0.5rem;
                border-radius: 4px;
                text-align: center;
            }
            
            .search-filters .row {
                gap: 1rem;
            }
            
            .search-filters .col-md-3,
            .search-filters .col-md-2 {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }
        
        @media (max-width: 576px) {
            .navbar-brand {
                font-size: 1.2rem;
            }
            
            .h2 {
                font-size: 1.5rem;
            }
            
            .btn {
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }
            
            .config-info .row .col-md-4 {
                flex: 0 0 100%;
                max-width: 100%;
                margin-bottom: 0.5rem;
            }
        }
        
        /* 页面淡入动画增强 */
        body {
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
        }
        
        body.fade-in {
            opacity: 1;
        }
        
        /* 统计数字样式增强 */
        .stat-number {
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        /* 增强的搜索框样式 */
        .search-input-container {
            position: relative;
        }
        
        .search-input-container .form-control {
            padding-left: 3rem;
            border-radius: 25px;
            border: 2px solid transparent;
            background: linear-gradient(white, white) padding-box,
                        linear-gradient(135deg, var(--primary-color), var(--secondary-color)) border-box;
            transition: all 0.3s ease;
        }
        
        .search-input-container .form-control:focus {
            box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
            transform: translateY(-1px);
        }
        
        /* 优先级指示器增强 */
        .priority-indicator {
            position: relative;
            overflow: hidden;
        }
        
        .priority-indicator::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s ease;
        }
        
        .priority-indicator:hover::before {
            left: 100%;
        }
        
        /* 表格行悬停效果增强 */
        .table tbody tr {
            transition: all 0.3s ease;
        }
        
        .table tbody tr:hover {
            background: linear-gradient(135deg, rgba(74, 144, 226, 0.05), rgba(142, 68, 173, 0.05));
            transform: translateX(5px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        /* 模态框增强动画 */
        .modal.fade .modal-dialog {
            transform: scale(0.8) translateY(-50px);
            transition: all 0.3s ease;
        }
        
        .modal.show .modal-dialog {
            transform: scale(1) translateY(0);
        }
        
        /* 工具提示样式 */
        .tooltip-custom {
            position: relative;
            cursor: help;
        }
        
        .tooltip-custom::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .tooltip-custom:hover::after {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(-5px);
        }
        
        /* 进度条动画 */
        .progress-bar-animated {
            background: linear-gradient(45deg, 
                rgba(255,255,255,0.15) 25%, 
                transparent 25%, 
                transparent 50%, 
                rgba(255,255,255,0.15) 50%, 
                rgba(255,255,255,0.15) 75%, 
                transparent 75%, 
                transparent);
            background-size: 1rem 1rem;
            animation: progress-bar-stripes 1s linear infinite;
        }
        
        @keyframes progress-bar-stripes {
            0% {
                background-position: 1rem 0;
            }
            100% {
                background-position: 0 0;
            }
        }
        
        /* 脉冲动画 */
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }
        
        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 9999;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            max-width: 400px;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .notification.error {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="/admin/dashboard">
                <i class="fas fa-graduation-cap me-2"></i>
                <span class="brand-text">麦子教育管理系统</span>
            </a>
            
            <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
                <!-- 通知下拉菜单 -->
                <div class="nav-item dropdown me-3">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="notificationDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <span class="badge bg-danger ms-1" id="notificationBadge" style="display: none;">0</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                        <li><h6 class="dropdown-header">系统通知</h6></li>
                        <li><hr class="dropdown-divider"></li>
                        <li class="dropdown-item-text">暂无通知</li>
                    </ul>
                </div>
                
                <!-- 用户下拉菜单 -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <img src="/static/images/default-avatar.png" alt="Avatar" class="rounded-circle me-2" width="32" height="32">
                        <span id="currentUserName">管理员</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#" onclick="showUserProfile()"><i class="fas fa-user me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item" href="#" onclick="showSettings()"><i class="fas fa-cog me-2"></i>系统设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div th:replace="fragments/admin-sidebar :: admin-sidebar"></div>

            <!-- 主要内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- 页面标题 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-check-circle me-2"></i>
                        AI修正审核管理
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshData()">
                                <i class="fas fa-sync-alt"></i> 刷新数据
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="viewLogs()">
                                <i class="fas fa-file-text"></i> 查看日志
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 配置信息 -->
                <div class="config-info">
                    <h5><i class="fas fa-gear"></i> 系统配置</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <strong>审核模式:</strong> 
                            <span th:if="${config != null and config.approvalEnabled != null and config.approvalEnabled}" class="badge bg-warning">启用</span>
                            <span th:unless="${config != null and config.approvalEnabled != null and config.approvalEnabled}" class="badge bg-success">关闭</span>
                        </div>
                        <div class="col-md-4">
                            <strong>时间限制:</strong> 
                            <span th:if="${config != null and config.scheduleEnabled != null and config.scheduleEnabled}" class="badge bg-warning">启用</span>
                            <span th:unless="${config != null and config.scheduleEnabled != null and config.scheduleEnabled}" class="badge bg-success">关闭</span>
                        </div>
                        <div class="col-md-4">
                            <strong>审核超时:</strong> 
                            <span th:text="${config != null and config.autoRejectHours != null ? config.autoRejectHours : 24}">24</span> 小时
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="quick-stats">
                    <div class="stat-card">
                        <div class="stat-number text-warning" id="pendingCount" th:text="${statistics != null and statistics.pendingCount != null ? statistics.pendingCount : 0}">0</div>
                        <div class="stat-label">待审核</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-success" id="approvedCount" th:text="${statistics != null and statistics.approvedCount != null ? statistics.approvedCount : 0}">0</div>
                        <div class="stat-label">已通过</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-danger" id="rejectedCount" th:text="${statistics != null and statistics.rejectedCount != null ? statistics.rejectedCount : 0}">0</div>
                        <div class="stat-label">已拒绝</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-info" id="withdrawnCount" th:text="${statistics != null and statistics.withdrawnCount != null ? statistics.withdrawnCount : 0}">0</div>
                        <div class="stat-label">已回撤</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-secondary" id="expiredCount" th:text="${statistics != null and statistics.expiredCount != null ? statistics.expiredCount : 0}">0</div>
                        <div class="stat-label">已过期</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number system-status" id="systemStatus">
                            <span th:if="${statistics != null and statistics.inAllowedWindow != null and statistics.inAllowedWindow}" class="badge bg-success">运行中</span>
                            <span th:unless="${statistics != null and statistics.inAllowedWindow != null and statistics.inAllowedWindow}" class="badge bg-secondary">已停止</span>
                        </div>
                        <div class="stat-label">系统状态</div>
                    </div>
                </div>

                <!-- 搜索和过滤器 -->
                <div class="search-filters">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <label class="form-label mb-1">关键词搜索</label>
                            <input type="text" class="form-control" id="searchInput" 
                                   placeholder="搜索题目内容、审核人...">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label mb-1">审核状态</label>
                            <select class="form-select" id="statusFilter" onchange="handleFilter()">
                                <option value="">全部状态</option>
                                <option value="0" selected>待审核</option>
                                <option value="1">已通过</option>
                                <option value="2">已拒绝</option>
                                <option value="3">已过期</option>
                                <option value="4">已回撤</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label mb-1">题目类型</label>
                            <select class="form-select" id="typeFilter" onchange="handleFilter()">
                                <option value="">全部类型</option>
                                <option value="choice">单选题</option>
                                <option value="multiple">多选题</option>
                                <option value="judge">判断题</option>
                                <option value="fill">填空题</option>
                                <option value="short">简答题</option>
                                <option value="essay">论述题</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label mb-1">创建时间</label>
                            <input type="date" class="form-control" id="dateFilter" onchange="handleFilter()">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label mb-1">操作</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" onclick="loadPendingApprovals()">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                                <button class="btn btn-outline-secondary" onclick="resetFilters()">
                                    <i class="fas fa-undo"></i> 重置
                                </button>
                                <button class="btn btn-outline-success" onclick="exportData()">
                                    <i class="fas fa-download"></i> 导出
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 审核记录列表 -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-list-check"></i> AI修正审核记录
                                </h6>
                            </div>
                            <div class="col-md-6 text-end">
                                <button class="btn btn-sm btn-outline-primary" onclick="refreshData()">
                                    <i class="fas fa-sync-alt"></i> 刷新数据
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewLogs()">
                                    <i class="fas fa-file-text"></i> 查看日志
                                </button>
                                <button class="btn btn-sm btn-outline-success" onclick="addNewCorrection()">
                                    <i class="fas fa-plus"></i> 新增审核
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- 批量操作 -->
                        <div class="batch-actions" id="batchOperations" style="display: none;">
                            <div class="row align-items-center">
                                <div class="col-md-3">
                                    <div class="d-flex align-items-center">
                                        <input type="checkbox" class="form-check-input me-2" id="selectAll" onchange="toggleSelectAll()">
                                        <label class="form-check-label me-3" for="selectAll">全选</label>
                                        <span class="badge bg-light text-dark" id="selectedCount">已选择 0 项</span>
                                    </div>
                                </div>
                                <div class="col-md-9">
                                    <div class="action-buttons">
                                        <button class="btn btn-success" onclick="batchApprove()">
                                            <i class="fas fa-check-circle"></i> 批量通过
                                        </button>
                                        <button class="btn btn-danger" onclick="batchReject()">
                                            <i class="fas fa-times-circle"></i> 批量拒绝
                                        </button>
                                        <button class="btn btn-info" onclick="batchWithdraw()">
                                            <i class="fas fa-undo"></i> 批量回撤
                                        </button>
                                        <button class="btn btn-warning" onclick="batchEdit()">
                                            <i class="fas fa-edit"></i> 批量编辑
                                        </button>
                                        <button class="btn btn-secondary" onclick="batchDelete()">
                                            <i class="fas fa-trash"></i> 批量删除
                                        </button>
                                        <button class="btn btn-outline-light" onclick="clearSelection()">
                                            <i class="fas fa-times"></i> 清除选择
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div id="pendingList">
                            <!-- 动态加载的内容 -->
                        </div>
                        
                        <!-- 分页控件 -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <span class="me-2">每页显示:</span>
                                    <select class="form-select form-select-sm" style="width: auto;" id="pageSizeSelect" onchange="changePageSize()">
                                        <option value="5">5</option>
                                        <option value="10" selected>10</option>
                                        <option value="20">20</option>
                                        <option value="50">50</option>
                                    </select>
                                    <span class="ms-3 text-muted" id="pageInfo">显示第 1-10 项，共 0 项</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <nav aria-label="分页导航">
                                    <ul class="pagination pagination-sm justify-content-end mb-0" id="pagination">
                                        <!-- 动态生成分页按钮 -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-eye"></i> AI修正详情对比
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body position-relative">
                    <div id="detailContent">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载数据...</p>
                        </div>
                    </div>
                    <div class="loading-overlay" id="detailLoading" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-primary" onclick="printDetail()">
                        <i class="fas fa-print"></i> 打印
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="exportDetail()">
                        <i class="fas fa-download"></i> 导出
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 审核模态框 -->
    <div class="modal fade" id="approvalModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="approvalModalTitle">
                        <i class="fas fa-check-circle"></i> 审核修正
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="approvalForm">
                        <input type="hidden" id="approvalId">
                        <input type="hidden" id="approvalAction">
                        
                        <div class="mb-3">
                            <label for="approver" class="form-label">审核人 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="approver" required 
                                   placeholder="请输入审核人姓名">
                        </div>
                        
                        <div class="mb-3">
                            <label for="comment" class="form-label">审核意见</label>
                            <textarea class="form-control" id="comment" rows="4" 
                                      placeholder="请输入审核意见（拒绝时必填）"></textarea>
                        </div>
                        
                        <div class="mb-3" id="prioritySection" style="display: none;">
                            <label for="priority" class="form-label">优先级</label>
                            <select class="form-select" id="priority">
                                <option value="normal">普通</option>
                                <option value="high">高</option>
                                <option value="urgent">紧急</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitApproval()">确认</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑模态框 -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit"></i> 编辑审核记录
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editForm">
                        <input type="hidden" id="editId">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editApprover" class="form-label">审核人</label>
                                    <input type="text" class="form-control" id="editApprover" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editStatus" class="form-label">状态</label>
                                    <select class="form-select" id="editStatus">
                                        <option value="0">待审核</option>
                                        <option value="1">已通过</option>
                                        <option value="2">已拒绝</option>
                                        <option value="3">已过期</option>
                                        <option value="4">已回撤</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="editComment" class="form-label">审核意见</label>
                            <textarea class="form-control" id="editComment" rows="4"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editPriority" class="form-label">优先级</label>
                                    <select class="form-select" id="editPriority">
                                        <option value="normal">普通</option>
                                        <option value="high">高</option>
                                        <option value="urgent">紧急</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editExpiresAt" class="form-label">过期时间</label>
                                    <input type="datetime-local" class="form-control" id="editExpiresAt">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitEdit()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">
                        <i class="fas fa-exclamation-triangle"></i> 确认删除
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-warning"></i>
                        <strong>警告：</strong>此操作不可撤销！
                    </div>
                    <p>您确定要删除选中的审核记录吗？</p>
                    <div id="deleteInfo"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                        <i class="fas fa-trash"></i> 确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量编辑模态框 -->
    <div class="modal fade" id="batchEditModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit"></i> 批量编辑
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="batchEditForm">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            已选择 <span id="batchEditCount">0</span> 项记录进行批量编辑
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="batchApprover" class="form-label">审核人</label>
                                    <input type="text" class="form-control" id="batchApprover" 
                                           placeholder="留空表示不修改">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="batchStatus" class="form-label">状态</label>
                                    <select class="form-select" id="batchStatus">
                                        <option value="">不修改</option>
                                        <option value="0">待审核</option>
                                        <option value="1">已通过</option>
                                        <option value="2">已拒绝</option>
                                        <option value="3">已过期</option>
                                        <option value="4">已回撤</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="batchComment" class="form-label">审核意见</label>
                            <textarea class="form-control" id="batchComment" rows="3" 
                                      placeholder="留空表示不修改"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="batchPriority" class="form-label">优先级</label>
                                    <select class="form-select" id="batchPriority">
                                        <option value="">不修改</option>
                                        <option value="normal">普通</option>
                                        <option value="high">高</option>
                                        <option value="urgent">紧急</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="batchExpiresAt" class="form-label">过期时间</label>
                                    <input type="datetime-local" class="form-control" id="batchExpiresAt">
                                    <small class="form-text text-muted">留空表示不修改</small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitBatchEdit()">
                        <i class="fas fa-save"></i> 保存修改
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量审核通过模态框 -->
    <div class="modal fade" id="batchApproveModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-check-circle"></i> 批量审核通过
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        即将批量通过 <span id="batchApproveCount">0</span> 项审核记录
                    </div>
                    
                    <form id="batchApproveForm">
                        <div class="mb-3">
                            <label for="batchApproveApprover" class="form-label">审核人 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="batchApproveApprover" 
                                   placeholder="请输入审核人姓名" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="batchApproveComment" class="form-label">审核意见</label>
                            <div class="mb-2">
                                <small class="text-muted">常用理由：</small>
                                <div class="btn-group-vertical d-grid gap-1 mt-1">
                                    <button type="button" class="btn btn-outline-secondary btn-sm text-start" 
                                            onclick="setBatchApproveComment('内容准确，格式规范，通过审核')">内容准确，格式规范，通过审核</button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm text-start" 
                                            onclick="setBatchApproveComment('修正建议合理，同意采纳')">修正建议合理，同意采纳</button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm text-start" 
                                            onclick="setBatchApproveComment('AI分析准确，修正有效')">AI分析准确，修正有效</button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm text-start" 
                                            onclick="setBatchApproveComment('符合质量标准，批准通过')">符合质量标准，批准通过</button>
                                </div>
                            </div>
                            <textarea class="form-control" id="batchApproveComment" rows="3" 
                                      placeholder="请输入审核意见（可选）"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" onclick="confirmBatchApprove()">
                        <i class="fas fa-check"></i> 确认通过
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量审核拒绝模态框 -->
    <div class="modal fade" id="batchRejectModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-times-circle"></i> 批量审核拒绝
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        即将批量拒绝 <span id="batchRejectCount">0</span> 项审核记录
                    </div>
                    
                    <form id="batchRejectForm">
                        <div class="mb-3">
                            <label for="batchRejectApprover" class="form-label">审核人 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="batchRejectApprover" 
                                   placeholder="请输入审核人姓名" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="batchRejectComment" class="form-label">拒绝原因 <span class="text-danger">*</span></label>
                            <div class="mb-2">
                                <small class="text-muted">常用理由：</small>
                                <div class="btn-group-vertical d-grid gap-1 mt-1">
                                    <button type="button" class="btn btn-outline-secondary btn-sm text-start" 
                                            onclick="setBatchRejectComment('内容存在错误，需要重新修正')">内容存在错误，需要重新修正</button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm text-start" 
                                            onclick="setBatchRejectComment('格式不规范，不符合标准')">格式不规范，不符合标准</button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm text-start" 
                                            onclick="setBatchRejectComment('修正建议不合理或不准确')">修正建议不合理或不准确</button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm text-start" 
                                            onclick="setBatchRejectComment('缺少必要信息或说明')">缺少必要信息或说明</button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm text-start" 
                                            onclick="setBatchRejectComment('质量不达标，需要完善')">质量不达标，需要完善</button>
                                </div>
                            </div>
                            <textarea class="form-control" id="batchRejectComment" rows="3" 
                                      placeholder="请输入拒绝原因（必填）" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmBatchReject()">
                        <i class="fas fa-times"></i> 确认拒绝
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量回撤模态框 -->
    <div class="modal fade" id="batchWithdrawModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title">
                        <i class="fas fa-undo"></i> 批量回撤审核
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        即将批量回撤 <span id="batchWithdrawCount">0</span> 项审核记录
                    </div>
                    
                    <form id="batchWithdrawForm">
                        <div class="mb-3">
                            <label for="batchWithdrawComment" class="form-label">回撤原因</label>
                            <div class="mb-2">
                                <small class="text-muted">常用理由：</small>
                                <div class="btn-group-vertical d-grid gap-1 mt-1">
                                    <button type="button" class="btn btn-outline-secondary btn-sm text-start" 
                                            onclick="setBatchWithdrawComment('需要重新审核和评估')">需要重新审核和评估</button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm text-start" 
                                            onclick="setBatchWithdrawComment('发现新的问题需要处理')">发现新的问题需要处理</button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm text-start" 
                                            onclick="setBatchWithdrawComment('审核标准发生变更')">审核标准发生变更</button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm text-start" 
                                            onclick="setBatchWithdrawComment('误操作，需要撤销')">误操作，需要撤销</button>
                                </div>
                            </div>
                            <textarea class="form-control" id="batchWithdrawComment" rows="3" 
                                      placeholder="请输入回撤原因（可选）"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning" onclick="confirmBatchWithdraw()">
                        <i class="fas fa-undo"></i> 确认回撤
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 10;
        let searchQuery = '';
        let statusFilter = '';
        let selectedItems = new Set();
        let deleteItems = new Set();
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadPendingApprovals();
            loadStatistics();
            initializeEventListeners();
        });
        
        // 初始化事件监听器
        function initializeEventListeners() {
            // 搜索按钮
            document.getElementById('searchBtn').addEventListener('click', performSearch);
            
            // 重置按钮
            document.getElementById('resetBtn').addEventListener('click', resetFilters);
            
            // 导出按钮
            document.getElementById('exportBtn').addEventListener('click', exportData);
            
            // 回车搜索
            document.getElementById('searchKeyword').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        }
        
        // 加载统计信息
        function loadStatistics() {
            fetch('/admin/correction-approval/statistics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateStatistics(data.data);
                    }
                })
                .catch(error => {
                    console.error('Error loading statistics:', error);
                });
        }
        
        // 更新统计信息
        function updateStatistics(stats) {
            document.getElementById('pendingCount').textContent = stats.pendingCount || 0;
            document.getElementById('approvedCount').textContent = stats.approvedCount || 0;
            document.getElementById('rejectedCount').textContent = stats.rejectedCount || 0;
            document.getElementById('withdrawnCount').textContent = stats.withdrawnCount || 0;
            document.getElementById('expiredCount').textContent = stats.expiredCount || 0;
            
            // 更新系统状态
            const systemStatusElement = document.getElementById('systemStatus');
            if (stats.inAllowedWindow) {
                systemStatusElement.innerHTML = '<span class="badge bg-success">运行中</span>';
            } else {
                systemStatusElement.innerHTML = '<span class="badge bg-secondary">已停止</span>';
            }
        }
        
        // 执行搜索
        function performSearch() {
            currentPage = 1;
            loadPendingApprovals();
        }
        
        // 重置过滤器
        function resetFilters() {
            document.getElementById('searchKeyword').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('typeFilter').value = '';
            document.getElementById('startDate').value = '';
            document.getElementById('endDate').value = '';
            currentPage = 1;
            loadPendingApprovals();
        }
        
        // 导出数据
        function exportData() {
            const params = {
                status: document.getElementById('statusFilter').value,
                type: document.getElementById('typeFilter').value,
                keyword: document.getElementById('searchKeyword').value,
                startDate: document.getElementById('startDate').value,
                endDate: document.getElementById('endDate').value
            };

            const queryString = Object.keys(params)
                .filter(key => params[key] !== '')
                .map(key => `${key}=${encodeURIComponent(params[key])}`)
                .join('&');

            window.open(`/admin/correction-approval/export?${queryString}`, '_blank');
        }
        
        function refreshData() {
            loadPendingApprovals();
        }

        function viewLogs() {
            window.open('/admin/correction/logs', '_blank');
        }

        function changePageSize() {
            pageSize = parseInt(document.getElementById('pageSizeSelect').value);
            currentPage = 1;
            loadPendingApprovals();
        }

        function handleFilter() {
            statusFilter = document.getElementById('statusFilter').value;
            currentPage = 1;
            loadPendingApprovals();
        }

        function handleSearch(event) {
            if (event.key === 'Enter') {
                searchQuery = document.getElementById('searchInput').value;
                currentPage = 1;
                loadPendingApprovals();
            }
        }
        
        // 加载待审核列表
        function loadPendingApprovals() {
            const params = {
                page: currentPage,
                size: pageSize,
                keyword: document.getElementById('searchKeyword')?.value || '',
                status: document.getElementById('statusFilter')?.value || '',
                type: document.getElementById('typeFilter')?.value || '',
                startDate: document.getElementById('startDate')?.value || '',
                endDate: document.getElementById('endDate')?.value || ''
            };
            
            const queryString = Object.keys(params)
                .filter(key => params[key] !== '')
                .map(key => `${key}=${encodeURIComponent(params[key])}`)
                .join('&');
            
            fetch(`/admin/correction/api/pending?${queryString}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderApprovalList(data.data);
                        renderPagination(data);
                        updatePageInfo(data);
                        updateBatchOperations();
                    } else {
                        document.getElementById('pendingList').innerHTML = 
                            `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ${data.message}</div>`;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('pendingList').innerHTML = 
                        '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> 加载数据失败</div>';
                });
        }
        
        // 渲染审核列表
        function renderApprovalList(approvals) {
            const listContainer = document.getElementById('pendingList');
            
            if (!approvals || approvals.length === 0) {
                listContainer.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle"></i> 当前没有待审核的修正记录</div>';
                return;
            }
            
            let html = '';
            approvals.forEach(approval => {
                const statusClass = getStatusClass(approval.status);
                const statusText = getStatusText(approval.status);
                const statusBadgeClass = getStatusBadgeClass(approval.status);
                
                html += `
                    <div class="card approval-item ${statusClass} mb-3">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <input type="checkbox" class="form-check-input me-2 item-checkbox" 
                                       value="${approval.id}" onchange="updateSelection()">
                                <strong>审核ID: ${approval.id}</strong>
                                <span class="badge ${statusBadgeClass} ms-2">${statusText}</span>
                                ${approval.priority && approval.priority !== 'normal' ? 
                                    `<span class="badge bg-warning ms-1">${approval.priority === 'high' ? '高优先级' : '紧急'}</span>` : ''}
                            </div>
                            <div>
                                <small class="text-muted">
                                    创建时间: ${formatDateTime(approval.createdAt)}
                                </small>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <p><strong>修正日期:</strong> ${approval.correctionDate}</p>
                                    <p><strong>过期时间:</strong> ${formatDateTime(approval.expiresAt)}</p>
                                    ${approval.approver ? `<p><strong>审核人:</strong> ${approval.approver}</p>` : ''}
                                    ${approval.approvalComment ? `<p><strong>审核意见:</strong> ${approval.approvalComment}</p>` : ''}
                                    <div class="mt-2">
                                        <button class="btn btn-sm btn-outline-info" onclick="viewDetail(${approval.id})">
                                            <i class="fas fa-eye"></i> 查看详情
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary ms-1" onclick="editApproval(${approval.id})">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger ms-1" onclick="deleteApproval(${approval.id})">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="btn-group-vertical">
                                        ${approval.status === 0 ? `
                                            <button class="btn btn-success btn-sm" onclick="approveCorrection(${approval.id})">
                                                <i class="fas fa-check-circle"></i> 审核通过
                                            </button>
                                            <button class="btn btn-danger btn-sm" onclick="rejectCorrection(${approval.id})">
                                                <i class="fas fa-times-circle"></i> 审核拒绝
                                            </button>
                                        ` : ''}
                                        ${(approval.status === 1 || approval.status === 2) ? `
                                            <button class="btn btn-warning btn-sm" onclick="withdrawApproval(${approval.id})">
                                                <i class="fas fa-undo"></i> 回撤
                                            </button>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            listContainer.innerHTML = html;
            
            // 渲染数学公式
            setTimeout(() => {
                renderMathFormulas(listContainer);
            }, 100);
        }
        
        // 渲染分页控件
        function renderPagination(data) {
            const pagination = document.getElementById('pagination');
            let html = '';
            
            // 上一页
            html += `
                <li class="page-item ${!data.hasPrev ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${data.page - 1})">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            `;
            
            // 页码
            const startPage = Math.max(1, data.page - 2);
            const endPage = Math.min(data.totalPages, data.page + 2);
            
            if (startPage > 1) {
                html += '<li class="page-item"><a class="page-link" href="#" onclick="changePage(1)">1</a></li>';
                if (startPage > 2) {
                    html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
                }
            }
            
            for (let i = startPage; i <= endPage; i++) {
                html += `
                    <li class="page-item ${i === data.page ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                    </li>
                `;
            }
            
            if (endPage < data.totalPages) {
                if (endPage < data.totalPages - 1) {
                    html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
                }
                html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${data.totalPages})">${data.totalPages}</a></li>`;
            }
            
            // 下一页
            html += `
                <li class="page-item ${!data.hasNext ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${data.page + 1})">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            `;
            
            pagination.innerHTML = html;
        }
        
        // 更新页面信息
        function updatePageInfo(data) {
            const start = (data.page - 1) * data.size + 1;
            const end = Math.min(data.page * data.size, data.total);
            document.getElementById('pageInfo').textContent = `显示第 ${start}-${end} 项，共 ${data.total} 项`;
        }
        
        // 切换页面
        function changePage(page) {
            if (page < 1) return;
            currentPage = page;
            selectedItems.clear();
            loadPendingApprovals();
        }
        
        // 改变页面大小
        function changePageSize() {
            pageSize = parseInt(document.getElementById('pageSizeSelect').value);
            currentPage = 1;
            selectedItems.clear();
            loadPendingApprovals();
        }
        
        // 搜索处理
        let searchTimeout;
        function handleSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                searchQuery = document.getElementById('searchInput').value.trim();
                currentPage = 1;
                selectedItems.clear();
                loadPendingApprovals();
            }, 500);
        }
        
        // 过滤处理
        function handleFilter() {
            statusFilter = document.getElementById('statusFilter').value;
            currentPage = 1;
            selectedItems.clear();
            loadPendingApprovals();
        }
        
        // 更新选择状态
        function updateSelection() {
            const checkboxes = document.querySelectorAll('.item-checkbox');
            selectedItems.clear();
            
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    selectedItems.add(checkbox.value);
                }
            });
            
            updateBatchOperations();
        }
        
        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.item-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
            
            updateSelection();
        }
        
        // 更新批量操作显示
        function updateBatchOperations() {
            const batchOps = document.getElementById('batchOperations');
            const selectedCount = document.getElementById('selectedCount');
            
            if (selectedItems.size > 0) {
                batchOps.style.display = 'block';
                selectedCount.textContent = `已选择 ${selectedItems.size} 项`;
            } else {
                batchOps.style.display = 'none';
                document.getElementById('selectAll').checked = false;
            }
        }
        
        // 获取当前登录用户（这里需要根据实际情况获取）
        function getCurrentUser() {
            // 从页面元素或session中获取当前用户信息
            // 这里假设用户信息存储在某个隐藏元素或全局变量中
            const userElement = document.querySelector('[data-current-user]');
            if (userElement) {
                return userElement.getAttribute('data-current-user');
            }
            
            // 或者从其他地方获取，比如从页面标题、导航栏等
            const navUser = document.querySelector('.navbar .user-name');
            if (navUser) {
                return navUser.textContent.trim();
            }
            
            // 默认返回空字符串，用户需要手动输入
            return '';
        }

        // 设置批量审核通过的常用理由
        function setBatchApproveComment(comment) {
            document.getElementById('batchApproveComment').value = comment;
        }

        // 设置批量审核拒绝的常用理由
        function setBatchRejectComment(comment) {
            document.getElementById('batchRejectComment').value = comment;
        }

        // 设置批量回撤的常用理由
        function setBatchWithdrawComment(comment) {
            document.getElementById('batchWithdrawComment').value = comment;
        }

        // 批量审核通过
        function batchApprove() {
            if (selectedItems.size === 0) {
                alert('请先选择要操作的项目');
                return;
            }
            
            // 设置选中数量
            document.getElementById('batchApproveCount').textContent = selectedItems.size;
            
            // 设置默认审核人为当前登录用户
            const currentUser = getCurrentUser();
            document.getElementById('batchApproveApprover').value = currentUser;
            
            // 清空之前的意见
            document.getElementById('batchApproveComment').value = '';
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('batchApproveModal'));
            modal.show();
        }

        // 确认批量审核通过
        function confirmBatchApprove() {
            const approver = document.getElementById('batchApproveApprover').value.trim();
            const comment = document.getElementById('batchApproveComment').value.trim();
            
            if (!approver) {
                alert('请输入审核人姓名');
                return;
            }
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('batchApproveModal'));
            modal.hide();
            
            // 执行批量操作
            batchOperation('approve', approver, comment);
        }

        // 批量审核拒绝
        function batchReject() {
            if (selectedItems.size === 0) {
                alert('请先选择要操作的项目');
                return;
            }
            
            // 设置选中数量
            document.getElementById('batchRejectCount').textContent = selectedItems.size;
            
            // 设置默认审核人为当前登录用户
            const currentUser = getCurrentUser();
            document.getElementById('batchRejectApprover').value = currentUser;
            
            // 清空之前的原因
            document.getElementById('batchRejectComment').value = '';
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('batchRejectModal'));
            modal.show();
        }

        // 确认批量审核拒绝
        function confirmBatchReject() {
            const approver = document.getElementById('batchRejectApprover').value.trim();
            const comment = document.getElementById('batchRejectComment').value.trim();
            
            if (!approver) {
                alert('请输入审核人姓名');
                return;
            }
            
            if (!comment) {
                alert('请输入拒绝原因');
                return;
            }
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('batchRejectModal'));
            modal.hide();
            
            // 执行批量操作
            batchOperation('reject', approver, comment);
        }
        
        // 批量回撤
        function batchWithdraw() {
            if (selectedItems.size === 0) {
                alert('请先选择要操作的项目');
                return;
            }
            
            // 设置选中数量
            document.getElementById('batchWithdrawCount').textContent = selectedItems.size;
            
            // 清空之前的原因
            document.getElementById('batchWithdrawComment').value = '';
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('batchWithdrawModal'));
            modal.show();
        }

        // 确认批量回撤
        function confirmBatchWithdraw() {
            const comment = document.getElementById('batchWithdrawComment').value.trim();
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('batchWithdrawModal'));
            modal.hide();
            
            // 执行批量操作
            batchOperation('withdraw', '', comment);
        }
        
        // 批量编辑
        function batchEdit() {
            if (selectedItems.size === 0) {
                alert('请先选择要操作的项目');
                return;
            }
            
            document.getElementById('batchEditCount').textContent = selectedItems.size;
            new bootstrap.Modal(document.getElementById('batchEditModal')).show();
        }
        
        // 批量删除
        function batchDelete() {
            if (selectedItems.size === 0) {
                alert('请先选择要操作的项目');
                return;
            }
            
            deleteItems = new Set(selectedItems);
            document.getElementById('deleteInfo').innerHTML = `
                <p>将要删除以下 ${deleteItems.size} 项审核记录：</p>
                <ul class="list-unstyled">
                    ${Array.from(deleteItems).map(id => `<li><i class="fas fa-file-alt"></i> 审核ID: ${id}</li>`).join('')}
                </ul>
            `;
            
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
        
        // 清除选择
        function clearSelection() {
            selectedItems.clear();
            document.querySelectorAll('.item-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            document.getElementById('selectAll').checked = false;
            updateBatchOperations();
        }
        
        // 执行批量操作
        function batchOperation(action, approver, comment) {
            // 对于approve和reject操作，使用批量API
            if (action === 'approve' || action === 'reject') {
                const formData = new FormData();
                formData.append('ids', Array.from(selectedItems).join(','));
                formData.append('approver', approver);
                formData.append('comment', comment);
                
                fetch(`/admin/correction/api/batch/${action}`, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        alert(result.message);
                    } else {
                        alert('批量操作失败: ' + result.message);
                    }
                    
                    selectedItems.clear();
                    loadPendingApprovals();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('批量操作失败');
                });
            } else {
                // 对于其他操作（如withdraw），继续使用循环调用
                const promises = Array.from(selectedItems).map(id => {
                    const formData = new FormData();
                    formData.append('approver', approver);
                    formData.append('comment', comment);
                    
                    return fetch(`/admin/correction/api/${action}/${id}`, {
                        method: 'POST',
                        body: formData
                    }).then(response => response.json());
                });
                
                Promise.all(promises)
                    .then(results => {
                        const successCount = results.filter(r => r.success).length;
                        const failCount = results.length - successCount;
                        
                        if (failCount === 0) {
                            alert(`批量操作成功！共处理 ${successCount} 项`);
                        } else {
                            alert(`批量操作完成！成功 ${successCount} 项，失败 ${failCount} 项`);
                        }
                        
                        selectedItems.clear();
                        loadPendingApprovals();
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('批量操作失败');
                    });
            }
        }
        
        // 辅助函数
        function getStatusClass(status) {
            const classes = {
                0: 'status-pending',
                1: 'status-approved', 
                2: 'status-rejected',
                3: 'status-expired',
                4: 'status-withdrawn'
            };
            return classes[status] || 'status-pending';
        }
        
        function getStatusText(status) {
            const texts = {
                0: '待审核',
                1: '已通过',
                2: '已拒绝', 
                3: '已过期',
                4: '已回撤'
            };
            return texts[status] || '未知';
        }
        
        function getStatusBadgeClass(status) {
            const classes = {
                0: 'bg-warning',
                1: 'bg-success',
                2: 'bg-danger',
                3: 'bg-secondary',
                4: 'bg-info'
            };
            return classes[status] || 'bg-secondary';
        }
        
        function formatDateTime(dateTime) {
            if (!dateTime) return '无';
            return new Date(dateTime).toLocaleString('zh-CN');
        }

        function viewDetail(id) {
            // 显示模态框和加载状态
            new bootstrap.Modal(document.getElementById('detailModal')).show();
            
            // 获取修正详情
            fetch(`/admin/correction/api/detail/${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const corrections = data.correctionData;
                        if (corrections && corrections.length > 0) {
                            // 获取所有涉及的题目ID
                            const topicIds = corrections.map(c => c.id);
                            // 获取原始题目数据
                            return Promise.all([
                                Promise.resolve(corrections),
                                ...topicIds.map(topicId => 
                                    fetch(`/admin/correction/api/original-topic/${topicId}`)
                                        .then(res => res.json())
                                )
                            ]);
                        } else {
                            // 没有修正建议的情况
                            document.getElementById('detailContent').innerHTML = 
                                '<div class="alert alert-info"><i class="fas fa-info-circle"></i> AI分析后认为此批题目无需修正</div>';
                            return null;
                        }
                    } else {
                        throw new Error(data.message);
                    }
                })
                .then(results => {
                    if (results) {
                        const [corrections, ...originalTopicsResponses] = results;
                        const originalTopics = originalTopicsResponses.map(res => res.success ? res.data : null);
                        
                        // 渲染对比界面
                        renderComparisonView(corrections, originalTopics);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('detailContent').innerHTML = 
                        '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> 获取详情失败: ' + error.message + '</div>';
                });
        }

        function approveCorrection(id) {
            document.getElementById('approvalId').value = id;
            document.getElementById('approvalAction').value = 'approve';
            document.getElementById('approvalModalTitle').textContent = '审核通过';
            document.getElementById('comment').required = false;
            new bootstrap.Modal(document.getElementById('approvalModal')).show();
        }

        function rejectCorrection(id) {
            document.getElementById('approvalId').value = id;
            document.getElementById('approvalAction').value = 'reject';
            document.getElementById('approvalModalTitle').textContent = '审核拒绝';
            document.getElementById('comment').required = true;
            new bootstrap.Modal(document.getElementById('approvalModal')).show();
        }

        function renderComparisonView(corrections, originalTopics) {
            let html = '<div class="container-fluid">';
            
            corrections.forEach((correction, index) => {
                const originalTopic = originalTopics[index];
                
                html += `
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-edit"></i> 题目ID: ${correction.id}
                                <span class="badge bg-info ms-2">修正建议</span>
                            </h6>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteOriginalTopic(${correction.id})" 
                                        title="删除原始题目数据">
                                    <i class="fas fa-trash"></i> 删除原题
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                        onclick="viewOriginalTopic(${correction.id})" 
                                        title="查看原始题目详情">
                                    <i class="fas fa-eye"></i> 查看原题
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            ${correction.reason ? `<div class="alert alert-warning mb-3"><strong>AI修正原因:</strong> ${correction.reason}</div>` : ''}
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary"><i class="fas fa-file-alt"></i> 原始内容</h6>
                                    <div class="border rounded p-3 bg-light">
                                        ${renderTopicContent(originalTopic)}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-success"><i class="fas fa-magic"></i> AI修正建议</h6>
                                    <div class="border rounded p-3 bg-light">
                                        ${renderCorrectionContent(correction.updates, originalTopic)}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <h6 class="text-info"><i class="fas fa-exchange-alt"></i> 变更对比</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>字段</th>
                                                <th>原始值</th>
                                                <th>修正值</th>
                                                <th>状态</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${renderFieldComparison(correction.updates, originalTopic)}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            document.getElementById('detailContent').innerHTML = html;
            
            // 渲染数学公式
            setTimeout(() => {
                renderMathFormulas(document.getElementById('detailContent'));
            }, 100);
        }
        
        function renderTopicContent(topic) {
            if (!topic) return '<div class="text-muted">无法获取原始题目数据</div>';
            
            return `
                <div class="mb-2"><strong>题型:</strong> ${getTypeDisplayName(topic.type)}</div>
                <div class="mb-2"><strong>题目:</strong> ${topic.title || '无'}</div>
                <div class="mb-2"><strong>选项:</strong> ${formatOptions(topic.options)}</div>
                <div class="mb-2"><strong>子题:</strong> ${formatSubs(topic.subs)}</div>
                <div class="mb-2"><strong>答案:</strong> ${topic.answer || '无'}</div>
                <div class="mb-2"><strong>解析:</strong> ${topic.parse || '无'}</div>
                <div class="mb-2"><strong>难度:</strong> ${topic.difficulty || '无'}</div>
            `;
        }
        
        function renderCorrectionContent(updates, originalTopic) {
            if (!updates || Object.keys(updates).length === 0) {
                return '<div class="text-muted">无修正建议</div>';
            }
            
            let html = '';
            const fields = ['type', 'title', 'options', 'subs', 'answer', 'parse', 'difficulty'];
            
            fields.forEach(field => {
                const originalValue = originalTopic ? originalTopic[field] : null;
                const correctedValue = updates[field] !== undefined ? updates[field] : originalValue;
                
                let displayValue;
                switch(field) {
                    case 'type':
                        displayValue = getTypeDisplayName(correctedValue);
                        break;
                    case 'options':
                        displayValue = formatOptions(correctedValue);
                        break;
                    case 'subs':
                        displayValue = formatSubs(correctedValue);
                        break;
                    default:
                        displayValue = correctedValue || '无';
                }
                
                const isChanged = updates[field] !== undefined;
                const changeClass = isChanged ? 'text-success fw-bold' : '';
                
                html += `<div class="mb-2 ${changeClass}"><strong>${getFieldDisplayName(field)}:</strong> ${displayValue}</div>`;
            });
            
            return html;
        }
        
        function renderFieldComparison(updates, originalTopic) {
            if (!updates || Object.keys(updates).length === 0) {
                return '<tr><td colspan="4" class="text-center text-muted">无字段变更</td></tr>';
            }
            
            let html = '';
            
            Object.keys(updates).forEach(field => {
                const originalValue = originalTopic ? originalTopic[field] : null;
                const correctedValue = updates[field];
                
                let originalDisplay, correctedDisplay;
                
                switch(field) {
                    case 'type':
                        originalDisplay = getTypeDisplayName(originalValue);
                        correctedDisplay = getTypeDisplayName(correctedValue);
                        break;
                    case 'options':
                        originalDisplay = formatOptions(originalValue);
                        correctedDisplay = formatOptions(correctedValue);
                        break;
                    case 'subs':
                        originalDisplay = formatSubs(originalValue);
                        correctedDisplay = formatSubs(correctedValue);
                        break;
                    default:
                        originalDisplay = originalValue || '无';
                        correctedDisplay = correctedValue || '无';
                }
                
                const isChanged = originalValue !== correctedValue;
                const statusBadge = isChanged ? 
                    '<span class="badge bg-warning">已修改</span>' : 
                    '<span class="badge bg-secondary">无变化</span>';
                
                html += `
                    <tr>
                        <td><strong>${getFieldDisplayName(field)}</strong></td>
                        <td class="text-muted">${originalDisplay}</td>
                        <td class="text-success">${correctedDisplay}</td>
                        <td>${statusBadge}</td>
                    </tr>
                `;
            });
            
            return html;
        }
        
        function getFieldDisplayName(field) {
            const fieldNames = {
                'type': '题型',
                'title': '题目',
                'options': '选项',
                'subs': '子题',
                'answer': '答案',
                'parse': '解析',
                'difficulty': '难度'
            };
            return fieldNames[field] || field;
        }
        
        function getTypeDisplayName(type) {
            const typeNames = {
                'choice': '单选题',
                'multiple': '多选题',
                'judge': '判断题',
                'fill': '填空题',
                'short': '简答题',
                'essay': '论述题'
            };
            return typeNames[type] || type || '未知';
        }
        
        function formatOptions(options) {
            if (!options) return '无';
            
            // 处理字符串类型
            if (typeof options === 'string') {
                try {
                    const parsed = JSON.parse(options);
                    return formatOptions(parsed); // 递归处理解析后的数据
                } catch (e) {
                    // 如果不是JSON，直接返回字符串
                    return options;
                }
            }
            
            // 处理数组类型
            if (Array.isArray(options)) {
                return options.map((option, idx) => {
                    let label, text;
                    
                    if (typeof option === 'object' && option !== null) {
                        // 支持多种对象格式：{key: "A", name: "选项内容"} 或 {text: "选项内容"} 等
                        label = option.key || String.fromCharCode(65 + idx);
                        text = option.name || option.text || option.value || option.content || '';
                    } else {
                        // 简单字符串格式
                        label = String.fromCharCode(65 + idx);
                        text = option;
                    }
                    
                    return `<div class="option-item"><span class="option-label">${label}.</span> <span class="option-text">${text}</span></div>`;
                }).join('');
            }
            
            // 处理对象类型
            if (typeof options === 'object' && options !== null) {
                // 检查是否是 {A: "选项A", B: "选项B"} 格式
                const keys = Object.keys(options);
                if (keys.some(key => /^[A-H]$/.test(key))) {
                    // 处理 {A: "选项A", B: "选项B"} 格式
                    const optionKeys = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
                    const result = [];
                    optionKeys.forEach(key => {
                        if (options[key] !== undefined && options[key] !== null) {
                            const content = typeof options[key] === 'object' ? 
                                (options[key].text || options[key].name || options[key].content || JSON.stringify(options[key])) : 
                                String(options[key]);
                            result.push(`<div class="option-item"><span class="option-label">${key}.</span> <span class="option-text">${content}</span></div>`);
                        }
                    });
                    return result.join('');
                } else {
                    // 处理其他对象格式，转换为数组格式
                    const values = Object.values(options);
                    if (values.length > 0) {
                        return values.map((opt, idx) => {
                            const label = String.fromCharCode(65 + idx);
                            const content = typeof opt === 'object' && opt !== null ? 
                                (opt.text || opt.name || opt.content || JSON.stringify(opt)) : 
                                String(opt);
                            return `<div class="option-item"><span class="option-label">${label}.</span> <span class="option-text">${content}</span></div>`;
                        }).join('');
                    }
                }
                
                // 最后尝试JSON序列化
                return JSON.stringify(options);
            }
            
            return String(options);
        }
        
        function formatSubs(subs) {
            if (!subs) return '无';
            
            // 处理字符串类型
            if (typeof subs === 'string') {
                try {
                    const parsed = JSON.parse(subs);
                    return formatSubs(parsed); // 递归处理解析后的数据
                } catch (e) {
                    return subs;
                }
            }
            
            // 处理数组类型
            if (Array.isArray(subs)) {
                return subs.map((sub, idx) => {
                    if (typeof sub === 'object' && sub !== null) {
                        // 如果子题是对象，尝试提取文本内容
                        const text = sub.text || sub.content || sub.title || JSON.stringify(sub);
                        return `${idx + 1}. ${text}`;
                    }
                    return `${idx + 1}. ${sub}`;
                }).join('<br>');
            }
            
            // 处理对象类型
            if (typeof subs === 'object' && subs !== null) {
                // 如果是单个对象，尝试提取文本内容
                const text = subs.text || subs.content || subs.title || JSON.stringify(subs);
                return text;
            }
            
            return String(subs);
        }

        function submitApproval() {
            const id = document.getElementById('approvalId').value;
            const action = document.getElementById('approvalAction').value;
            const approver = document.getElementById('approver').value;
            const comment = document.getElementById('comment').value;

            if (!approver.trim()) {
                alert('请输入审核人');
                return;
            }

            if (action === 'reject' && !comment.trim()) {
                alert('拒绝审核时必须填写原因');
                return;
            }

            const formData = new FormData();
            formData.append('approver', approver);
            formData.append('comment', comment);

            fetch(`/admin/correction/api/${action}/${id}`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    bootstrap.Modal.getInstance(document.getElementById('approvalModal')).hide();
                    loadPendingApprovals();
                    loadStatistics();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败');
            });
        }
        
        // 编辑审核记录
        function editApproval(id) {
            fetch(`/admin/correction/api/detail/${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const approval = data.data;
                        document.getElementById('editId').value = approval.id;
                        document.getElementById('editApprover').value = approval.approver || '';
                        document.getElementById('editStatus').value = approval.status;
                        document.getElementById('editComment').value = approval.approvalComment || '';
                        document.getElementById('editPriority').value = approval.priority || 'normal';
                        
                        if (approval.expiresAt) {
                            const date = new Date(approval.expiresAt);
                            document.getElementById('editExpiresAt').value = date.toISOString().slice(0, 16);
                        }
                        
                        new bootstrap.Modal(document.getElementById('editModal')).show();
                    } else {
                        alert('获取记录详情失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取记录详情失败');
                });
        }
        
        // 提交编辑
        function submitEdit() {
            const id = document.getElementById('editId').value;
            const approver = document.getElementById('editApprover').value;
            const status = document.getElementById('editStatus').value;
            const comment = document.getElementById('editComment').value;
            const priority = document.getElementById('editPriority').value;
            const expiresAt = document.getElementById('editExpiresAt').value;
            
            if (!approver.trim()) {
                alert('请输入审核人');
                return;
            }
            
            const formData = new FormData();
            formData.append('approver', approver);
            formData.append('status', status);
            formData.append('comment', comment);
            formData.append('priority', priority);
            if (expiresAt) {
                formData.append('expiresAt', expiresAt);
            }
            
            fetch(`/admin/correction/api/update/${id}`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('编辑成功');
                    bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
                    loadPendingApprovals();
                    loadStatistics();
                } else {
                    alert('编辑失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('编辑失败');
            });
        }
        
        // 删除审核记录
        function deleteApproval(id) {
            deleteItems = new Set([id]);
            document.getElementById('deleteInfo').innerHTML = `
                <p>将要删除审核记录：</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-file-alt"></i> 审核ID: ${id}</li>
                </ul>
            `;
            
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
        
        // 确认删除
        function confirmDelete() {
            const promises = Array.from(deleteItems).map(id => 
                fetch(`/admin/correction/api/delete/${id}`, {
                    method: 'DELETE'
                }).then(response => response.json())
            );
            
            Promise.all(promises)
                .then(results => {
                    const successCount = results.filter(r => r.success).length;
                    const failCount = results.length - successCount;
                    
                    if (failCount === 0) {
                        alert(`删除成功！共删除 ${successCount} 项`);
                    } else {
                        alert(`删除完成！成功 ${successCount} 项，失败 ${failCount} 项`);
                    }
                    
                    bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                    selectedItems.clear();
                    deleteItems.clear();
                    loadPendingApprovals();
                    loadStatistics();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除失败');
                });
        }
        
        // 回撤审核
        function withdrawApproval(id) {
            const reason = prompt('请输入回撤原因（可选）:') || '';
            
            if (confirm('确定要回撤此审核吗？')) {
                const formData = new FormData();
                formData.append('reason', reason);
                
                fetch(`/admin/correction/api/withdraw/${id}`, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('回撤成功');
                        loadPendingApprovals();
                        loadStatistics();
                    } else {
                        alert('回撤失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('回撤失败');
                });
            }
        }
        
        // 提交批量编辑
        function submitBatchEdit() {
            const approver = document.getElementById('batchApprover').value;
            const status = document.getElementById('batchStatus').value;
            const comment = document.getElementById('batchComment').value;
            const priority = document.getElementById('batchPriority').value;
            const expiresAt = document.getElementById('batchExpiresAt').value;
            
            const updates = {};
            if (approver.trim()) updates.approver = approver;
            if (status) updates.status = status;
            if (comment.trim()) updates.comment = comment;
            if (priority) updates.priority = priority;
            if (expiresAt) updates.expiresAt = expiresAt;
            
            if (Object.keys(updates).length === 0) {
                alert('请至少选择一个字段进行修改');
                return;
            }
            
            const promises = Array.from(selectedItems).map(id => {
                const formData = new FormData();
                Object.keys(updates).forEach(key => {
                    formData.append(key, updates[key]);
                });
                
                return fetch(`/admin/correction/api/batch-update/${id}`, {
                    method: 'POST',
                    body: formData
                }).then(response => response.json());
            });
            
            Promise.all(promises)
                .then(results => {
                    const successCount = results.filter(r => r.success).length;
                    const failCount = results.length - successCount;
                    
                    if (failCount === 0) {
                        alert(`批量编辑成功！共处理 ${successCount} 项`);
                    } else {
                        alert(`批量编辑完成！成功 ${successCount} 项，失败 ${failCount} 项`);
                    }
                    
                    bootstrap.Modal.getInstance(document.getElementById('batchEditModal')).hide();
                    selectedItems.clear();
                    loadPendingApprovals();
                    loadStatistics();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('批量编辑失败');
                });
        }
        
        // 打印详情
        function printDetail() {
            window.print();
        }
        
        // 导出详情
        function exportDetail() {
            const content = document.getElementById('detailContent').innerHTML;
            const blob = new Blob([content], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'correction_detail.html';
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // 页面加载完成后初始化美化效果
        document.addEventListener('DOMContentLoaded', function() {
            // 添加页面加载动画
            document.body.classList.add('fade-in');
            
            // 添加卡片悬停效果
            addCardHoverEffects();
            
            // 添加统计数字动画
            setTimeout(() => {
                animateStatNumbers();
            }, 300);
            
            // 添加按钮点击波纹效果
            addRippleEffect();
            
            // 添加滚动动画
            addScrollAnimations();
            
            // 添加搜索框图标
            enhanceSearchBox();
        });
        
        // 添加卡片悬停效果
        function addCardHoverEffects() {
            const cards = document.querySelectorAll('.approval-item, .stat-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px) scale(1.01)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        }
        
        // 统计数字动画
        function animateStatNumbers() {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(element => {
                const textContent = element.textContent.trim();
                const finalValue = parseInt(textContent) || 0;
                if (finalValue > 0 && !isNaN(finalValue)) {
                    animateNumber(element, 0, finalValue, 1000);
                }
            });
        }
        
        // 数字动画函数
        function animateNumber(element, start, end, duration) {
            const startTime = performance.now();
            
            function updateNumber(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // 使用缓动函数
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const currentValue = Math.floor(start + (end - start) * easeOutQuart);
                
                element.textContent = currentValue;
                
                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                } else {
                    element.textContent = end;
                }
            }
            
            requestAnimationFrame(updateNumber);
        }
        
        // 添加按钮波纹效果
        function addRippleEffect() {
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255, 255, 255, 0.5);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                    `;
                    
                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
            
            // 添加波纹动画CSS
            if (!document.querySelector('#ripple-style')) {
                const style = document.createElement('style');
                style.id = 'ripple-style';
                style.textContent = `
                    @keyframes ripple {
                        to {
                            transform: scale(4);
                            opacity: 0;
                        }
                    }
                `;
                document.head.appendChild(style);
            }
        }
        
        // 添加滚动动画
        function addScrollAnimations() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });
            
            // 观察所有卡片元素
            const animatedElements = document.querySelectorAll('.approval-item, .stat-card, .search-filters');
            animatedElements.forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });
        }
        
        // 增强搜索框
        function enhanceSearchBox() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                const container = searchInput.parentElement;
                container.style.position = 'relative';
                
                const icon = document.createElement('i');
                icon.className = 'fas fa-search';
                icon.style.cssText = `
                    position: absolute;
                    left: 15px;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #6c757d;
                    z-index: 2;
                `;
                
                container.insertBefore(icon, searchInput);
                searchInput.style.paddingLeft = '3rem';
            }
        }
        
        // 添加成功/错误提示动画
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 600;
                z-index: 9999;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                max-width: 400px;
            `;
            
            if (type === 'success') {
                notification.style.background = 'linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%)';
                notification.innerHTML = `<i class="fas fa-check-circle me-2"></i>${message}`;
            } else {
                notification.style.background = 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)';
                notification.innerHTML = `<i class="fas fa-exclamation-circle me-2"></i>${message}`;
            }
            
            document.body.appendChild(notification);
            
            // 显示动画
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // 自动隐藏
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
        
        // 重写原有的alert函数以使用新的通知系统
        const originalAlert = window.alert;
        window.alert = function(message) {
            if (message.includes('成功')) {
                showNotification(message, 'success');
            } else {
                showNotification(message, 'error');
            }
        };
        
        // 删除原始题目数据
        function deleteOriginalTopic(topicId) {
            if (!confirm(`确定要删除题目ID为 ${topicId} 的原始题目数据吗？\n\n⚠️ 警告：此操作不可逆，删除后将无法恢复！`)) {
                return;
            }
            
            // 再次确认
            if (!confirm('请再次确认：您真的要删除这个原始题目数据吗？')) {
                return;
            }
            
            fetch(`/api/admin/topics/${topicId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showNotification(`题目ID ${topicId} 删除成功`, 'success');
                    // 刷新当前详情视图
                    const currentModal = bootstrap.Modal.getInstance(document.getElementById('detailModal'));
                    if (currentModal) {
                        currentModal.hide();
                    }
                    // 刷新列表
                    loadPendingApprovals();
                } else {
                    showNotification(`删除失败: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                console.error('删除题目失败:', error);
                showNotification('删除题目失败，请稍后重试', 'error');
            });
        }
        
        // 查看原始题目详情
        function viewOriginalTopic(topicId) {
            // 通过API获取题目详情并在模态框中显示
            fetch(`/api/admin/topics/${topicId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200 && data.data) {
                    showTopicDetailModal(data.data);
                } else {
                    showNotification(`获取题目详情失败: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                console.error('获取题目详情失败:', error);
                showNotification('获取题目详情失败，请稍后重试', 'error');
            });
        }
        
        // 显示题目详情模态框
        function showTopicDetailModal(topic) {
            // 创建模态框HTML
            const modalHtml = `
                <div class="modal fade" id="topicDetailModal" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-eye me-2"></i>题目详情 - ID: ${topic.id}
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card h-100">
                                            <div class="card-header bg-light">
                                                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>基本信息</h6>
                                            </div>
                                            <div class="card-body">
                                                <table class="table table-borderless">
                                                    <tr><td class="fw-bold">题目ID:</td><td>${topic.id}</td></tr>
                                                    <tr><td class="fw-bold">题目类型:</td><td>${getTopicTypeText(topic.type)}</td></tr>
                                                    <tr><td class="fw-bold">难度:</td><td>${getDifficultyText(topic.difficulty)}</td></tr>
                                                    <tr><td class="fw-bold">知识点ID:</td><td>${topic.knowId || topic.know_id || '未设置'}</td></tr>
                                                    <tr><td class="fw-bold">分值:</td><td>${topic.score || '未设置'}</td></tr>
                                                    <tr><td class="fw-bold">标签:</td><td>${topic.tags || '无'}</td></tr>
                                                    <tr><td class="fw-bold">来源:</td><td>${topic.source || '未知'}</td></tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card h-100">
                                            <div class="card-header bg-light">
                                                <h6 class="mb-0"><i class="fas fa-question-circle me-2"></i>题目内容</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <strong>题目标题:</strong>
                                                    <div class="mt-2 p-3 bg-light rounded">${topic.title}</div>
                                                </div>
                                                ${topic.options && topic.options.length > 0 ? `
                                                <div class="mb-3">
                                                    <strong>选项:</strong>
                                                    <div class="mt-2 topic-options">
                                                        ${topic.options.map(opt => `<div class="option-item p-2 border rounded mb-1"><span class="option-label">${opt.key}.</span> <span class="option-text">${opt.name}</span></div>`).join('')}
                                                    </div>
                                                </div>
                                                ` : ''}
                                                <div class="mb-3">
                                                    <strong>答案:</strong>
                                                    <div class="mt-2 p-3 bg-success bg-opacity-10 border border-success rounded">${topic.answer}</div>
                                                </div>
                                                ${topic.parse ? `
                                                <div class="mb-3">
                                                    <strong>解析:</strong>
                                                    <div class="mt-2 p-3 bg-info bg-opacity-10 border border-info rounded">${topic.parse}</div>
                                                </div>
                                                ` : ''}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                    <i class="fas fa-times me-2"></i>关闭
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 移除已存在的模态框
            const existingModal = document.getElementById('topicDetailModal');
            if (existingModal) {
                existingModal.remove();
            }
            
            // 添加新模态框到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('topicDetailModal'));
            modal.show();
            
            // 模态框显示后渲染LaTeX公式
            document.getElementById('topicDetailModal').addEventListener('shown.bs.modal', function() {
                // 延迟渲染以确保DOM完全加载
                setTimeout(() => {
                    if (typeof renderMathFormulas === 'function') {
                        renderMathFormulas(this);
                    }
                }, 100);
            });
            
            // 模态框关闭后清理DOM
            document.getElementById('topicDetailModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }
        
        // 获取题目类型文本（与后端TopicTypeMapper保持一致）
        function getTopicTypeText(type) {
            if (!type) {
                return '未设置';
            }
            
            const typeMap = {
                // 数据库标准格式（与后端DB_*常量对应）
                'choice': '单选题',
                'multiple': '多选题', 
                'judge': '判断题',
                'fill': '填空题',
                'short': '简答题',
                'subjective': '主观题',
                'group': '组合题',
                
                // 前端格式（与后端FRONTEND_*常量对应）
                'SINGLE_CHOICE': '单选题',
                'MULTIPLE_CHOICE': '多选题',
                'JUDGE': '判断题',
                'FILL': '填空题',
                'SHORT': '简答题',
                'SUBJECTIVE': '主观题',
                'GROUP': '组合题',
                
                // 驼峰格式（与后端CAMEL_*常量对应）
                'singleChoice': '单选题',
                'multipleChoice': '多选题',
                'judgment': '判断题',
                'fillBlank': '填空题',
                'shortAnswer': '简答题',
                'subjective': '主观题',
                'groupQuestion': '组合题',
                
                // 兼容旧格式
                'essay': '问答题',
                'ESSAY': '问答题',
                'FILL_IN_BLANK': '填空题',
                'TRUE_FALSE': '判断题'
            };
            
            // 先尝试直接匹配
            if (typeMap[type]) {
                return typeMap[type];
            }
            
            // 尝试大写匹配
            const upperType = type.toUpperCase();
            if (typeMap[upperType]) {
                return typeMap[upperType];
            }
            
            // 尝试小写匹配
            const lowerType = type.toLowerCase();
            if (typeMap[lowerType]) {
                return typeMap[lowerType];
            }
            
            // 如果都没匹配到，返回原值
            return type;
        }
        
        // 获取难度文本（与后端难度定义保持一致）
        function getDifficultyText(difficulty) {
            if (difficulty === null || difficulty === undefined) {
                return '未设置';
            }
            
            const difficultyValue = parseFloat(difficulty);
            if (isNaN(difficultyValue)) {
                return '未知';
            }
            
            // 与后端难度定义保持一致：≤0.4为简单，≤0.7为中等，≤1为难题
            if (difficultyValue <= 0.4) {
                return '简单';
            } else if (difficultyValue <= 0.7) {
                return '中等';
            } else if (difficultyValue <= 1.0) {
                return '困难';
            } else {
                return '超难';
            }
        }
    </script>
    
    <!-- KaTeX JavaScript for math formula rendering -->
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js" integrity="sha384-cpW21h6RZv/phavutF+AuVYrr+dA8xD9zs6FwLpaCct6O9ctzYFfFr4dgmgccOTx" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/auto-render.min.js" integrity="sha384-+VBxd3r6XgURycqtZ117nYw44OOcIax56Z4dCRWbxyPt0Koah1uHoK0o4+/RRE05" crossorigin="anonymous"></script>
    <!-- KaTeX mhchem extension for chemistry formulas -->
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/mhchem.min.js" crossorigin="anonymous"></script>
    
    <!-- KaTeX initialization script -->
    <script>
        // 确保KaTeX在页面加载完成后可用
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟执行以确保所有内容都已加载
            setTimeout(function() {
                if (typeof renderMathInElement !== 'undefined') {
                    try {
                        // 为整个页面渲染数学公式
                        renderMathInElement(document.body, {
                            delimiters: [
                            {left: '$$', right: '$$', display: true},
                            {left: '$', right: '$', display: false},
                            {left: '\\(', right: '\\)', display: false},
                            {left: '\\[', right: '\\]', display: true},
                            // 集合类数学公式分隔符
                            {left: '\\{', right: '\\}', display: false},
                            // 化学公式分隔符
                            {left: '\\ce{', right: '}', display: false},
                            {left: '\\cee{', right: '}', display: true}
                        ],
                            throwOnError: false,
                            errorColor: '#cc0000',
                            strict: false,
                            // 启用 mhchem 扩展
                            trust: true,
                            macros: {
                                "\\ce": "\\ce",
                                "\\cee": "\\cee"
                            }
                        });
                        console.log('✅ KaTeX数学公式渲染完成');
                    } catch (e) {
                        console.warn('❌ KaTeX渲染失败:', e);
                    }
                } else {
                    console.warn('⚠️ KaTeX未加载，跳过数学公式渲染');
                }
            }, 500); // 延迟500ms确保DOM更新完成
        });
        
        // 全局函数：重新渲染数学公式（用于动态内容更新后）
        function renderMathFormulas(container) {
            if (typeof renderMathInElement !== 'undefined') {
                try {
                    const targetContainer = container || document.body;
                    renderMathInElement(targetContainer, {
                        delimiters: [
                            {left: '$$', right: '$$', display: true},
                            {left: '$', right: '$', display: false},
                            {left: '\\(', right: '\\)', display: false},
                            {left: '\\[', right: '\\]', display: true},
                            // 集合类数学公式分隔符
                            {left: '\\{', right: '\\}', display: false},
                            // 化学公式分隔符
                            {left: '\\ce{', right: '}', display: false},
                            {left: '\\cee{', right: '}', display: true}
                        ],
                        throwOnError: false,
                        errorColor: '#cc0000',
                        strict: false,
                        // 启用 mhchem 扩展
                        trust: true,
                        macros: {
                            "\\ce": "\\ce",
                            "\\cee": "\\cee"
                        }
                    });

                } catch (e) {
                    console.warn('动态内容KaTeX渲染失败:', e);
                }
            }
        }
    </script>
</body>
</html>
 