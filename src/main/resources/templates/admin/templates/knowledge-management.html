<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识点管理 - 麦子教育系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/admin-dashboard.css">
    <link rel="stylesheet" href="/static/css/admin-layout.css">
    <style>
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
        }
        .border-left-primary { border-left: 4px solid #007bff !important; }
        .border-left-success { border-left: 4px solid #28a745 !important; }
        .border-left-info { border-left: 4px solid #17a2b8 !important; }
        .border-left-warning { border-left: 4px solid #ffc107 !important; }
        .text-gray-800 { color: #5a5c69 !important; }
        .action-buttons .btn {
            margin-right: 0.25rem;
            margin-bottom: 0.25rem;
        }
        .modal-body {
            max-height: 70vh;
            overflow-y: auto;
        }
        .knowledge-group-badge {
            font-size: 0.85rem;
            padding: 0.35em 0.65em;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <!-- 移动端菜单按钮 -->
            <button class="btn btn-outline-light d-md-none me-2" type="button" onclick="toggleMobileSidebar()" title="显示菜单">
                <i class="fas fa-bars"></i>
            </button>
            
            <a class="navbar-brand d-flex align-items-center" href="/admin/dashboard">
                <i class="fas fa-graduation-cap me-2"></i>
                <span class="brand-text">麦子教育管理系统</span>
            </a>
            
            <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <img src="/static/images/default-avatar.png" alt="Avatar" class="rounded-circle me-2" width="32" height="32">
                        <span id="currentUserName">管理员</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>系统设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div th:replace="fragments/admin-sidebar :: admin-sidebar"></div>

            <!-- 主要内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- 页面标题 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-sitemap me-2"></i>
                        知识点管理
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-primary" onclick="showAddKnowledgeModal()">
                                <i class="fas fa-plus"></i> 添加知识点
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            总知识点数
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalKnowledgeCount">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-sitemap fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            分组数量
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="groupCount">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-layer-group fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            免费知识点
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="freeKnowledgeCount">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-unlock fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            付费知识点
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="paidKnowledgeCount">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-lock fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 知识点列表 -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">知识点列表</h6>
                    </div>
                    <div class="card-body">
                        <!-- 搜索筛选栏 -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="searchInput" placeholder="搜索知识点名称...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="groupFilter">
                                    <option value="">所有分组</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-control" id="freeFilter">
                                    <option value="">全部</option>
                                    <option value="1">免费</option>
                                    <option value="0">付费</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="button" class="btn btn-primary" onclick="searchKnowledge()">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetSearch()">
                                    <i class="fas fa-refresh"></i> 重置
                                </button>
                            </div>
                        </div>

                        <!-- 表格 -->
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>知识点ID</th>
                                        <th>知识点名称</th>
                                        <th>分组</th>
                                        <th>类型</th>
                                        <th>排序</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="knowledgeTableBody">
                                    <!-- 通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <nav aria-label="Knowledge pagination">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 添加/编辑知识点模态框 -->
    <div class="modal fade" id="knowledgeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="knowledgeModalTitle">添加知识点</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="knowledgeForm">
                        <input type="hidden" id="knowledgeFormId">
                        
                        <div class="mb-3">
                            <label for="knowledgeFormKnowledgeId" class="form-label">知识点ID <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="knowledgeFormKnowledgeId" required>
                            <div class="form-text">唯一的知识点标识</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="knowledgeFormName" class="form-label">知识点名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="knowledgeFormName" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="knowledgeFormGroupName" class="form-label">分组名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="knowledgeFormGroupName" required>
                            <div class="form-text">如：语文、数学、英语等</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="knowledgeFormIsFree" class="form-label">类型</label>
                            <select class="form-control" id="knowledgeFormIsFree">
                                <option value="1">免费</option>
                                <option value="0">付费</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="knowledgeFormSort" class="form-label">排序</label>
                            <input type="number" class="form-control" id="knowledgeFormSort" value="1" min="0" max="255">
                            <div class="form-text">数值越小排序越靠前</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveKnowledge()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/admin-layout.js"></script>
    <script src="/static/assets/js/knowledge-management.js"></script>
</body>
</html> 