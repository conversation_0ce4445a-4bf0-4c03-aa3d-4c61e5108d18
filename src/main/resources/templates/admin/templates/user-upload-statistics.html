<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户上传统计 - 麦子教育系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/admin-dashboard.css">
    <style>
        .anomaly-high { 
            background-color: #f8d7da; 
            border-left: 4px solid #dc3545; 
        }
        .anomaly-medium { 
            background-color: #fff3cd; 
            border-left: 4px solid #ffc107; 
        }
        .anomaly-low { 
            background-color: #d1ecf1; 
            border-left: 4px solid #17a2b8; 
        }
        .timeline-item {
            border-bottom: 1px solid #eee;
            padding: 8px 0;
        }
        .timeline-item:last-child {
            border-bottom: none;
        }
        .hourly-chart {
            height: 200px;
        }
        .user-detail-modal .modal-dialog {
            max-width: 800px;
        }
        .risk-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="/admin/dashboard">
                <i class="fas fa-graduation-cap me-2"></i>
                <span class="brand-text">麦子教育管理系统</span>
            </a>
            
            <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
                <!-- 通知下拉菜单 -->
                <div class="nav-item dropdown me-3">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="notificationDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <span class="badge bg-danger ms-1" id="notificationBadge" style="display: none;">0</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                        <li><h6 class="dropdown-header">系统通知</h6></li>
                        <li><hr class="dropdown-divider"></li>
                        <li class="dropdown-item-text">暂无通知</li>
                    </ul>
                </div>
                
                <!-- 用户下拉菜单 -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <img src="/static/images/default-avatar.png" alt="Avatar" class="rounded-circle me-2" width="32" height="32">
                        <span id="currentUserName">管理员</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#" onclick="showUserProfile()"><i class="fas fa-user me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item" href="#" onclick="showSettings()"><i class="fas fa-cog me-2"></i>系统设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div th:replace="fragments/admin-sidebar :: admin-sidebar"></div>

            <!-- 主要内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- 页面标题 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-chart-line me-2"></i>
                        用户上传统计
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportUploadStats()">
                                <i class="fas fa-download"></i> 导出统计
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="refreshBtn">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-warning" onclick="showAnomalyDetection()">
                                <i class="fas fa-exclamation-triangle"></i> 异常检测
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 上传概览统计 -->
                <div class="row mb-4" id="overviewContainer">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            今日总上传
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="todayTotal">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-upload fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            活跃用户
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="activeUsers">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            异常用户
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="anomalousUsers">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            平均上传量
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="avgUploadPerUser">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-chart-bar fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选栏 -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <input type="text" class="form-control" id="usernameFilter" placeholder="搜索用户名">
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="startDate">
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" id="endDate">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="orderBy">
                            <option value="date">按日期排序</option>
                            <option value="uploadCount">按上传量排序</option>
                            <option value="username">按用户名排序</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="orderType">
                            <option value="desc">降序</option>
                            <option value="asc">升序</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <button class="btn btn-primary" onclick="searchUploadStats()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- 用户上传统计表格 -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">用户上传统计详情</h6>
                    </div>
                    <div class="card-body">
                        <div id="uploadStatsContainer">
                            <div class="text-center py-4">
                                <i class="fas fa-spinner fa-spin fa-2x"></i>
                                <p class="mt-2">加载中...</p>
                            </div>
                        </div>
                        <!-- 分页 -->
                        <nav id="uploadStatsPagination" style="display: none;">
                            <ul class="pagination justify-content-center">
                                <!-- 分页链接将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 用户详情模态框 -->
    <div class="modal fade user-detail-modal" id="userDetailModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户上传详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="userDetailContent">
                        <!-- 用户详情内容将在这里显示 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-warning" onclick="showSetLimitModal()">设置限制</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设置上传限制模态框 -->
    <div class="modal fade" id="setLimitModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">设置用户上传限制</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="setLimitForm">
                        <input type="hidden" id="limitUserId" name="userId">
                        <div class="mb-3">
                            <label for="dailyLimit" class="form-label">每日上传限制</label>
                            <input type="number" class="form-control" id="dailyLimit" name="dailyLimit" min="0" max="200" required>
                            <div class="form-text">设置为0表示禁止上传，建议正常用户限制在50以内</div>
                        </div>
                        <div class="mb-3">
                            <label for="limitReason" class="form-label">限制原因</label>
                            <textarea class="form-control" id="limitReason" name="reason" rows="3" required 
                                     placeholder="请说明设置限制的原因"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning" onclick="submitSetLimit()">确认设置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 异常检测模态框 -->
    <div class="modal fade" id="anomalyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">异常上传行为检测</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="anomalyDate" class="form-label">检测日期</label>
                        <input type="date" class="form-control" id="anomalyDate">
                    </div>
                    <div id="anomalyResults">
                        <!-- 异常检测结果将在这里显示 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="runAnomalyDetection()">重新检测</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.3.3/dist/echarts.min.js"></script>
    <script src="/assets/libs/jquery-1.12.4/jquery.min.js"></script>
    <script src="/assets/js/admin-user-upload-stats.js"></script>
    <script>
        // 设置当前页面活动状态
        window.activeTab = 'user-upload-stats';
    </script>
</body>
</html> 