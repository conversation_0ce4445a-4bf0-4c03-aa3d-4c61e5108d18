<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>审核管理 - 麦子教育系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- KaTeX CSS for math formula rendering -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css" integrity="sha384-GvrOXuhMATgEsSwCs4smul74iXGOixntILdUW9XmUC6+HX0sLNAK3q71HotJqlAn" crossorigin="anonymous">
    <link rel="stylesheet" href="/assets/css/admin-dashboard.css">
    <link rel="stylesheet" href="/css/audit-management.css">
    <style>
        .audit-status-pending { color: #ffc107; }
        .audit-status-approved { color: #198754; }
        .audit-status-rejected { color: #dc3545; }
        .topic-content { 
            max-height: 200px; 
            overflow-y: auto; 
            background-color: #f8f9fa; 
            padding: 10px; 
            border-radius: 5px; 
        }
        .message-unread { background-color: #fff3cd; }
        .audit-card {
            transition: all 0.3s ease;
        }
        .audit-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        .topic-preview {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .audit-actions {
            background: #fff;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="/admin/dashboard">
                <i class="fas fa-graduation-cap me-2"></i>
                <span class="brand-text">麦子教育管理系统</span>
            </a>
            
            <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
                <!-- 通知下拉菜单 -->
                <div class="nav-item dropdown me-3">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="notificationDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <span class="badge bg-danger ms-1" id="notificationBadge" style="display: none;">0</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                        <li><h6 class="dropdown-header">系统通知</h6></li>
                        <li><hr class="dropdown-divider"></li>
                        <li class="dropdown-item-text">暂无通知</li>
                    </ul>
                </div>
                
                <!-- 用户下拉菜单 -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <img src="/static/images/default-avatar.png" alt="Avatar" class="rounded-circle me-2" width="32" height="32">
                        <span id="currentUserName">管理员</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#" onclick="showUserProfile()"><i class="fas fa-user me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item" href="#" onclick="showSettings()"><i class="fas fa-cog me-2"></i>系统设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div th:replace="fragments/admin-sidebar :: admin-sidebar"></div>

            <!-- 主要内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- 页面标题 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-check-circle me-2"></i>
                        审核管理
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportAuditData()">
                                <i class="fas fa-download"></i> 导出审核数据
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="refreshBtn">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-primary" onclick="showBatchAuditModal()">
                                <i class="fas fa-tasks"></i> 批量审核
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 审核统计 -->
                <div class="row mb-4" id="statsContainer">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            待审核
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="pendingCount">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            已通过
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="approvedCount">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-check fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-danger shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                            已拒绝
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="rejectedCount">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-times fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            总计
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalCount">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-list fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 高级搜索栏 -->
                <div class="card mb-3 advanced-search-card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-filter me-2"></i>
                            高级搜索与筛选
                            <button class="btn btn-sm btn-outline-secondary float-end" type="button" data-bs-toggle="collapse" data-bs-target="#advancedSearch">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </h6>
                    </div>
                    <div class="collapse show" id="advancedSearch">
                        <div class="card-body">
                            <div class="row g-3 search-form-row">
                                <!-- 关键词搜索 -->
                                <div class="col-md-4">
                                    <label for="auditSearchInput" class="form-label">关键词搜索</label>
                                    <div class="input-group enhanced-search-input">
                                        <input type="text" class="form-control" id="auditSearchInput" 
                                               placeholder="搜索题目标题、内容...">
                                        <button class="btn btn-outline-secondary" type="button" onclick="performSearch()">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- 题目类型筛选 -->
                                <div class="col-md-2">
                                    <label for="topicTypeFilter" class="form-label">题目类型</label>
                                    <select class="form-select" id="topicTypeFilter" onchange="performSearch()">
                                        <option value="">全部类型</option>
                                        <option value="choice">单选题</option>
                                        <option value="multiple">多选题</option>
                                        <option value="judge">判断题</option>
                                        <option value="fill">填空题</option>
                                        <option value="short">简答题</option>
                                        <option value="essay">论述题</option>
                                        <option value="calculation">计算题</option>
                                        <option value="analysis">分析题</option>
                                    </select>
                                </div>
                                
                                <!-- 提交者筛选 -->
                                <div class="col-md-2">
                                    <label for="submitterFilter" class="form-label">提交者</label>
                                    <select class="form-select" id="submitterFilter" onchange="performSearch()">
                                        <option value="">全部用户</option>
                                        <!-- 动态加载用户选项 -->
                                    </select>
                                </div>
                                
                                <!-- 知识点筛选 -->
                                <div class="col-md-2">
                                    <label for="knowledgeFilter" class="form-label">知识点</label>
                                    <select class="form-select" id="knowledgeFilter" onchange="performSearch()">
                                        <option value="">全部知识点</option>
                                        <!-- 动态加载知识点选项 -->
                                    </select>
                                </div>
                                
                                <!-- 审核状态筛选 -->
                                <div class="col-md-2">
                                    <label for="statusFilter" class="form-label">审核状态</label>
                                    <select class="form-select" id="statusFilter" onchange="performSearch()">
                                        <option value="">全部状态</option>
                                        <option value="0">待审核</option>
                                        <option value="1">已通过</option>
                                        <option value="2">已拒绝</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- 第二行筛选条件 -->
                            <div class="row g-3 mt-2 search-form-row">
                                <!-- 时间范围筛选 -->
                                <div class="col-md-3">
                                    <label for="dateRangeFilter" class="form-label">提交时间</label>
                                    <select class="form-select" id="dateRangeFilter" onchange="performSearch()">
                                        <option value="">全部时间</option>
                                        <option value="today">今天</option>
                                        <option value="yesterday">昨天</option>
                                        <option value="week">最近一周</option>
                                        <option value="month">最近一个月</option>
                                        <option value="custom">自定义范围</option>
                                    </select>
                                </div>
                                
                                <!-- 自定义时间范围 -->
                                <div class="col-md-2 custom-date-range" id="customDateRange" style="display: none;">
                                    <label for="startDate" class="form-label">开始日期</label>
                                    <input type="date" class="form-control" id="startDate" onchange="performSearch()">
                                </div>
                                <div class="col-md-2 custom-date-range" id="customDateRange2" style="display: none;">
                                    <label for="endDate" class="form-label">结束日期</label>
                                    <input type="date" class="form-control" id="endDate" onchange="performSearch()">
                                </div>
                                
                                <!-- 视图模式 -->
                                <div class="col-md-2 view-mode-selector">
                                    <label for="viewModeSelect" class="form-label">显示模式</label>
                                    <select class="form-select" id="viewModeSelect" onchange="performSearch()">
                                        <option value="list">列表视图</option>
                                        <option value="group">分组视图</option>
                                        <option value="card">卡片视图</option>
                                    </select>
                                </div>
                                
                                <!-- 操作按钮 -->
                                <div class="col-md-3 d-flex align-items-end search-button-group">
                                    <button type="button" class="btn btn-primary" onclick="performSearch()">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="resetSearch()">
                                        <i class="fas fa-undo"></i> 重置
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="saveSearchPreset()">
                                        <i class="fas fa-save"></i> 保存
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 快捷筛选标签 -->
                            <div class="mt-3 quick-filter-tags">
                                <div class="d-flex align-items-center gap-2 flex-wrap">
                                    <span class="text-muted small">快捷筛选:</span>
                                    <button type="button" class="btn btn-sm btn-outline-warning" onclick="applyQuickFilter('urgent')">
                                        <i class="fas fa-exclamation-triangle"></i> 紧急待审
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="applyQuickFilter('today')">
                                        <i class="fas fa-calendar-day"></i> 今日提交
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="applyQuickFilter('frequent')">
                                        <i class="fas fa-user-plus"></i> 高频用户
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="applyQuickFilter('new')">
                                        <i class="fas fa-star"></i> 新用户
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 搜索结果统计 -->
                            <div class="mt-3 search-result-stats" id="searchResultStats" style="display: none;">
                                <div class="alert alert-info d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-info-circle"></i>
                                        <span id="searchStatsText">搜索结果: 找到 0 条记录</span>
                                    </div>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="showExportOptions()">
                                            <i class="fas fa-download"></i> 导出结果
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 审核选项卡 -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <ul class="nav nav-tabs card-header-tabs" id="auditTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="group-audit-tab" data-bs-toggle="tab" data-bs-target="#group-audit" type="button" role="tab">
                                    <i class="fas fa-users"></i> 分组审核
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab">
                                    <i class="fas fa-clock"></i> 待审核题目
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="approved-tab" data-bs-toggle="tab" data-bs-target="#approved" type="button" role="tab">
                                    <i class="fas fa-check"></i> 已通过
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="rejected-tab" data-bs-toggle="tab" data-bs-target="#rejected" type="button" role="tab">
                                    <i class="fas fa-times"></i> 已拒绝
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="auditor-records-tab" data-bs-toggle="tab" data-bs-target="#auditor-records" type="button" role="tab">
                                    <i class="fas fa-history"></i> 审核记录
                                </button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="auditTabContent">
                            <!-- 分组审核 -->
                            <div class="tab-pane fade show active" id="group-audit" role="tabpanel">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>分组审核说明：</strong>按用户和日期分组显示待审核题目，支持批量操作，提高审核效率。
                                    <br><small class="text-muted mt-1">
                                        • <strong>单批次：</strong>用户在同一时间段内提交的题目
                                        • <strong>多批次：</strong>用户在不同时间段提交的题目（时间间隔超过1小时）
                                    </small>
                                </div>
                                
                                <!-- 搜索和筛选 -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="groupSearchInput" placeholder="搜索用户名或题目标题...">
                                            <button class="btn btn-outline-secondary" type="button" onclick="searchGroupAudit()">
                                                <i class="fas fa-search"></i> 搜索
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button type="button" class="btn btn-outline-secondary" onclick="loadGroupAuditData(1, '')">
                                            <i class="fas fa-sync-alt"></i> 刷新
                                        </button>
                                        <button type="button" class="btn btn-outline-info" onclick="showGroupAuditHelp()">
                                            <i class="fas fa-question-circle"></i> 帮助
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- 数据展示区域 -->
                                <div id="groupAuditContainer">
                                    <div class="text-center py-4">
                                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                                        <p class="mt-2">加载分组数据中...</p>
                                    </div>
                                </div>
                                
                                <!-- 分页 -->
                                <nav id="groupAuditPagination" style="display: none;">
                                    <ul class="pagination justify-content-center">
                                        <!-- 分页链接将通过JavaScript动态生成 -->
                                    </ul>
                                </nav>
                            </div>

                            <!-- 待审核题目 -->
                            <div class="tab-pane fade" id="pending" role="tabpanel">
                                <div id="pendingAuditsContainer">
                                    <div class="text-center py-4">
                                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                                        <p class="mt-2">加载中...</p>
                                    </div>
                                </div>
                                <!-- 分页 -->
                                <nav id="pendingPagination" style="display: none;">
                                    <ul class="pagination justify-content-center">
                                        <!-- 分页链接将通过JavaScript动态生成 -->
                                    </ul>
                                </nav>
                            </div>

                            <!-- 已通过 -->
                            <div class="tab-pane fade" id="approved" role="tabpanel">
                                <div id="approvedAuditsContainer">
                                    <!-- 内容将通过JavaScript加载 -->
                                </div>
                                <nav id="approvedPagination" style="display: none;">
                                    <ul class="pagination justify-content-center">
                                        <!-- 分页链接将通过JavaScript动态生成 -->
                                    </ul>
                                </nav>
                            </div>

                            <!-- 已拒绝 -->
                            <div class="tab-pane fade" id="rejected" role="tabpanel">
                                <div id="rejectedAuditsContainer">
                                    <!-- 内容将通过JavaScript加载 -->
                                </div>
                                <nav id="rejectedPagination" style="display: none;">
                                    <ul class="pagination justify-content-center">
                                        <!-- 分页链接将通过JavaScript动态生成 -->
                                    </ul>
                                </nav>
                            </div>

                            <!-- 审核记录 -->
                            <div class="tab-pane fade" id="auditor-records" role="tabpanel">
                                <div id="auditorRecordsContainer">
                                    <!-- 内容将通过JavaScript加载 -->
                                </div>
                                <nav id="auditorRecordsPagination" style="display: none;">
                                    <ul class="pagination justify-content-center">
                                        <!-- 分页链接将通过JavaScript动态生成 -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 审核模态框 -->
    <div class="modal fade" id="auditModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">审核题目</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="topic-preview" id="auditTopicContent">
                                <!-- 题目内容将在这里显示 -->
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="audit-actions">
                                <form id="auditForm">
                                    <input type="hidden" id="auditId" name="auditId">
                                    <div class="mb-3">
                                        <label class="form-label">审核结果 <span class="text-danger">*</span></label>
                                        <div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="radio" name="auditResult" id="approve" value="1" required>
                                                <label class="form-check-label text-success" for="approve">
                                                    <i class="fas fa-check"></i> 通过
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="auditResult" id="reject" value="2" required>
                                                <label class="form-check-label text-danger" for="reject">
                                                    <i class="fas fa-times"></i> 拒绝
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="auditComment" class="form-label">审核意见</label>
                                        <textarea class="form-control" id="auditComment" name="auditComment" rows="4" 
                                                placeholder="请输入审核意见（拒绝时必填）"></textarea>
                                        <div class="form-text">通过时可选，拒绝时请说明原因</div>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-primary" onclick="submitAudit()">
                                            <i class="fas fa-check"></i> 提交审核
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                            <i class="fas fa-times"></i> 取消
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量审核模态框 -->
    <div class="modal fade" id="batchAuditModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量审核</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="batchAuditForm">
                        <div class="mb-3">
                            <label class="form-label">选择操作 <span class="text-danger">*</span></label>
                            <div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="batchAction" id="batchApprove" value="approve" required>
                                    <label class="form-check-label text-success" for="batchApprove">
                                        <i class="fas fa-check"></i> 批量通过
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="batchAction" id="batchReject" value="reject" required>
                                    <label class="form-check-label text-danger" for="batchReject">
                                        <i class="fas fa-times"></i> 批量拒绝
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="batchComment" class="form-label">审核意见</label>
                            <textarea class="form-control" id="batchComment" name="batchComment" rows="3" 
                                    placeholder="请输入批量审核意见"></textarea>
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>注意：</strong>批量操作将应用于当前筛选条件下的所有待审核题目，请谨慎操作。
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitBatchAudit()">确认操作</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户每日审核详情模态框 -->
    <div class="modal fade" id="userDailyDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户每日审核详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="userDailyDetailContent">
                        <!-- 内容将通过JavaScript加载 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-success me-2" onclick="batchAuditUserDaily('approve')">
                        <i class="fas fa-check"></i> 全部通过
                    </button>
                    <button type="button" class="btn btn-danger" onclick="showBatchRejectModal()">
                        <i class="fas fa-times"></i> 全部拒绝
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量拒绝原因模态框 -->
    <div class="modal fade" id="batchRejectModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量拒绝原因</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="batchRejectForm">
                        <div class="mb-3">
                            <label for="batchRejectReason" class="form-label">拒绝原因 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="batchRejectReason" name="batchRejectReason" rows="4" 
                                    placeholder="请说明批量拒绝的原因..." required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="submitBatchReject()">确认拒绝</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/libs/jquery-1.12.4/jquery.min.js"></script>
    <script src="/assets/js/admin-audit-management.js"></script>
    
    <!-- KaTeX JavaScript for math formula rendering -->
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js" integrity="sha384-cpW21h6RZv/phavutF+AuVYrr+dA8xD9zs6FwLpaCct6O9ctzYFfFr4dgmgccOTx" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/auto-render.min.js" integrity="sha384-+VBxd3r6XgURycqtZ117nYw44OOcIax56Z4dCRWbxyPt0Koah1uHoK0o4+/RRE05" crossorigin="anonymous"></script>
    <!-- KaTeX mhchem extension for chemistry formulas -->
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/mhchem.min.js" crossorigin="anonymous"></script>
    
    <!-- KaTeX initialization script -->
    <script>
        // 确保KaTeX在页面加载完成后可用
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟执行以确保所有内容都已加载
            setTimeout(function() {
                if (typeof renderMathInElement !== 'undefined') {
                    try {
                        // 为整个页面渲染数学公式
                        renderMathInElement(document.body, {
                            delimiters: [
                            {left: '$$', right: '$$', display: true},
                            {left: '$', right: '$', display: false},
                            {left: '\\(', right: '\\)', display: false},
                            {left: '\\[', right: '\\]', display: true},
                            // 集合类数学公式分隔符
                            {left: '\\{', right: '\\}', display: false},
                            // 化学公式分隔符
                            {left: '\\ce{', right: '}', display: false},
                            {left: '\\cee{', right: '}', display: true}
                        ],
                            throwOnError: false,
                            errorColor: '#cc0000',
                            strict: false,
                            // 启用 mhchem 扩展
                            trust: true,
                            macros: {
                                "\\ce": "\\ce",
                                "\\cee": "\\cee"
                            }
                        });
                        console.log('✅ KaTeX数学公式渲染完成');
                    } catch (e) {
                        console.warn('❌ KaTeX渲染失败:', e);
                    }
                } else {
                    console.warn('⚠️ KaTeX未加载，跳过数学公式渲染');
                }
            }, 500); // 延迟500ms确保DOM更新完成
        });
        
        // 全局函数：重新渲染数学公式（用于动态内容更新后）
        function renderMathFormulas(container) {
            if (typeof renderMathInElement !== 'undefined') {
                try {
                    const targetContainer = container || document.body;
                    renderMathInElement(targetContainer, {
                        delimiters: [
                            {left: '$$', right: '$$', display: true},
                            {left: '$', right: '$', display: false},
                            {left: '\\(', right: '\\)', display: false},
                            {left: '\\[', right: '\\]', display: true},
                            // 集合类数学公式分隔符
                            {left: '\\{', right: '\\}', display: false},
                            // 化学公式分隔符
                            {left: '\\ce{', right: '}', display: false},
                            {left: '\\cee{', right: '}', display: true}
                        ],
                        throwOnError: false,
                        errorColor: '#cc0000',
                        strict: false,
                        // 启用 mhchem 扩展
                        trust: true,
                        macros: {
                            "\\ce": "\\ce",
                            "\\cee": "\\cee"
                        }
                    });

                } catch (e) {
                    console.warn('动态内容KaTeX渲染失败:', e);
                }
            }
        }
        
        // 设置当前页面活动状态
        window.activeTab = 'audit';
    </script>
</body>
</html>