<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理调试 - 麦子教育系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .debug-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .json-output {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-result {
            margin-top: 10px;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <h1><i class="fas fa-bug"></i> 用户管理调试工具</h1>
        
        <!-- 基础连接测试 -->
        <div class="debug-section">
            <h3>1. 基础连接测试</h3>
            <button class="btn btn-primary" onclick="testBasicConnection()">测试数据库连接</button>
            <div id="basicTest" class="test-result"></div>
            <div id="basicResult" class="json-output"></div>
        </div>
        
        <!-- 用户列表测试 -->
        <div class="debug-section">
            <h3>2. 用户列表API测试</h3>
            <button class="btn btn-primary" onclick="testUserList()">测试获取用户列表</button>
            <div id="listTest" class="test-result"></div>
            <div id="listResult" class="json-output"></div>
        </div>
        
        <!-- 单个用户测试 -->
        <div class="debug-section">
            <h3>3. 单个用户查询测试</h3>
            <div class="mb-3">
                <label for="testUserId" class="form-label">用户ID:</label>
                <input type="text" class="form-control" id="testUserId" placeholder="输入用户ID，如: 1920280447393230851">
            </div>
            <button class="btn btn-primary" onclick="testUserById()">测试获取用户信息</button>
            <div id="userTest" class="test-result"></div>
            <div id="userResult" class="json-output"></div>
        </div>
        
        <!-- JavaScript数值精度测试 -->
        <div class="debug-section">
            <h3>4. JavaScript数值精度测试</h3>
            <button class="btn btn-warning" onclick="testJavaScriptPrecision()">测试大数值精度</button>
            <button class="btn btn-success" onclick="testIdFixSolution()">测试ID修复方案</button>
            <button class="btn btn-info" onclick="quickVerifyDeleteFix()">🔧 验证删除修复</button>
            <div id="precisionTest" class="test-result"></div>
            <div id="precisionResult" class="json-output"></div>
        </div>
        
        <!-- CRUD操作真实测试 -->
        <div class="debug-section">
            <h3>5. CRUD操作真实测试</h3>
            <div class="mb-3">
                <label for="crudTestUserId" class="form-label">选择测试用户ID:</label>
                <select class="form-control" id="crudTestUserId">
                    <option value="">先获取用户列表</option>
                </select>
                <button class="btn btn-info btn-sm mt-2" onclick="loadUsersForCrudTest()">加载用户列表</button>
            </div>
            <div class="btn-group mb-3" role="group">
                <button class="btn btn-primary" onclick="testCrudGetUser()">测试获取用户</button>
                <button class="btn btn-warning" onclick="testCrudUpdateUser()">测试更新用户</button>
                <button class="btn btn-danger" onclick="testCrudDeleteUser()">⚠️ 测试删除功能</button>
            </div>
            
            <!-- 删除功能专项测试 -->
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle"></i> 删除功能专项测试</h5>
                <p>这会真正删除用户数据，请谨慎操作！建议使用测试账号。</p>
                <button class="btn btn-danger btn-sm" onclick="testDeleteFunctionality()">
                    <i class="fas fa-trash"></i> 执行删除功能完整测试
                </button>
            </div>
            
            <div id="crudTest" class="test-result"></div>
            <div id="crudResult" class="json-output"></div>
        </div>
        
        <!-- 添加用户测试 -->
        <div class="debug-section">
            <h3>6. 添加用户测试</h3>
            <div class="row">
                <div class="col-md-6">
                    <input type="text" class="form-control mb-2" id="newUsername" placeholder="用户名">
                </div>
                <div class="col-md-6">
                    <input type="email" class="form-control mb-2" id="newEmail" placeholder="邮箱">
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <input type="password" class="form-control mb-2" id="newPassword" placeholder="密码">
                </div>
                <div class="col-md-6">
                    <select class="form-control mb-2" id="newRole">
                        <option value="1">管理员</option>
                        <option value="2">普通用户</option>
                        <option value="3">教师</option>
                    </select>
                </div>
            </div>
            <button class="btn btn-success" onclick="testAddUser()">测试添加用户</button>
            <div id="addTest" class="test-result"></div>
            <div id="addResult" class="json-output"></div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 基础连接测试
        async function testBasicConnection() {
            setLoading('basicTest', '正在测试基础连接...');
            try {
                const response = await fetch('/api/admin/users/debug');
                const result = await response.json();
                
                if (result.success) {
                    setSuccess('basicTest', '✅ 基础连接测试成功');
                    document.getElementById('basicResult').textContent = JSON.stringify(result, null, 2);
                } else {
                    setError('basicTest', '❌ 基础连接测试失败: ' + result.message);
                    document.getElementById('basicResult').textContent = JSON.stringify(result, null, 2);
                }
            } catch (error) {
                setError('basicTest', '❌ 请求失败: ' + error.message);
                document.getElementById('basicResult').textContent = error.stack;
            }
        }
        
        // 用户列表测试
        async function testUserList() {
            setLoading('listTest', '正在测试用户列表API...');
            try {
                const response = await fetch('/api/admin/users?page=1&size=5');
                const result = await response.json();
                
                if (result.success) {
                    setSuccess('listTest', `✅ 用户列表获取成功，共 ${result.total} 用户`);
                    document.getElementById('listResult').textContent = JSON.stringify(result, null, 2);
                } else {
                    setError('listTest', '❌ 用户列表获取失败: ' + result.message);
                    document.getElementById('listResult').textContent = JSON.stringify(result, null, 2);
                }
            } catch (error) {
                setError('listTest', '❌ 请求失败: ' + error.message);
                document.getElementById('listResult').textContent = error.stack;
            }
        }
        
        // 单个用户测试
        async function testUserById() {
            const userId = document.getElementById('testUserId').value;
            if (!userId) {
                setError('userTest', '❌ 请输入用户ID');
                return;
            }
            
            setLoading('userTest', '正在测试单个用户查询...');
            try {
                const response = await fetch(`/api/admin/users/${userId}`);
                const result = await response.json();
                
                if (result.success) {
                    setSuccess('userTest', '✅ 用户信息获取成功');
                    document.getElementById('userResult').textContent = JSON.stringify(result, null, 2);
                } else {
                    setError('userTest', '❌ 用户信息获取失败: ' + result.message);
                    document.getElementById('userResult').textContent = JSON.stringify(result, null, 2);
                }
            } catch (error) {
                setError('userTest', '❌ 请求失败: ' + error.message);
                document.getElementById('userResult').textContent = error.stack;
            }
        }
        
        // JavaScript数值精度测试
        function testJavaScriptPrecision() {
            const testId = '1920280447393230851';
            const results = {
                original: testId,
                asNumber: Number(testId),
                numberToString: Number(testId).toString(),
                maxSafeInteger: Number.MAX_SAFE_INTEGER,
                isSafe: Number.isSafeInteger(Number(testId)),
                precision_lost: Number(testId).toString() !== testId
            };
            
            if (results.precision_lost) {
                setError('precisionTest', '❌ JavaScript数值精度丢失！这可能是前端问题的根源');
            } else {
                setSuccess('precisionTest', '✅ JavaScript数值精度正常');
            }
            
            document.getElementById('precisionResult').textContent = JSON.stringify(results, null, 2);
        }
        
        // 测试ID修复方案
        function testIdFixSolution() {
            const testId = '1920280447393230853';
            const results = {
                test_case: testId,
                string_handling: {
                    input: testId,
                    ensured_string: String(testId),
                    stays_accurate: String(testId) === testId
                },
                validation: {
                    is_valid_format: /^\d+$/.test(testId),
                    is_not_empty: testId.trim() !== '',
                    length: testId.length
                },
                safe_operations: {
                    can_compare_as_string: testId === '1920280447393230851',
                    can_append_to_url: `/api/admin/users/${testId}`,
                    can_serialize_json: JSON.stringify({id: testId})
                },
                recommendations: [
                    '✅ 总是使用 String(id) 确保为字符串',
                    '✅ 在URL中直接使用字符串ID',
                    '✅ 在JSON中传递字符串ID',
                    '✅ 避免 Number(id) 转换操作',
                    '✅ 使用字符串比较而非数值比较'
                ]
            };
            
            setSuccess('precisionTest', '✅ ID修复方案验证通过 - 字符串处理可解决精度问题');
            document.getElementById('precisionResult').textContent = JSON.stringify(results, null, 2);
        }
        
        // 加载用户列表供CRUD测试
        async function loadUsersForCrudTest() {
            try {
                const response = await fetch('/api/admin/users?page=1&size=5');
                const result = await response.json();
                
                const select = document.getElementById('crudTestUserId');
                select.innerHTML = '<option value="">选择一个用户进行测试</option>';
                
                if (result.success && result.data) {
                    result.data.forEach(user => {
                        const option = document.createElement('option');
                        option.value = String(user.id); // 确保ID为字符串
                        option.textContent = `${user.id} - ${user.username}`;
                        select.appendChild(option);
                    });
                    setSuccess('crudTest', `✅ 加载了 ${result.data.length} 个用户供测试`);
                }
            } catch (error) {
                setError('crudTest', '❌ 加载用户列表失败: ' + error.message);
            }
        }
        
        // 测试获取用户
        async function testCrudGetUser() {
            const userId = document.getElementById('crudTestUserId').value;
            if (!userId) {
                setError('crudTest', '❌ 请先选择一个用户');
                return;
            }
            
            setLoading('crudTest', '正在测试获取用户...');
            try {
                console.log('测试获取用户，ID（字符串）:', userId);
                const response = await fetch(`/api/admin/users/${userId}`);
                const result = await response.json();
                
                if (result.success) {
                    setSuccess('crudTest', '✅ 获取用户成功 - ID精度问题已修复！');
                    document.getElementById('crudResult').textContent = JSON.stringify(result, null, 2);
                } else {
                    setError('crudTest', '❌ 获取用户失败: ' + result.message);
                    document.getElementById('crudResult').textContent = JSON.stringify(result, null, 2);
                }
            } catch (error) {
                setError('crudTest', '❌ 请求失败: ' + error.message);
                document.getElementById('crudResult').textContent = error.stack;
            }
        }
        
        // 测试更新用户
        async function testCrudUpdateUser() {
            const userId = document.getElementById('crudTestUserId').value;
            if (!userId) {
                setError('crudTest', '❌ 请先选择一个用户');
                return;
            }
            
            setLoading('crudTest', '正在测试更新用户...');
            try {
                console.log('测试更新用户，ID（字符串）:', userId);
                const testData = {
                    bio: '调试测试更新 - ' + new Date().toLocaleString()
                };
                
                const response = await fetch(`/api/admin/users/${userId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                const result = await response.json();
                
                if (result.success) {
                    setSuccess('crudTest', '✅ 更新用户成功 - ID精度问题已修复！');
                    document.getElementById('crudResult').textContent = JSON.stringify(result, null, 2);
                } else {
                    setError('crudTest', '❌ 更新用户失败: ' + result.message);
                    document.getElementById('crudResult').textContent = JSON.stringify(result, null, 2);
                }
            } catch (error) {
                setError('crudTest', '❌ 请求失败: ' + error.message);
                document.getElementById('crudResult').textContent = error.stack;
            }
        }
        
        // 测试删除用户（注意：这里不会真正删除，只是测试API调用）
        async function testCrudDeleteUser() {
            alert('注意：删除测试会真正删除用户数据！\n如果要继续，请手动调用删除API进行测试。\n\n建议：\n1. 先创建一个测试用户\n2. 再测试删除该用户\n\n当前仅显示删除API的URL格式。');
            
            const userId = document.getElementById('crudTestUserId').value;
            if (!userId) {
                setError('crudTest', '❌ 请先选择一个用户');
                return;
            }
            
            const deleteInfo = {
                message: '删除测试（未实际执行）',
                delete_url: `/api/admin/users/${userId}`,
                method: 'DELETE',
                user_id_string: userId,
                note: '如需真正测试删除，请使用下方的"执行删除功能完整测试"'
            };
            
            setSuccess('crudTest', '✅ 删除API格式正确 - ID精度问题已修复！');
            document.getElementById('crudResult').textContent = JSON.stringify(deleteInfo, null, 2);
        }
        
        // 删除功能完整测试
        async function testDeleteFunctionality() {
            const userId = document.getElementById('crudTestUserId').value;
            if (!userId) {
                setError('crudTest', '❌ 请先选择一个用户');
                return;
            }
            
            // 二次确认
            if (!confirm(`⚠️ 警告：这将真正删除用户 ID: ${userId}\n\n确定要继续吗？此操作不可撤销！`)) {
                return;
            }
            
            setLoading('crudTest', '正在执行删除功能完整测试...');
            
            const testResults = {
                test_id: userId,
                timestamp: new Date().toISOString(),
                steps: []
            };
            
            try {
                // 步骤1：获取删除前状态
                testResults.steps.push({
                    step: 1,
                    description: '获取删除前用户状态',
                    status: 'starting'
                });
                
                const beforeResponse = await fetch(`/api/admin/users/${userId}`);
                const beforeResult = await beforeResponse.json();
                
                if (beforeResult.success) {
                    testResults.steps[0].status = 'success';
                    testResults.steps[0].data = beforeResult.data;
                    testResults.before_delete = beforeResult.data;
                } else {
                    testResults.steps[0].status = 'failed';
                    testResults.steps[0].error = beforeResult.message;
                    setError('crudTest', '无法获取删除前状态: ' + beforeResult.message);
                    document.getElementById('crudResult').textContent = JSON.stringify(testResults, null, 2);
                    return;
                }
                
                // 步骤2：执行删除操作
                testResults.steps.push({
                    step: 2,
                    description: '执行删除操作',
                    status: 'starting'
                });
                
                console.log('执行删除操作，用户ID:', userId);
                const deleteResponse = await fetch(`/api/admin/users/${userId}`, {
                    method: 'DELETE'
                });
                const deleteResult = await deleteResponse.json();
                
                testResults.steps[1].status = deleteResult.success ? 'success' : 'failed';
                testResults.steps[1].response = deleteResult;
                
                // 步骤3：验证删除结果
                testResults.steps.push({
                    step: 3,
                    description: '验证删除结果',
                    status: 'starting'
                });
                
                // 等待一下让数据库更新
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const afterResponse = await fetch(`/api/admin/users/${userId}`);
                const afterResult = await afterResponse.json();
                
                testResults.steps[2].response = afterResult;
                testResults.after_delete = afterResult;
                
                // 分析测试结果
                const analysis = {
                    delete_api_success: deleteResult.success,
                    user_still_accessible: afterResult.success,
                    expected_behavior: '删除后应该返回"用户不存在"或"用户已被删除"',
                    actual_behavior: afterResult.success ? '用户仍然可以访问' : afterResult.message,
                    test_passed: !afterResult.success && (
                        afterResult.message.includes('用户不存在') || 
                        afterResult.message.includes('已被删除')
                    )
                };
                
                testResults.analysis = analysis;
                testResults.steps[2].status = analysis.test_passed ? 'success' : 'failed';
                
                // 步骤4：数据库状态检查（通过调试API）
                testResults.steps.push({
                    step: 4,
                    description: '检查数据库实际状态',
                    status: 'starting'
                });
                
                const debugResponse = await fetch('/api/admin/users/debug');
                const debugResult = await debugResponse.json();
                
                if (debugResult.success) {
                    testResults.steps[3].status = 'success';
                    testResults.steps[3].data = debugResult.data;
                    testResults.database_status = debugResult.data;
                } else {
                    testResults.steps[3].status = 'failed';
                    testResults.steps[3].error = debugResult.message;
                }
                
                // 最终结论
                if (analysis.test_passed) {
                    setSuccess('crudTest', '✅ 删除功能测试通过！用户已成功被软删除');
                } else {
                    setError('crudTest', '❌ 删除功能测试失败！删除操作可能没有生效');
                }
                
                document.getElementById('crudResult').textContent = JSON.stringify(testResults, null, 2);
                
            } catch (error) {
                setError('crudTest', '❌ 删除功能测试过程中出错: ' + error.message);
                testResults.error = error.message;
                testResults.stack = error.stack;
                document.getElementById('crudResult').textContent = JSON.stringify(testResults, null, 2);
            }
        }
        
        // 添加用户测试
        async function testAddUser() {
            const userData = {
                username: document.getElementById('newUsername').value,
                email: document.getElementById('newEmail').value,
                password: document.getElementById('newPassword').value,
                role: parseInt(document.getElementById('newRole').value)
            };
            
            if (!userData.username || !userData.email || !userData.password) {
                setError('addTest', '❌ 请填写所有必填字段');
                return;
            }
            
            setLoading('addTest', '正在测试添加用户...');
            try {
                const response = await fetch('/api/admin/users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userData)
                });
                const result = await response.json();
                
                if (result.success) {
                    setSuccess('addTest', '✅ 用户添加成功');
                    document.getElementById('addResult').textContent = JSON.stringify(result, null, 2);
                    // 清空表单
                    document.getElementById('newUsername').value = '';
                    document.getElementById('newEmail').value = '';
                    document.getElementById('newPassword').value = '';
                } else {
                    setError('addTest', '❌ 用户添加失败: ' + result.message);
                    document.getElementById('addResult').textContent = JSON.stringify(result, null, 2);
                }
            } catch (error) {
                setError('addTest', '❌ 请求失败: ' + error.message);
                document.getElementById('addResult').textContent = error.stack;
            }
        }
        
        // 辅助函数
        function setLoading(elementId, message) {
            const element = document.getElementById(elementId);
            element.className = 'test-result';
            element.textContent = message;
        }
        
        function setSuccess(elementId, message) {
            const element = document.getElementById(elementId);
            element.className = 'test-result success';
            element.textContent = message;
        }
        
        function setError(elementId, message) {
            const element = document.getElementById(elementId);
            element.className = 'test-result error';
            element.textContent = message;
        }
        
        // 页面加载时自动运行精度测试
        window.onload = function() {
            testJavaScriptPrecision();
        };
        
        // ✅ 快速验证删除修复
        async function quickVerifyDeleteFix() {
            setLoading('precisionTest', '正在验证删除功能修复...');
            
            try {
                // 调用调试API查看删除状态
                const response = await fetch('/api/admin/users/debug');
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data;
                    const verifyResults = {
                        total_users: data.totalUsersInDB,
                        active_users: data.activeUsers, 
                        explicitly_deleted_users: data.explicitlyDeletedCount || 0,
                        recent_users_with_status: data.recentUsersWithDeletedStatus || [],
                        deleted_users_list: data.explicitlyDeletedUsers || [],
                        logic_delete_working: data.explicitlyDeletedCount > 0 ? '✅ 逻辑删除有效果' : '⚠️ 尚未有删除记录',
                        fix_status: '✅ 已使用MyBatis-Plus标准删除方法'
                    };
                    
                    setSuccess('precisionTest', `✅ 删除功能修复验证完成 - 发现 ${verifyResults.explicitly_deleted_users} 个已删除用户`);
                    document.getElementById('precisionResult').textContent = JSON.stringify(verifyResults, null, 2);
                } else {
                    setError('precisionTest', '❌ 验证失败: ' + result.message);
                }
            } catch (error) {
                setError('precisionTest', '❌ 验证过程出错: ' + error.message);
            }
        }
    </script>
</body>
</html> 