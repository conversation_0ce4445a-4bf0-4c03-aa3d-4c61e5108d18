/* 管理员登录页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: #667eea;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    overflow-x: hidden;
}

/* 全屏背景动画 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    z-index: -2;
}

/* 添加动态粒子效果背景 */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    z-index: -1;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(120deg); }
    66% { transform: translateY(30px) rotate(240deg); }
}

.page-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 100vh;
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 1;
}

/* 左侧品牌区域 */
.left-section {
    background: linear-gradient(135deg, 
        rgba(102, 126, 234, 0.95) 0%, 
        rgba(118, 75, 162, 0.95) 50%,
        rgba(102, 126, 234, 0.9) 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    padding: 3rem 2rem;
    position: relative;
    overflow: hidden;
}

/* 增强左侧背景效果 */
.left-section::before {
    content: '';
    position: absolute;
    top: -100%;
    left: -100%;
    width: 300%;
    height: 300%;
    background: 
        radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
    animation: backgroundPulse 8s ease-in-out infinite;
    z-index: 0;
}

/* 添加几何装饰 */
.left-section::after {
    content: '';
    position: absolute;
    top: 10%;
    right: -5%;
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
    z-index: 0;
}

@keyframes backgroundPulse {
    0%, 100% { 
        transform: scale(1) rotate(0deg);
        opacity: 0.8;
    }
    50% { 
        transform: scale(1.1) rotate(180deg);
        opacity: 1;
    }
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.brand {
    text-align: center;
    position: relative;
    z-index: 2;
}

.brand-icon {
    font-size: 4.5rem;
    margin-bottom: 1.5rem;
    color: white;
    text-shadow: 
        2px 2px 4px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(255, 255, 255, 0.2);
    animation: iconGlow 3s ease-in-out infinite alternate;
}

@keyframes iconGlow {
    0% { 
        text-shadow: 
            2px 2px 4px rgba(0, 0, 0, 0.3),
            0 0 20px rgba(255, 255, 255, 0.2);
    }
    100% { 
        text-shadow: 
            2px 2px 4px rgba(0, 0, 0, 0.3),
            0 0 30px rgba(255, 255, 255, 0.4);
    }
}

.brand h1 {
    font-size: 2.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 1px;
}

.brand .slogan {
    font-size: 1.2rem;
    font-weight: 300;
    opacity: 0.95;
    margin-bottom: 2.5rem;
    letter-spacing: 0.5px;
}

.feature-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-top: 2.5rem;
    width: 100%;
    max-width: 300px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.95rem;
    opacity: 0.9;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.75rem 1rem;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.feature-item i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.02); opacity: 1; }
}

/* 右侧表单区域 */
.right-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
}

/* 右侧装饰元素 */
.right-section::before {
    content: '';
    position: absolute;
    top: -20%;
    left: -20%;
    width: 140%;
    height: 140%;
    background: 
        radial-gradient(circle at 80% 20%, rgba(102, 126, 234, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 20% 80%, rgba(118, 75, 162, 0.03) 0%, transparent 50%);
    animation: rightFloat 15s ease-in-out infinite;
    z-index: 0;
}

@keyframes rightFloat {
    0%, 100% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.05); }
}

.form-container {
    width: 100%;
    max-width: 420px;
    position: relative;
    z-index: 1;
}

.login-card {
    background: white;
    border-radius: 24px;
    padding: 3rem 2.5rem;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.08),
        0 0 0 1px rgba(102, 126, 234, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.8);
    position: relative;
    animation: slideInUp 0.8s ease-out;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
    background-size: 200% 100%;
    border-radius: 24px 24px 0 0;
    animation: gradientMove 3s ease infinite;
}

@keyframes gradientMove {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.form-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.form-header h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
    letter-spacing: 0.5px;
}

.form-header .subtitle {
    color: #666;
    font-size: 1rem;
    opacity: 0.8;
}

/* 表单样式 */
.input-group {
    margin-bottom: 1.8rem;
    position: relative;
}

.input-group label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

.input-with-icon {
    position: relative;
}

.input-with-icon i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 1.1rem;
    z-index: 2;
    transition: color 0.3s ease;
}

.input-group input {
    width: 100%;
    padding: 1rem 1rem 1rem 2.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 14px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fff;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.input-group input:focus + i {
    color: #667eea;
    transform: translateY(-50%) scale(1.1);
}

/* 验证码样式 */
.captcha-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.captcha-container .input-with-icon {
    flex: 1;
}

.captcha-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

#captchaCanvas {
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    background: #f8f9fa;
    cursor: pointer;
    height: 44px;
    transition: all 0.3s ease;
}

#captchaCanvas:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.refresh-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 10px;
    color: white;
    width: 44px;
    height: 44px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.refresh-btn:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: rotate(180deg) translateY(-2px);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

/* 选项区域 */
.options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 2rem 0;
    font-size: 0.9rem;
}

.remember-me {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #666;
    transition: color 0.3s ease;
}

.remember-me:hover {
    color: #667eea;
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    margin-right: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.remember-me input[type="checkbox"]:checked + .checkmark {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

.remember-me input[type="checkbox"]:checked + .checkmark:after {
    content: "";
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.forgot-password {
    color: #667eea;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.forgot-password:hover {
    color: #5a67d8;
    text-decoration: underline;
}

/* 提交按钮 */
.submit-btn {
    width: 100%;
    padding: 1.25rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 14px;
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    letter-spacing: 0.5px;
}

.submit-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 25px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.submit-btn:active {
    transform: translateY(-1px);
}

.submit-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 错误信息 */
.error-message {
    background: linear-gradient(135deg, #fee 0%, #fef2f2 100%);
    color: #c53030;
    padding: 12px 16px;
    border-radius: 10px;
    border: 1px solid #feb2b2;
    font-size: 14px;
    text-align: left;
    margin-top: 1rem;
    box-shadow: 0 4px 12px rgba(197, 48, 48, 0.1);
}

/* 安全提示 */
.security-notice {
    background: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: 24px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    animation: slideInUp 1s ease-out;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
}

.security-notice h4 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    color: #333;
    font-size: 1.1rem;
    font-weight: 700;
}

.security-notice h4 i {
    color: #667eea;
    font-size: 1.2rem;
}

.notice-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.notice-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.85rem;
    color: #666;
    padding: 0.75rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.notice-item:hover {
    background: rgba(102, 126, 234, 0.08);
    transform: translateY(-1px);
}

.notice-item i {
    color: #667eea;
    font-size: 1rem;
}

/* 找回密码模态框 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    animation: slideInUp 0.3s ease-out;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
}

.close {
    color: #999;
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #666;
}

.modal-body {
    padding: 2rem;
}

.admin-forgot-message {
    text-align: center;
    padding: 1rem;
}

.admin-forgot-message i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.admin-forgot-message h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.admin-forgot-message p {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.contact-info {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 12px;
    margin: 1.5rem 0;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: #666;
}

.contact-item:last-child {
    margin-bottom: 0;
}

.contact-item i {
    color: #667eea;
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

/* 加载遮罩 */
.loading-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-content {
    background: #fff;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* 表单验证样式 */
.msg {
    position: absolute;
    top: 50%;
    right: -200px;
    transform: translateY(-50%);
    font-size: 12px;
    white-space: nowrap;
}

.msg.onError {
    color: #c53030;
}

.msg.onSuccess {
    color: #38a169;
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }
    
    .left-section {
        padding: 1rem;
        min-height: auto;
    }
    
    .brand h1 {
        font-size: 2rem;
    }
    
    .brand-icon {
        font-size: 3rem;
    }
    
    .feature-list {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .right-section {
        padding: 1rem;
    }
    
    .login-card {
        padding: 1.5rem;
    }
    
    .captcha-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .captcha-display {
        justify-content: center;
        margin-top: 0.5rem;
    }
    
    .notice-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 5% auto;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
    
    .msg {
        position: static;
        transform: none;
        margin-top: 5px;
        text-align: left;
    }
} 