/* 算法监控页面样式 */

/* 基础样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 导航栏样式 */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.2rem;
}

.brand-text {
    background: linear-gradient(45deg, #fff, #e3f2fd);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 卡片样式增强 */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.25rem;
    font-weight: 600;
}

/* 状态卡片样式 */
.border-left-primary {
    border-left: 4px solid #007bff !important;
}

.border-left-success {
    border-left: 4px solid #28a745 !important;
}

.border-left-info {
    border-left: 4px solid #17a2b8 !important;
}

.border-left-warning {
    border-left: 4px solid #ffc107 !important;
}

.text-xs {
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* 图表容器样式 */
.chart-container {
    position: relative;
    height: 400px;
    padding: 10px;
}

#fitnessRadarChart,
#populationHeatmap,
#constraintAnalysisChart,
#convergenceAnalysisChart {
    width: 100%;
    height: 100%;
}

/* 适应度雷达图样式 */
.fitness-radar-container {
    position: relative;
}

.fitness-legend {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.fitness-legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    font-size: 0.85rem;
}

.fitness-legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin-right: 8px;
}

/* 约束统计样式 */
.constraint-stats {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.constraint-stat {
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

/* 收敛指标样式 */
.convergence-indicators {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.indicator {
    text-align: center;
}

.indicator-value {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.indicator-label {
    font-size: 0.75rem;
    color: #6c757d;
    font-weight: 500;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: none;
    font-weight: 600;
    font-size: 0.85rem;
    color: #495057;
    padding: 12px;
}

.table td {
    padding: 12px;
    vertical-align: middle;
    font-size: 0.9rem;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 状态标签样式 */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-running {
    background-color: #d4edda;
    color: #155724;
}

.status-completed {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-failed {
    background-color: #f8d7da;
    color: #721c24;
}

/* 按钮样式增强 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* 筛选区域样式 */
.filter-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 模态框样式 */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
    border-radius: 12px 12px 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* 实时状态指示器 */
.real-time-indicator {
    position: relative;
    display: inline-block;
}

.real-time-indicator::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background-color: #28a745;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    
    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    
    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 通知样式 */
.notification-dropdown {
    min-width: 280px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item:last-child {
    border-bottom: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .chart-container {
        height: 300px;
    }
    
    .stat-value {
        font-size: 1.2rem;
    }
    
    .indicator-value {
        font-size: 1rem;
    }
    
    .table-responsive {
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .btn-toolbar {
        flex-direction: column;
        gap: 10px;
    }
    
    .btn-group {
        width: 100%;
    }
    
    .chart-container {
        height: 250px;
    }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #e0e0e0;
    }
    
    .card-header {
        background: linear-gradient(135deg, #3a3a3a 0%, #2d2d2d 100%);
        border-bottom-color: #444;
    }
    
    .table {
        color: #e0e0e0;
    }
    
    .table th {
        background: linear-gradient(135deg, #3a3a3a 0%, #2d2d2d 100%);
        color: #e0e0e0;
    }
    
    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(255, 255, 255, 0.05);
    }
    
    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.1);
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.8rem;
}

.tooltip-inner {
    background-color: rgba(0, 0, 0, 0.9);
    border-radius: 6px;
    padding: 8px 12px;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
    transition: width 0.6s ease;
}

/* 标签样式 */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.35em 0.65em;
    border-radius: 0.375rem;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
