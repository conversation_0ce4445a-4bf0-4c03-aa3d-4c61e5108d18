/**
 * 管理后台仪表板JavaScript
 */
class AdminDashboard {
    constructor() {
        this.charts = {};
        this.currentPage = 'dashboard';
        this.refreshInterval = null;
        
        this.init();
    }
    
    init() {
        this.checkAuth();
        this.loadUserInfo();
        this.loadDashboardData();
        this.initCharts();
        this.bindEvents();
        this.startAutoRefresh();
    }
    
    // 检查认证状态
    async checkAuth() {
        const token = localStorage.getItem('adminToken');
        if (!token) {
            window.location.href = '/admin/login';
            return;
        }
        
        try {
            const response = await this.fetchAPI('/api/admin/auth/check');
            if (!response.success) {
                localStorage.removeItem('adminToken');
                localStorage.removeItem('adminUser');
                window.location.href = '/admin/login';
            }
        } catch (error) {
            console.error('Auth check failed:', error);
            // 可能是网络问题，不立即跳转
        }
    }
    
    // 加载用户信息
    loadUserInfo() {
        const userInfo = JSON.parse(localStorage.getItem('adminUser') || '{}');
        if (userInfo.username) {
            document.getElementById('currentUserName').textContent = userInfo.username;
        }
    }
    
    // 绑定事件
    bindEvents() {
        // 侧边栏切换
        document.getElementById('sidebarToggle')?.addEventListener('click', () => {
            document.getElementById('sidebar').classList.toggle('show');
        });
        
        // 点击空白区域关闭侧边栏
        document.addEventListener('click', (e) => {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.getElementById('sidebarToggle');
            
            if (sidebar && toggle && 
                !sidebar.contains(e.target) && 
                !toggle.contains(e.target) && 
                sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
            }
        });
        
        // 窗口大小改变时调整图表
        window.addEventListener('resize', () => {
            this.resizeCharts();
        });
    }
    
    // 加载仪表板数据
    async loadDashboardData() {
        try {
            // 并行加载各种数据
            const [statsData, recentTopics, systemLogs, notifications] = await Promise.all([
                this.fetchAPI('/api/admin/dashboard/stats'),
                this.fetchAPI('/api/admin/dashboard/recent-topics'),
                this.fetchAPI('/api/admin/dashboard/system-logs'),
                this.fetchAPI('/api/admin/notifications/unread')
            ]);
            
            // 更新统计数据
            if (statsData.success) {
                this.updateStats(statsData.data);
            }
            
            // 更新最近题目
            if (recentTopics.success) {
                this.updateRecentTopics(recentTopics.data);
            }
            
            // 更新系统日志
            if (systemLogs.success) {
                this.updateSystemLogs(systemLogs.data);
            }
            
            // 更新通知
            if (notifications.success) {
                this.updateNotifications(notifications.data);
            }
            
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            this.showError('加载仪表板数据失败');
        }
    }
    
    // 更新统计数据
    updateStats(stats) {
        document.getElementById('totalUsers').textContent = stats.totalUsers || 0;
        document.getElementById('totalTopics').textContent = stats.totalTopics || 0;
        document.getElementById('pendingAudits').textContent = stats.pendingAudits || 0;
        document.getElementById('onlineUsers').textContent = stats.onlineUsers || 0;
        
        // 更新图表数据
        this.updateChartData(stats);
    }
    
    // 更新最近题目
    updateRecentTopics(topics) {
        const container = document.getElementById('recentTopics');
        
        if (!topics || topics.length === 0) {
            container.innerHTML = this.getEmptyState('暂无最近提交的题目');
            return;
        }
        
        let html = '';
        topics.forEach(topic => {
            html += `
                <div class="activity-item">
                    <div class="activity-icon bg-primary">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">${this.escapeHtml(topic.title)}</div>
                        <div class="activity-description">
                            提交者: ${this.escapeHtml(topic.username)} | 
                            类型: ${this.getTopicTypeText(topic.type)}
                        </div>
                        <div class="activity-time">${this.formatTime(topic.submitTime)}</div>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }
    
    // 更新系统日志
    updateSystemLogs(logs) {
        const container = document.getElementById('systemLogs');
        
        if (!logs || logs.length === 0) {
            container.innerHTML = this.getEmptyState('暂无系统日志');
            return;
        }
        
        let html = '';
        logs.forEach(log => {
            const iconClass = this.getLogIconClass(log.level);
            const bgClass = this.getLogBgClass(log.level);
            
            html += `
                <div class="activity-item">
                    <div class="activity-icon ${bgClass}">
                        <i class="fas ${iconClass}"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">${this.escapeHtml(log.message)}</div>
                        <div class="activity-description">
                            模块: ${this.escapeHtml(log.module)} | 
                            用户: ${this.escapeHtml(log.username || '系统')}
                        </div>
                        <div class="activity-time">${this.formatTime(log.createTime)}</div>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }
    
    // 更新通知
    updateNotifications(notifications) {
        const badge = document.getElementById('notificationBadge');
        const dropdown = document.querySelector('.notification-dropdown');
        
        if (!notifications || notifications.length === 0) {
            badge.style.display = 'none';
            dropdown.innerHTML = `
                <li><h6 class="dropdown-header">系统通知</h6></li>
                <li><hr class="dropdown-divider"></li>
                <li class="dropdown-item-text text-muted">暂无通知</li>
            `;
            return;
        }
        
        // 更新徽章
        badge.textContent = notifications.length;
        badge.style.display = 'inline';
        
        // 更新下拉菜单
        let html = `
            <li><h6 class="dropdown-header">系统通知 (${notifications.length})</h6></li>
            <li><hr class="dropdown-divider"></li>
        `;
        
        notifications.forEach(notification => {
            html += `
                <li>
                    <a class="dropdown-item notification-item" href="#" onclick="markNotificationRead(${notification.id})">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <div class="fw-bold">${this.escapeHtml(notification.title)}</div>
                                <div class="small text-muted">${this.escapeHtml(notification.content)}</div>
                                <div class="small text-muted">${this.formatTime(notification.createTime)}</div>
                            </div>
                        </div>
                    </a>
                </li>
            `;
        });
        
        html += `
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item text-center" href="#" onclick="markAllNotificationsRead()">标记全部已读</a></li>
        `;
        
        dropdown.innerHTML = html;
    }
    
    // 初始化图表
    initCharts() {
        this.initUserActivityChart();
        this.initTopicTypeChart();
    }
    
    // 初始化用户活跃度图表
    initUserActivityChart() {
        const ctx = document.getElementById('userActivityChart').getContext('2d');
        
        this.charts.userActivity = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '活跃用户数',
                    data: [],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                }
            }
        });
    }
    
    // 初始化题目类型分布图表
    initTopicTypeChart() {
        const ctx = document.getElementById('topicTypeChart').getContext('2d');
        
        this.charts.topicType = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['单选题', '多选题', '判断题', '填空题', '简答题'],
                datasets: [{
                    data: [0, 0, 0, 0, 0],
                    backgroundColor: [
                        '#667eea',
                        '#764ba2',
                        '#f093fb',
                        '#f5576c',
                        '#4facfe'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }
    
    // 更新图表数据
    updateChartData(stats) {
        // 更新用户活跃度图表
        if (stats.userActivity && this.charts.userActivity) {
            this.charts.userActivity.data.labels = stats.userActivity.labels;
            this.charts.userActivity.data.datasets[0].data = stats.userActivity.data;
            this.charts.userActivity.update();
        }
        
        // 更新题目类型分布图表
        if (stats.topicTypeDistribution && this.charts.topicType) {
            this.charts.topicType.data.datasets[0].data = [
                stats.topicTypeDistribution.choice || 0,
                stats.topicTypeDistribution.multiple || 0,
                stats.topicTypeDistribution.judge || 0,
                stats.topicTypeDistribution.fill || 0,
                stats.topicTypeDistribution.short || 0
            ];
            this.charts.topicType.update();
        }
    }
    
    // 调整图表大小
    resizeCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart) {
                chart.resize();
            }
        });
    }
    
    // 开始自动刷新
    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            if (this.currentPage === 'dashboard') {
                this.loadDashboardData();
            }
        }, 30000); // 30秒刷新一次
    }
    
    // 停止自动刷新
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    
    // API请求方法
    async fetchAPI(url, options = {}) {
        const token = localStorage.getItem('adminToken');
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        };
        
        const response = await fetch(url, { ...defaultOptions, ...options });
        return await response.json();
    }
    
    // 工具方法
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    formatTime(timestamp) {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) { // 1分钟内
            return '刚刚';
        } else if (diff < 3600000) { // 1小时内
            return `${Math.floor(diff / 60000)}分钟前`;
        } else if (diff < 86400000) { // 1天内
            return `${Math.floor(diff / 3600000)}小时前`;
        } else {
            return date.toLocaleDateString('zh-CN');
        }
    }
    
    getTopicTypeText(type) {
        if (!type) {
            return '未设置';
        }
        
        const typeMap = {
            // 数据库标准格式（与后端DB_*常量对应）
            'choice': '单选题',
            'multiple': '多选题', 
            'judge': '判断题',
            'fill': '填空题',
            'short': '简答题',
            'subjective': '主观题',
            'group': '组合题',
            
            // 前端格式（与后端FRONTEND_*常量对应）
            'SINGLE_CHOICE': '单选题',
            'MULTIPLE_CHOICE': '多选题',
            'JUDGE': '判断题',
            'FILL': '填空题',
            'SHORT': '简答题',
            'SUBJECTIVE': '主观题',
            'GROUP': '组合题',
            
            // 驼峰格式（与后端CAMEL_*常量对应）
            'singleChoice': '单选题',
            'multipleChoice': '多选题',
            'judgment': '判断题',
            'fillBlank': '填空题',
            'shortAnswer': '简答题',
            'subjective': '主观题',
            'groupQuestion': '组合题',
            
            // 兼容旧格式
            'essay': '论述题',
            'ESSAY': '论述题'
        };
        
        // 先尝试直接匹配
        if (typeMap[type]) {
            return typeMap[type];
        }
        
        // 尝试大写匹配
        const upperType = type.toUpperCase();
        if (typeMap[upperType]) {
            return typeMap[upperType];
        }
        
        // 尝试小写匹配
        const lowerType = type.toLowerCase();
        if (typeMap[lowerType]) {
            return typeMap[lowerType];
        }
        
        // 如果都没匹配到，返回原值
        return type;
    }
    
    getLogIconClass(level) {
        const iconMap = {
            'INFO': 'fa-info-circle',
            'WARN': 'fa-exclamation-triangle',
            'ERROR': 'fa-times-circle',
            'DEBUG': 'fa-bug'
        };
        return iconMap[level] || 'fa-info-circle';
    }
    
    getLogBgClass(level) {
        const bgMap = {
            'INFO': 'bg-primary',
            'WARN': 'bg-warning',
            'ERROR': 'bg-danger',
            'DEBUG': 'bg-info'
        };
        return bgMap[level] || 'bg-primary';
    }
    
    getEmptyState(message) {
        return `
            <div class="empty-state">
                <i class="fas fa-inbox"></i>
                <h5>暂无数据</h5>
                <p>${message}</p>
            </div>
        `;
    }
    
    showError(message) {
        // 这里可以集成通知组件，暂时使用alert
        console.error(message);
    }
}

// 全局函数
function loadPage(pageName) {
    // 更新导航状态
    document.querySelectorAll('.sidebar .nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    event.target.closest('.nav-link').classList.add('active');
    
    // 根据页面名称加载对应内容
    const dashboard = window.adminDashboard;
    dashboard.currentPage = pageName;
    
    switch (pageName) {
        case 'dashboard':
            showDashboard();
            break;
        case 'users':
            loadUsersPage();
            break;
        case 'topics':
            loadTopicsPage();
            break;
        case 'audit':
            loadAuditPage();
            break;
        case 'knowledge':
            loadKnowledgePage();
            break;
        case 'exams':
            loadExamsPage();
            break;
        case 'reports':
            loadReportsPage();
            break;
        case 'system':
            loadSystemPage();
            break;
        default:
            showDashboard();
    }
}

function showDashboard() {
    document.getElementById('dashboard-content').style.display = 'block';
    // 隐藏其他页面内容
    hideOtherPageContent();
    
    // 重新加载仪表板数据
    window.adminDashboard.loadDashboardData();
}

function hideOtherPageContent() {
    // 这里需要隐藏其他页面的内容
    // 实际实现中可能需要更复杂的页面管理
}

function quickAction(action) {
    const modal = new bootstrap.Modal(document.getElementById('quickActionModal'));
    const title = document.getElementById('quickActionTitle');
    const content = document.getElementById('quickActionContent');
    
    switch (action) {
        case 'addUser':
            title.textContent = '添加用户';
            content.innerHTML = getAddUserForm();
            break;
        case 'uploadTopics':
            title.textContent = '批量导入题目';
            content.innerHTML = getUploadTopicsForm();
            break;
        case 'backup':
            title.textContent = '数据备份';
            content.innerHTML = getBackupForm();
            break;
    }
    
    modal.show();
}

function getAddUserForm() {
    return `
        <form id="addUserForm">
            <div class="mb-3">
                <label class="form-label">用户名</label>
                <input type="text" class="form-control" name="username" required>
            </div>
            <div class="mb-3">
                <label class="form-label">邮箱</label>
                <input type="email" class="form-control" name="email" required>
            </div>
            <div class="mb-3">
                <label class="form-label">角色</label>
                <select class="form-control" name="role" required>
                    <option value="">请选择角色</option>
                    <option value="1">管理员</option>
                    <option value="2">普通用户</option>
                    <option value="3">教师</option>
                </select>
            </div>
            <div class="d-flex justify-content-end">
                <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">取消</button>
                <button type="submit" class="btn btn-primary">添加</button>
            </div>
        </form>
    `;
}

function getUploadTopicsForm() {
    return `
        <form id="uploadTopicsForm">
            <div class="mb-3">
                <label class="form-label">选择文件</label>
                <input type="file" class="form-control" name="file" accept=".xlsx,.xls,.csv" required>
                <div class="form-text">支持Excel和CSV格式</div>
            </div>
            <div class="mb-3">
                <label class="form-label">知识点</label>
                <select class="form-control" name="knowledgePoint" required>
                    <option value="">请选择知识点</option>
                    <!-- 这里需要动态加载知识点列表 -->
                </select>
            </div>
            <div class="d-flex justify-content-end">
                <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">取消</button>
                <button type="submit" class="btn btn-primary">上传</button>
            </div>
        </form>
    `;
}

function getBackupForm() {
    return `
        <div class="text-center">
            <p>确定要创建数据备份吗？</p>
            <p class="text-muted">备份过程可能需要几分钟时间</p>
            <div class="d-flex justify-content-center">
                <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="startBackup()">开始备份</button>
            </div>
        </div>
    `;
}

function refreshDashboard() {
    window.adminDashboard.loadDashboardData();
}

function exportReport() {
    // 导出报告功能
    alert('导出功能开发中...');
}

function changeChartPeriod(period) {
    // 更改图表时间周期
    console.log('Change chart period to:', period);
}

function markNotificationRead(id) {
    // 标记通知已读
    console.log('Mark notification read:', id);
}

function markAllNotificationsRead() {
    // 标记所有通知已读
    console.log('Mark all notifications read');
}

function showUserProfile() {
    // 显示用户资料
    alert('用户资料功能开发中...');
}

function showSettings() {
    // 显示系统设置
    alert('系统设置功能开发中...');
}

function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('adminToken');
        localStorage.removeItem('adminUser');
        window.location.href = '/admin/login';
    }
}

// 页面加载函数（占位符）
function loadUsersPage() {
    alert('人员管理页面开发中...');
}

function loadTopicsPage() {
    alert('题目管理页面开发中...');
}

function loadAuditPage() {
    alert('审核管理页面开发中...');
}

function loadKnowledgePage() {
    alert('知识点管理页面开发中...');
}

function loadExamsPage() {
    alert('考试管理页面开发中...');
}

function loadReportsPage() {
    alert('数据报表页面开发中...');
}

function loadSystemPage() {
    alert('系统监控页面开发中...');
}

function startBackup() {
    alert('备份功能开发中...');
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.adminDashboard = new AdminDashboard();
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (window.adminDashboard) {
        window.adminDashboard.stopAutoRefresh();
    }
});