/**
 * 管理员登录页面JavaScript
 */
class AdminLogin {
    constructor() {
        this.captchaCode = '';
        this.loginAttempts = 0;
        this.maxAttempts = 5;
        this.lockoutTime = 15 * 60 * 1000; // 15分钟
        
        this.init();
    }
    
    init() {
        this.generateCaptcha();
        this.bindEvents();
        this.checkLockout();
        
        // 检查是否有记住的登录信息
        this.loadRememberedLogin();
    }
    
    bindEvents() {
        // 表单提交
        $('#loginForm').on('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });
        
        // 刷新验证码
        $('#refreshCaptcha, #captchaCanvas').on('click', () => {
            this.generateCaptcha();
        });
        
        // 输入框焦点事件
        $('form input').on('focus', function() {
            $(this).parent().find('.msg').remove();
        });
        
        // 输入框失焦验证
        $('form input').on('blur', (e) => {
            this.validateField(e.target);
        });
        
        // 实时验证
        $('form input').on('keyup', (e) => {
            this.validateField(e.target);
        });
        
        // 记住我选项
        $('#rememberMe').on('change', (e) => {
            if (!e.target.checked) {
                this.clearRememberedLogin();
            }
        });
        
        // 忘记密码
        $('.forgot-password').on('click', () => {
            this.showForgotPasswordDialog();
        });
    }
    
    // 生成验证码
    generateCaptcha() {
        const canvas = document.getElementById('captchaCanvas');
        const ctx = canvas.getContext('2d');
        
        // 清除画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 生成随机验证码
        const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
        this.captchaCode = '';
        for (let i = 0; i < 4; i++) {
            this.captchaCode += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        
        // 设置背景
        ctx.fillStyle = '#f8f9fa';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // 添加干扰线
        for (let i = 0; i < 3; i++) {
            ctx.strokeStyle = this.getRandomColor();
            ctx.beginPath();
            ctx.moveTo(Math.random() * canvas.width, Math.random() * canvas.height);
            ctx.lineTo(Math.random() * canvas.width, Math.random() * canvas.height);
            ctx.stroke();
        }
        
        // 绘制验证码
        ctx.font = '18px Arial';
        ctx.textBaseline = 'middle';
        
        for (let i = 0; i < this.captchaCode.length; i++) {
            ctx.fillStyle = this.getRandomColor();
            const x = 15 + i * 15;
            const y = canvas.height / 2;
            const angle = (Math.random() - 0.5) * 0.5;
            
            ctx.save();
            ctx.translate(x, y);
            ctx.rotate(angle);
            ctx.fillText(this.captchaCode.charAt(i), 0, 0);
            ctx.restore();
        }
        
        // 添加干扰点
        for (let i = 0; i < 20; i++) {
            ctx.fillStyle = this.getRandomColor();
            ctx.beginPath();
            ctx.arc(Math.random() * canvas.width, Math.random() * canvas.height, 1, 0, 2 * Math.PI);
            ctx.fill();
        }
    }
    
    getRandomColor() {
        const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe'];
        return colors[Math.floor(Math.random() * colors.length)];
    }
    
    // 字段验证
    validateField(field) {
        const $field = $(field);
        const $parent = $field.parent();
        const value = $field.val().trim();
        
        $parent.find('.msg').remove();
        
        let isValid = true;
        let message = '';
        
        switch (field.id) {
            case 'username':
                if (!value) {
                    isValid = false;
                    message = '请输入管理员账号';
                } else if (value.length < 3) {
                    isValid = false;
                    message = '账号长度至少3位';
                } else {
                    message = '输入正确';
                }
                break;
                
            case 'password':
                if (!value) {
                    isValid = false;
                    message = '请输入密码';
                } else if (value.length < 6) {
                    isValid = false;
                    message = '密码长度至少6位';
                } else {
                    message = '输入正确';
                }
                break;
                
            case 'captcha':
                if (!value) {
                    isValid = false;
                    message = '请输入验证码';
                } else if (value.length !== 4) {
                    isValid = false;
                    message = '验证码为4位';
                } else {
                    message = '输入正确';
                }
                break;
        }
        
        if (message) {
            const msgClass = isValid ? 'onSuccess' : 'onError';
            $parent.append(`<span class="msg ${msgClass}">${message}</span>`);
        }
        
        return isValid;
    }
    
    // 表单验证
    validateForm() {
        const username = $('#username').val().trim();
        const password = $('#password').val().trim();
        const captcha = $('#captcha').val().trim();
        
        let isValid = true;
        
        // 验证所有字段
        $('form input').each((index, field) => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        // 验证验证码
        if (captcha.toLowerCase() !== this.captchaCode.toLowerCase()) {
            $('#captcha').parent().find('.msg').remove();
            $('#captcha').parent().append('<span class="msg onError">验证码错误</span>');
            isValid = false;
        }
        
        return isValid;
    }
    
    // 处理登录
    async handleLogin() {
        if (!this.validateForm()) {
            return;
        }
        
        if (this.isLockedOut()) {
            this.showError('登录尝试次数过多，请稍后再试');
            return;
        }
        
        const username = $('#username').val().trim();
        const password = $('#password').val().trim();
        const rememberMe = $('#rememberMe').prop('checked');
        
        this.showLoading(true);
        this.setButtonState(false);
        
        try {
            const response = await this.loginRequest(username, password);
            
            if (response.success) {
                // 登录成功
                this.loginAttempts = 0;
                localStorage.removeItem('loginAttempts');
                localStorage.removeItem('lockoutTime');
                
                // 保存token
                if (response.token) {
                    localStorage.setItem('adminToken', response.token);
                    localStorage.setItem('adminUser', JSON.stringify(response.user));
                    
                    // 同时设置cookie，这样页面跳转时token会自动传递
                    this.setCookie('Authorization', response.token, 7); // 7天有效期
                    this.setCookie('JWT_TOKEN', response.token, 7); // 备用cookie名
                    
                    console.log('新token已保存到localStorage和cookie:', response.token);
                }
                
                // 记住登录信息
                if (rememberMe) {
                    this.saveRememberedLogin(username);
                } else {
                    this.clearRememberedLogin();
                }
                
                this.showSuccess('登录成功，正在跳转...');
                
                // 跳转到管理后台
                setTimeout(() => {
                    window.location.href = '/admin/dashboard';
                }, 1500);
                
            } else {
                // 登录失败
                this.handleLoginFailure(response.message || '登录失败');
            }
            
        } catch (error) {
            console.error('Login error:', error);
            this.handleLoginFailure('网络连接失败，请稍后再试');
        } finally {
            this.showLoading(false);
            this.setButtonState(true);
        }
    }
    
    // 登录请求
    async loginRequest(username, password) {
        const response = await fetch('/api/admin/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                password: password,
                captcha: $('#captcha').val().trim()
            })
        });
        
        return await response.json();
    }
    
    // 处理登录失败
    handleLoginFailure(message) {
        this.loginAttempts++;
        localStorage.setItem('loginAttempts', this.loginAttempts.toString());
        
        if (this.loginAttempts >= this.maxAttempts) {
            const lockoutTime = Date.now() + this.lockoutTime;
            localStorage.setItem('lockoutTime', lockoutTime.toString());
            message = `登录失败次数过多，账户已被锁定${this.lockoutTime / 60000}分钟`;
        } else {
            const remainingAttempts = this.maxAttempts - this.loginAttempts;
            message += `，还可尝试${remainingAttempts}次`;
        }
        
        this.showError(message);
        this.generateCaptcha(); // 刷新验证码
        $('#captcha').val('').focus();
    }
    
    // 检查是否被锁定
    isLockedOut() {
        const lockoutTime = localStorage.getItem('lockoutTime');
        if (lockoutTime && Date.now() < parseInt(lockoutTime)) {
            return true;
        }
        return false;
    }
    
    // 检查锁定状态
    checkLockout() {
        if (this.isLockedOut()) {
            const lockoutTime = parseInt(localStorage.getItem('lockoutTime'));
            const remainingTime = Math.ceil((lockoutTime - Date.now()) / 1000);
            this.showError(`账户已被锁定，请${Math.ceil(remainingTime / 60)}分钟后再试`);
            this.setButtonState(false);
            
            // 设置定时器解锁
            setTimeout(() => {
                this.checkLockout();
            }, 1000);
        } else {
            this.setButtonState(true);
            this.loginAttempts = parseInt(localStorage.getItem('loginAttempts') || '0');
        }
    }
    
    // 保存记住的登录信息
    saveRememberedLogin(username) {
        localStorage.setItem('rememberedUsername', username);
    }
    
    // 加载记住的登录信息
    loadRememberedLogin() {
        const rememberedUsername = localStorage.getItem('rememberedUsername');
        if (rememberedUsername) {
            $('#username').val(rememberedUsername);
            $('#rememberMe').prop('checked', true);
        }
    }
    
    // 清除记住的登录信息
    clearRememberedLogin() {
        localStorage.removeItem('rememberedUsername');
    }
    
    // 显示/隐藏加载状态
    showLoading(show) {
        if (show) {
            $('#loadingMask').show();
        } else {
            $('#loadingMask').hide();
        }
    }
    
    // 设置按钮状态
    setButtonState(enabled) {
        $('#btnSubmit').prop('disabled', !enabled);
    }
    
    // 显示错误信息
    showError(message) {
        const $errorMsg = $('#errorMsg');
        $errorMsg.text(message).show();
        
        setTimeout(() => {
            $errorMsg.fadeOut();
        }, 5000);
    }
    
    // 显示成功信息
    showSuccess(message) {
        const $errorMsg = $('#errorMsg');
        $errorMsg.removeClass('error-message').addClass('success-message')
                .text(message).show();
    }
    
    // 显示忘记密码对话框
    showForgotPasswordDialog() {
        alert('请联系系统管理员重置密码\n\n技术支持：<EMAIL>\n电话：400-123-4567');
    }

    // 设置Cookie
    setCookie(name, value, days) {
        let expires = "";
        if (days) {
            const date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + value + expires + "; path=/; SameSite=Lax";
        console.log(`Cookie设置成功: ${name}=${value.substring(0, 10)}...`);
    }

    // 获取Cookie
    getCookie(name) {
        const nameEQ = name + "=";
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            let cookie = cookies[i];
            while (cookie.charAt(0) === ' ') cookie = cookie.substring(1, cookie.length);
            if (cookie.indexOf(nameEQ) === 0) return cookie.substring(nameEQ.length, cookie.length);
        }
        return null;
    }

    // 删除Cookie
    deleteCookie(name) {
        document.cookie = name + "=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    }
}

// 页面加载完成后初始化
$(document).ready(function() {
    new AdminLogin();
    
    // 添加成功消息样式
    if (!$('style#success-style').length) {
        $('head').append(`
            <style id="success-style">
                .success-message {
                    background: #d4edda;
                    color: #155724;
                    padding: 10px;
                    border-radius: 6px;
                    border: 1px solid #c3e6cb;
                    font-size: 13px;
                    text-align: left;
                }
            </style>
        `);
    }
});

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('Global error:', e.error);
});

// 阻止右键菜单（可选的安全措施）
$(document).on('contextmenu', function(e) {
    e.preventDefault();
});

// 阻止F12开发者工具（可选的安全措施）
$(document).on('keydown', function(e) {
    if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
        e.preventDefault();
        return false;
    }
}); 