/**
 * 管理员审核管理页面JavaScript
 */
class AdminAuditManagement {
    constructor() {
        this.currentTab = 'pending';
        this.currentPage = 1;
        this.pageSize = 10;
        this.refreshInterval = null;
        
        this.init();
    }
    
    init() {
        this.checkAuth();
        this.loadUserInfo();
        this.loadAuditStats();
        this.loadPendingAudits();
        this.bindEvents();
        this.startAutoRefresh();
    }
    
    // 检查认证状态
    async checkAuth() {
        const adminToken = localStorage.getItem('adminToken');
        const userToken = localStorage.getItem('token') || localStorage.getItem('Authorization');
        
        // 如果没有管理员token，检查是否有普通用户token
        if (!adminToken) {
            if (userToken) {
                // 尝试使用普通用户token验证管理员权限
                try {
                    const response = await fetch('/api/admin/auth/check', {
                        headers: {
                            'Authorization': `Bearer ${userToken}`
                        }
                    });
                    const result = await response.json();
                    if (result.success) {
                        // 普通用户token有效且有管理员权限，保存为管理员token
                        localStorage.setItem('adminToken', userToken);
                        return;
                    }
                } catch (error) {
                    console.log('普通用户token验证失败:', error);
                }
            }
            
            // 没有有效的管理员权限，重定向到管理员登录页面
            console.log('未找到有效的管理员token，重定向到登录页面');
            window.location.href = '/admin/login';
            return;
        }
        
        // 有管理员token，验证其有效性
        try {
            const response = await fetch('/api/admin/auth/check', {
                headers: {
                    'Authorization': `Bearer ${adminToken}`
                }
            });
            const result = await response.json();
            if (!result.success) {
                console.log('管理员token验证失败，清除token并重定向');
                localStorage.removeItem('adminToken');
                localStorage.removeItem('adminUser');
                window.location.href = '/admin/login';
            }
        } catch (error) {
            console.error('Auth check failed:', error);
            // 网络错误时不强制跳转，允许继续访问
            console.log('网络错误，继续使用现有token');
        }
    }
    
    // 加载用户信息
    loadUserInfo() {
        const userInfo = JSON.parse(localStorage.getItem('adminUser') || '{}');
        if (userInfo.username) {
            document.getElementById('currentUserName').textContent = userInfo.username;
        }
    }
    
    // 绑定事件
    bindEvents() {
        // 选项卡切换
        document.querySelectorAll('#auditTabs button[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                const target = e.target.getAttribute('data-bs-target');
                this.currentTab = target.replace('#', '');
                this.currentPage = 1;
                this.loadTabData(this.currentTab);
            });
        });
        
        // 审核表单验证
        document.querySelectorAll('input[name="auditResult"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                const comment = document.getElementById('auditComment');
                if (e.target.value === '2') { // 拒绝
                    comment.setAttribute('required', 'required');
                    comment.parentElement.querySelector('.form-text').textContent = '拒绝时必须填写审核意见';
                } else { // 通过
                    comment.removeAttribute('required');
                    comment.parentElement.querySelector('.form-text').textContent = '通过时可选，拒绝时请说明原因';
                }
            });
        });
    }
    
    // 加载审核统计
    async loadAuditStats() {
        try {
            const response = await this.fetchAPI('/api/admin/audit/stats');
            
            if (response.success) {
                const stats = response.data;
                document.getElementById('pendingCount').textContent = stats.pending || 0;
                document.getElementById('approvedCount').textContent = stats.approved || 0;
                document.getElementById('rejectedCount').textContent = stats.rejected || 0;
                document.getElementById('totalCount').textContent = stats.total || 0;
            }
        } catch (error) {
            console.error('Failed to load audit stats:', error);
            this.showError('加载审核统计失败');
        }
    }
    
    // 加载选项卡数据
    async loadTabData(tab, page = 1) {
        this.currentPage = page;
        
        switch (tab) {
            case 'pending':
                await this.loadPendingAudits(page);
                break;
            case 'approved':
                await this.loadApprovedAudits(page);
                break;
            case 'rejected':
                await this.loadRejectedAudits(page);
                break;
            case 'auditor-records':
                await this.loadAuditorRecords(page);
                break;
        }
    }
    
    // 加载待审核题目
    async loadPendingAudits(page = 1) {
        try {
            this.showLoading('pendingAuditsContainer');
            
            const response = await this.fetchAPI(`/api/admin/audit/pending?page=${page}&size=${this.pageSize}`);
            
            if (response.success) {
                const container = document.getElementById('pendingAuditsContainer');
                const data = response.data;
                
                if (!data.list || data.list.length === 0) {
                    container.innerHTML = this.getEmptyState('暂无待审核题目');
                    document.getElementById('pendingPagination').style.display = 'none';
                    return;
                }
                
                let html = '';
                data.list.forEach(audit => {
                    html += this.renderAuditCard(audit, 'pending');
                });
                
                container.innerHTML = html;
                this.renderPagination('pendingPagination', data.total, page);
            }
        } catch (error) {
            console.error('Failed to load pending audits:', error);
            this.showError('加载待审核题目失败');
        }
    }
    
    // 加载已通过题目
    async loadApprovedAudits(page = 1) {
        try {
            this.showLoading('approvedAuditsContainer');
            
            const response = await this.fetchAPI(`/api/admin/audit/approved?page=${page}&size=${this.pageSize}`);
            
            if (response.success) {
                const container = document.getElementById('approvedAuditsContainer');
                const data = response.data;
                
                if (!data.list || data.list.length === 0) {
                    container.innerHTML = this.getEmptyState('暂无已通过题目');
                    document.getElementById('approvedPagination').style.display = 'none';
                    return;
                }
                
                let html = '';
                data.list.forEach(audit => {
                    html += this.renderAuditCard(audit, 'approved');
                });
                
                container.innerHTML = html;
                this.renderPagination('approvedPagination', data.total, page);
            }
        } catch (error) {
            console.error('Failed to load approved audits:', error);
            this.showError('加载已通过题目失败');
        }
    }
    
    // 加载已拒绝题目
    async loadRejectedAudits(page = 1) {
        try {
            this.showLoading('rejectedAuditsContainer');
            
            const response = await this.fetchAPI(`/api/admin/audit/rejected?page=${page}&size=${this.pageSize}`);
            
            if (response.success) {
                const container = document.getElementById('rejectedAuditsContainer');
                const data = response.data;
                
                if (!data.list || data.list.length === 0) {
                    container.innerHTML = this.getEmptyState('暂无已拒绝题目');
                    document.getElementById('rejectedPagination').style.display = 'none';
                    return;
                }
                
                let html = '';
                data.list.forEach(audit => {
                    html += this.renderAuditCard(audit, 'rejected');
                });
                
                container.innerHTML = html;
                this.renderPagination('rejectedPagination', data.total, page);
            }
        } catch (error) {
            console.error('Failed to load rejected audits:', error);
            this.showError('加载已拒绝题目失败');
        }
    }
    
    // 加载审核记录
    async loadAuditorRecords(page = 1) {
        try {
            this.showLoading('auditorRecordsContainer');
            
            const response = await this.fetchAPI(`/api/admin/audit/records?page=${page}&size=${this.pageSize}`);
            
            if (response.success) {
                const container = document.getElementById('auditorRecordsContainer');
                const data = response.data;
                
                if (!data.list || data.list.length === 0) {
                    container.innerHTML = this.getEmptyState('暂无审核记录');
                    document.getElementById('auditorRecordsPagination').style.display = 'none';
                    return;
                }
                
                let html = '<div class="table-responsive"><table class="table table-hover"><thead><tr>';
                html += '<th>题目标题</th><th>提交者</th><th>审核结果</th><th>审核意见</th><th>审核时间</th></tr></thead><tbody>';
                
                data.list.forEach(record => {
                    const statusClass = record.auditResult === 1 ? 'success' : 'danger';
                    const statusText = record.auditResult === 1 ? '通过' : '拒绝';
                    
                    html += `
                        <tr>
                            <td>${this.escapeHtml(record.topicTitle)}</td>
                            <td>${this.escapeHtml(record.submitterName)}</td>
                            <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                            <td>${this.escapeHtml(record.auditComment || '-')}</td>
                            <td>${this.formatTime(record.auditTime)}</td>
                        </tr>
                    `;
                });
                
                html += '</tbody></table></div>';
                container.innerHTML = html;
                this.renderPagination('auditorRecordsPagination', data.total, page);
            }
        } catch (error) {
            console.error('Failed to load auditor records:', error);
            this.showError('加载审核记录失败');
        }
    }
    
    // 渲染审核卡片
    renderAuditCard(audit, type) {
        const actions = this.getAuditActions(audit, type);
        const statusBadge = this.getStatusBadge(audit.status);
        
        return `
            <div class="card audit-card mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">${this.escapeHtml(audit.topicTitle)}</h6>
                        <small class="text-muted">
                            提交者: ${this.escapeHtml(audit.submitterName)} | 
                            类型: ${this.getTopicTypeText(audit.topicType)} | 
                            知识点: ${this.escapeHtml(audit.knowledgePoint)}
                        </small>
                    </div>
                    <div class="d-flex align-items-center">
                        ${statusBadge}
                        ${actions}
                    </div>
                </div>
                <div class="card-body">
                    <div class="topic-content">
                        ${this.escapeHtml(audit.topicContent)}
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            提交时间: ${this.formatTime(audit.submitTime)}
                            ${audit.auditTime ? ` | 审核时间: ${this.formatTime(audit.auditTime)}` : ''}
                        </small>
                    </div>
                    ${audit.auditComment ? `
                        <div class="mt-2">
                            <strong>审核意见:</strong> ${this.escapeHtml(audit.auditComment)}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }
    
    // 获取审核操作按钮
    getAuditActions(audit, type) {
        if (type === 'pending') {
            return `
                <div class="btn-group">
                    <button class="btn btn-sm btn-success" onclick="approveAudit(${audit.id})">
                        <i class="fas fa-check"></i> 通过
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="rejectAudit(${audit.id})">
                        <i class="fas fa-times"></i> 拒绝
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="showAuditModal(${audit.id})">
                        <i class="fas fa-eye"></i> 详情
                    </button>
                </div>
            `;
        } else {
            return `
                <button class="btn btn-sm btn-outline-primary" onclick="showAuditDetail(${audit.id})">
                    <i class="fas fa-eye"></i> 查看详情
                </button>
            `;
        }
    }
    
    // 获取状态徽章
    getStatusBadge(status) {
        switch (status) {
            case 0:
                return '<span class="badge bg-warning">待审核</span>';
            case 1:
                return '<span class="badge bg-success">已通过</span>';
            case 2:
                return '<span class="badge bg-danger">已拒绝</span>';
            default:
                return '<span class="badge bg-secondary">未知</span>';
        }
    }
    
    // 渲染分页
    renderPagination(containerId, total, currentPage) {
        const container = document.getElementById(containerId);
        const totalPages = Math.ceil(total / this.pageSize);
        
        if (totalPages <= 1) {
            container.style.display = 'none';
            return;
        }
        
        let html = '';
        
        // 上一页
        html += `<li class="page-item ${currentPage <= 1 ? 'disabled' : ''}">`;
        html += `<a class="page-link" href="#" onclick="window.adminAudit.loadTabData('${this.currentTab}', ${currentPage - 1})">上一页</a>`;
        html += `</li>`;
        
        // 页码
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            html += `<li class="page-item ${i === currentPage ? 'active' : ''}">`;
            html += `<a class="page-link" href="#" onclick="window.adminAudit.loadTabData('${this.currentTab}', ${i})">${i}</a>`;
            html += `</li>`;
        }
        
        // 下一页
        html += `<li class="page-item ${currentPage >= totalPages ? 'disabled' : ''}">`;
        html += `<a class="page-link" href="#" onclick="window.adminAudit.loadTabData('${this.currentTab}', ${currentPage + 1})">下一页</a>`;
        html += `</li>`;
        
        container.querySelector('.pagination').innerHTML = html;
        container.style.display = 'block';
    }
    
    // 开始自动刷新
    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            this.loadAuditStats();
            if (this.currentTab === 'pending') {
                this.loadPendingAudits(this.currentPage);
            }
        }, 30000); // 30秒刷新一次
    }
    
    // 停止自动刷新
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    
    // API请求方法
    async fetchAPI(url, options = {}) {
        const token = localStorage.getItem('adminToken');
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        };
        
        const response = await fetch(url, { ...defaultOptions, ...options });
        return await response.json();
    }
    
    // 工具方法
    showLoading(containerId) {
        const container = document.getElementById(containerId);
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-2">加载中...</p>
            </div>
        `;
    }
    
    getEmptyState(message) {
        return `
            <div class="empty-state text-center py-5">
                <i class="fas fa-inbox fs-1 text-muted mb-3"></i>
                <h5 class="text-muted">暂无数据</h5>
                <p class="text-muted">${message}</p>
            </div>
        `;
    }
    
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    formatTime(timestamp) {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN');
    }
    
    getTopicTypeText(type) {
        if (!type) {
            return '未设置';
        }
        
        const typeMap = {
            // 数据库标准格式（与后端DB_*常量对应）
            'choice': '单选题',
            'multiple': '多选题', 
            'judge': '判断题',
            'fill': '填空题',
            'short': '简答题',
            'subjective': '主观题',
            'group': '组合题',
            
            // 前端格式（与后端FRONTEND_*常量对应）
            'SINGLE_CHOICE': '单选题',
            'MULTIPLE_CHOICE': '多选题',
            'JUDGE': '判断题',
            'FILL': '填空题',
            'SHORT': '简答题',
            'SUBJECTIVE': '主观题',
            'GROUP': '组合题',
            
            // 驼峰格式（与后端CAMEL_*常量对应）
            'singleChoice': '单选题',
            'multipleChoice': '多选题',
            'judgment': '判断题',
            'fillBlank': '填空题',
            'shortAnswer': '简答题',
            'subjective': '主观题',
            'groupQuestion': '组合题',
            
            // 兼容旧格式
            'essay': '论述题',
            'ESSAY': '论述题'
        };
        
        // 先尝试直接匹配
        if (typeMap[type]) {
            return typeMap[type];
        }
        
        // 尝试大写匹配
        const upperType = type.toUpperCase();
        if (typeMap[upperType]) {
            return typeMap[upperType];
        }
        
        // 尝试小写匹配
        const lowerType = type.toLowerCase();
        if (typeMap[lowerType]) {
            return typeMap[lowerType];
        }
        
        // 如果都没匹配到，返回原值
        return type;
    }
    
    showError(message) {
        console.error(message);
        // 这里可以集成通知组件
        alert(message);
    }
    
    showSuccess(message) {
        console.log(message);
        // 这里可以集成通知组件
        alert(message);
    }
}

// 全局函数
async function showAuditModal(auditId) {
    try {
        const response = await window.adminAudit.fetchAPI(`/api/admin/audit/detail/${auditId}`);
        
        if (response.success) {
            const audit = response.data;
            
            // 填充题目内容
            document.getElementById('auditTopicContent').innerHTML = `
                <h6>${window.adminAudit.escapeHtml(audit.topicTitle)}</h6>
                <div class="topic-content mt-3">
                    ${window.adminAudit.escapeHtml(audit.topicContent)}
                </div>
                <div class="mt-3">
                    <p><strong>提交者:</strong> ${window.adminAudit.escapeHtml(audit.submitterName)}</p>
                    <p><strong>题目类型:</strong> ${window.adminAudit.getTopicTypeText(audit.topicType)}</p>
                    <p><strong>知识点:</strong> ${window.adminAudit.escapeHtml(audit.knowledgePoint)}</p>
                    <p><strong>提交时间:</strong> ${window.adminAudit.formatTime(audit.submitTime)}</p>
                </div>
            `;
            
            // 设置审核ID
            document.getElementById('auditId').value = auditId;
            
            // 重置表单
            document.getElementById('auditForm').reset();
            document.getElementById('auditComment').removeAttribute('required');
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('auditModal'));
            modal.show();
        }
    } catch (error) {
        console.error('Failed to load audit detail:', error);
        window.adminAudit.showError('加载审核详情失败');
    }
}

async function submitAudit() {
    const form = document.getElementById('auditForm');
    const formData = new FormData(form);
    
    // 验证表单
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const auditData = {
        auditId: formData.get('auditId'),
        auditResult: parseInt(formData.get('auditResult')),
        auditComment: formData.get('auditComment')
    };
    
    // 如果是拒绝但没有填写意见
    if (auditData.auditResult === 2 && !auditData.auditComment) {
        window.adminAudit.showError('拒绝时必须填写审核意见');
        return;
    }
    
    try {
        const response = await window.adminAudit.fetchAPI('/api/admin/audit/submit', {
            method: 'POST',
            body: JSON.stringify(auditData)
        });
        
        if (response.success) {
            window.adminAudit.showSuccess('审核提交成功');
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('auditModal'));
            modal.hide();
            
            // 刷新数据
            window.adminAudit.loadAuditStats();
            window.adminAudit.loadTabData(window.adminAudit.currentTab, window.adminAudit.currentPage);
        } else {
            window.adminAudit.showError(response.message || '审核提交失败');
        }
    } catch (error) {
        console.error('Failed to submit audit:', error);
        window.adminAudit.showError('网络错误，审核提交失败');
    }
}

async function approveAudit(auditId) {
    if (!confirm('确定要通过这个题目吗？')) {
        return;
    }
    
    try {
        const response = await window.adminAudit.fetchAPI('/api/admin/audit/approve', {
            method: 'POST',
            body: JSON.stringify({ auditId: auditId })
        });
        
        if (response.success) {
            window.adminAudit.showSuccess('审核通过成功');
            window.adminAudit.loadAuditStats();
            window.adminAudit.loadTabData(window.adminAudit.currentTab, window.adminAudit.currentPage);
        } else {
            window.adminAudit.showError(response.message || '审核通过失败');
        }
    } catch (error) {
        console.error('Failed to approve audit:', error);
        window.adminAudit.showError('网络错误，审核通过失败');
    }
}

async function rejectAudit(auditId) {
    const reason = prompt('请输入拒绝原因：');
    if (!reason) {
        return;
    }
    
    try {
        const response = await window.adminAudit.fetchAPI('/api/admin/audit/reject', {
            method: 'POST',
            body: JSON.stringify({ auditId: auditId, auditComment: reason })
        });
        
        if (response.success) {
            window.adminAudit.showSuccess('审核拒绝成功');
            window.adminAudit.loadAuditStats();
            window.adminAudit.loadTabData(window.adminAudit.currentTab, window.adminAudit.currentPage);
        } else {
            window.adminAudit.showError(response.message || '审核拒绝失败');
        }
    } catch (error) {
        console.error('Failed to reject audit:', error);
        window.adminAudit.showError('网络错误，审核拒绝失败');
    }
}

function showBatchAuditModal() {
    const modal = new bootstrap.Modal(document.getElementById('batchAuditModal'));
    document.getElementById('batchAuditForm').reset();
    modal.show();
}

async function submitBatchAudit() {
    const form = document.getElementById('batchAuditForm');
    const formData = new FormData(form);
    
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // ⚠️ 修复安全问题：获取实际选中的审核项
    const selectedAudits = document.querySelectorAll('.audit-checkbox:checked');
    const auditIds = Array.from(selectedAudits).map(cb => parseInt(cb.value));
    
    if (auditIds.length === 0) {
        window.adminAudit.showError('请先选择要批量操作的审核项');
        return;
    }
    
    const action = formData.get('batchAction');
    const comment = formData.get('batchComment');
    
    if (!confirm(`确定要执行批量${action === 'approve' ? '通过' : '拒绝'}操作吗？（共 ${auditIds.length} 个审核项）`)) {
        return;
    }
    
    try {
        showLoading('批量审核中...');
        
        // action和comment作为URL参数，auditIds作为JSON body
        const params = new URLSearchParams({
            action: action,
            comment: comment
        });
        
        const response = await window.adminAudit.fetchAPI(`/api/admin/audit/batch?${params}`, {
            method: 'POST',
            body: JSON.stringify(auditIds) // 传递实际选中的审核ID
        });
        
        hideLoading();
        
        if (response.success || (response.code === 200)) {
            const data = response.data || response;
            const successCount = data.successCount || auditIds.length;
            const failureCount = data.failureCount || 0;
            
            window.adminAudit.showSuccess(`批量${action === 'approve' ? '通过' : '拒绝'}成功！成功 ${successCount} 个${failureCount > 0 ? `，失败 ${failureCount} 个` : ''}`);
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('batchAuditModal'));
            modal.hide();
            
            // 刷新数据
            window.adminAudit.loadAuditStats();
            window.adminAudit.loadTabData(window.adminAudit.currentTab, window.adminAudit.currentPage);
        } else {
            window.adminAudit.showError(response.message || '批量操作失败');
        }
    } catch (error) {
        hideLoading();
        console.error('Failed to submit batch audit:', error);
        window.adminAudit.showError('网络错误，批量操作失败');
    }
}

function exportAuditData() {
    window.adminAudit.showError('导出功能开发中...');
}

function refreshAuditData() {
    window.adminAudit.loadAuditStats();
    window.adminAudit.loadTabData(window.adminAudit.currentTab, window.adminAudit.currentPage);
}

function showAuditDetail(auditId) {
    showAuditModal(auditId);
}

// 共享的全局函数
function showUserProfile() {
    alert('个人资料功能开发中...');
}

function showSettings() {
    alert('系统设置功能开发中...');
}

function logout() {
    // 清理localStorage
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminUser');
    localStorage.removeItem('token'); // 也清理普通用户token
    
    // 清理cookies
    document.cookie = "Authorization=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    document.cookie = "JWT_TOKEN=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    
    // 停止自动刷新
    if (window.adminAudit) {
        window.adminAudit.stopAutoRefresh();
    }
    
    console.log('已清理所有认证信息');
    
    // 重定向到管理员登录页面
    window.location.href = '/admin/login';
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.adminAudit = new AdminAuditManagement();
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (window.adminAudit) {
        window.adminAudit.stopAutoRefresh();
    }
});