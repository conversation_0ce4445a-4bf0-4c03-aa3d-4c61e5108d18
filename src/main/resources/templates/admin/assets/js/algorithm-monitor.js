/**
 * 遗传算法监控页面JavaScript
 * 负责图表初始化、数据更新、用户交互等功能
 */

// 全局变量
let fitnessChart = null;
let heatmapChart = null;
let constraintChart = null;
let convergenceChart = null;
let isRealTimeEnabled = true;
let refreshInterval = null;
let currentPaper = null;

// 配置常量
const CONFIG = {
    refreshInterval: 5000, // 5秒刷新间隔
    maxDataPoints: 100,
    chartColors: {
        primary: '#007bff',
        success: '#28a745',
        warning: '#ffc107',
        danger: '#dc3545',
        info: '#17a2b8',
        secondary: '#6c757d'
    },
    fitnessComponents: [
        '难度分布', '知识点覆盖', '题型平衡', '时间分配',
        '分值分布', '认知层次', '重复度控制', '综合质量'
    ]
};

/**
 * 页面初始化
 */
function initializeMonitor() {
    console.log('初始化算法监控页面...');
    
    // 初始化图表
    initializeCharts();
    
    // 加载初始数据
    loadInitialData();
    
    // 设置事件监听器
    setupEventListeners();
    
    // 启动自动刷新
    startAutoRefresh();

    // 如果默认启用了实时监控, 更新图标为暂停
    const rtIcon = document.getElementById('fitnessRealTimeIcon');
    if (rtIcon) {
        rtIcon.className = 'fas fa-pause';
    }
    
    console.log('算法监控页面初始化完成');
}

/**
 * 初始化所有图表
 */
function initializeCharts() {
    initializeFitnessRadarChart();
    initializePopulationHeatmap();
    initializeConstraintAnalysisChart();
    initializeConvergenceAnalysisChart();
}

/**
 * 初始化适应度雷达图
 */
function initializeFitnessRadarChart() {
    const chartDom = document.getElementById('fitnessRadarChart');
    fitnessChart = echarts.init(chartDom);
    
    const option = {
        title: {
            text: '多维度适应度分析',
            left: 'center',
            textStyle: {
                fontSize: 14,
                fontWeight: 'normal'
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                return `${params.name}: ${params.value}`;
            }
        },
        legend: {
            data: ['当前最优', '历史最优', '平均水平'],
            orient: 'horizontal',
            bottom: 10
        },
        radar: {
            indicator: CONFIG.fitnessComponents.map(name => ({
                name: name,
                max: 1.0,
                min: 0.0
            })),
            radius: '60%',
            center: ['50%', '45%'],
            splitNumber: 4,
            shape: 'polygon',
            splitArea: {
                areaStyle: {
                    color: ['rgba(114, 172, 209, 0.2)', 'rgba(114, 172, 209, 0.1)']
                }
            },
            splitLine: {
                lineStyle: {
                    color: 'rgba(114, 172, 209, 0.5)'
                }
            }
        },
        series: [{
            name: '适应度分析',
            type: 'radar',
            data: [
                {
                    value: [0.85, 0.92, 0.78, 0.88, 0.75, 0.82, 0.90, 0.86],
                    name: '当前最优',
                    areaStyle: {
                        color: 'rgba(0, 123, 255, 0.3)'
                    },
                    lineStyle: {
                        color: CONFIG.chartColors.primary,
                        width: 2
                    }
                },
                {
                    value: [0.88, 0.95, 0.82, 0.91, 0.79, 0.85, 0.93, 0.89],
                    name: '历史最优',
                    areaStyle: {
                        color: 'rgba(40, 167, 69, 0.2)'
                    },
                    lineStyle: {
                        color: CONFIG.chartColors.success,
                        width: 2
                    }
                },
                {
                    value: [0.72, 0.78, 0.65, 0.75, 0.68, 0.71, 0.76, 0.73],
                    name: '平均水平',
                    areaStyle: {
                        color: 'rgba(108, 117, 125, 0.1)'
                    },
                    lineStyle: {
                        color: CONFIG.chartColors.secondary,
                        width: 1,
                        type: 'dashed'
                    }
                }
            ]
        }]
    };
    
    fitnessChart.setOption(option);
}

/**
 * 初始化种群进化热力图
 */
function initializePopulationHeatmap() {
    const chartDom = document.getElementById('populationHeatmap');
    heatmapChart = echarts.init(chartDom);
    
    // 生成模拟数据
    const data = generateHeatmapData();
    
    const option = {
        title: {
            text: '种群进化热力图',
            left: 'center',
            textStyle: {
                fontSize: 14,
                fontWeight: 'normal'
            }
        },
        tooltip: {
            position: 'top',
            formatter: function(params) {
                return `代数: ${params.data[0]}<br/>个体: ${params.data[1]}<br/>适应度: ${params.data[2]}`;
            }
        },
        grid: {
            height: '70%',
            top: '15%',
            left: '10%',
            right: '10%'
        },
        xAxis: {
            type: 'category',
            data: Array.from({length: 100}, (_, i) => i + 1),
            name: '代数',
            nameLocation: 'middle',
            nameGap: 30,
            splitArea: {
                show: true
            }
        },
        yAxis: {
            type: 'category',
            data: Array.from({length: 50}, (_, i) => `个体${i + 1}`),
            name: '个体',
            nameLocation: 'middle',
            nameGap: 50,
            splitArea: {
                show: true
            }
        },
        visualMap: {
            min: 0,
            max: 1,
            calculable: true,
            orient: 'horizontal',
            left: 'center',
            bottom: '5%',
            inRange: {
                color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffbf', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
            }
        },
        series: [{
            name: '适应度',
            type: 'heatmap',
            data: data,
            label: {
                show: false
            },
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };
    
    heatmapChart.setOption(option);
}

/**
 * 初始化约束分析图表
 */
function initializeConstraintAnalysisChart() {
    const chartDom = document.getElementById('constraintAnalysisChart');
    constraintChart = echarts.init(chartDom);
    
    const option = {
        title: {
            text: '约束处理统计',
            left: 'center',
            textStyle: {
                fontSize: 14,
                fontWeight: 'normal'
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: ['约束违反', '修复成功', '修复失败'],
            bottom: 10
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: ['难度约束', '知识点约束', '题型约束', '时间约束', '分值约束', '重复度约束'],
            axisLabel: {
                rotate: 45
            }
        },
        yAxis: {
            type: 'value',
            name: '次数'
        },
        series: [
            {
                name: '约束违反',
                type: 'bar',
                stack: '总量',
                data: [25, 18, 32, 12, 28, 15],
                itemStyle: {
                    color: CONFIG.chartColors.danger
                }
            },
            {
                name: '修复成功',
                type: 'bar',
                stack: '总量',
                data: [22, 16, 28, 11, 25, 13],
                itemStyle: {
                    color: CONFIG.chartColors.success
                }
            },
            {
                name: '修复失败',
                type: 'bar',
                stack: '总量',
                data: [3, 2, 4, 1, 3, 2],
                itemStyle: {
                    color: CONFIG.chartColors.warning
                }
            }
        ]
    };
    
    constraintChart.setOption(option);
}

/**
 * 初始化收敛分析图表
 */
function initializeConvergenceAnalysisChart() {
    const chartDom = document.getElementById('convergenceAnalysisChart');
    convergenceChart = echarts.init(chartDom);
    
    // 生成收敛曲线数据
    const convergenceData = generateFullConvergenceData();
    
    const option = {
        title: {
            text: '收敛性分析',
            left: 'center',
            textStyle: {
                fontSize: 14,
                fontWeight: 'normal'
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            }
        },
        legend: {
            data: ['最优适应度', '平均适应度', '多样性指数'],
            bottom: 10
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: convergenceData.generations,
            name: '代数'
        },
        yAxis: [
            {
                type: 'value',
                name: '适应度',
                min: 0,
                max: 1,
                position: 'left'
            },
            {
                type: 'value',
                name: '多样性',
                min: 0,
                max: 1,
                position: 'right'
            }
        ],
        series: [
            {
                name: '最优适应度',
                type: 'line',
                data: convergenceData.bestFitness,
                smooth: true,
                lineStyle: {
                    color: CONFIG.chartColors.primary,
                    width: 2
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(0, 123, 255, 0.3)' },
                        { offset: 1, color: 'rgba(0, 123, 255, 0.1)' }
                    ])
                }
            },
            {
                name: '平均适应度',
                type: 'line',
                data: convergenceData.avgFitness,
                smooth: true,
                lineStyle: {
                    color: CONFIG.chartColors.success,
                    width: 2
                }
            },
            {
                name: '多样性指数',
                type: 'line',
                yAxisIndex: 1,
                data: convergenceData.diversity,
                smooth: true,
                lineStyle: {
                    color: CONFIG.chartColors.info,
                    width: 2,
                    type: 'dashed'
                }
            }
        ]
    };
    
    convergenceChart.setOption(option);
}

/**
 * 生成热力图数据
 */
function generateHeatmapData() {
    const data = [];
    for (let generation = 1; generation <= 100; generation++) {
        for (let individual = 1; individual <= 50; individual++) {
            // 模拟适应度随代数增加而提高的趋势
            const baseFitness = Math.min(0.9, generation / 100 * 0.8 + 0.1);
            const noise = (Math.random() - 0.5) * 0.2;
            const fitness = Math.max(0, Math.min(1, baseFitness + noise));
            data.push([generation, individual, parseFloat(fitness.toFixed(3))]);
        }
    }
    return data;
}

/**
 * 生成收敛曲线数据
 */
function generateConvergenceData(generations = 100, targetFitness = 0.95) {
    const data = [];
    let fitness = 0.1;
    
    for (let i = 0; i < generations; i++) {
        // 逐渐趋向目标适应度
        fitness = Math.min(targetFitness, fitness + Math.random() * 0.02);
        data.push(parseFloat(fitness.toFixed(3)));
    }
    
    return data;
}

/**
 * 生成多样性数据
 */
function generateDiversityData(generations) {
    const data = [];
    let diversity = 0.9;
    
    for (let i = 0; i < generations; i++) {
        // 模拟多样性逐渐降低但不过于低的趋势
        diversity *= (0.995 - Math.random() * 0.01);
        if (diversity < 0.1) diversity = 0.1 + Math.random() * 0.05;
        
        data.push({
            generation: i + 1,
            diversity: diversity
        });
    }
    
    return data;
}

// 生成完整的收敛曲线数据
function generateFullConvergenceData() {
    const generations = 100;
    const generationData = [];
    const bestFitnessData = [];
    const avgFitnessData = [];
    const diversityData = [];
    
    let bestFitness = 0.3;
    let avgFitness = 0.2;
    let diversity = 0.9;
    
    for (let i = 0; i < generations; i++) {
        // 模拟适应度逐渐提高
        bestFitness += (0.95 - bestFitness) * 0.05 + Math.random() * 0.02;
        avgFitness += (0.85 - avgFitness) * 0.03 + Math.random() * 0.015;
        
        // 模拟多样性逐渐降低
        diversity *= (0.995 - Math.random() * 0.01);
        if (diversity < 0.1) diversity = 0.1 + Math.random() * 0.05;
        
        generationData.push(i + 1);
        bestFitnessData.push(bestFitness);
        avgFitnessData.push(avgFitness);
        diversityData.push(diversity);
    }
    
    return {
        generations: generationData,
        bestFitness: bestFitnessData,
        avgFitness: avgFitnessData,
        diversity: diversityData
    };
}

/**
 * 生成完整的收敛数据（包含best, average, diversity）
 */
function generateFullConvergenceData() {
    const best = [];
    const average = [];
    const diversity = [];
    
    let bestFitness = 0.1;
    let avgFitness = 0.05;
    let diversityIndex = 0.9;
    
    for (let i = 0; i < 100; i++) {
        // 最优适应度逐渐提高
        bestFitness = Math.min(0.95, bestFitness + Math.random() * 0.02);
        best.push(parseFloat(bestFitness.toFixed(3)));
        
        // 平均适应度跟随最优适应度但略低
        avgFitness = Math.min(bestFitness - 0.1, avgFitness + Math.random() * 0.015);
        average.push(parseFloat(avgFitness.toFixed(3)));
        
        // 多样性指数逐渐降低
        diversityIndex = Math.max(0.1, diversityIndex - Math.random() * 0.01);
        diversity.push(parseFloat(diversityIndex.toFixed(3)));
    }
    
    return { best, average, diversity };
}

/**
 * 加载初始数据
 */
function loadInitialData() {
    // 加载概览数据
    loadOverviewData();
    
    // 加载详细表格数据
    loadTableData();
    
    // 更新图表数据
    updateChartsData();
}

/**
 * 加载概览数据
 */
function loadOverviewData() {
    fetch('/admin/algorithm-monitor/api/overview')
        .then(response => response.json())
        .then(data => {
            document.getElementById('runningAlgorithms').textContent = data.runningAlgorithms || 0;
            document.getElementById('todayPapers').textContent = data.todayPapers || 0;
            document.getElementById('avgGenerations').textContent = data.avgGenerations || 0;
            document.getElementById('anomalyCount').textContent = data.anomalyCount || 0;
        })
        .catch(error => {
            console.error('加载概览数据失败:', error);
            // 使用默认值
            document.getElementById('runningAlgorithms').textContent = 0;
            document.getElementById('todayPapers').textContent = 0;
            document.getElementById('avgGenerations').textContent = 0;
            document.getElementById('anomalyCount').textContent = 0;
        });
}

/**
 * 加载表格数据
 */
function loadTableData() {
    const dateRange = document.getElementById('dateRange')?.value || 'today';
    const statusFilter = document.getElementById('statusFilter')?.value || 'all';
    const performanceFilter = document.getElementById('performanceFilter')?.value || 'all';
    
    const params = new URLSearchParams({
        dateRange: dateRange,
        status: statusFilter,
        performance: performanceFilter
    });
    
    fetch(`/admin/algorithm-monitor/api/papers?${params}`)
        .then(response => response.json())
        .then(data => {
            const tableData = data || [];
            
            const tbody = document.getElementById('algorithmDetailsBody');
            // 如果页面中不存在表格，则直接跳过后续处理，避免空引用错误
            if (!tbody) {
                console.warn('algorithmDetailsBody element not found. Skip rendering table data.');
                return;
            }
            tbody.innerHTML = '';
            
            tableData.forEach(row => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${row.paperId}</td>
                    <td>${new Date(row.startTime).toLocaleString()}</td>
                    <td>
                        <span class="badge bg-${getStatusColor(row.status)}">
                            ${getStatusText(row.status)}
                        </span>
                    </td>
                    <td>${row.generation || 0}</td>
                    <td>${(row.bestFitness || 0).toFixed(4)}</td>
                    <td>${(row.convergenceSpeed || 0).toFixed(3)}</td>
                    <td>${(row.progress || 0).toFixed(1)}%</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="viewDetails('${row.paperId}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="exportData('${row.paperId}')">
                            <i class="fas fa-download"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        })
        .catch(error => {
            console.error('加载表格数据失败:', error);
            // 显示错误信息
            const tbody = document.getElementById('algorithmDetailsBody');
            if (tbody) {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">加载数据失败</td></tr>';
            }
        });
}

/**
 * 获取状态对应的颜色
 */
function getStatusColor(status) {
    switch (status) {
        case 'running': return 'primary';
        case 'completed': return 'success';
        case 'failed': return 'danger';
        case 'paused': return 'warning';
        case 'stopped': return 'secondary';
        default: return 'light';
    }
}

/**
 * 获取状态对应的文本
 */
function getStatusText(status) {
    switch (status) {
        case 'running': return '运行中';
        case 'completed': return '已完成';
        case 'failed': return '失败';
        case 'paused': return '暂停';
        case 'stopped': return '已停止';
        default: return '未知';
    }
}

/**
 * 更新适应度雷达图
 */
function updateFitnessRadarChart(paperId) {
    if (!paperId) {
        console.warn('试卷ID不存在，使用默认数据');
        updateDefaultFitnessChart();
        return;
    }
    
    fetch(`/admin/algorithm-monitor/api/fitness-radar/${paperId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('从后端获取的适应度雷达图数据:', JSON.stringify(data, null, 2));

            if (fitnessChart && data && data.dimensions && data.values && 
                data.dimensions.length === data.values.length && data.dimensions.length > 0) {
                
                // 生成当前最优、历史最优和平均水平的数据
                const currentValues = data.values;
                const historyValues = data.values.map(v => Math.min(v + 0.02, 1.0));
                const avgValues = data.values.map(v => Math.max(v - 0.1, 0.0));
                
                fitnessChart.clear();
                fitnessChart.setOption({
                    radar: {
                        indicator: data.dimensions.map(dim => ({ 
                            name: dim, 
                            max: 1.0,
                            min: 0.0 
                        })),
                        radius: '60%',
                        center: ['50%', '45%'],
                        splitNumber: 4,
                        shape: 'polygon'
                    },
                    series: [{
                        name: '适应度分析',
                        type: 'radar',
                        data: [
                            {
                                value: currentValues,
                                name: '当前最优',
                                areaStyle: { color: 'rgba(0, 123, 255, 0.3)' },
                                lineStyle: { color: CONFIG.chartColors.primary, width: 2 }
                            },
                            {
                                value: historyValues,
                                name: '历史最优',
                                areaStyle: { color: 'rgba(40, 167, 69, 0.2)' },
                                lineStyle: { color: CONFIG.chartColors.success, width: 2 }
                            },
                            {
                                value: avgValues,
                                name: '平均水平',
                                areaStyle: { color: 'rgba(108, 117, 125, 0.1)' },
                                lineStyle: { color: CONFIG.chartColors.secondary, width: 1, type: 'dashed' }
                            }
                        ]
                    }]
                });
            } else {
                console.warn('适应度数据格式不正确，使用默认数据:', data);
                updateDefaultFitnessChart();
            }
        })
        .catch(error => {
            console.error('更新适应度雷达图失败:', error);
            // 使用默认数据
            updateDefaultFitnessChart();
        });
}

/**
 * 更新收敛分析图
 */
function updateConvergenceChart(paperId) {
    fetch(`/admin/algorithm-monitor/api/convergence/${paperId}`)
        .then(response => response.json())
        .then(data => {
            if (convergenceChart && data.generations) {
                convergenceChart.setOption({
                    xAxis: {
                        data: data.generations
                    },
                    series: [
                        {
                            name: '最优适应度',
                            data: data.bestFitness
                        },
                        {
                            name: '平均适应度',
                            data: data.avgFitness
                        },
                        {
                            name: '多样性指数',
                            data: data.diversity
                        }
                    ]
                });
            }
        })
        .catch(error => {
            console.error('更新收敛分析图失败:', error);
            updateDefaultConvergenceChart();
        });
}

/**
 * 更新种群热力图
 */
function updatePopulationHeatmap(paperId) {
    fetch(`/admin/algorithm-monitor/api/population-heatmap/${paperId}`)
        .then(response => response.json())
        .then(data => {
            if (heatmapChart && data.length > 0) {
                heatmapChart.setOption({
                    series: [{
                        data: data
                    }]
                });
            }
        })
        .catch(error => {
            console.error('更新种群热力图失败:', error);
            updateDefaultPopulationChart();
        });
}

/**
 * 更新默认约束图表
 */
function updateDefaultConstraintChart() {
    if (constraintChart) {
        const defaultConstraintTypes = ['难度约束', '知识点约束', '题型约束', '时间约束', '分值约束', '重复度约束'];
        const defaultViolationData = [25, 18, 32, 12, 28, 15];
        const defaultFixedData = [22, 16, 28, 11, 25, 13];
        const defaultFailedData = [3, 2, 4, 1, 3, 2];
        
        constraintChart.setOption({
            xAxis: {
                data: defaultConstraintTypes
            },
            series: [
                {
                    name: '违反约束',
                    data: defaultViolationData
                },
                {
                    name: '修复成功',
                    data: defaultFixedData
                },
                {
                    name: '修复失败',
                    data: defaultFailedData
                }
            ]
        });
    }
}

/**
 * 更新聚合的真实数据（没有选中具体试卷时）
 */
function updateAggregatedChartsData() {
    // 获取所有试卷的聚合适应度数据
    fetch('/admin/algorithm-monitor/api/fitness-radar/all')
        .then(response => response.json())
        .then(data => {
            if (fitnessChart && data.dimensions && data.values && data.values.length > 0 && data.dimensions.length === data.values.length) {
                // 生成当前最优、历史最优和平均水平的聚合数据
                const currentValues = data.values;
                const historyValues = data.values.map(v => Math.min(v + 0.03, 1.0));
                const avgValues = data.values.map(v => Math.max(v - 0.08, 0.0));
                
                fitnessChart.clear();
                fitnessChart.setOption({
                    radar: {
                        indicator: data.dimensions.map(dim => ({ 
                            name: dim, 
                            max: 1.0,
                            min: 0.0 
                        })),
                        radius: '60%',
                        center: ['50%', '45%'],
                        splitNumber: 4,
                        shape: 'polygon'
                    },
                    series: [{
                        name: '适应度分析',
                        type: 'radar',
                        data: [
                            {
                                value: currentValues,
                                name: '当前最优',
                                areaStyle: { color: 'rgba(0, 123, 255, 0.3)' },
                                lineStyle: { color: CONFIG.chartColors.primary, width: 2 }
                            },
                            {
                                value: historyValues,
                                name: '历史最优',
                                areaStyle: { color: 'rgba(40, 167, 69, 0.2)' },
                                lineStyle: { color: CONFIG.chartColors.success, width: 2 }
                            },
                            {
                                value: avgValues,
                                name: '平均水平',
                                areaStyle: { color: 'rgba(108, 117, 125, 0.1)' },
                                lineStyle: { color: CONFIG.chartColors.secondary, width: 1, type: 'dashed' }
                            }
                        ]
                    }]
                });
            }
        })
        .catch(error => {
            console.error('更新聚合适应度数据失败:', error);
            updateDefaultFitnessChart();
        });
    
    // 获取所有试卷的聚合收敛数据
    fetch('/admin/algorithm-monitor/api/convergence/all')
        .then(response => response.json())
        .then(data => {
            if (convergenceChart && data.generations) {
                convergenceChart.setOption({
                    xAxis: {
                        data: data.generations
                    },
                    series: [{
                        name: '最优值',
                        type: 'line',
                        data: data.bestFitness,
                        smooth: true,
                        lineStyle: { color: CONFIG.chartColors.primary, width: 2 }
                    }, {
                        name: '平均值',
                        type: 'line',
                        data: data.avgFitness,
                        smooth: true,
                        lineStyle: { color: CONFIG.chartColors.secondary, width: 2 }
                    }]
                });
            }
        })
        .catch(error => {
            console.error('更新聚合收敛数据失败:', error);
            updateDefaultConvergenceChart();
        });
    
    // 获取所有试卷的聚合种群数据
    fetch('/admin/algorithm-monitor/api/population/all')
        .then(response => response.json())
        .then(data => {
            if (heatmapChart && data.heatmapData) {
                heatmapChart.setOption({
                    xAxis: {
                        data: data.xLabels || data.heatmapData[0].map((_, i) => `个体${i+1}`)
                    },
                    yAxis: {
                        data: data.yLabels || data.heatmapData.map((_, i) => `代数${i+1}`)
                    },
                    series: [{
                        data: data.heatmapData.flatMap((row, y) => 
                            row.map((value, x) => [x, y, value])
                        )
                    }]
                });
            }
        })
        .catch(error => {
            console.error('更新聚合种群数据失败:', error);
            updateDefaultPopulationChart();
        });
    
    // 获取所有试卷的聚合约束数据
    fetch('/admin/algorithm-monitor/api/constraints/all')
        .then(response => response.json())
        .then(data => {
            if (constraintChart && data.violations && data.fixes && data.failures) {
                constraintChart.setOption({
                    series: [{
                        data: [
                            { value: data.violations, name: '违反约束' },
                            { value: data.fixes, name: '修复成功' },
                            { value: data.failures, name: '修复失败' }
                        ]
                    }]
                });
            }
        })
        .catch(error => {
            console.error('更新聚合约束数据失败:', error);
            updateDefaultConstraintChart();
        });
}

/**
 * 更新默认图表数据
 */
function updateDefaultChartsData() {
    updateDefaultFitnessChart();
    updateDefaultConvergenceChart();
    updateDefaultPopulationChart();
    updateDefaultConstraintChart();
}

/**
 * 更新默认适应度图表
 */
function updateDefaultFitnessChart() {
    if (fitnessChart) {
        const defaultCurrentValues = generateRandomFitnessData();
        const defaultHistoryValues = generateRandomFitnessData().map(v => Math.min(v + 0.02, 1.0));
        const defaultAvgValues = generateRandomFitnessData().map(v => Math.max(v - 0.1, 0.0));
        
        fitnessChart.clear();
                fitnessChart.setOption({
            radar: {
                indicator: CONFIG.fitnessComponents.map(component => ({
                    name: component,
                    max: 1.0,
                    min: 0.0
                })),
                radius: '60%',
                center: ['50%', '45%'],
                splitNumber: 4,
                shape: 'polygon'
            },
            series: [{
                name: '适应度分析',
                type: 'radar',
                data: [
                    {
                        value: defaultCurrentValues,
                        name: '当前最优',
                        areaStyle: { color: 'rgba(0, 123, 255, 0.3)' },
                        lineStyle: { color: CONFIG.chartColors.primary, width: 2 }
                    },
                    {
                        value: defaultHistoryValues,
                        name: '历史最优',
                        areaStyle: { color: 'rgba(40, 167, 69, 0.2)' },
                        lineStyle: { color: CONFIG.chartColors.success, width: 2 }
                    },
                    {
                        value: defaultAvgValues,
                        name: '平均水平',
                        areaStyle: { color: 'rgba(108, 117, 125, 0.1)' },
                        lineStyle: { color: CONFIG.chartColors.secondary, width: 1, type: 'dashed' }
                    }
                ]
            }]
        });
    }
}

/**
 * 更新默认收敛图表
 */
function updateDefaultConvergenceChart() {
    if (convergenceChart) {
        const defaultGenerations = Array.from({length: 100}, (_, i) => i + 1);
        const defaultBestFitness = generateConvergenceData(100, 0.95);
        const defaultAvgFitness = generateConvergenceData(100, 0.85);
        const defaultDiversity = generateDiversityData(100);
        
        convergenceChart.setOption({
            xAxis: {
                data: defaultGenerations
            },
            series: [
                { name: '最优适应度', data: defaultBestFitness },
                { name: '平均适应度', data: defaultAvgFitness },
                { name: '多样性指数', data: defaultDiversity }
            ]
        });
    }
}

/**
 * 更新默认种群图表
 */
function updateDefaultPopulationChart() {
    if (heatmapChart) {
        const defaultData = generateHeatmapData();
        heatmapChart.setOption({
            series: [{
                data: defaultData
            }]
        });
    }
}

/**
 * 查看详细信息
 */
function viewDetails(paperId) {
    currentPaper = paperId;
    
    // 加载详细数据
    fetch(`/admin/algorithm-monitor/api/details/${paperId}`)
        .then(response => response.json())
        .then(data => {
            // 更新详细信息面板 - 添加元素存在性检查
            const detailPaperId = document.getElementById('detailPaperId');
            const detailPaperName = document.getElementById('detailPaperName');
            const detailStatus = document.getElementById('detailStatus');
            const detailStartTime = document.getElementById('detailStartTime');
            const detailProgress = document.getElementById('detailProgress');
            
            if (detailPaperId) detailPaperId.textContent = data.paperId || 'N/A';
            if (detailPaperName) detailPaperName.textContent = data.paperName || 'N/A';
            if (detailStatus) detailStatus.textContent = getStatusText(data.status) || 'N/A';
            if (detailStartTime) detailStartTime.textContent = 
                data.startTime ? new Date(data.startTime).toLocaleString() : 'N/A';
            if (detailProgress) detailProgress.textContent = (data.progress || 0) + '%';
            
            // 更新图表
            updateChartsData();
        })
        .catch(error => {
            console.error('加载详细信息失败:', error);
            alert('加载详细信息失败');
        });
}

/**
 * 导出数据
 */
function exportData(paperId) {
    window.open(`/admin/algorithm-monitor/api/export?paperId=${paperId}&format=pdf`, '_blank');
}

// 当前选中的试卷ID已在全局变量中声明

/**
 * 算法控制函数
 */
function controlAlgorithm(paperId, action) {
    fetch(`/admin/algorithm-monitor/api/control/${paperId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: action
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 刷新数据
            loadTableData();
            loadOverviewData();
            alert(`操作成功：${action}`);
        } else {
            alert(`操作失败：${data.message}`);
        }
    })
    .catch(error => {
        console.error('控制算法失败:', error);
        alert('控制算法失败');
    });
}

/**
 * 筛选条件变化处理
 */
function handleFilterChange() {
    loadTableData();
}

/**
 * 获取状态映射文本（为了兼容性保留）
 */
function getStatusMapText(status) {
    const statusMap = {
        running: '运行中',
        completed: '已完成',
        failed: '失败'
    };
    return statusMap[status] || status;
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // 窗口大小改变时重新调整图表
    window.addEventListener('resize', function() {
        if (fitnessChart) fitnessChart.resize();
        if (heatmapChart) heatmapChart.resize();
        if (constraintChart) constraintChart.resize();
        if (convergenceChart) convergenceChart.resize();
    });
}

/**
 * 启动自动刷新
 */
function startAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
    
    refreshInterval = setInterval(() => {
        if (isRealTimeEnabled) {
            refreshMonitor();
        }
    }, CONFIG.refreshInterval);
}

/**
 * 停止自动刷新
 */
function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
    }
}

/**
 * 刷新监控数据
 */
function refreshMonitor() {
    console.log('刷新监控数据...');
    
    // 显示加载状态
    showLoadingState();
    
    // 模拟API调用延迟
    setTimeout(() => {
        loadOverviewData();
        loadTableData();
        updateChartsData();
        hideLoadingState();
    }, 1000);
}

/**
 * 显示加载状态
 */
function showLoadingState() {
    const refreshBtn = document.querySelector('button[onclick="refreshMonitor()"]');
    if (refreshBtn) {
        refreshBtn.innerHTML = '<div class="loading-spinner"></div> 刷新中...';
        refreshBtn.disabled = true;
    }
}

/**
 * 隐藏加载状态
 */
function hideLoadingState() {
    const refreshBtn = document.querySelector('button[onclick="refreshMonitor()"]');
    if (refreshBtn) {
        refreshBtn.innerHTML = '<i class="fas fa-sync"></i> 刷新数据';
        refreshBtn.disabled = false;
    }
}

/**
 * 更新约束分析图表
 */
function updateConstraintChart(paperId) {
    fetch(`/admin/algorithm-monitor/api/constraints/${paperId}`)
        .then(response => response.json())
        .then(data => {
            if (constraintChart && data.constraintTypes && data.violationData) {
                constraintChart.setOption({
                    xAxis: {
                        data: data.constraintTypes
                    },
                    series: [
                        {
                            name: '违反约束',
                            data: data.violationData
                        },
                        {
                            name: '修复成功',
                            data: data.fixedData
                        },
                        {
                            name: '修复失败',
                            data: data.failedData
                        }
                    ]
                });
            }
        })
        .catch(error => {
            console.error('更新约束分析图失败:', error);
            // 使用默认数据保持图表显示
        });
}

/**
 * 更新图表数据
 */
function updateChartsData() {
    if (currentPaper) {
        // 更新适应度雷达图
        updateFitnessRadarChart(currentPaper);
        
        // 更新收敛分析图
        updateConvergenceChart(currentPaper);
        
        // 更新种群热力图
        updatePopulationHeatmap(currentPaper);
        
        // 更新约束分析图
        updateConstraintChart(currentPaper);
    } else {
        // 如果没有选中试卷，获取聚合的真实数据
        updateAggregatedChartsData();
    }
}

/**
 * 生成随机适应度数据
 */
function generateRandomFitnessData() {
    return CONFIG.fitnessComponents.map(() => 
        parseFloat((Math.random() * 0.3 + 0.7).toFixed(3))
    );
}

/**
 * 试卷选择改变事件
 */
function onPaperChange() {
    const paperSelect = document.getElementById('paperSelect');
    currentPaper = paperSelect.value;
    
    if (currentPaper) {
        console.log('选择试卷:', currentPaper);
        // 根据选择的试卷加载对应数据
        loadPaperData(currentPaper);
    }
}

/**
 * 加载特定试卷数据
 */
function loadPaperData(paperId) {
    console.log('加载试卷数据:', paperId);
    // 实现加载特定试卷的监控数据
    refreshMonitor();
}

/**
 * 日期范围改变事件
 */
function onDateRangeChange() {
    const dateRange = document.getElementById('dateRange').value;
    console.log('日期范围改变:', dateRange);
    refreshMonitor();
}

/**
 * 状态筛选改变事件
 */
function onStatusFilterChange() {
    const statusFilter = document.getElementById('statusFilter').value;
    console.log('状态筛选改变:', statusFilter);
    refreshMonitor();
}

/**
 * 性能筛选改变事件
 */
function onPerformanceFilterChange() {
    const performanceFilter = document.getElementById('performanceFilter').value;
    console.log('性能筛选改变:', performanceFilter);
    refreshMonitor();
}

/**
 * 切换实时监控
 */
function toggleRealTime(chartType) {
    isRealTimeEnabled = !isRealTimeEnabled;
    const icon = document.getElementById(chartType + 'RealTimeIcon');
    
    if (isRealTimeEnabled) {
        icon.className = 'fas fa-pause';
        console.log('启用实时监控');
    } else {
        icon.className = 'fas fa-play';
        console.log('停用实时监控');
    }
}

/**
 * 导出图表
 */
function exportChart(chartType) {
    let chart = null;
    let filename = '';
    
    switch (chartType) {
        case 'fitness':
            chart = fitnessChart;
            filename = '适应度分析';
            break;
        case 'heatmap':
            chart = heatmapChart;
            filename = '种群进化热力图';
            break;
        case 'constraint':
            chart = constraintChart;
            filename = '约束分析';
            break;
        case 'convergence':
            chart = convergenceChart;
            filename = '收敛分析';
            break;
    }
    
    if (chart) {
        const url = chart.getDataURL({
            pixelRatio: 2,
            backgroundColor: '#fff'
        });
        
        const link = document.createElement('a');
        link.download = `${filename}_${new Date().toISOString().slice(0, 10)}.png`;
        link.href = url;
        link.click();
    }
}

/**
 * 导出报告
 */
function exportReport() {
    console.log('导出监控报告');
    // 实现报告导出功能
    alert('报告导出功能正在开发中...');
}

/**
 * 显示设置对话框
 */
function showSettings() {
    const settingsModal = new bootstrap.Modal(document.getElementById('settingsModal'));
    settingsModal.show();
}

/**
 * 保存设置
 */
function saveSettings() {
    const refreshInterval = document.getElementById('refreshInterval').value;
    const autoRefresh = document.getElementById('autoRefresh').checked;
    const maxDataPoints = document.getElementById('maxDataPoints').value;
    const showAnimations = document.getElementById('showAnimations').checked;
    
    // 保存设置到本地存储
    localStorage.setItem('monitorSettings', JSON.stringify({
        refreshInterval: parseInt(refreshInterval) * 1000,
        autoRefresh,
        maxDataPoints: parseInt(maxDataPoints),
        showAnimations
    }));
    
    // 应用新设置
    CONFIG.refreshInterval = parseInt(refreshInterval) * 1000;
    CONFIG.maxDataPoints = parseInt(maxDataPoints);
    
    if (autoRefresh) {
        startAutoRefresh();
    } else {
        stopAutoRefresh();
    }
    
    // 关闭模态框
    const settingsModal = bootstrap.Modal.getInstance(document.getElementById('settingsModal'));
    settingsModal.hide();
    
    console.log('设置已保存');
}



/**
 * 切换热力图指标
 */
function toggleHeatmapMetric() {
    console.log('切换热力图指标');
    // 实现指标切换功能
}

/**
 * 显示约束详情
 */
function showConstraintDetails() {
    console.log('显示约束详情');
    // 实现约束详情显示
}

/**
 * 切换收敛视图
 */
function toggleConvergenceView() {
    console.log('切换收敛视图');
    // 实现视图切换功能
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', function() {
    stopAutoRefresh();
    
    if (fitnessChart) fitnessChart.dispose();
    if (heatmapChart) heatmapChart.dispose();
    if (constraintChart) constraintChart.dispose();
    if (convergenceChart) convergenceChart.dispose();
});
