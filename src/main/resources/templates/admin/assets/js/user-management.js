/**
 * 用户管理页面JavaScript
 */

// ID处理工具 - 解决JavaScript大数值精度问题
class IdUtils {
    // 确保ID始终作为字符串处理
    static ensureString(id) {
        if (typeof id === 'number') {
            console.warn('检测到数值型ID，可能存在精度问题:', id);
            return id.toString();
        }
        return String(id);
    }
    
    // 检查ID是否超过JavaScript安全范围
    static isSafeId(id) {
        const numId = Number(id);
        return Number.isSafeInteger(numId);
    }
    
    // 验证ID格式
    static validate(id) {
        const strId = this.ensureString(id);
        if (!strId || strId.trim() === '') {
            return { valid: false, error: 'ID不能为空' };
        }
        if (!/^\d+$/.test(strId)) {
            return { valid: false, error: 'ID格式无效' };
        }
        if (!this.isSafeId(strId)) {
            console.warn('ID超过JavaScript安全范围，请确保以字符串形式处理:', strId);
        }
        return { valid: true, id: strId };
    }
    
    // 批量处理ID数组
    static processBatch(ids) {
        return ids.map(id => this.ensureString(id));
    }
}

class UserManagement {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 10;
        this.searchParams = {
            search: '',
            role: '',
            status: ''
        };
        this.selectedUsers = new Set();
        
        this.init();
    }

    /**
     * 获取认证token
     */
    getAuthToken() {
        return localStorage.getItem('adminToken') || sessionStorage.getItem('adminToken');
    }

    /**
     * 获取默认请求配置
     */
    getDefaultHeaders() {
        const token = this.getAuthToken();
        return {
            'Content-Type': 'application/json',
            'Authorization': token ? `Bearer ${token}` : ''
        };
    }

    /**
     * 处理认证错误
     */
    handleAuthError() {
        this.showError('登录已过期，请重新登录');
        setTimeout(() => {
            window.location.href = '/admin/login';
        }, 1500);
    }
    
    init() {
        this.bindEvents();
        this.loadUserList();
        this.loadUserStats();
    }
    
    bindEvents() {
        // 搜索和筛选
        document.getElementById('searchInput')?.addEventListener('input', 
            this.debounce(() => this.handleSearch(), 500));
        document.getElementById('roleFilter')?.addEventListener('change', 
            () => this.handleFilterChange());
        document.getElementById('statusFilter')?.addEventListener('change', 
            () => this.handleFilterChange());
        
        // 批量操作
        document.getElementById('selectAll')?.addEventListener('change', 
            (e) => this.handleSelectAll(e.target.checked));
        document.getElementById('batchDeleteBtn')?.addEventListener('click', 
            () => this.showBatchDeleteModal());
        
        // 添加用户按钮
        document.querySelector('[onclick="showAddUserModal()"]')?.addEventListener('click', 
            (e) => { e.preventDefault(); this.showAddUserModal(); });
    }
    
    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // 加载用户列表
    async loadUserList(page = 1) {
        try {
            const params = new URLSearchParams({
                page: page,
                size: this.pageSize,
                ...this.searchParams
            });
            
            // 移除空值参数
            for (let [key, value] of params.entries()) {
                if (!value) {
                    params.delete(key);
                }
            }
            
            const response = await fetch(`/api/admin/users?${params}`, {
                headers: this.getDefaultHeaders()
            });
            
            if (response.status === 401) {
                this.handleAuthError();
                return;
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.renderUserList(result.data);
                this.renderPagination(result);
                this.currentPage = page;
            } else {
                this.showError(result.message || '加载用户列表失败');
            }
        } catch (error) {
            console.error('加载用户列表失败:', error);
            this.showError('加载用户列表失败');
        }
    }
    
    // 渲染用户列表
    renderUserList(users) {
        const tbody = document.querySelector('#userTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = users.map(user => {
            // 使用ID工具确保ID安全处理
            const userId = IdUtils.ensureString(user.id);
            const validation = IdUtils.validate(userId);
            
            if (!validation.valid) {
                console.error('无效用户ID:', userId, validation.error);
                return '';
            }
            
            // 使用通用头像工具格式化头像
            const avatarUrl = AvatarUtils.formatAvatarUrl(user.avatar, user);
            
            return `
            <tr>
                <td>
                    <input type="checkbox" class="user-checkbox" value="${userId}" 
                           onchange="userManagement.handleUserSelect('${userId}', this.checked)">
                </td>
                <td>${userId}</td>
                <td>
                    <div class="d-flex align-items-center">
                        <img src="${avatarUrl}" 
                             class="avatar avatar-sm me-2" alt="头像"
                             onerror="AvatarUtils.setAvatarElement(this, null, {id: '${userId}', username: '${user.username}', email: '${user.email}'})">
                        <span>${user.username}</span>
                    </div>
                </td>
                <td>${user.email}</td>
                <td>${user.phone || '-'}</td>
                <td>
                    <span class="badge bg-${this.getRoleBadgeColor(user.role)}">${this.getRoleName(user.role)}</span>
                </td>
                <td>
                    <span class="badge bg-${user.status == 1 ? 'success' : 'danger'}">${user.statusName}</span>
                </td>
                <td>${user.createTime ? new Date(user.createTime).toLocaleDateString() : '-'}</td>
                <td>${user.lastLoginTime ? new Date(user.lastLoginTime).toLocaleString() : '-'}</td>
                <td>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                onclick="userManagement.editUser('${userId}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                onclick="userManagement.resetPassword('${userId}')" title="重置密码">
                            <i class="fas fa-key"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                onclick="userManagement.deleteUser('${userId}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;}).join('');
        
        // 清除选择状态
        this.selectedUsers.clear();
        document.getElementById('selectAll').checked = false;
        this.updateBatchButtons();
    }
    
    // 渲染分页
    renderPagination(result) {
        const pagination = document.querySelector('.pagination');
        if (!pagination) return;
        
        const totalPages = result.pages;
        const current = result.current;
        
        let paginationHtml = '';
        
        // 上一页
        if (current > 1) {
            paginationHtml += `<li class="page-item">
                <a class="page-link" href="#" onclick="userManagement.loadUserList(${current - 1})">上一页</a>
            </li>`;
        }
        
        // 页码
        for (let i = Math.max(1, current - 2); i <= Math.min(totalPages, current + 2); i++) {
            paginationHtml += `<li class="page-item ${i === current ? 'active' : ''}">
                <a class="page-link" href="#" onclick="userManagement.loadUserList(${i})">${i}</a>
            </li>`;
        }
        
        // 下一页
        if (current < totalPages) {
            paginationHtml += `<li class="page-item">
                <a class="page-link" href="#" onclick="userManagement.loadUserList(${current + 1})">下一页</a>
            </li>`;
        }
        
        pagination.innerHTML = paginationHtml;
    }
    
    // 搜索处理
    handleSearch() {
        this.searchParams.search = document.getElementById('searchInput').value;
        this.loadUserList(1);
    }
    
    // 筛选处理
    handleFilterChange() {
        this.searchParams.role = document.getElementById('roleFilter').value;
        this.searchParams.status = document.getElementById('statusFilter').value;
        this.loadUserList(1);
    }
    
    // 用户选择处理
    handleUserSelect(userId, checked) {
        const safeUserId = IdUtils.ensureString(userId);
        if (checked) {
            this.selectedUsers.add(safeUserId);
        } else {
            this.selectedUsers.delete(safeUserId);
        }
        this.updateBatchButtons();
    }
    
    // 全选处理
    handleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.user-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            const userId = IdUtils.ensureString(checkbox.value);
            if (checked) {
                this.selectedUsers.add(userId);
            } else {
                this.selectedUsers.delete(userId);
            }
        });
        this.updateBatchButtons();
    }
    
    // 更新批量操作按钮状态
    updateBatchButtons() {
        const batchDeleteBtn = document.getElementById('batchDeleteBtn');
        if (batchDeleteBtn) {
            batchDeleteBtn.disabled = this.selectedUsers.size === 0;
        }
    }
    
    // 显示添加用户模态框
    showAddUserModal() {
        const modal = new bootstrap.Modal(document.getElementById('addUserModal'));
        document.getElementById('addUserForm').reset();
        modal.show();
        
        // 绑定表单提交事件
        document.getElementById('addUserForm').onsubmit = (e) => {
            e.preventDefault();
            this.addUser();
        };
    }
    
    // 添加用户
    async addUser() {
        try {
            const form = document.getElementById('addUserForm');
            const formData = new FormData(form);
            const userData = {
                username: formData.get('username'),
                password: formData.get('password'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                role: parseInt(formData.get('role')),
                status: parseInt(formData.get('status') || '1'),
                bio: formData.get('bio')
            };
            
            const response = await fetch('/api/admin/users', {
                method: 'POST',
                headers: this.getDefaultHeaders(),
                body: JSON.stringify(userData)
            });
            
            if (response.status === 401) {
                this.handleAuthError();
                return;
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('用户添加成功');
                bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
                this.loadUserList();
                this.loadUserStats();
            } else {
                this.showError(result.message || '添加用户失败');
            }
        } catch (error) {
            console.error('添加用户失败:', error);
            this.showError('添加用户失败');
        }
    }
    
    // 编辑用户
    async editUser(userId) {
        try {
            // ID验证和处理
            const validation = IdUtils.validate(userId);
            if (!validation.valid) {
                this.showError(validation.error);
                return;
            }
            const safeUserId = validation.id;
            
            console.log('编辑用户ID:', safeUserId);
            
            // 获取用户信息
            const response = await fetch(`/api/admin/users/${safeUserId}`, {
                headers: this.getDefaultHeaders()
            });
            
            if (response.status === 401) {
                this.handleAuthError();
                return;
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.showEditUserModal(result.data);
            } else {
                this.showError(result.message || '获取用户信息失败');
            }
        } catch (error) {
            console.error('获取用户信息失败:', error);
            this.showError('获取用户信息失败');
        }
    }
    
    // 显示编辑用户模态框
    showEditUserModal(user) {
        const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
        
        // 确保ID以字符串形式处理
        const safeUserId = IdUtils.ensureString(user.id);
        
        // 填充表单数据
        document.getElementById('editUserId').value = safeUserId;
        document.getElementById('editUsername').textContent = user.username;
        document.getElementById('editEmail').value = user.email;
        document.getElementById('editPhone').value = user.phone || '';
        document.getElementById('editRole').value = user.role;
        document.getElementById('editStatus').value = user.status;
        document.getElementById('editBio').value = user.bio || '';
        
        modal.show();
        
        // 绑定表单提交事件
        document.getElementById('editUserForm').onsubmit = (e) => {
            e.preventDefault();
            this.updateUser(safeUserId);
        };
    }
    
    // 更新用户
    async updateUser(userId) {
        try {
            // ID验证和处理
            const validation = IdUtils.validate(userId);
            if (!validation.valid) {
                this.showError(validation.error);
                return;
            }
            const safeUserId = validation.id;
            
            console.log('更新用户ID:', safeUserId);
            
            const form = document.getElementById('editUserForm');
            const formData = new FormData(form);
            const userData = {
                email: formData.get('email'),
                phone: formData.get('phone'),
                role: parseInt(formData.get('role')),
                status: parseInt(formData.get('status')),
                bio: formData.get('bio')
            };
            
            // 如果有新密码
            const newPassword = formData.get('newPassword');
            if (newPassword) {
                userData.password = newPassword;
            }
            
            const response = await fetch(`/api/admin/users/${safeUserId}`, {
                method: 'PUT',
                headers: this.getDefaultHeaders(),
                body: JSON.stringify(userData)
            });
            
            if (response.status === 401) {
                this.handleAuthError();
                return;
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('用户信息更新成功');
                bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
                this.loadUserList();
            } else {
                this.showError(result.message || '更新用户信息失败');
            }
        } catch (error) {
            console.error('更新用户信息失败:', error);
            this.showError('更新用户信息失败');
        }
    }
    
    // 删除用户
    async deleteUser(userId) {
        // ID验证和处理
        const validation = IdUtils.validate(userId);
        if (!validation.valid) {
            this.showError(validation.error);
            return;
        }
        const safeUserId = validation.id;
        
        if (!confirm('确定要删除这个用户吗？此操作不可恢复。\n\n注意：管理员账户受到保护，无法删除。')) {
            return;
        }
        
        try {
            console.log('删除用户ID:', safeUserId);
            
            const response = await fetch(`/api/admin/users/${safeUserId}`, {
                method: 'DELETE',
                headers: this.getDefaultHeaders()
            });
            
            if (response.status === 401) {
                this.handleAuthError();
                return;
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('用户删除成功');
                this.loadUserList();
                this.loadUserStats();
            } else {
                this.showError(result.message || '删除用户失败');
            }
        } catch (error) {
            console.error('删除用户失败:', error);
            this.showError('删除用户失败');
        }
    }
    
    // 重置密码
    async resetPassword(userId) {
        // ID验证和处理
        const validation = IdUtils.validate(userId);
        if (!validation.valid) {
            this.showError(validation.error);
            return;
        }
        const safeUserId = validation.id;
        
        if (!confirm('确定要重置这个用户的密码吗？')) {
            return;
        }
        
        try {
            console.log('重置密码用户ID:', safeUserId);
            
            const response = await fetch(`/api/admin/users/${safeUserId}/reset-password`, {
                method: 'PUT',
                headers: this.getDefaultHeaders(),
                body: JSON.stringify({})
            });
            
            if (response.status === 401) {
                this.handleAuthError();
                return;
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess(result.data);
            } else {
                this.showError(result.message || '重置密码失败');
            }
        } catch (error) {
            console.error('重置密码失败:', error);
            this.showError('重置密码失败');
        }
    }
    
    // 显示批量删除模态框
    showBatchDeleteModal() {
        if (this.selectedUsers.size === 0) {
            this.showError('请先选择要删除的用户');
            return;
        }
        
        if (confirm(`确定要删除选中的 ${this.selectedUsers.size} 个用户吗？此操作不可恢复。\n\n注意：管理员账户受到保护，无法删除。`)) {
            this.batchDeleteUsers();
        }
    }
    
    // 批量删除用户
    async batchDeleteUsers() {
        try {
            // 使用ID工具处理选中的用户ID
            const idsArray = IdUtils.processBatch(Array.from(this.selectedUsers));
            
            console.log('准备批量删除的用户ID（字符串数组）:', idsArray);
            
            const response = await fetch('/api/admin/users/batch', {
                method: 'DELETE',
                headers: this.getDefaultHeaders(),
                body: JSON.stringify({
                    ids: idsArray
                })
            });
            
            if (response.status === 401) {
                this.handleAuthError();
                return;
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess(result.data);
                this.loadUserList();
                this.loadUserStats();
                this.selectedUsers.clear();
                document.getElementById('selectAll').checked = false;
                this.updateBatchButtons();
            } else {
                this.showError(result.message || '批量删除失败');
            }
        } catch (error) {
            console.error('批量删除失败:', error);
            this.showError('批量删除失败');
        }
    }
    
    // 加载用户统计信息
    async loadUserStats() {
        try {
            const response = await fetch('/api/admin/users/stats', {
                headers: this.getDefaultHeaders()
            });
            
            if (response.status === 401) {
                this.handleAuthError();
                return;
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.renderUserStats(result.data);
            }
        } catch (error) {
            console.error('加载用户统计失败:', error);
        }
    }
    
    // 渲染用户统计信息
    renderUserStats(stats) {
        document.getElementById('totalUsers').textContent = stats.totalUsers || 0;
        document.getElementById('activeUsers').textContent = stats.activeUsers || 0;
        document.getElementById('disabledUsers').textContent = stats.disabledUsers || 0;
        document.getElementById('adminUsers').textContent = stats.adminUsers || 0;
    }
    
    // 导出用户数据
    async exportUsers() {
        try {
            const params = new URLSearchParams(this.searchParams);
            window.open(`/api/admin/users/export?${params}`, '_blank');
        } catch (error) {
            console.error('导出用户数据失败:', error);
            this.showError('导出用户数据失败');
        }
    }
    
    // 获取角色徽章颜色
    getRoleBadgeColor(role) {
        switch (role) {
            case 1: return 'secondary';
            case 2: return 'warning';
            case 3: return 'danger';
            default: return 'secondary';
        }
    }
    
    // 获取角色名称
    getRoleName(role) {
        switch (role) {
            case 1: return '管理员';
            case 2: return '普通用户';
            case 3: return '教师';
            default: return '未知角色';
        }
    }
    
    // 显示成功消息
    showSuccess(message) {
        // 使用 SweetAlert2 或者简单的 alert
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'success',
                title: '成功',
                text: message,
                timer: 2000,
                showConfirmButton: false
            });
        } else {
            alert(message);
        }
    }
    
    // 显示错误消息
    showError(message) {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'error',
                title: '错误',
                text: message
            });
        } else {
            alert('错误: ' + message);
        }
    }
}

// 全局函数，用于模板中的 onclick 事件
function showAddUserModal() {
    window.userManagement.showAddUserModal();
}

function exportUsers() {
    window.userManagement.exportUsers();
}

function importUsers() {
    // TODO: 实现导入用户功能
    alert('导入功能正在开发中...');
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.userManagement = new UserManagement();
}); 